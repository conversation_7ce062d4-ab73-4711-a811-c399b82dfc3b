<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.wheel.LuckyWheelParticipantsInfoMapper">

    <select id="sumWheelData" resultType="com.simi.common.dto.wheel.LuckyWheelDataDTO">
        select
        wheel_id as wheelId,
        sum(fee) as totalFee,
        count(if(disuse_timestamp 	&gt; #{timestamp} or disuse_timestamp = 0, uid, null)) as participatingNumber,
        count(uid) as totalParticipateNumber,
        GROUP_CONCAT(if(disuse_timestamp &gt; #{timestamp} or disuse_timestamp = 0 , uid, null)) as uidList
        from
        simi.lucky_wheel_participants_info
        where wheel_id = #{wheelId}
        group by wheel_id
    </select>
</mapper>