<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.tag.AttestationTagInfoMapper">

    <select id="getEffectiveTag" resultType="com.simi.entity.tag.AttestationTagInfo">
        select
            *
        from attestation_tag_info
        <where>
            <if test="uid != null and uid > 0">
                and uid = #{uid}
            </if>
            <if test="tagStatus != null">
                <if test="tagStatus == 1">
                    and start_time  &gt;= #{timestamp}
                </if>
                <if test="tagStatus == 2">
                    and ((start_time &lt;= #{timestamp} and end_time &gt;= #{timestamp}) or start_time = 0 )
                </if>
                <if test="tagStatus == 3">
                    and end_time  &lt; #{timestamp} and end_time &gt; 0
                </if>
            </if>
        </where>
        order by create_time desc
    </select>
</mapper>