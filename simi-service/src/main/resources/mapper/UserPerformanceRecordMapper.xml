<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.bd.UserPerformanceRecordMapper">

    <select id="listGroupByBdId" resultType="com.simi.common.vo.bd.BDPerformanceItem">
        select
        bd_uid,
        performance_month,
        count(distinct anchor_uid) as anchorAmount,
        sum(income) as income,
        sum(performance) as performance,
        max(issue_status) as issueStatus,
        max(proportion) as proportion,
        GROUP_CONCAT(distinct guild_uid) as guideIdStr
        from
        simi.user_performance_record
        <where>
            <if test="bdUid != null">
                and bd_uid= #{bdUid}
            </if>
            <if test="monthGte != null">
                and performance_month &gt;= #{monthGte}
            </if>
            <if test="monthLte != null">
                and performance_month &lt;=#{monthLte}
            </if>
            <if test="issueStatus != null">
                and issue_status =#{issueStatus}
            </if>
            and is_deleted = 0
        </where>
        group by
        bd_uid,
        performance_month
        order by performance_month desc,performance desc
    </select>

    <select id="sumTotalPerformance" resultType="com.simi.common.dto.bd.BDPerformanceDTO">
        select
        sum(income) as totalIncome,
        sum(performance) as totalPerformance,
        count(distinct guild_uid) as totalGuideAmount,
        count(distinct anchor_uid) as totalAnchorAmount
        from
        simi.user_performance_record
        <where>
            <if test="bdUid != null">
                and bd_uid= #{bdUid}
            </if>
            <if test="monthGte != null">
                and performance_month &gt;= #{monthGte}
            </if>
            <if test="monthLte != null">
                and performance_month &lt;=#{monthLte}
            </if>
            <if test="issueStatus != null">
                and issue_status =#{issueStatus}
            </if>
            <if test="guideId != null">
                and guild_uid =#{guideId}
            </if>
            and is_deleted = 0
        </where>
    </select>

    <select id="selectAnchorList" resultType="com.simi.common.vo.bd.AnchorPerformanceItem">
        select
            bd_uid,
            performance_month,
            max(binding_time) as binding_time,
            guild_uid,
            anchor_uid,
            sum(performance) as performance
        from simi.user_performance_record
        <where>
            <if test="bdUid != null">
                and bd_uid= #{bdUid}
            </if>
            <if test="monthGte != null">
                and performance_month &gt;= #{monthGte}
            </if>
            <if test="monthLte != null">
                and performance_month &lt;=#{monthLte}
            </if>
            <if test="anchorId != null">
                and anchor_uid =#{anchorId}
            </if>
            <if test="guideId != null">
                and guild_uid =#{guideId}
            </if>
            and is_deleted = 0
        </where>
        group by anchor_uid,performance_month,guild_uid,bd_uid
        order by performance_month desc, performance desc
    </select>

    <select id="selectByParentUserId" resultType="com.simi.common.vo.bd.MemberPerformanceItem">
        select
            ${groupField} as userId,
            sum(performance) as performance,
            count(distinct anchor_uid) as totalAnchorAmount
        from simi.user_performance_record
        where ${parentUserField} = #{parentUserId}
        and performance_month = #{performanceMonth} and is_deleted = 0
        <if test="level != null">
            and level=#{level}
        </if>
        group by  ${groupField}
        order by performance desc,totalAnchorAmount desc
    </select>

    <update id="updateIncome">
        update simi.user_performance_record
        set proportion = #{proportion},income = performance * #{proportion}
        where
            performance_month = #{performanceMonth} and bd_uid = #{bdUid}
    </update>
</mapper>