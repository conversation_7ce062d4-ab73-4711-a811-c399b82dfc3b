<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.room.RoomUserInRecordMapper">

    <select id="getInRoomRecord" resultType="com.simi.common.dto.dataReport.RoomRecordDTO">
        select
            sum(pv) inRoomPv,
            count(distinct uid) inRoomUv,
            sum(if(totalTime > #{limitTimestamp}, #{limitTimestamp}, totalTime)) as totalTime,
            count(distinct if(totalTime > 300, uid, null)) effectiveNumber
        from
            (
                select
                    uid,
                    room_id,
                    count(uid) as pv,
                    sum(unix_timestamp(if(out_room_time is null or out_room_time > #{endTime},#{endTime},out_room_time)) - unix_timestamp(in_room_time)) as totalTime
                from
                    simi.room_user_in_record
                where in_room_time between #{beginTime} and #{endTime}
                and room_id = #{roomId}
                group by uid,room_id
            ) tmp
        group by
            room_id
    </select>
</mapper>