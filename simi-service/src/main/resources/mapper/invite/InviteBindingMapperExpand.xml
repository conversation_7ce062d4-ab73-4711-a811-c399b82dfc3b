<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.simi.mapper.invite.InviteBindingMapperExpand">

    <select id="recordUserInvite" resultType="com.simi.common.dto.invite.InviteUserRecordDTO">
        SELECT
        uu.uid AS invitedUid,
        uu.user_no AS invitedUserNo,
        uu.nick AS invitedNick,
        uu.create_time AS createTime,
        ib.invite_uid AS inviteUid,
        ib.active_time AS activeTime,
        if(p_t.diamond, p_t.diamond, 0) AS diamond,
        ib.`status`
        FROM
        `user` uu
        LEFT JOIN `invite_binding` ib ON uu.uid = ib.invited_uid
        LEFT JOIN `purse` p_t ON uu.uid = p_t.uid
        <where>
            <if test="invitedUid != null">
                AND ib.invited_uid = #{invitedUid}
            </if>
            <if test="inviteUid != null">
                AND ib.invite_uid = #{inviteUid}
            </if>
            <if test="status != null and status > 0">
                AND ib.`status` = #{status}
            </if>
            <if test="startTime != null and endTime != null">
                AND uu.create_time BETWEEN #{startTime} AND #{endTime}
            </if>
            <if test="activeStartTime != null and activeEndTime != null">
                AND ib.active_time BETWEEN #{activeStartTime} AND #{activeEndTime}
            </if>
            <!-- 正常状态的用户 -->
            AND uu.`status` = 1
        </where>
        ORDER BY p_t.diamond DESC, uu.create_time DESC
    </select>

    <select id="recordSimpleUserInvite" resultType="com.simi.common.dto.invite.InviteUserRecordDTO">
        SELECT
            uu.uid AS invitedUid,
            uu.user_no AS invitedUserNo,
            uu.nick AS invitedNick,
            uu.avatar AS avatar,
            uu.gender AS gender,
            uu.create_time AS createTime,
            ib_s.invite_uid AS inviteUid,
            ib_s.active_time AS activeTime,
            if(p_t.diamond, p_t.diamond, 0) AS diamond
        FROM
            ( SELECT `invite_uid`, `invited_uid`, `status`, `active_time`
              FROM invite_binding WHERE invite_uid = #{inviteUid} AND `status` IN
                    <foreach collection="statusList" item="item" index="index" open="(" separator="," close=")">
                        #{item}
                    </foreach>
            ) ib_s
                JOIN `user` uu ON ib_s.invited_uid = uu.uid
                <if test="searchKey != null and searchKey != ''">
                    AND uu.user_no = #{searchKey}
                </if>
                LEFT JOIN purse p_t ON ib_s.invited_uid = p_t.uid
        ORDER BY p_t.diamond DESC, ib_s.active_time DESC, uu.create_time DESC
    </select>


    <select id="recordInviteIncome" resultType="com.simi.common.dto.invite.InviteUserRecordDTO">
        SELECT
            uu.uid AS invitedUid,
            uu.user_no AS invitedUserNo,
            uu.nick AS invitedNick,
            uu.avatar,
            uu.create_time AS createTime,
            ib_s.invite_uid AS inviteUid,
            ib_s.`status` AS `status`,
            IFNULL( iai.usd_count, 0 ) AS usdCount
        FROM
            ( SELECT `invite_uid`, `invited_uid`, `status`, `active_time` FROM invite_binding WHERE invite_uid = #{inviteUid} ) ib_s
                JOIN `user` uu ON ib_s.invited_uid = uu.uid
                <if test="searchKey != null and searchKey != ''">
                    AND uu.user_no = #{searchKey}
                </if>
                JOIN `invite_agent_income` iai ON ib_s.invited_uid = iai.invited_uid
        ORDER BY
            iai.usd_count DESC,
            uu.create_time DESC
    </select>
</mapper>