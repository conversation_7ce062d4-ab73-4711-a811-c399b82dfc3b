<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.simi.mapper.expand.RoomPartyMapperExpand">

    <resultMap id="roomPartyResultMap" type="com.simi.common.dto.room.RoomPartyDTO">
        <id property="id" column="id" />
        <result property="uid" column="uid" />
        <result property="roomId" column="room_id" />
        <result property="picUrl" column="pic_url" />
        <result property="topic" column="topic" />
        <result property="description" column="description" />
        <result property="duration" column="duration" />
        <result property="beginTime" column="begin_time" />
        <result property="endTime" column="end_time" />
        <result property="subscribeNum" column="subscribe_num" />
        <result property="enable" column="enable" />
        <result property="tagIds" column="tag_ids" />
        <result property="settlePercent" column="settle_percent" />
        <result property="settleGold" column="settle_gold" />
        <result property="adminId" column="admin_id" />
        <result property="version" column="version" />
        <result property="cancle" column="cancel" />
        <result property="createTime" column="create_time" />
        <result property="updateTime" column="update_time" />
    </resultMap>

    <select id="goingParty" resultType="com.simi.common.dto.room.RoomPartyDTO">
        SELECT * FROM room_party
        <where>
            `enable` = 1 AND cancle = 0
            <![CDATA[
                AND end_time > #{now} AND begin_time <= #{now}
            ]]>
        </where>
        ORDER BY begin_time ASC, subscribe_num DESC
    </select>

    <select id="beginSoon" resultType="com.simi.common.dto.room.RoomPartyDTO">
        SELECT * FROM room_party
        <where>
            `enable` = 1 AND cancle = 0
            <if test="now != null">
                <![CDATA[
            AND `begin_time` > #{now}
            ]]>
            </if>
        </where>
        ORDER BY begin_time ASC, subscribe_num DESC
    </select>

    <select id="reminded" resultType="com.simi.common.dto.room.RoomPartyDTO">
        SELECT rp.*
        FROM room_party rp
        WHERE rp.enable = 1 AND rp.cancle = 0
        AND rp.end_time &gt; #{now}
        AND rp.id IN
        <foreach item="id" collection="subscribeIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY
        CASE
        WHEN rp.begin_time &lt;= #{now} AND rp.end_time &gt;= #{now} THEN 3
        WHEN rp.begin_time &gt; #{now} THEN 2
        ELSE 1
        END DESC,
        begin_time ASC,
        subscribe_num DESC
    </select>

    <select id="myCreate" resultType="com.simi.common.dto.room.RoomPartyDTO">
        SELECT rp.id, rp.uid, rp.room_id, rp.pic_url, rp.topic, rp.description, rp.duration, rp.begin_time,
               rp.end_time,
               rp.subscribe_num,
               rp.enable,
               rp.tag_ids,
               rp.version,
               rp.cancle,
               rp.create_time,
               rp.update_time
        FROM room_party rp
        WHERE rp.enable = 1 and uid = #{uid}
        ORDER BY
            CASE
                WHEN rp.begin_time &lt;= #{now} AND rp.end_time &gt;= #{now} AND rp.cancle = 0 THEN 3
                WHEN rp.begin_time &gt; #{now} AND rp.cancle = 0 THEN 2
                ELSE 1
                END DESC,
            create_time DESC,
            subscribe_num DESC
    </select>

    <update id="updateSubscribeNum">
        UPDATE room_party SET subscribe_num = subscribe_num + #{num} WHERE id = #{partyId} AND #{partyId} IS NOT NULL
    </update>

    <select id="getPartyListByUidAndEndTime" resultType="com.simi.common.dto.room.RoomPartyDTO">
        SELECT
        id,
        uid,
        room_id,
        pic_url,
        topic,
        description,
        duration,
        begin_time,
        end_time,
        subscribe_num,
        enable,
        tag_ids,
        version,
        cancle,
        create_time,
        update_time
        FROM room_party
        <where>
            `enable` = 1 AND cancle = 0  AND room_id = #{roomId}
            <if test="endTimeGte != null">
                <![CDATA[
            AND `end_time` > #{endTimeGte}
            ]]>
            </if>
        </where>
        ORDER BY begin_time ASC
    </select>

    <select id="getHistoryPartyByUidAndEndTime" resultType="com.simi.common.dto.room.RoomPartyDTO">
        SELECT
            id,
            uid,
            room_id,
            pic_url,
            topic,
            description,
            duration,
            begin_time,
            end_time,
            subscribe_num,
            enable,
            tag_ids,
            version,
            cancle,
            create_time,
            update_time
        FROM room_party
        WHERE (end_time &lt;= #{endTimeLt} OR cancle = 1) AND enable = 1 AND room_id = #{roomId}
        ORDER BY begin_time DESC
    </select>
</mapper>