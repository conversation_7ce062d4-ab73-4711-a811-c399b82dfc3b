<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.simi.mapper.expand.UserMapperExpand">

    <select id="listUserFriends" resultType="com.simi.common.dto.user.UserFriendDTO">
        SELECT
            u.uid as uid,
            u.user_no as userNo,
            u.nick as nick,
            u.avatar as avatar,
            u.gender
        FROM
            following f
                JOIN
            following f2 ON f.target_uid = f2.uid AND f.uid = f2.target_uid
                JOIN
            user u ON f.target_uid = u.uid
        <where>
            f.uid = #{uid}
            <if test="searchKey != null and searchKey != ''">
                AND (u.user_no LIKE concat('%',#{searchKey},'%') OR u.nick LIKE concat('%',#{searchKey},'%'))
            </if>
        </where>
        ORDER BY
            f.create_time ASC
    </select>

    <select id="listUserInfoCmsPush" resultType="com.simi.common.dto.user.UserInfoCmsPushDTO">
        SELECT
            uid,
            app_language AS lang
        FROM
            `user`
        WHERE
            def_user = 1
          AND `status` = 1
    </select>

    <select id="listUserRegisterSpecifiedTime" resultType="java.lang.String">
        select uid from user where `status` = 1 AND create_time BETWEEN #{beginTime} AND #{endTime}
    </select>
</mapper>