<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.PropPriceDetailMapper">

    <select id="getPriceListByProIds" resultType="com.simi.entity.PropPriceDetail">
        select
            prop_id,prop_type,day,currency_type,price
        from prop_price_detail
        where   currency_type = #{currencyType} and
        prop_id in
        <foreach collection="propIds" item="propId" index="index" open="(" close=")" separator=",">
            #{propId}
        </foreach>
        <if test="propType != null">
            and prop_type = #{propType}
        </if>

    </select>

    <select id="selectDayPrice" resultType="java.lang.Integer">
        select
            price
        from prop_price_detail
        where prop_type = #{propType} and currency_type = #{currencyType} and
            prop_id = #{propId} and day =#{day}
    </select>
</mapper>