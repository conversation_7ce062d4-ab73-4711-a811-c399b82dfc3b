<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.adminUser.UserAdminManagerMapper">

    <select id="getAccountUserInfo" resultType="com.simi.dto.SyncUserInfoDTO">
        SELECT
        u.uid,
        u.user_no,
        u.nick,
        u.avatar,
        u.gender,
        u.birth,
        a.area_code,
        u.country_code AS countryCode,
        a.sign_type,
        a.phone,
        a.os,
        a.model,
        u.last_login_ip,
        a.device_id,
        a.register_ip,
        u.`status`,
        u.block,
        u.remark,
        u.user_desc,
        u.operation_time,
        a.sign_time,
        a.source
        FROM
        `user` u
        LEFT JOIN account a ON u.uid = a.uid
        <where>
            <if test="uidSet != null and uidSet.size > 0">
                AND u.uid  in
                <foreach collection="uidSet" item="uid" index="index" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
            </if>
            <if test="uidGt != null">
                AND u.uid &gt;#{uidGt}
            </if>
        </where>
        order by u.uid asc limit #{limit}
    </select>

    <select id="latestAppVersion" resultType="com.simi.entity.account.AccountLoginRecord">
        SELECT uid, create_time, app_version, login_ip, model, device_id
        FROM (
        SELECT *,
        ROW_NUMBER() OVER (PARTITION BY uid ORDER BY create_time DESC) AS row_num
        FROM account_login_record
        WHERE uid IN
        <foreach item="item" index="index" collection="uidList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
        ) AS subquery
        WHERE subquery.row_num = 1;
    </select>
</mapper>