<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.room.RoomUserMicRecordMapper">

    <select id="getRoomMicRecord" resultType="com.simi.common.dto.dataReport.MicRecordDTO">
        select
            sum(pv) inMicPv,
            count(distinct uid) inMicUv,
            sum(if(totalTime > #{limitTimestamp}, #{limitTimestamp}, totalTime)) as totalTime,
            count(distinct if(totalTime > 300, uid, null)) effectiveUpMicNum
        from
            (
                select
                    count(uid) as pv,
                    uid,
                    room_id,
                    sum(unix_timestamp(if(out_mic_time is null or out_mic_time > #{endTime},#{endTime},out_mic_time)) - unix_timestamp(in_mic_time)) as totalTime
                from
                    simi.room_user_mic_record
                where in_mic_time between #{beginTime} and #{endTime}
                  and room_id = #{roomId}
                group by room_id,uid
            ) tmp
        group by
            room_id
    </select>

    <select id="getMaxRoomMicUid" resultType="java.lang.Long">
        select uid from (
                   select
                       uid,
                       sum(unix_timestamp(if(out_mic_time is null or out_mic_time > #{endTime},#{endTime},out_mic_time)) - unix_timestamp(in_mic_time))as totalTime
                   from
                       simi.room_user_mic_record
                   where in_mic_time between #{beginTime} and #{endTime}
                     and room_id = #{roomId}
                       <if test="uidSet != null">
                           and uid in
                           <foreach collection="uidSet" item="item" index="index" open="(" separator="," close=")">
                               #{item}
                           </foreach>
                       </if>
                   group by uid order by totalTime desc
                   )tmp limit 1
    </select>
</mapper>