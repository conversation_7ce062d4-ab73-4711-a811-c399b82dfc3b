<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.bd.BDBaseInfoMapper">

    <select id="searchBDList" resultType="com.simi.common.dto.bd.BDMsgDTO">
        select
            tmp.bdId,
            tmp.guideIdStr,
            tmp.anchorAmount,
            tmp.anchorStr,
            t4.id,
            t4.proportion,
            t4.remark,
            t4.create_time
        from
            `simi`.`bd_base_info` t4
                inner join (
                select
                    t1.uid as bdId,
                    GROUP_CONCAT(distinct t2.association_uid) as guideIdStr,
                    count(distinct t3.invited_uid) as anchorAmount,
                    GROUP_CONCAT(distinct t3.invited_uid) as anchorStr
                from
                    `simi`.`bd_base_info` t1
                        inner join `simi`.`bd_relation` t2 on
                        t1.uid = t2.bd_uid and t2.association_type = 1
                        left join `simi`.`invite_binding` t3 on
                        t2.association_uid = t3.invite_uid and t3.status in(2,4,5)
                <where>
                    and t1.is_deleted = 0 and t2.is_deleted = 0
                    <if test="bdId != null">
                        and t1.uid = #{bdId}
                    </if>
                    <if test="guideId != null">
                        and t2.association_uid = #{guideId}
                    </if>
                </where>
                group by
                    t1.uid) tmp on
                t4.uid = tmp.bdId and t4.is_deleted = 0
        order by t4.id desc
    </select>

    <select id="getBDMsgByBdIds" resultType="com.simi.common.vo.bd.BDPerformanceItem">
        select
            t1.uid as bdUid,
            GROUP_CONCAT(t2.association_uid) as guideIdStr,
            max(proportion) as proportion
        from
            `simi`.`bd_base_info` t1
                inner join `simi`.`bd_relation` t2 on
                t1.uid = t2.bd_uid and t2.association_type = 1
            where
            t1.uid in
        <foreach item="bdId" index="index" collection="bdIds" open="(" separator="," close=")">
            #{bdId}
        </foreach>
        and t1.is_deleted = 0 and t2.is_deleted = 0
        group by t1.uid
    </select>
</mapper>