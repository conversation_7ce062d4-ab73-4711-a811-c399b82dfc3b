<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.purse.BillMapper">

    <select id="sumUserCoinByUidList" resultType="com.simi.entity.purse.Bill">
        select sum(ABS(amount)) as amount,uid
        from bill
        where uid in
        <foreach collection="uidList" item="uid" index="index" open="(" close=")" separator=",">
        #{uid}
        </foreach>
        and obj_type in
        <foreach collection="ojbTypeList" item="objType" index="index" open="(" close=")" separator=",">
            #{objType}
        </foreach>
        group by uid
    </select>

    <select id="sumUSDByUserIds" resultType="com.simi.entity.purse.Bill">
        select sum(ABS(amount)) as amount, ${relateField} as uid
        from bill t1
        inner join bd_relation t2 on ${relateField} = t2.association_uid and t1.create_time &gt;= t2.create_time and t2.is_deleted = 0
        where ${relateField} in
        <foreach collection="userIds" item="uid" index="index" open="(" close=")" separator=",">
            #{uid}
        </foreach>
        and  obj_type in
        <foreach collection="ojbTypeList" item="ojbType" index="index" open="(" close=")" separator=",">
            #{ojbType}
        </foreach>
        and t1.create_time BETWEEN #{beginTime}  AND #{endTime}
        group by  ${relateField}
    </select>

    <select id="sumUserCoinByTargetUidList" resultType="com.simi.entity.purse.Bill">
        select sum(ABS(amount)) as amount,target_uid
        from bill
        where target_uid in
        <foreach collection="targetUserIds" item="uid" index="index" open="(" close=")" separator=",">
            #{uid}
        </foreach>
        and obj_type in
        <foreach collection="ojbTypeList" item="objType" index="index" open="(" close=")" separator=",">
            #{objType}
        </foreach>
        group by target_uid
    </select>

    <select id="sumAnchorUSDByGuideIds" resultType="com.simi.common.dto.bd.AnchorUSDAmountDTO">
        select
        tmp.amount as amount,
        t3.invited_uid as anchorId,
        t3.invite_uid as guideId,
        t3.create_time as bindingTime
        from
        simi.invite_binding t3
        left join
        (
        select
        sum(ABS(t2.amount)) as amount,
        t1.invited_uid
        from
        `simi`.`invite_binding` t1
        inner join `simi`.`bill` t2 on
        t1.invited_uid = ${relateField} and t2.create_time &gt;= t1.create_time
        inner join `simi`.`bd_relation` t3 on t1.invite_uid = t3.association_uid and t2.create_time &gt;= t3.create_time and t3.is_deleted = 0
        where
        t2.obj_type in
        <foreach collection="ojbTypeList" item="objType" index="index" open="(" close=")" separator=",">
            #{objType}
        </foreach>
        and t1.invite_uid in
        <foreach collection="guideIds" item="uid" index="index" open="(" close=")" separator=",">
            #{uid}
        </foreach>
        <if test="anchorIds != null">
            and t1.invited_uid in
            <foreach collection="anchorIds" item="anchorId" index="index" open="(" close=")" separator=",">
                #{anchorId}
            </foreach>
        </if>
        and t1.status in(2,4,5)
        and t2.create_time BETWEEN #{beginTime}  AND #{endTime}
        group by
        t1.invited_uid) tmp
        on
        tmp.invited_uid = t3.invited_uid
        where
        t3.invite_uid in
        <foreach collection="guideIds" item="guideId" index="index" open="(" close=")" separator=",">
            #{guideId}
        </foreach>
        <if test="anchorIds != null">
            and t3.invited_uid in
            <foreach collection="anchorIds" item="anchorId" index="index" open="(" close=")" separator=",">
                #{anchorId}
            </foreach>
        </if>
        and t3.status in(2,4,5)
    </select>

    <select id="getUserPayList" resultType="com.simi.common.dto.revenue.PayItem">
        select
            sum(onlineAmount) as onlineAmount,
            sum(offlineAmount) as offlineAmount,
            sum(onlineAmount) + sum(offlineAmount) as totalAmount,
            uid
        from (
                 SELECT
                     uid as uid,
                     SUM(
                             CASE
                                 WHEN obj_type = 14 THEN CONVERT(ABS(amount) / 700, DECIMAL(10, 2))
                                 WHEN obj_type = 15 THEN CONVERT(ABS(amount) / 700, DECIMAL(10, 2))
                                 WHEN obj_type = 50 THEN CONVERT(ABS(amount) / 800, DECIMAL(10, 2))
                                 ELSE 0
                                 END
                     ) AS onlineAmount,
                     SUM(
                             CASE
                                 WHEN obj_type = 29 THEN CONVERT(ABS(amount) / 900, DECIMAL(10, 2))
                                 ELSE 0
                                 END
                     ) AS offlineAmount
                 FROM simi.bill  where obj_type in(14,15,50,29)
                and uid in
                <foreach collection="uidList" item="uid" index="index" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
                <if test="uidGt != null">
                    and uid &gt; #{uidGt}
                </if>
                <if test="beginTime != null">
                    and create_time &lt; #{beginTime}
                </if>
                <if test="endTime != null">
                    and create_time &gt;= #{endTime}
                </if>
                 group by uid
                 union all
                 SELECT
                     target_uid as uid,
                    SUM(
                            CASE
                                WHEN obj_type = 51 THEN CONVERT(ABS(amount) / 900, DECIMAL(10, 2))
                                ELSE 0
                                END
                    ) AS onlineAmount,
                    SUM(
                            CASE
                                WHEN obj_type = 40 THEN CONVERT(ABS(amount) / 850, DECIMAL(10, 2))
                                ELSE 0
                                END
                        ) AS offlineAmount
                 FROM simi.bill  where obj_type in(40)
                and target_uid in
                <foreach collection="uidList" item="uid" index="index" open="(" close=")" separator=",">
                    #{uid}
                </foreach>
                <if test="uidGt != null">
                    and target_uid &gt; #{uidGt}
                 </if>
                <if test="beginTime != null">
                    and create_time &lt; #{beginTime}
                </if>
                <if test="endTime != null">
                    and create_time &gt;= #{endTime}
                </if>
        group by target_uid
             ) tmp
        group by uid
        order by uid asc
    </select>


    <select id="getLastPayIdsByUserIds" resultType="java.lang.Long">
        select
            max(id) as id
        from
            simi.bill
        where
            uid in
        <foreach collection="uidList" item="uid" index="index" open="(" close=")" separator=",">
            #{uid}
        </foreach>
            and obj_type in(14,15,50,29)
        group by
            uid
        union all
        select
            max(id) as id
        from
            simi.bill
        where
            target_uid in
        <foreach collection="uidList" item="targetUid" index="index" open="(" close=")" separator=",">
            #{targetUid}
        </foreach>
          and obj_type in(40)
        group by
            target_uid
    </select>

    <select id="getLastPayByIds" resultType="com.simi.common.dto.revenue.PayItem">
        select
        create_time as lastPayDate,
        case
        when obj_type = 40 then convert(ABS(amount) / 850,DECIMAL(10,2))
        WHEN obj_type = 51 THEN CONVERT(ABS(amount) / 900, DECIMAL(10, 2))
        WHEN obj_type = 14 THEN CONVERT(ABS(amount) / 700, DECIMAL(10, 2))
        WHEN obj_type = 15 THEN CONVERT(ABS(amount) / 700, DECIMAL(10, 2))
        WHEN obj_type = 50 THEN CONVERT(ABS(amount) / 800, DECIMAL(10, 2))
        WHEN obj_type = 29 THEN CONVERT(ABS(amount) / 900, DECIMAL(10, 2))
        else 0
        end as lastPayAmount,
        case
        when obj_type = 40 then target_uid
        when obj_type = 51 then target_uid
        WHEN obj_type = 14 THEN uid
        WHEN obj_type = 15 THEN uid
        WHEN obj_type = 50 THEN uid
        WHEN obj_type = 29 THEN uid
        else 0
        end as uid
        from
        simi.bill
        where id in
        <foreach collection="ids" item="id" index="index" open="(" close=")" separator=",">
            #{id}
        </foreach>
        order by id asc
    </select>
</mapper>