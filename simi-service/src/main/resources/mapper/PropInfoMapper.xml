<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.simi.mapper.PropInfoMapper">

    <select id="getNewProductTimestamp" resultType="com.simi.common.dto.shop.prop.PropNewProductTimestampDTO">
        select
        id,
        mark_new_product_timestamp as markNewProductTimestamp,
        type
        from prop_info
        where
        delete_flag = 1 and status = 1 and mark_new_product_timestamp &gt;#{newProductTimestampGt}
        <if test="type != null and type > 0">
            and type = #{type}
        </if>
    </select>

    <select id="listPropByType" resultType="com.simi.entity.PropInfo">
        select
        id,
        mark_new_product_timestamp,
        type,
        name_en,
        name_ar,
        icon,
        animation_url,
        animation_type,
        direction,
        circulation_url,
        mark_new_product_timestamp
        from prop_info
        where
        delete_flag = 1 and status = 1
        <if test="type != null and type > 0">
            and type = #{type}
        </if>
        order by weight desc
    </select>

    <select id="listPropByType2" resultType="com.simi.entity.PropInfo">
        select
        id,
        mark_new_product_timestamp,
        type,
        name_en,
        name_ar,
        icon,
        animation_url,
        animation_type,
        direction,
        circulation_url,
        mark_new_product_timestamp,
        group_ids
        from prop_info
        where
        delete_flag = 1
        AND status = 1
        AND (
        -- 精确匹配 group_ids 含有指定的 groupId 且没有其他无关的值
        group_ids LIKE CONCAT('%,', TRIM( #{groupId}), ',%')    -- 中间包含 groupId，去除空格
        OR group_ids LIKE CONCAT(TRIM( #{groupId}), ',%')        -- 开头是 groupId，去除空格
        OR group_ids LIKE CONCAT('%,', TRIM( #{groupId}))        -- 结尾是 groupId，去除空格
        OR group_ids = TRIM(#{groupId})                         -- 完全匹配只有一个 groupId，去除空格
        )
        <if test="type != null and type > 0">
            and type = #{type}
        </if>
        order by weight desc
    </select>
</mapper>