66609=The text you edited contains sensitive words
66610=Contains sensitive words, publishing failed
8011=Network anomaly, pleaselogin again.
8012=Network anomaly, pleaselogin again.
8013=Network anomaly, pleaselogin again.
8014=Network anomaly, pleaselogin again.
8015=Network anomaly, pleaselogin again.
reward.pack.sendReward.success=Congratulations on receiving the big gift package, the reward content is: #prizeContent#
usd=USD
golden_ticket=golden ticket
admin_name=Scheduling Center
admin_name_full=Distributed Task Scheduling Platform XXL-JOB
admin_version=2.4.0
admin_i18n=en
## system
system_tips=System message
system_ok=Confirm
system_close=Close
system_save=Save
system_cancel=Cancel
system_search=Search
system_status=Status
system_opt=Operate
system_please_input=please input
system_please_choose=please choose
system_success=success
system_fail=fail
system_add_suc=add success
system_add_fail=add fail
system_update_suc=update success
system_update_fail=update fail
system_all=All
system_api_error=net error
system_show=Show
system_empty=Empty
system_opt_suc=operate success
system_opt_fail=operate fail
system_opt_edit=Edit
system_opt_del=Delete
system_opt_copy=Copy
system_unvalid=illegal
system_not_found=not exist
system_nav=Navigation
system_digits=digits
system_lengh_limit=Length limit
system_permission_limit=Permission limit
system_welcome=Welcome
## daterangepicker
daterangepicker_ranges_recent_hour=recent one hour
daterangepicker_ranges_today=today
daterangepicker_ranges_yesterday=yesterday
daterangepicker_ranges_this_month=this month
daterangepicker_ranges_last_month=last month
daterangepicker_ranges_recent_week=recent one week
daterangepicker_ranges_recent_month=recent one month
daterangepicker_custom_name=custom
daterangepicker_custom_starttime=start time
daterangepicker_custom_endtime=end time
daterangepicker_custom_daysofweek=Sun,Mon,Tue,Wed,Thu,Fri,Sat
daterangepicker_custom_monthnames=Jan,Feb,Mar,Apr,May,Jun,Jul,Aug,Sep,Oct,Nov,Dec
## dataTable
dataTable_sProcessing=processing...
dataTable_sLengthMenu=_MENU_ records per page
dataTable_sZeroRecords=No matching results
dataTable_sInfo=page _PAGE_  ( Total _PAGES_ pagesï¼_TOTAL_ records )
dataTable_sInfoEmpty=No Record
dataTable_sInfoFiltered=(Filtered by _MAX_ results)
dataTable_sSearch=Search
dataTable_sEmptyTable=Table data is empty
dataTable_sLoadingRecords=Loading...
dataTable_sFirst=FIRST PAGE
dataTable_sPrevious=Previous Page
dataTable_sNext=Next Page
dataTable_sLast=LAST PAGE
dataTable_sSortAscending=: Rank this column in ascending order
dataTable_sSortDescending=: Rank this column in descending order
## login
login_btn=Login
login_remember_me=Remember Me
login_username_placeholder=Please enter username
login_password_placeholder=Please enter password
login_username_empty=Please enter username
login_username_lt_4=Username length should not be less than 4
login_password_empty=Please enter password
login_password_lt_4=Password length should not be less than 4
login_success=Login success
login_fail=Login fail
login_param_empty=Username or password is empty
login_param_unvalid=Username or password error
## logout
logout_btn=Logout
logout_confirm=Confirm logout?
logout_success=Logout success
logout_fail=Logout fail
## change pwd
change_pwd=Change password
change_pwd_suc_to_logout=Change password successful, about to log out login
change_pwd_field_newpwd=new password
## dashboard
job_dashboard_name=Run report
job_dashboard_job_num=Job number
job_dashboard_job_num_tip=The number of tasks running in the scheduling center
job_dashboard_trigger_num=trigger number
job_dashboard_trigger_num_tip=The number of trigger record scheduled by the scheduling center
job_dashboard_jobgroup_num=Executor number
job_dashboard_jobgroup_num_tip=The number of online executor machines perceived by the scheduling center
job_dashboard_report=Scheduling report
job_dashboard_report_loaddata_fail=Scheduling report load data error
job_dashboard_date_report=Date distribution
job_dashboard_rate_report=Percentage distribution
## job info
jobinfo_name=Job Manage
jobinfo_job=Job
jobinfo_field_add=Add Job
jobinfo_field_update=Edit Job
jobinfo_field_id=Job ID
jobinfo_field_jobgroup=Executor
jobinfo_field_jobdesc=Job description
jobinfo_field_timeout=Job timeout period
jobinfo_field_gluetype=GLUE Type
jobinfo_field_executorparam=Param
jobinfo_field_author=Author
jobinfo_field_alarmemail=Alarm email
jobinfo_field_alarmemail_placeholder=Please enter alarm mail, if there are more than one comma separated
jobinfo_field_executorRouteStrategy=Route Strategy
jobinfo_field_childJobId=Child Job ID
jobinfo_field_childJobId_placeholder=Please enter the Child job ID, if there are more than one comma separated
jobinfo_field_executorBlockStrategy=Block Strategy
jobinfo_field_executorFailRetryCount=Fail Retry Count
jobinfo_field_executorFailRetryCount_placeholder=Fail Retry Count. effect if greater than zero
jobinfo_script_location=Script location
jobinfo_shard_index=Shard index
jobinfo_shard_total=Shard total
jobinfo_opt_stop=Stop
jobinfo_opt_start=Start
jobinfo_opt_log=Query Log
jobinfo_opt_run=Run Once
jobinfo_opt_run_tips=Please input the address for this trigger. Null will be obtained from the executor
jobinfo_opt_registryinfo=Registry Info
jobinfo_opt_next_time=Next trigger time
jobinfo_glue_remark=Resource Remark
jobinfo_glue_remark_limit=Resource Remark length is limited to 4~100
jobinfo_glue_rollback=Version Backtrack
jobinfo_glue_jobid_unvalid=Job ID is illegal
jobinfo_glue_gluetype_unvalid=The job is not GLUE Type
jobinfo_field_executorTimeout_placeholder=Job Timeout periodï¼in seconds. effect if greater than zero
schedule_type=Schedule Type
schedule_type_none=None
schedule_type_cron=Cron
schedule_type_fix_rate=Fix rate
schedule_type_fix_delay=Fix delay
schedule_type_none_limit_start=The current schedule type disables startup
misfire_strategy=Misfire strategy
misfire_strategy_do_nothing=Do nothing
misfire_strategy_fire_once_now=Fire once now
jobinfo_conf_base=Base configuration
jobinfo_conf_schedule=Schedule configuration
jobinfo_conf_job=Job configuration
jobinfo_conf_advanced=Advanced configuration
## job log
joblog_name=Trigger Log
joblog_status=Status
joblog_status_all=All
joblog_status_suc=Success
joblog_status_fail=Fail
joblog_status_running=Running
joblog_field_triggerTime=Trigger Time
joblog_field_triggerCode=Trigger Result
joblog_field_triggerMsg=Trigger Msg
joblog_field_handleTime=Handle Time
joblog_field_handleCode=Handle Result
joblog_field_handleMsg=Trigger Msg
joblog_field_executorAddress=Executor Address
joblog_clean=Clean
joblog_clean_log=Clean Log
joblog_clean_type=Clean Type
joblog_clean_type_1=Clean up log data a month ago
joblog_clean_type_2=Clean up log data three month ago
joblog_clean_type_3=Clean up log data six month ago
joblog_clean_type_4=Clean up log data a year ago
joblog_clean_type_5=Clean up log data a thousand record ago
joblog_clean_type_6=Clean up log data ten thousand record ago
joblog_clean_type_7=Clean up log data thirty thousand record ago
joblog_clean_type_8=Clean up log data hundred thousand record ago
joblog_clean_type_9=Clean up all log data
joblog_clean_type_unvalid=Clean type is illegal
joblog_handleCode_200=Success
joblog_handleCode_500=Fail
joblog_handleCode_502=Timeout
joblog_kill_log=Kill Job
joblog_kill_log_limit=Trigger Fail, can not kill job
joblog_kill_log_byman=Manual operation, kill job
joblog_lost_fail=Job result lost, marked as failure
joblog_rolling_log=Rolling log
joblog_rolling_log_refresh=Refresh 
joblog_rolling_log_triggerfail=The job trigger fail, can not view the rolling log
joblog_rolling_log_failoften=The request for the Rolling log is terminated, the number of failed requests exceeds the limit, Reload the log on the refresh page
joblog_logid_unvalid=Log ID is illegal
## job group
jobgroup_name=Executor Manage
jobgroup_list=Executor List
jobgroup_add=Add Executor
jobgroup_edit=Edit Executor
jobgroup_del=Delete Executor
jobgroup_field_title=Title
jobgroup_field_addressType=Registry Type
jobgroup_field_addressType_0=Automatic registration
jobgroup_field_addressType_1=Manual registration
jobgroup_field_addressType_limit=Manually registration type, the machine address must not be empty
jobgroup_field_registryList=machine address
jobgroup_field_registryList_unvalid=registry machine address is illegal
jobgroup_field_registryList_placeholder=Please enter the machine address, if there are more than one comma separated
jobgroup_field_appname_limit=Limit the beginning of a lowercase letter, consists of lowercase lettersãnumber and hyphen.
jobgroup_field_appname_length=AppName length is limited to 4~64
jobgroup_field_title_length=Title length is limited to 4~12
jobgroup_field_order_digits=Please enter a positive integer
jobgroup_field_orderrange=Order is limited to 1~1000
jobgroup_del_limit_0=Refuse to delete, the executor is being used
jobgroup_del_limit_1=Refuses to delete, the system retains at least one executor
jobgroup_empty=There is no valid executor. Please contact the administrator
## job conf
jobconf_block_SERIAL_EXECUTION=Serial execution
jobconf_block_DISCARD_LATER=Discard Later
jobconf_block_COVER_EARLY=Cover Early
jobconf_route_first=First
jobconf_route_last=Last
jobconf_route_round=Round
jobconf_route_random=Random
jobconf_route_consistenthash=Consistent Hash
jobconf_route_lfu=Least Frequently Used
jobconf_route_lru=Least Recently Used
jobconf_route_failover=Failover
jobconf_route_busyover=Busyover
jobconf_route_shard=Sharding Broadcast
jobconf_idleBeat=Idle check
jobconf_beat=Heartbeats
jobconf_monitor=Task Scheduling Center monitor alarm
jobconf_monitor_detail=monitor alarm details
jobconf_monitor_alarm_title=Alarm Type
jobconf_monitor_alarm_type=Trigger Fail
jobconf_monitor_alarm_content=Alarm Content
jobconf_trigger_admin_adress=Trigger machine address
jobconf_trigger_exe_regtype=Execotor-Registry Type
jobconf_trigger_exe_regaddress=Execotor-Registry Address
jobconf_trigger_address_empty=Trigger Failï¼registry address is empty
jobconf_trigger_run=Trigger Job
jobconf_trigger_child_run=Trigger child job
jobconf_callback_child_msg1={0}/{1} [Job ID={2}], Trigger {3}, Trigger msg: {4} <br>
jobconf_callback_child_msg2={0}/{1} [Job ID={2}], Trigger Fail, Trigger msg: Job ID is illegal <br>
jobconf_trigger_type=Job trigger type
jobconf_trigger_type_cron=Cron trigger
jobconf_trigger_type_manual=Manual trigger
jobconf_trigger_type_parent=Parent job trigger
jobconf_trigger_type_api=Api trigger
jobconf_trigger_type_retry=Fail retry trigger
jobconf_trigger_type_misfire=Misfire compensation trigger
## user
user_manage=User Manage
user_username=Username
user_password=Password
user_role=Role
user_role_admin=Admin User
user_role_normal=Normal User
user_permission=Permission
user_add=Add User
user_update=Edit User
user_username_repeat=Username Repeat
user_username_valid=Restrictions start with a lowercase letter and consist of lowercase letters and Numbers
user_password_update_placeholder=Please input password, empty means not update
user_update_loginuser_limit=Operation of current login account is not allowed
## help
job_help=Tutorial
job_help_document=Official Document
cp.music.server.2=\ just sang a song\
cp.music.server.4=\ has left the room
cp.room.server.4=\ removed you as room admin
cp.room.server.7=Admin unmutes your seat
cp.music.server.4=\ has left the room
cp.user.server.10=I followed you, may I make friends with you?
DIAMONDS_FOR_DOLLAR=diamonds for dollar
coin_dealer_present_title=Arrived in {} Wallet
0=Success
1000=Bad Request
1001=Duplicate operation
1002=Illegal argument
1003=Server error
1004=Network error
1005=Server busy
1006=Need login
1007=Signature cannot be empty
1008=signature error
1009=request expired
1010=token error
1011=Contains sensitive words
1401=Permission denied
1402=You have exited the room
2000=Device registration limit
2001=Registration failed, please try another method
2002=Login failed
2003=The account does not exist
2004=Incorrect password
2005=Abnormal account
2006=Abnormal account，please contact Customer Service: 10
3000=Not exist
3001=No match found
3002=Nickname cannot be empty
3010=Already followed
3011=Not follow yet
3020=Nickname contains illegal content, operation failed!
3021=The profile contains sensitive content
3030=Did not check in before
3031=Checked in already
3032=No check-in task
3033=Task not completed
3034=Rewards have been claimed
3036=Add up to 5 accounts
3040=Number of photos exceeds the limit
4000=Permission denied
4001=Permission denied
4002=Permission denied
4003=No match found
4010=You cannot enter this room
4011=Please enter the password
4012=Wrong password
4020=The seat is occupied
4021=The user is already seated
4022=No seats available
4023=Too frequent operation
4024=The seat is occupied
4025=The operation failed
4026=The seat is locked
4027=You already applied
4030=The operation failed
4031=The operation failed
4032=The operation failed
4033=The admin limit has been reached
4040=Speak too fast, please wait a moment
4041=Character limit exceeded
4050=Room title contains illegal content, operation failed
4051=Bulletin contains illegal content, operation failed!
4052=Room dismissed
5000=music
5001=This song does not exist
5010=Abnormal queue list
5020=The booking is full, please try again later
5021=Book only 3 songs at a time, and please rebook after
5022=The message was sent too quickly. Please slow down.
6000=Messages fail to send
6001=Verification failed
6002=SMS restrictions
7000=Purchase failed
9000=Please login
9001=Account does not exist
9002=Account invalid
9003=Password is wrong
9004=The role does not exist
9005=Account already exists
9006=Account does not exist
9010=skuid can not repeat
9020=Price cannot be empty
10000=The amount of Diamonds must be greater than 0
10001=Insufficient diamond balance
10002=Insufficient diamond balance
10003=Insufficient coin balance
10004=Failed to add diamonds
10005=The gift has been removed
10006=There is no audience in the room
10007=Insufficient gifts
10008=Cannot send gift to yourself
10009=This gift can only be sent 1 at a time
10010=Failed to add coins
10011=Operation failed
10012=Unknown bill type
10013=Minimum exchange amount of ${0}
10014=Exceeded the maximum redeemable amount
10015=Insufficient balance
10100=The product does not exist
10101=Insufficient product
10102=Repeated request
10103=Update anomalies
10200=Abnormal recharge
10201=Unsuccessful payment
10202=Repeated verification
11000=This post cannot be opened
11001=This comment has been deleted
11002=This comment has been deleted
11003=This like has been canceled
11004=Cannot send due to containing sensitive words.
18001=Recharge Agent not exist
18002=Recharge Agent not exist
18003=Insufficient balance
cp.scheduler.server.1=\ paused singing
cp.scheduler.server.2=Quality Room Deduction
cp.music.server.1=\ just booked a song\
cp.music.server.2 =\ just sang a song\
cp.music.server.4 =\ has left the room
cp.music.server.5=\ paused singing
cp.music.server.6=\ resumed singing
cp.music.server.8=\ has been deleted by admin, please book the song again
cp.room.server.3=\ becomes room admin
cp.music.server.11=\ start Boom contest
cp.room.server.6=Admin mutes your seat
cp.music.server.10=Wait for approval
cp.music.server.9=Admin invites you to sing
cp.music.server.7=Your song\
cp.room.server.4 =\ removed you as room admin
cp.music.server.3=\ and got a score of\
cp.room.server.7=Admin unmutes your mic
cp.room.server.5=Room admin invites you to take a mic
cp.room.server.1='s  Room
cp.room.server.2=\ set you as room admin
cp.room.server.10=You have been removed from the room
cp.room.server.8=\ Enter the room
cp.room.server.11=Admin removed you from the mic
cp.room.server.9=Welcome to my room, let's chatting together!
cp.room.server.12=The image violates EverySing regulations. Room cover has been reset to the default image
cp.room.server.20=Quality Room Rewards
cp.room.server.19=Welcome to join us
cp.room.server.18=Congratulations on passing the Premium Singing Room Policy and receiving the reward Gold X {coinAmount}!
cp.room.server.21=Room dismissed
cp.room.server.14=Admin has unmuted all seats
cp.room.server.15=Wait for approval
cp.user.server.4=Active
cp.user.server.2=Welcome to EverySing! If you have any questions or need help, please feel free to contact me.
cp.room.server.16=Admin has muted the seat, and you cannot unmute it. (Mute only when someone is singing)
cp.user.server.1=The image violates EverySing regulations. Avatar has been reset to the default image
cp.user.server.3=Congratulations!\nYour {} Level have reached\n
cp.room.server.13=The seat is muted until admin unmutes it
cp.room.server.17=Mic is off when someone is singing on stage
cp.user.server.5=Charm
cp.user.server.9=Lv.{}
cp.user.server.12=\ just followed\
cp.user.server.10=I followed you, may I make friends with you?
cp.user.server.11=Follow
cp.reward.pack.sendReward.success=Congratulations on receiving the following prize:
cp.push.title.receive_new_items=Receive new items
active_exp=Active Exp
cp.push.title.set_as_admin=Set as admin
cp.push.title.violation_notice=Violation Notice
cp.push.title.admin_removed=Admin removed
cp.reward.pack.sendReward.success.high_quality_room_day=Congratulations on receiving daily gifts of Quality Room: {prizeContent}
cp.push.title.receive_reward=Receive reward
diamond=Diamond
cp.user.server.8=Your {} Level have reached Lv.{}
cp.user.server.6=Wealth
cp.user.server.7={} Level
cp.push.title.level_up=Upgrade
cp.reward.pack.sendReward.success.high_quality_room_week=Congratulations on receiving backpack of Quality Room: {prizeContent}
coin=Coin
you_send=You sent
cp.post.notice.received_reply=Reply:{content}
day=day
days=days
cp.post.notice.received_gift=Received gift
send_you=Sent you
cp.post.push.title.post_an_update={postUserName} posted a moment
cp.post.notice.received_like=Liked your post
cp.post.violation.title=Notice of violation
cp.post.notice.received_comment=Comment:{content}
cp.post.8=Your posting on {releaseTime} violated the rules and has been blocked by the system. Please re-edit and post
cp.post.push.content.received_gift=Sent gift {giftName}×{giftNum}
cp.post.review.violation.msg=Your post at {releaseTime} violated rules of EverySing and was blocked by system. Please re-edit and post.
currency_conversion_by_coin_dealers_title=You received a transfer
friend_settlement_rewards=Friend settlement rewards
gift.send.banner.all.mic=All on mic
normal.room=Chat
level_privileges_wealth=coin=1 Experience\nBy sending gifts, you can gain corresponding experience points
cp.room.server.22=Followed
gift.send.banner.all.room=All Room
automatically=After deleting your account, you can log in again within 72 hours and the system will automatically restore your account.
account-content=Please note that the account cannot be restored after deletion. Please confirm
APPLE_STORE_REFUND=Refund
CMS_DEDUCT_USD=Official Deduction
GIFT_COMMISSION=Received gifts
GIFT_COMMISSION_DIAMOND=gift commission diamond
REWARD_PACK_USD=Reward package
DOLLAR_FOR_GOLD_INCOME=US Dollars Exchange Coins
CMS_TOP_UP_REFUND=Recharge
DOLLAR_FOR_GOLD_EXPEND=US Dollars Exchange Coins
PURCHASE_COSTUME_TO_SELF=Purchased {}
DIAMONDS_FOR_DOLLARS=Diamonds to US Dollars
REWARD_PACK_COIN=Reward package
OFFICIAL_DEDUCT_COIN=Official Deduction
CMS_REWARD_USD=Official rewards
SENDING_GIFT_IN_ROOM=Sending gifts
REWARD_PACK_GOLDEN_TICKET=Reward package
PLATFORM_INCREASE_GOLDEN_TICKET=Recharge from the official
PLATFORM_DEDUCTION_GOLDEN_TICKET=platform deduction golden ticket
CMS_REWARD_GOLDEN_TICKET=Official rewards
PURCHASE_COSTUME_TO_OTHER=Sent {}
USD_WITHDRAW_LOSE=Withdrawal rejected
COIN_DEALER_FOR_COIN_DEALER=Transferring ticket to distributors
USD_WITHDRAW_SPONSOR=Withdraw through the official
CMS_DEDUCT_GOLDEN_TICKET=Official Deduction
USER_FOR_COIN_DEALER=Transfer from user
BULLET_CHAT_DEDUCT_GOLD=Send Broadcast
COIN_DEALER_FOR_USER=Transfer Coins to user
COIN_DEALER_FOR_GOLD=Receive coins from Recharge Agent
SENDING_GIFT_BY_POST=Send gift
USD_FOR_COIN_DEALER=transfer Usd to Recharge Agent
DIAMONDS_FOR_DOLLARS_DEDUCT=Diamonds exchange for US Dollars
GOOGLE_PAY_TOP_UP=Recharge
GOOGLE_PAY_REFUND=Refund
JUNIOR_COIN_DEALER_HARVEST=Received gold ticket from recharge agent
withdraw_successfully=withdraw successfully
withdrawal_failed=withdrawal failed
level_privileges_charm=1 coin=1 Experience\n By Received gifts, you can gain corresponding experience points
SENDING_GIFT_IN_ROOM_ALL_MIC=Send gifts to all mic (x{})
SENDING_GIFT_IN_ROOM_ALL_ROOM=Send gifts to all room(x{})
COIN_DEALER_PRESENT_TITLE=Arrived coins {} in Wallet
wallet_usd_title=What is USD?
g4645132165165=Sodomite
reward_perpetually_valid=Perpetually valid
cp.new.reward.pack.sendReward.success=Newbie Bonus
cp.female.supporters.reward.pack=Female supporter
avatar_review_failed_text=Image violation（{0}）
8031=Activity is outdated
eid_al_adha_act_reward_day=Eid al-Adha Bonus: {0} Days
eid_al_adha_act_reward_gift=Eid al-Adha Bonus: Gifts {0}
fruit_party_name=Fruit Party
become_an_administrator_room_msg=becomes the Admin
become_an_administrator_system={} has set you as the room admin
removed_an_administrator_room_msg=removed the Admin
removed_an_administrator_system={} removed you as room admin
2009=frequent operation ({})
8040=You can send text after the countdown ends {{}}
room_public_screen_silence=has been banned from sending text by {0} {1} after:{2}
room_owner=Owner
room_manager=Admin
12003=Game has not started
12004=Selection has closed
mission_daily_sign=Task completion reward
level_privileges_active=1.Get more attention\n2.More features
2010={}
crazy_triple=Crazy Triple
task_completed=Task completed
wealth_exp=Wealth Exp
charm_exp=Charm Exp
mission_reward=Task completed reward
12005=The current week cannot be modified
daily_tasks=Daily Tasks
take_the_mic_for_15mins=Take the mic for 15mins
stay_in_room_45_mins=Stay in Room 45 mins
win_1000_coins_in_fruit_party=Win 1000 coins in Fruit party
send_gifts_in_the_room=Send Gifts in the room
follow_a_new_friend=Follow a new friend
first_recharge_gift_bag=First Recharge Bouns Packs
weekly_star=Weekly star Top{0}
aristocracy_check_in=Aristocracy check in
turned_off_the_1V1_PK={0} turned off the 1V1 PK
turned_off_the_Multiplayer_PK={0} turned off the Multiplayer PK
1V1_PK_no_one_win=1V1 PK is over, no one win in pk
multiplayer_PK_no_one_win=Multiplayer PK is over, no one win in pk
1V1_PK_someone_win=1V1 PK is over, {0} win in pk
multiplayer_PK_someone_win=Multiplayer PK is over, {0} win in pk
turned_on_the_1V1_PK={0} turned on the 1V1  PK for {1} minutes
turned_on_the_Multiplayer_PK={0} turned on the Multiplayer  PK for {1} minutes
12006=PK has already started!
aristocracy_pack=Aristocracy {0}
aristocracy=aristocracy
13001=Unable to send to yourself
13002=This item is sold out
adjust_room_mic_hint=Due to Change the mic layout,You have been kicked out of the Mic
room_party_pic_examine_failed=Party cover image is out of order, please re-upload
room_party_begin_soon_notify=The party you subscribed to starts in 15 minutes.
room_party_has_already_started=The party has started
room_party_has_ended=This party has ended
12102=The room party topic contains sensitive words
12103=The room party description contains sensitive words
12104=The party is already in progress at that time
12105=Room party time does not comply with regulations
12106=Room party create maximum of 3
12107=Wealth level greater than {} to open
12108=Charm level greater than {} to open
12109=Active level greater than {} to open
aristocracy_in_room_message=<p>{0} Enter the room</p>
12012=having a higher level of Aristocracy.
12013=He having a higher level of Aristocracy.
12110=The party has already started and cannot be modified
12111=Change limit reached
weekly_star_gift_ranking=Congratulations on winning the TOP{0} in PK guardian ranking
weekly_star_receive_ranking=Congratulations on winning the TOP{0} in PK king ranking
act_push_msg_0817_title=PK king and guardian
act_push_msg_0817_text=🌟 Join the Ultimate Showdown: PK King and Guardian! 🌟Get ready for an epic battle where only the strongest will rise! 💪 Whether you're a fearless warrior or a strategic mastermind, this is your chance to claim the throne as the ultimate PK King or Guardian. 🏆
cp.room.server.24=Change the room mic layout to
normal_10_mic=Normal 10 Mic
partner_12_mic=Partner 12 Mic
chat_15_mic=Chat 15 Mic
chat_20_mic=Chat 20 Mic
Coins=Coins
3035=Your album limit has been reached
vip_exp=VIP experience
vip_time=VIP duration
vip_give_msg=%s days
vip_level_up_title=Congratulations
vip_level_up_msg=VIP level has been upgraded to %s
vip_give_msg_title=VIP activation successful
vip_recharge=Recharge
vip_reward=Reward
vip_month_deduce=Monthly rent
achieve_achievements_title=Achieve Achievements
achieve_achievements_text=🎉 Congratulations you got the badge “{}”,please check.
13200=Badges wearing limit reached
13201=Badge giving failed
fruit_winner=Best Winner
fruit_lottery_total=Most Player
4053=Failed to operate, please re-enter the room
12016=Your aristocracy level is too low to send this gift
13220=This phone number has already been used
aristocracy_level_up=Upgrade to Aristocracy LV.%s
12017=The aristocracy nameplate has been claimed
aristocracy_up=Your Emperor Return in Triumph

aristocracy_on_line=Online
luck_777=luck 777
three_card_brag=king Battle


send_lucky_bag_banner=Send Lucky box
send_lucky_bag_screen=I sent a lucky box
garb_lucky_bag_screen=I received a lucky box from {0}
lucky_bag_refund_title=Unclaimed lucky bag balance
lucky_bag_refund_text=Has been deposited in the wallet {}
grab_lucky_bag_title=Congrats! You got
grab_lucky_bag_text=The prize has been deposited in the wallet {}

send_lucky_bag_screen_first=I sent a
send_lucky_bag_screen_second=lucky box

14200=The lucky box does not exist
14201=The lucky box is not available within the redemption period
14202=The lucky box has been claimed
14203=You have already claimed this lucky box
13112=The VIP level for sending VIP gifts is not enough
13113=The number of times you can send VIP experience cards has reached the limit
13114=The person is a VIP and you can't send
13115=The person is a VIP and can't be muted
13116=Do not have this privilege yet
13117=Failed to send costume

15000=Can not create lucky wheel repeatedly
15001=Can not join lucky wheel repeatedly
15002=Can not start lucky wheel
15003=Can not cancel lucky wheel
15004=Insufficient players to start
15005=Can not join lucky wheel
13118=The other party is a VIP user and cannot be kicked off the microphone
vip_send_prop=You received %s
13119=The other party is a VIP user and cannot be kicked out of the room

6003=image violation

auto_push_pa_user_title=Game Awards
auto_push_pa_user_text=Your Friend wind a super prize


taps_cloud_content=[Habi] Your verification code is %s, valid for 2 mins.Don't share it with others.

Aries=Aries
Taurus=Taurus
Gemini=Gemini
Cancer=Cancer
Leo=Leo
Virgo=Virgo
Libra=Libra
Scorpio=Scorpio
Sagittarius=Sagittarius
Capricorn=Capricorn
Aquarius=Aquarius
Pisces=Pisces
interactive_notification=Interactive notification

comment=Comment on your post
invitation_reply=Reply to your updates
what_is_up=@You
invitation_like=Liked the post
1403=Data does not exist
invitation_party_activity_reward=Invite friends to get rewards

2011=Not less than 18 years old
15008=Not at betting time
4054=You have been banned
4055=Non admin muting