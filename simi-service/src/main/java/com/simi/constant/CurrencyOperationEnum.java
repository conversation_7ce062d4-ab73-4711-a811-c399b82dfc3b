package com.simi.constant;

import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 货币操作枚举
 *
 * <AUTHOR>
 * @date 2024/03/25 16:15
 **/
@Getter
@AllArgsConstructor
public enum CurrencyOperationEnum {
    CURRENCY_OPERATION_NONE(0, "占位"),
    CMS_TOP_UP_FUND(1, "CMS充值补单"),
    CMS_REWARD_COIN(2, "CMS奖励金币"),
    CMS_REWARD_DIAMOND(3, "CMS奖励钻石"),
    CMS_DEDUCT_COIN(4, "CMS扣除金币"),
    CMS_DEDUCT_DIAMOND(5, "CMS扣除钻石"),

    CMS_REWARD_USD(6, "CMS奖励美元"),
    CMS_DEDUCT_USD(7, "CMS扣除美元"),

    CMS_REWARD_GOLDEN_TICKET(8, "CMS奖励金票"),
    CMS_DEDUCT_GOLDEN_TICKET(9, "CMS扣除金票"),
    ;
    final Integer type;
    final String desc;

    public static CurrencyOperationEnum getByType(Integer type) {
        if (Objects.isNull(type)) {
            return CURRENCY_OPERATION_NONE;
        }
        return Arrays.stream(CurrencyOperationEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findAny().orElseThrow(() -> new ApiException(CodeEnum.PARAM_ILLEGAL));
    }
}
