package com.simi.constant.crazyTriple;

public enum CrazyTripleGameStageEnum {
    CRAZY_TRIPLE_GAME_STAGE_NONE(0),  //占位(无游戏)
    CRAZY_TRIPLE_GAME_STAGE_BET(1), //投注阶段（20s）
    CRAZY_TRIPLE_GAME_STAGE_STOP_BET(2),   //stop bets(1s)
    CRAZY_TRIPLE_GAME_STAGE_PLAY_ANIMATION(3),   //摇骰子动画，开奖倒计时(5s)
    CRAZY_TRIPLE_GAME_STAGE_SHOW_RESULT(4),   //显示结果(2s)
    CRAZY_TRIPLE_GAME_STAGE_FLYING_CURRENCY(5),   //飞金币(4s)
    CRAZY_TRIPLE_GAME_STAGE_BREAK_TIME(6);   // 休息时间(3s)

    private final int value;

    CrazyTripleGameStageEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }
}
