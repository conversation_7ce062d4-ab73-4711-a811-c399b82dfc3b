package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PropRedisKey implements BaseRedisKey {

    /**
     * 标记成新品的时间戳
     */
    prop_mark_new_product_timestamp,
    /**
     * 道具商城分页数据
     */
    prop_shop_page_info,

    /**
     * 标记成新品的用户已读列表
     */
    prop_new_product_read_list
    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
