package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Oauth2 Redis Key
 *
 * <AUTHOR>
 * @date 2023/11/2 10:58
 */
@Getter
@AllArgsConstructor
public enum Oauth2RedisKey implements BaseRedisKey {

    access_token,
    uid_token,
    login_lock,
    /**
     * 封禁用户
     */
    block_user,
    /**
     * 封禁设备
     */
    block_device,
    uid_bigset,
    /**
     * 购买贵族上锁
     */
    buy_aristocracy_lock,
    /**
     * 领取贵族铭牌上锁
     */
    receive_aristocracy_plaque_lock,


    device_id,

    /**
     * VIP上锁
     */
    vip_lock,

    /**
     * 贵族赠送贵族上锁
     */
    give_aristocracy_lock

    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
