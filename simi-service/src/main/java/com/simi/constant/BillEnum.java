package com.simi.constant;


import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum BillEnum {

    /**
     * 兑换金币
     */
    COIN_EXCHANGE_TO_COINS(BillDetailEnum.EXCHANGE_INCOME, BillItemEnum.EXCHANGE_TO_COINS, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * 金票兑换金币
     */
    COIN_DEALER_FOR_GOLD(BillDetailEnum.COIN_DEALER_FOR_GOLD, BillItemEnum.COIN_DEALER_FOR_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * 钻石兑换美元兑换
     */
    DIAMONDS_FOR_DOLLARS(BillDetailEnum.DIAMONDS_FOR_DOLLARS, BillItemEnum.DIAMONDS_FOR_DOLLARS, DigitalCurrencyEnum.USD, BillTypeEnum.INCOME),

    /**
     * 美元提现发起
     */
    USD_WITHDRAW_SPONSOR(BillDetailEnum.USD_WITHDRAW_SPONSOR, BillItemEnum.USD_WITHDRAW_SPONSOR, DigitalCurrencyEnum.USD, BillTypeEnum.EXPENSE),

    /**
     * 美元提现失败
     */
    USD_WITHDRAW_LOSE(BillDetailEnum.USD_WITHDRAW_LOSE, BillItemEnum.USD_WITHDRAW_LOSE, DigitalCurrencyEnum.USD, BillTypeEnum.INCOME),

    /**
     * 平台增加金票
     */
    PLATFORM_INCREASE_GOLDEN_TICKET(BillDetailEnum.PLATFORM_INCREASE_GOLDEN_TICKET, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),

    /**
     * 平台扣减金票
     */
    PLATFORM_DEDUCTION_GOLDEN_TICKET(BillDetailEnum.PLATFORM_DEDUCTION_GOLDEN_TICKET, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.EXPENSE),

    /**
     * 一级币商转赠二级币商增加
     */
    COIN_DEALER_FOR_COIN_DEALER_INCREASE(BillDetailEnum.COIN_DEALER_FOR_COIN_DEALER, BillItemEnum.COIN_DEALER_FOR_COIN_DEALER, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),

    /**
     * 一级币商转赠二级币商扣减
     */
    COIN_DEALER_FOR_COIN_DEALER_DEDUCTION(BillDetailEnum.COIN_DEALER_FOR_COIN_DEALER, BillItemEnum.COIN_DEALER_FOR_COIN_DEALER, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.EXPENSE),

    /**
     * 用户转增币商
     */
    USER_FOR_COIN_DEALER(BillDetailEnum.USER_FOR_COIN_DEALER, BillItemEnum.USER_FOR_COIN_DEALER, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),

    /**
     * 币商转增用户
     */
    COIN_DEALER_FOR_USER(BillDetailEnum.COIN_DEALER_FOR_USER, BillItemEnum.COIN_DEALER_FOR_USER, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.EXPENSE),

    /**
     * 钻石兑换美元兑换
     */
    DIAMONDS_FOR_DOLLARS_DEDUCT(BillDetailEnum.DIAMONDS_FOR_DOLLARS_DEDUCT, BillItemEnum.DIAMONDS_FOR_DOLLARS_DEDUCT, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.EXPENSE),
    /**
     * 金币兑换美元兑换
     */
    GOLD_FOR_DOLLAR(BillDetailEnum.DIAMONDS_FOR_DOLLAR, BillItemEnum.DIAMONDS_FOR_DOLLAR, DigitalCurrencyEnum.USD, BillTypeEnum.INCOME),

    /**
     * 美元兑换金币兑换支出
     */
    DOLLAR_FOR_GOLD_EXPEND(BillDetailEnum.DOLLAR_FOR_GOLD_EXPEND, BillItemEnum.DOLLAR_FOR_GOLD_EXPEND, DigitalCurrencyEnum.USD, BillTypeEnum.EXPENSE),

    /**
     * 美元兑换金币兑换收入
     */
    DOLLAR_FOR_GOLD_INCOME(BillDetailEnum.DOLLAR_FOR_GOLD_INCOME, BillItemEnum.DOLLAR_FOR_GOLD_INCOME, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 美元兑换金票
     */
    USD_FOR_COIN_DEALER(BillDetailEnum.USD_FOR_COIN_DEALER, BillItemEnum.USD_FOR_COIN_DEALER, DigitalCurrencyEnum.USD, BillTypeEnum.EXPENSE),
    /**
     * 钻石兑换金币
     */
    DIAMOND_EXCHANGE_TO_COINS(BillDetailEnum.EXCHANGE_EXPENSE, BillItemEnum.EXCHANGE_TO_COINS, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.EXPENSE),

    /**
     * 发送弹幕扣除金币
     */
    BULLET_CHAT_DEDUCT_GOLD(BillDetailEnum.BULLET_CHAT_DEDUCT_GOLD, BillItemEnum.BULLET_CHAT_DEDUCT_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 房间送礼
     */
    SEND_IN_ROOM(BillDetailEnum.SENDING_GIFT_IN_ROOM, BillItemEnum.SENDING_GIFTS, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 房间送礼-房间全麦
     */
    SEND_IN_ROOM_ROOM_MIC_ALL(BillDetailEnum.SENDING_GIFT_IN_ROOM_ALL_MIC, BillItemEnum.SENDING_GIFTS, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 房间送礼-全房
     */
    SEND_IN_ROOM_ROOM_ALL(BillDetailEnum.SENDING_GIFT_IN_ROOM_ALL_ROOM, BillItemEnum.SENDING_GIFTS, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 私聊送礼
     */
    SEND_IN_CHAT(BillDetailEnum.SENDING_GIFT_BY_CHAT, BillItemEnum.SENDING_GIFTS, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 动态送礼
     */
    SEND_IN_POST(BillDetailEnum.SENDING_GIFT_BY_POST, BillItemEnum.SENDING_GIFTS, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * CMS奖励金币
     */
    CMS_REWARD_COIN(BillDetailEnum.CMS_REWARD_COIN, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * CMS奖励钻石
     */
    CMS_REWARD_DIAMOND(BillDetailEnum.CMS_REWARD_DIAMOND, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.INCOME),
    /**
     * 奖励包奖励金币
     */
    REWARD_PACK_COIN(BillDetailEnum.REWARD_PACK_COIN, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 奖励包奖励钻石
     */
    REWARD_PACK_DIAMOND(BillDetailEnum.REWARD_PACK_DIAMOND, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.INCOME),
    /**
     * 官方扣除金币
     */
    OFFICIAL_DEDUCT_COIN(BillDetailEnum.OFFICIAL_DEDUCT_COIN, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 官方扣除钻石
     */
    OFFICIAL_DEDUCT_DIAMOND(BillDetailEnum.OFFICIAL_DEDUCT_DIAMOND, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.EXPENSE),
    /**
     * CMS扣除金币
     */
    CMS_DEDUCT_COIN(BillDetailEnum.CMS_DEDUCT_COIN, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * CMS扣除钻石
     */
    CMS_DEDUCT_DIAMOND(BillDetailEnum.CMS_DEDUCT_DIAMOND, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.EXPENSE),
    /**
     * CMS充值补单
     */
    CMS_TOP_UP_REFUND(BillDetailEnum.CMS_TOP_UP_REFUND, BillItemEnum.TOP_UP, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * Apple store充值
     */
    APPLE_STORE_TOP_UP(BillDetailEnum.APPLE_STORE_TOP_UP, BillItemEnum.TOP_UP, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * Google pay充值
     */
    GOOGLE_PAY_TOP_UP(BillDetailEnum.GOOGLE_PAY_TOP_UP, BillItemEnum.TOP_UP, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * apple store退款
     */
    APPLE_STORE_REFUND(BillDetailEnum.APPLE_STORE_REFUND, BillItemEnum.REFUND, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * google pay退款
     */
    GOOGLE_PAY_REFUND(BillDetailEnum.GOOGLE_PAY_REFUND, BillItemEnum.REFUND, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    PAYER_MAX_REFUND(BillDetailEnum.PAYER_MAX_REFUND, BillItemEnum.REFUND, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 礼物分成钻石
     */
    GIFT_COMMISSION_DIAMOND(BillDetailEnum.GIFT_COMMISSION_DIAMOND, BillItemEnum.COMMISSION, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.INCOME),
    /**
     * 礼物分成金币
     */
    GIFT_COMMISSION_GOLD(BillDetailEnum.GIFT_COMMISSION_COIN, BillItemEnum.COMMISSION, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 自购装扮
     */
    PURCHASE_COSTUME_TO_SELF(BillDetailEnum.PURCHASE_COSTUME_TO_SELF, BillItemEnum.PURCHASE_COSTUME, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 赠送装扮
     */
    PURCHASE_COSTUME_TO_OTHER(BillDetailEnum.PURCHASE_COSTUME_TO_OTHER, BillItemEnum.PURCHASE_COSTUME, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * CMS奖励美金
     */
    CMS_REWARD_USD(BillDetailEnum.CMS_REWARD_USD, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.USD, BillTypeEnum.INCOME),
    /**
     * CMS扣除美金
     */
    CMS_DEDUCT_USD(BillDetailEnum.CMS_DEDUCT_USD, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.USD, BillTypeEnum.EXPENSE),
    /**
     * 奖励包奖励美金
     */
    REWARD_PACK_USD(BillDetailEnum.REWARD_PACK_USD, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.USD, BillTypeEnum.INCOME),
    /**
     * CMS奖励金票
     */
    CMS_REWARD_GOLDEN_TICKET(BillDetailEnum.CMS_REWARD_GOLDEN_TICKET, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),
    /**
     * CMS扣除金票
     */
    CMS_DEDUCT_GOLDEN_TICKET(BillDetailEnum.CMS_DEDUCT_GOLDEN_TICKET, BillItemEnum.OFFICIAL_DEDUCTION, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.EXPENSE),
    /**
     * 奖励包奖励金票
     */
    REWARD_PACK_GOLDEN_TICKET(BillDetailEnum.REWARD_PACK_GOLDEN_TICKET, BillItemEnum.OFFICIAL_REWARDS, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),

    /**
     * 水果派对消耗金币
     */
    FRUIT_PARTY_DEDUCT_GOLD(BillDetailEnum.FRUIT_PARTY_DEDUCT_GOLD, BillItemEnum.FRUIT_PARTY_DEDUCT_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * luck777消耗金币
     */
    LUCK_777_DEDUCT_GOLD(BillDetailEnum.LUCK_777_DEDUCT_GOLD, BillItemEnum.LUCK_777_DEDUCT_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),


    /**
     * three_card_brag消耗金币
     */
    THREE_CARD_BRAG_DEDUCT_GOLD(BillDetailEnum.THREE_CARD_BRAG_DEDUCT_GOLD, BillItemEnum.THREE_CARD_BRAG_DEDUCT_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * 水果派对金币奖励
     */
    FRUIT_PARTY_RECEIVE_AWARD(BillDetailEnum.FRUIT_PARTY_RECEIVE_AWARD, BillItemEnum.FRUIT_PARTY_RECEIVE_AWARD, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * luck777金币奖励
     */
    LUCK_777_RECEIVE_AWARD(BillDetailEnum.LUCK_777_RECEIVE_AWARD, BillItemEnum.LUCK_777_RECEIVE_AWARD, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * three_card_brag消耗金币
     */
    THREE_CARD_BRAG_RECEIVE_AWARD(BillDetailEnum.THREE_CARD_BRAG_RECEIVE_AWARD, BillItemEnum.THREE_CARD_BRAG_RECEIVE_AWARD, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 官网充值金币
     */
    OFFICIAL_WEBSITE_CHARGE_COIN(BillDetailEnum.OFFICIAL_WEBSITE_CHARGE_COIN, BillItemEnum.OFFICIAL_WEBSITE_CHARGE_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 官网充值金票
     */
    OFFICIAL_WEBSITE_CHARGE_GOLDEN_TICKET(BillDetailEnum.OFFICIAL_WEBSITE_CHARGE_GOLDEN_TICKET, BillItemEnum.OFFICIAL_WEBSITE_CHARGE_GOLDEN_TICKET, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),
    /**
     * 疯狂Triple消耗
     */
    CRAZY_TRIPLE_DEDUCT(BillDetailEnum.GAME_DEDUCT_COIN_CRAZY_TRIPLE, BillItemEnum.CRAZY_TRIPLE, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 疯狂Triple奖励
     */
    CRAZY_TRIPLE_REWARD(BillDetailEnum.GAME_REWARD_COIN_CRAZY_TRIPLE, BillItemEnum.CRAZY_TRIPLE, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * 商城自购装扮
     */
    SHOP_PROP_FOR_ONESELF_BY_GOLD(BillDetailEnum.PURCHASE_PROP_FOR_ONESELF, BillItemEnum.SHOP_PROP_FOR_ONESELF_BY_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     *  商城赠送装扮
     */
    SHOP_PROP_FOR_OTHERS_BY_GOLD(BillDetailEnum.PURCHASE_PROP_FOR_OTHERS, BillItemEnum.SHOP_PROP_FOR_OTHERS_BY_GOLD, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 购买贵族
     */
    ARISTOCRACY(BillDetailEnum.ACTIVATE_ARISTOCRACY, BillItemEnum.ARISTOCRACY, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 贵族回退金币
     */
    ARISTOCRACY_REWARD_GOLDEN_TICKET(BillDetailEnum.ARISTOCRACY_REWARD_GOLDEN_TICKET, BillItemEnum.ARISTOCRACY, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 房间party奖励金币
     */
    ROOM_PARTY_REWARD_GOLD(BillDetailEnum.ROOM_PARTY_REWARD_GOLD, BillItemEnum.ROOM_PARTY, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 贵族签到
     */
    ARISTOCRACY_SIGN_IN(BillDetailEnum.ARISTOCRACY_CHECK_IN, BillItemEnum.ARISTOCRACY, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 购买VIP
     */
    BUY_VIP(BillDetailEnum.ARISTOCRACY_BUY_VIP, BillItemEnum.VIP, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * ludo游戏扣除金币
     */
    LUDO_GAME_DEDUCT_COIN(BillDetailEnum.LUDO_GAME_DEDUCT_COIN, BillItemEnum.LUDO_GAME_DEDUCT_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * baLoot游戏扣除金币
     */
    BA_LOOT_GAME_DEDUCT_COIN(BillDetailEnum.BA_LOOT_GAME_DEDUCT_COIN, BillItemEnum.BA_LOOT_GAME_DEDUCT_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * UMO游戏扣除金币
     */
    UMO_GAME_DEDUCT_COIN(BillDetailEnum.UMO_GAME_DEDUCT_COIN, BillItemEnum.UMO_GAME_DEDUCT_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * ludo游戏增加金币
     */
    LUDO_GAME_INCREASE_COIN(BillDetailEnum.LUDO_GAME_INCREASE_COIN, BillItemEnum.LUDO_GAME_INCREASE_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * baLoot游戏增加金币
     */
    BA_LOOT_GAME_INCREASE_COIN(BillDetailEnum.BA_LOOT_GAME_INCREASE_COIN, BillItemEnum.BA_LOOT_GAME_INCREASE_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * UMO游戏增加金币
     */
    UMO_GAME_INCREASE_COIN(BillDetailEnum.PAYDAPAY_RECHARGE_COIN, BillItemEnum.UMO_GAME_INCREASE_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * pandaPay充值金币
     */
    PANDAPAY_RECHARGE_COIN(BillDetailEnum.PAYDAPAY_RECHARGE_COIN, BillItemEnum.TOP_UP, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * pandaPay充值金票
     */
    PANDAPAY_RECHARGE_GOLDEN_TICKET(BillDetailEnum.PAYDAPAY_RECHARGE_GOLDEN_TICKET, BillItemEnum.TOP_UP, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),
    /**
     * pandaPau退款扣除金币
     */
    PANDAPAY_REFUND_COIN(BillDetailEnum.PAYDAPAY_REFUND, BillItemEnum.REFUND, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * pandaPau退款扣除金票
     */
    //PANDAPAY_REFUND_GOLDEN_TICKET(BillDetailEnum.PAYDAPAY_REFUND, BillItemEnum.REFUND, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.EXPENSE),

    SEND_REDPACKET_DEDUCT_COIN(BillDetailEnum.SEND_REDPACKET_DEDUCT_COIN, BillItemEnum.SEND_REDPACKET_DEDUCT_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    REDPACKET_REFUND_INCREASE_COIN(BillDetailEnum.REDPACKET_REFUND_INCREASE_COIN, BillItemEnum.REDPACKET_REFUND_INCREASE_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    GRAB_REDPACKET_INCREASE_COIN(BillDetailEnum.GRAB_REDPACKET_INCREASE_COIN, BillItemEnum.GRAB_REDPACKET_INCREASE_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * 幸运转盘扣除金币
     */
    LUCKY_WHEEL_DEDUCT_COIN(BillDetailEnum.LUCKY_WHEEL_DEDUCT_COIN, BillItemEnum.LUCKY_WHEEL_DEDUCT_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),
    /**
     * 幸运转盘退金币
     */
    LUCKY_WHEEL_REFUND_COIN(BillDetailEnum.LUCKY_WHEEL_REFUND_COIN, BillItemEnum.LUCKY_WHEEL_REFUND_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 幸运转盘赢取金币
     */
    LUCKY_WHEEL_WIN_COIN(BillDetailEnum.LUCKY_WHEEL_WIN_COIN, BillItemEnum.LUCKY_WHEEL_WIN_COIN, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),

    /**
     * 华为充值金币
     */
    HUAWEI_RECHARGE_COIN(BillDetailEnum.HUAWEI_RECHARGE_COIN, BillItemEnum.TOP_UP, DigitalCurrencyEnum.COIN, BillTypeEnum.INCOME),
    /**
     * 华为充值金票
     */
    HUAWEI_RECHARGE_GOLDEN_TICKET(BillDetailEnum.HUAWEI_RECHARGE_GOLDEN_TICKET, BillItemEnum.TOP_UP, DigitalCurrencyEnum.GOLDEN_TICKET, BillTypeEnum.INCOME),
    /**
     * 华为退款扣除金币
     */
    HUAWEI_REFUND_COIN(BillDetailEnum.HUAWEI_REFUND, BillItemEnum.REFUND, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),

    /**
     * VIP 花金币解封
     */
    VIP_RECOVER_COIN(BillDetailEnum.VIP_UNBLOCK, BillItemEnum.VIP, DigitalCurrencyEnum.COIN, BillTypeEnum.EXPENSE),






    /**
     * 激活奖励
     */
    ACTIVATION_REWARDS(BillDetailEnum.ACTIVATION_REWARDS, BillItemEnum.ACTIVATION_REWARDS, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.INCOME),
    /**
     * 代理贡献奖励
     */
    CONTRIBUTION_REWARDS(BillDetailEnum.CONTRIBUTION_REWARDS, BillItemEnum.CONTRIBUTION_REWARDS, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.INCOME),
    /**
     * 代理充值奖励
     */
    AGENT_RECHARGE_REWARDS(BillDetailEnum.AGENT_RECHARGE_REWARDS, BillItemEnum.AGENT_RECHARGE_REWARDS, DigitalCurrencyEnum.DIAMOND, BillTypeEnum.INCOME),
    ;

    /**
     * 账单详情项
     */
    private final BillDetailEnum detailEnum;

    /**
     * 账单项
     */
    private final BillItemEnum billItemEnum;

    /**
     * 货币类型
     */
    private final DigitalCurrencyEnum digitalCurrencyEnum;

    /**
     * 账单类型
     */
    private final BillTypeEnum billTypeEnum;

    /**
     * 根据账单详情项获取账单枚举
     *
     * @param billDetailEnum
     * @return
     */
    public static BillEnum getByDetailEnum(BillDetailEnum billDetailEnum) {
        return Stream.of(BillEnum.values()).filter(be -> be.getDetailEnum() == billDetailEnum).findAny().orElseThrow(() -> new ApiException(CodeEnum.PARAM_ILLEGAL));
    }
}
