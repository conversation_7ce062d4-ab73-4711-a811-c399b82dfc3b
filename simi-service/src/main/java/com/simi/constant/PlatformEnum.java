package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PlatformEnum {

    PLATFORM_NONE(0,"占位", ""),
    ANDROID(1,"安卓", "Android"),
    IOS(2,"IOS", "ios"),;


    final Integer number;

    final String desc;

    final String name;

    public static PlatformEnum forNumber(Integer platform) {
        return Stream.of(PlatformEnum.values()).filter(e -> Objects.equals(e.getNumber(), platform)).findAny().orElse(null);
    }
}
