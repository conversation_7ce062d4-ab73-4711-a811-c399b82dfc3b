package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 邀请相关rediskey
 */
@Getter
@AllArgsConstructor
public enum InviteRedisKey implements BaseRedisKey {

    /**
     * 用户对应邀请码
     */
    user_invite_code,

    /**
     * 邀请码对应用户
     */
    user_counter_invite_code,

    /**
     * 触发记录
     */
    invite_code_trigger,

    /**
     * 用户名下被邀请者
     */
    user_branch_invited,

    /**
     * 用户所属代理邀请者
     */
    user_superior_invite,

    /**
     * 注册完善给予二次绑定邀请机会
     */
    improve_information_flag

    ;

    @Override
    public String getName() {
        return this.name();
    }

}
