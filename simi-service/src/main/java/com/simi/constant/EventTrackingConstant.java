package com.simi.constant;

/**
 * 数据埋点
 * @Author: Andy
 * @Date: 2024-03-06
 */
public class EventTrackingConstant {

    public static final String EVENT_TOPIC = "esing-tkdata";

    /**
     * 事件名
     */
    public static class EventName{
        /**
         * 充值_后端
         * 充值时上报
         */
        public static final String RECHARGE_SERVICE = "recharge_service";

        /**
         * 美金兑换金币_后端
         * 兑换时上报
         */
        public static final String EXCHANGE_SERVICE = "exchange_service";

        /**
         * 钻石兑换美金
         * 兑换时上报
         */
        public static final String DIAMOND_FOR_USD = "diamond_for_usd";

        /**
         * 美金提现币商
         * 兑换时上报
         */
        public static final String USD_FOR_COIN_DEALER = "usd_for_coin_dealer";

        /**
         * 有效房间
         * 1. 当日有人上过麦位的房间
         * 2. 后端每日上报
         */
        public static final String ROOM_EFFECTIVE = "room_effective";

        /**
         * 同时在线房间数峰值
         * 1. 当天同时在线的房间数最大值（目标房间：有效房间）
         * 2. 后端每分钟上报
         * ####
         */
        public static final String ROOM_PEAK_VALUE = "room_peak_value";

  /*      *//**
         * 好友数≥3
         * 1. 相互关注的好友数大于等于3个的用户
         * 2. 后端每分钟上报
         * 3. 已上报过的用户不重复记录、上报
         * 每分钟上报这一分钟满足条件的用户数，历史已上报过的用户不重复记录、上报
         *//*
        public static final String FRIENDS_MORE_3 = "friends_more_3";*/

        /**
         * 在线用户
         * 1. 同时在线的用户数最大值
         * 2. 后端每分钟上报
         * ####
         */
        public static final String ONLINE_USER = "online_user";

/*        *//**
         * 活跃用户好友数≥3
         * 1. 相互关注的好友数大于等于3个的用户
         * 2. 后端每日上报
         *//*
        public static final String ACTIVE_FRIENDS_MORE_3 = "active_friends_more_3";*/

        /**
         * 房间同时在线用户数峰值
         * 1. 当天同时在线的房间数最大值（目标房间：有效房间）
         * 2. 后端每小时上报
         * #####
         */
        public static final String ROOM_VIEWER_PEAK_VALUE = "room_viwer_peak_value";

 /*       *//**
         * 创建家族数
         * 1. 当天新创建的家族数
         * 2. 后端每天上报
         *//*
        public static final String FAMILY_CREATE_NUM = "family_create_num";

        *//**
         * 有效家族数
         * 1.  家族已有成员的50%当天在家族聊天群内发过言（文本、送礼、表情、分享）
         * 2. 后端每天上报
         *//*
        public static final String FAMILY_VALID_NUM = "family_valid_num";*/

        /**
         * Pk结束
         */
        public static final String PK_END = "pk_end";


        /**
         * 在房时长
         */
        public static final String ROOM_END_STAY_TIME = "room_end_stay_time";

        /**
         * 在麦时长
         */
        public static final String SEAT_END_STAY_TIME = "seat_end_stay_time";

        /**
         * 上麦
         */
        public static final String SEAT_UP_CLICK = "seat_up_click";


        /**
         * 踢出房间
         */
        public static final String ROOM_REMOVE = "room_remove";


        /**
         * 送礼（连击结束）
         */
        public static final String GIFT_SEND = "gift_send";

        /**
         * 退出房间
         */
        public static final String ROOM_EXIT = "room_exit";

        /**
         * 完成任务
         */
        public static final String COMPLETE_THE_TASK = "complete_the_task";

        /**
         * 水果机活动-每天活动的数据
         */
        public static final String FRUIT_MACHINE_PER_DATA = "fruit_machine_per_data";
        /**
         * 水果机活动-活动总参与人数
         */
        public static final String FRUIT_MACHINE_TOTAL_PARTICIPANTS = "fruit_machine_total_participants";


    }
}
