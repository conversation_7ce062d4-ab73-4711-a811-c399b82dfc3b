package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FontWeightEnum {

    w100(0,"字重100"),
    w200(1,"字重200"),
    w300(2,"字重300"),
    w400(3,"字重400"),
    w500(4,"字重500"),
    w600(5,"字重600"),
    w700(6,"字重700"),
    w800(7,"字重800"),
    w900(8,"字重900"),
    UNRECOGNIZED(-1,"");

    private Integer number;

    private String desc;
}
