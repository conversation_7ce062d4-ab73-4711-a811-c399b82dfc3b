package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RoomPartyRedisKey implements BaseRedisKey {

    /**
     * 房间活动标签
     */
    room_party_tag,

    /**
     * 房间party
     */
    room_party,

    /**
     * 订阅者
     */
    room_party_subscribe,

    /**
     * 送礼值
     */
    room_party_send_score,

    /**
     * 收礼值
     */
    room_party_receive_score,

    /**
     * 送礼数量
     */
    room_party_send_gift_count,

    /**
     * 收礼数量
     */
    room_party_receive_gift_count,

    /**
     * 进行中的party
     */
    running_room_party,

    /**
     * 消息锁
     */
    room_party_mq_msg_local,

    /**
     * 房间最高在线
     */
    room_party_online_count,
    /**
     * 订阅锁
     */
    room_party_subscribe_lock,
    /**
     * party送礼成员记录
     */
    room_party_send_member_record,
    /**
     * party收礼成员记录
     */
    room_party_receive_member_record,

    /**
     * party公屏数量
     */
    room_part_screen_count,

    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
