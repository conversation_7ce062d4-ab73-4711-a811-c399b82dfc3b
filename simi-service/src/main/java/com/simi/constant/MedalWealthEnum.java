package com.simi.constant;

import com.simi.config.MedalConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MedalWealthEnum {

    wealth_Lv01(1, MedalConfig.wealth_Lv01),
    wealth_Lv02(2, MedalConfig.wealth_Lv02),
    wealth_Lv03(3, MedalConfig.wealth_Lv03),
    wealth_Lv04(4, MedalConfig.wealth_Lv04),
    wealth_Lv05(5, MedalConfig.wealth_Lv05),
    wealth_Lv06(6, MedalConfig.wealth_Lv06),
    wealth_Lv07(7, MedalConfig.wealth_Lv07),
    wealth_Lv08(8, MedalConfig.wealth_Lv08),
    wealth_Lv09(9, MedalConfig.wealth_Lv09),
    wealth_Lv10(10, MedalConfig.wealth_Lv10),
    ;

    private Integer number;

    private String desc;

    public static String getDesc(Integer number){
        for (MedalWealthEnum value : values()) {
            if (value.getNumber().equals(number)){
                return value.getDesc();
            }
        }
        return "";
    }
}
