package com.simi.constant;

import com.simi.config.MedalConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MedalCharmEnum {

    charm_Lv01(1, MedalConfig.charm_Lv01),
    charm_Lv02(2, MedalConfig.charm_Lv02),
    charm_Lv03(3, MedalConfig.charm_Lv03),
    charm_Lv04(4, MedalConfig.charm_Lv04),
    charm_Lv05(5, MedalConfig.charm_Lv05),
    charm_Lv06(6, MedalConfig.charm_Lv06),
    charm_Lv07(7, MedalConfig.charm_Lv07),
    charm_Lv08(8, MedalConfig.charm_Lv08),
    charm_Lv09(9, MedalConfig.charm_Lv09),
    charm_Lv10(10, MedalConfig.charm_Lv10),
    ;

    private Integer number;

    private String desc;

    public static String getDesc(Integer number){
        for (MedalCharmEnum value : values()) {
            if (value.getNumber().equals(number)){
                return value.getDesc();
            }
        }
        return "";
    }
}
