package com.simi.constant;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CoinDealerStatus {
    @SerializedName("-1")
    DELETED(-1),

    @SerializedName("0")
    FREEZING(0),

    @SerializedName("1")
    NORMAL(1);
    
    @EnumValue
    private final Integer code;
}
