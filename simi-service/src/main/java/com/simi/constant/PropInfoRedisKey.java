package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PropInfoRedisKey  implements BaseRedisKey{

    user_goods_in,
    prop_info,


    ;
    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
