package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PkRedisKey implements BaseRedisKey {

    /**
     * pk进行中的房间
     */
    running_pk_room,

    /**
     * 操作锁
     */
    pk_operate_lock,

    /**
     * 详情
     */
    pk_detail,

    /**
     * pk值
     */
    pk_val,

    /**
     * pk贡献
     */
    pk_contributor,

    /**
     * 消息锁
     */
    pk_message_lock,

    /**
     * 结果
     */
    pk_result,


    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
