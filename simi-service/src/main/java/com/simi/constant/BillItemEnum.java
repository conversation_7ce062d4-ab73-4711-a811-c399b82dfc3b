package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum BillItemEnum {

    BILL_DETAIL_NONE(0,"占位"),
    EXCHANGE_TO_COINS(1,"钻石转换金币"),
    SENDING_GIFTS(2,"赠送礼物"),
    OFFICIAL_REWARDS(3,"官方奖励"),
    OFFICIAL_DEDUCTION(4,"官方扣除"),
    TOP_UP(5,"充值"),
    REFUND(6,"退款"),
    COMMISSION(7,"收礼分成"),
    PURCHASE_COSTUME(8,"购买装扮"),
    COIN_DEALER_FOR_GOLD(9,"金票兑换金币收入"),
    USD_FOR_COIN_DEALER(10,"美元兑换金票"),
    DIAMONDS_FOR_DOLLARS(11,"钻石兑换美金收入"),
    DIAMONDS_FOR_DOLLAR(12,"钻石兑换美金收入（代理）"),
    DIAMONDS_FOR_DOLLARS_DEDUCT(13,"钻石兑换美金支出"),
    COIN_DEALER_FOR_COIN_DEALER(14,"一级币商转赠二级币商"),
    GOLDEN_TICKET_FOR_GOLD(15,"金票兑换金币"),
    DOLLAR_FOR_GOLD_EXPEND(16,"美金兑换金币支出"),
    DOLLAR_FOR_GOLD_INCOME(17,"美金兑换金币收入"),
    USER_FOR_COIN_DEALER(18,"用户转赠币商收入"),
    COIN_DEALER_FOR_USER(19,"币商转赠用户支出"),
    BULLET_CHAT_DEDUCT_GOLD(20,"发送弹幕扣除金币"),
    USD_WITHDRAW_SPONSOR(21,"美元提现发起"),
    USD_WITHDRAW_LOSE(22,"美元提现失败"),
    FRUIT_PARTY_DEDUCT_GOLD(23,"水果派对消耗金币"),
    FRUIT_PARTY_RECEIVE_AWARD(24,"水果派对获得奖励"),
    OFFICIAL_WEBSITE_CHARGE_COIN(26,"官网充值获得金币"),
    OFFICIAL_WEBSITE_CHARGE_GOLDEN_TICKET(27,"官网充值获得金票"),
    // 疯狂Triple
    CRAZY_TRIPLE(28,"疯狂Triple"),
    SHOP_PROP_FOR_ONESELF_BY_GOLD(30,"商城自购装扮"),
    SHOP_PROP_FOR_OTHERS_BY_GOLD(31,"商城赠送装扮"),
    // 贵族
    ARISTOCRACY(29,"贵族"),

    // VIP
    VIP(32,"VIP"),
    ROOM_PARTY(33,"房间party"),

    LUDO_GAME_DEDUCT_COIN(34,"ludo游戏扣除金币"),

    BA_LOOT_GAME_DEDUCT_COIN(35,"baLoot游戏扣除金币"),

    UMO_GAME_DEDUCT_COIN(36,"UMO游戏扣除金币"),

    LUDO_GAME_INCREASE_COIN(34,"ludo游戏获得金币"),

    BA_LOOT_GAME_INCREASE_COIN(35,"baLoot游戏获得金币"),

    UMO_GAME_INCREASE_COIN(36,"UMO游戏获得金币"),

    LUCK_777_DEDUCT_GOLD(37,"luck 777消耗金币"),

    LUCK_777_RECEIVE_AWARD(38,"luck 777获得奖励"),

    THREE_CARD_BRAG_DEDUCT_GOLD(39,"King Battle消耗金币"),

    THREE_CARD_BRAG_RECEIVE_AWARD(40,"King Battle消耗金币"),

    SEND_REDPACKET_DEDUCT_COIN(45,"发红包消耗金币"),
    REDPACKET_REFUND_INCREASE_COIN(46,"发红包退款收入金币"),
    GRAB_REDPACKET_INCREASE_COIN(47,"抢红包获得金币"),

    LUCKY_WHEEL_DEDUCT_COIN(50,"幸运转盘扣减金币"),
    LUCKY_WHEEL_REFUND_COIN(51,"幸运转盘退金币"),
    LUCKY_WHEEL_WIN_COIN(52,"幸运转盘赢金币"),


    ACTIVATION_REWARDS(53,"激活奖励"),
    CONTRIBUTION_REWARDS(54,"代理贡献奖励"),
    AGENT_RECHARGE_REWARDS(55, );
    final Integer number;

    final String desc;

    public static BillItemEnum getByNumber(Integer type) {
        return Arrays.stream(BillItemEnum.values())
                .filter(e -> Objects.equals(type, e.number))
                .findFirst().orElse(BillItemEnum.BILL_DETAIL_NONE);
    }

}
