package com.simi.constant;

import com.simi.common.config.CopywritingEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum RegularGameEnum {


    /**
     * fruit
     */
    FRUIT_PARTY(1, "Fruit Party", BillEnum.FRUIT_PARTY_DEDUCT_GOLD, BillEnum.FRUIT_PARTY_RECEIVE_AWARD, CopywritingEnum.FRUIT_PARTY_NAME),


    /**
     * 777游戏
     */
    LUCK_777(2, "luck 777", BillEnum.LUCK_777_DEDUCT_GOLD, BillEnum.LUCK_777_RECEIVE_AWARD, CopywritingEnum.LUCK_777_NAME),

    /**
     * three-card-brag游戏
     */
    THREE_CARD_BRAG(3, "three card brag", BillEnum.THREE_CARD_BRAG_DEDUCT_GOLD, BillEnum.THREE_CARD_BRAG_RECEIVE_AWARD, CopywritingEnum.THREE_CARD_BRAG),

    ;

    /**
     * 类型
     */
    final Integer joyType;

    /**
     * 描述
     */
    final String desc;

    /**
     * 支出的账单项
     */
    final BillEnum expenseBill;

    /**
     * 收入的账单项
     */
    final BillEnum incomeBill;

    /**
     * 名称枚举
     */
    final CopywritingEnum copywritingEnum;


    public static RegularGameEnum getByJoyType(Integer joyType) {
        return Stream.of(RegularGameEnum.values()).filter(e -> Objects.equals(e.joyType, joyType))
                .findFirst().orElse(null);
    }
}
