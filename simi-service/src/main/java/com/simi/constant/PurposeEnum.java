package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurposeEnum {

    REGISTER(1,"注册"),
    FORGET_THE_PASSWORD(2,"忘记密码"),
    BIND_ON_ACCOUNT(3,"账号绑定"),
    REPLACEMENT_PHONE(4, "手机换绑"),
    ;

    private Integer number;

    private String desc;

    public static String getDescByNumber(Integer number){
        for (PurposeEnum value : values()) {
            if (value.getNumber().equals(number)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
