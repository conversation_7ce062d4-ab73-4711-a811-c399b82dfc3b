package com.simi.constant;

import lombok.AllArgsConstructor;

/**
 * 用户文件枚举
 * @Author: Andy
 * @Date: 2024-01-09
 */
@AllArgsConstructor
public enum UserCopywritingEnum {
    CP_1("cp.user.server.1", "图片内容违反规则，用户头像已重置为默认头像。"),
    CP_2("cp.user.server.2", "客服欢迎新用户"),
    CP_3("cp.user.server.3", "Congratulations!\nYour {} Level have reached\n"),
    CP_4("cp.user.server.4", "Active"),
    CP_5("cp.user.server.5", "Charm"),
    CP_6("cp.user.server.6", "Wealth"),
    CP_7("cp.user.server.7", "{} Level"),
    CP_8("cp.user.server.8", "Your {} Level have reached Lv.{}"),

    CP_9("cp.user.server.9", "Lv.{}"),
    CP_10("cp.user.server.10", "I followed you, may I make friends with you?"),
    CP_11("cp.user.server.11", "Follow")
    ;

    private final String key;
    private final String copywriting;
    public String getKey() {
        return this.key;
    }
    public String getCopywriting() {
        return this.copywriting;
    }
}
