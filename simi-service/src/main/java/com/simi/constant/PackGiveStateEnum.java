package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PackGiveStateEnum {

    /**
     * 占位
     */
    PACK_GIVE_STATE_NONE(0),

    /**
     * 未开始
     */
    PACK_GIVE_NOT_STARTED(1),

    /**
     * 取消下发
     */
    PACK_GIVE_CANCEL(2),

    /**
     * 开始下发
     */
    PACK_GIVE_GIVE_ING(3),

    /**
     * 下发完毕
     */
    PACK_GIVE_COMPLETED(4),
    ;
    final Integer type;
}
