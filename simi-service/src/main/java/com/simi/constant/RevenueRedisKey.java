package com.simi.constant;


import com.simi.common.redis.BaseRedisKey;

public enum RevenueRedisKey implements BaseRedisKey {

    /**
     * 充值套餐
     */
    recharge_package,
    /**
     * 充值锁
     */
    recharge_apply_lock,
    /**
     * Google订单校验锁
     */
    recharge_google_check_lock,
    /**
     * 钱包
     */
    purse,
    /**
     * 代理钱包
     */
    agent_purse,
    /**
     * 钱包锁
     */
    purse_lock,

    /**
     * 充值锁
     */
    recharge_result_lock,
    /**
     * 兑换锁
     */
    exchange_lock,
    /**
     * 送礼锁
     */
    send_gift_lock,
    /**
     * combo次数
     */
    send_combo_times,
    /**
     * mq消息
     */
    mq_gift_status,
    /**
     * 礼物消息锁
     */
    gift_message_lock,

    /**
     * 背包锁
     */
    backpack_lock,
    /**
     * 连击时间
     */
    combo_timestamp,
    /**
     * 连击
     */
    combo,
    /**
     * 送礼关注消息
     */
    follow_message_by_gift,
    /**
     * 送礼关注消息锁
     */
    follow_message_by_gift_lock,

    /**
     * 送礼统计
     */
    gift_send_census,

    /**
     * 商城下单
     */
    shop_purchase,

    /**
     * 背包物品
     */
    backpack_goods_stock,

    apple_token,

    pandapay_refund_req,
    ;

    @Override
    public String getName() {
        return this.name();
    }

    public static String giftSendCensusKey(String roomId){
        return gift_send_census.getKey(roomId);
    }

    public static String rechargeResultLockKey(String recordId){
        return recharge_result_lock.getKey(recordId);
    }

}
