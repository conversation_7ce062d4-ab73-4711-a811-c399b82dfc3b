package com.simi.constant;

import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum RouteTypePageEnum {

    H5(RouteTypeEnum.ROUTE_H5, "", ""),
    ROOM(RouteTypeEnum.ROUTE_ROOM, "room", "roomId"),
    USER(RouteTypeEnum.ROUTE_HOMEPAGE, "user_space", "uid"),
    ;
    private final RouteTypeEnum routeType;
    private final String routePage;
    private final String param;

    public static RouteTypePageEnum getByRouteType(final int routeType){
        return Stream.of(RouteTypePageEnum.values()).filter(e -> e.routeType.getType() == routeType).findAny().orElseThrow(() -> new ApiException(CodeEnum.PARAM_ILLEGAL));
    }
}
