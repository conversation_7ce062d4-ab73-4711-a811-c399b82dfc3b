package com.simi.constant;

public interface PushEvent {

    String room_mic_update_event = "RoomMicUpdateEvent";//房间麦位更新

    String room_screen_message_event = "RoomScreenMessageEvent";//房间公屏消息
    /**
     * 进房欢迎公告
     */
    String in_room_welcome_content_event = "inRoomWelcomeContentEvent";

    String room_screen_system_notice_event = "RoomScreenSystemNoticeEvent";//房间公屏消息

    String room_screen_send_gift_combo_event = "roomScreenSendGiftComboEvent";//房间

    String room_screen_expression_event = "RoomScreenExpressionEvent";//房间公屏表情

    String room_screen_image_event = "RoomScreenImageEvent";

    String room_screen_game_expression_event = "RoomScreenGameExpressionEvent";//房间公屏游戏表情

    String room_toast_notify_event = "RoomScreenMessageEvent";//房间toast提示

    String room_popup_notify_event = "RoomPopupNotifyEvent";//房间弹窗提示

    String room_mic_invite_up_event = "RoomMicInviteUpEvent";//房间邀请上麦

    String room_user_prop_info_event = "RoomUserPropInfoEvent";//房间装扮信息
    String room_party_event = "RoomPartyEvent";//房间party信息

    String room_audience_update_event = "RoomAudienceUpdateEvent";//房间观众更新

    String room_identity_update_event = "RoomIdentityUpdateEvent";//房间身份更新

    String room_user_conf_update_event = "RoomUserConfUpdateEvent";//房间用户配置更新

    String room_mic_all_mute_event = "RoomUserConfUpdateEvent";//房间全麦禁言

    String room_config_update_event = "RoomConfigUpdateEvent";//房间配置更新

    String room_gift_send_banner_event = "RoomGiftSendBannerEvent"; // 房间送礼横幅

    String room_luck_gift_send_banner_event = "RoomLuckGiftSendBannerEvent"; // 房间幸运送礼横幅

    String room_kick_out_event = "RoomKickOutEvent";//踢出房间

    String room_mic_kick_out_event = "RoomMicKickOutEvent";//房间全麦禁言

    String room_gift_stream_update_event = "RoomGiftStreamUpdateEvent"; //房间礼物流水变更

    String room_send_gift_public_screen_event = "RoomSendGiftPublicScreenEvent"; // 房间送礼公屏消息

    String regular_joy_banner_effect_event = "RegularJoyBannerEffectEvent";

    String regular_joy_banner_effect_event_2 = "RegularJoyBannerEffectEvent2";// 保底礼物特效横幅

    String room_screen_system_clear = "RoomScreenSystemClear";

    // 公屏禁言状态更新
    String room_user_public_screen_silence_update = "RoomUserPublicScreenSilenceUpdate";

    String user_black_list_event = "UserBlackListEvent";

    String room_pk_info_update = "RoomPkInfoUpdate";

    String custom_public_screen_msg = "CustomPublicScreenMsg";

    String user_upload_log_msg = "UserUploadLogMsg";

    String adjust_room_mic_kick_out_event = "AdjustRoomMicKickOutEvent";

    String room_mode_switch = "RoomModeSwitch";

    String room_info_update_event = "RoomInfoUpdateEvent";

    /**
     * 幸运礼物小倍率
     */
    String lucky_gift_small_multiple = "LuckyGiftSmallMultiple";

    /**
     * 幸运礼物小倍率
     */
    String lucky_gift_big_multiple = "LuckyGiftBigMultiple";

    // 勋章
    String global_common_receive_badge_event = "GlobalCommonReceiveBadgeEvent";

    //  贵族升级全服房间通知 RoomAristocracyUpgradeEvent
    String room_aristocracy_upgrade_event = "RoomAristocracyUpgradeEvent";

    // GlobalAristocracyOnlineEvent     贵族上线全服通知
    String global_aristocracy_online_event = "GlobalAristocracyOnlineEvent";

    // 开奖结果通知
    String fruit_machine_event = "FruitMachineEvent";
    /**
     * 红包
     */
    String room_screen_lucky_bag_send_event = "RoomScreenLuckyBagSendEvent";
    String room_screen_lucky_bag_recive_event = "RoomScreenLuckyBagReciveEvent";
    String world_lucky_bag_event = "WorldLuckyBagEvent";

    /**
     * 幸运转盘信息
     */
    String lucky_wheel_info = "LuckyWheelInfo";

    /**
     * 幸运转盘取消文案
     */
    String LUCKY_WHEEL_CANCEL_CONTENT = "LuckyWheelCancelContent";

    /**
     * 转盘开始文案
     */
    String LUCKY_WHEEL_START_CONTENT = "LuckyWheelStartContent";


    String ROOM_MIC_CHARM_IS_ENABLE_EVENT = "RoomMicCharmIsEnableEvent";

    //-------------------------大厅-------------------------
    /**
     * 大厅模式触发事件
     */
    String RoomLobbyStatusEvent = "RoomLobbyStatusEvent";

    String ROOM_GAME_AWARD_EVENT = "RoomGameAwardEvent";

}

