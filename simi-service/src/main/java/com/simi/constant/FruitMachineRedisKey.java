package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FruitMachineRedisKey implements BaseRedisKey {

    /**
     * 第几轮
     */
    fruit_machine_round,

    /**
     * 第几轮每个位置下注金额
     */
    fruit_machine_round_bet,

    /**
     * 奖池
     */
    fruit_machine_pool,

    /**
     * 商品  倍率
     */
    fruit_machine_name_multiple,

    /**
     * 第几轮，开奖结果
     */
    fruit_machine_round_result,

    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
