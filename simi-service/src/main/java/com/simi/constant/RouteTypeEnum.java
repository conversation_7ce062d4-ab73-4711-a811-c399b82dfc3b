package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum RouteTypeEnum {

    /**
     * 无
     */
    ROUTE_TYPE_NONE(0),
    /**
     * 跳转链接
     */
    ROUTE_H5(1),
    /**
     * 跳转主页
     */
    ROUTE_HOMEPAGE(2),
    /**
     * 跳转房间
     */
    ROUTE_ROOM(3),
    /**
     *
     */
    UNRECOGNIZED(4),
    ;

    final Integer type;

    public static RouteTypeEnum getByRouteType(Integer routeType) {
        return Arrays.stream(RouteTypeEnum.values()).filter(e -> Objects.equals(e.getType(), routeType))
                .findAny().orElse(RouteTypeEnum.ROUTE_TYPE_NONE);
    }
}
