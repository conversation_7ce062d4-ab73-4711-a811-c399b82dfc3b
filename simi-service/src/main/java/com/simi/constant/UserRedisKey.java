package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 用户Redis Key
 *
 * <AUTHOR>
 * @date 2023/11/2 10:58
 */
@Getter
@AllArgsConstructor
public enum UserRedisKey implements BaseRedisKey {

    user,
    user_summary,
    user_no,
    /**
     * 用户完善资料锁
     */
    user_complete_lock,
    /**
     * 用户在线状态
     */
    user_online,

    follow_lock,
    following,  //用户关注列表
    followers,  //用户被关注列表
    follow_message, //推送求关注消息

    visit_user,  //访客用户列表
    visit_record,  //访问记录

    blacklist_lock,
    blacklist,  //拉黑列表

    new_user,

    new_user_get_reward,
    /**
     * 收礼值
     */
    receive_gift_value,
    /**
     * 用户活跃等级
     */
    user_active_level,
    /**
     * 魅力等级
     */
    user_charm_level,
    /**
     * 财富等级
     */
    user_wealth_level,

    /**
     * 活跃人数
     */
    user_active,

    /**
     * 新用户活跃
     */
    user_new_user_active,

    /**
     * 个人相册
     */
    user_private_photo_top,
    user_private_photo_lock,

    /**
     * 用户设备
     */
    user_device,

    /**
     * meilisearch 用户信息更新时间戳
     */
    meilisearch_user_info_update_flag,

    gift_merge,

    /**
     * 用户语言hash
     */
    user_lang,
    /**
     * 用户活跃标志
     */
    user_active_flag,
    user_active_info,
    /**
     * 最新一次请求时间
     */
    user_last_request_time,

    /**
     * 用户账号封禁（设备/ip）
     */
    black_content,

    /**
     * 用户国家
     */
    user_country,

    /**
     * 贵族用户信息
     */
    user_aristocracy,
    /**
     * 贵族信息刷新冷却时间
     */
    user_aristocracy_refresh_time,
    /**
     * 贵族用户，过期信息推送记录
     */
    user_aristocracy_expire_push_record,
    /**
     * 贵族上线推送消息
     */
    user_aristocracy_online_push_msg,
    /**
     * 贵族赠送贵族记录
     */
    user_aristocracy_send,
    /**
     * 贵族签到金币补领
     */
    user_aristocracy_check_in,
    /**
     * VIP 用户信息
     */
    user_vip,
    /**
     * Vip 用户刷新冷却时间
     */
    user_vip_refresh_time,
    /**
     * VIP 当前月升级的用户
     */
    user_vip_upgrade,
    /**
     * Vip冻结时间
     */
    user_vip_freeze_time,

    /**
     * vip用户，过期信息推送记录
     */
    user_vip_expire_push_record,
    /**
     * vip赠送vip记录
     */
    user_vip_send,
    /**
     * 用户bd表示
     */
    user_bd_flag,
    /**

    /**
     * 水果赢家总榜
     */
    fruit_winner,


    /**
     * 充值锁
     */
    recharge_apply_lock,

    huawei_recharge_lock,

    user_sig,

    user_long_link_token,

    user_block
    ;

    @Override
    public String getName() {
        return this.name();
    }

}
