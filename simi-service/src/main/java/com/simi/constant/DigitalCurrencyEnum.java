package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@AllArgsConstructor
@Getter
public enum DigitalCurrencyEnum {

    CURRENCY_NONE(0,"占位"),
    COIN(1,"金币"),
    DIAMOND(2,"钻石"),
    USD(3,"美金"),
    GOLDEN_TICKET(4,"金票")
    ;

    private final Integer number;

    private final String desc;

    public static DigitalCurrencyEnum getByNumber(Integer number) {
        if (Objects.isNull(number) || Objects.equals(CURRENCY_NONE.getNumber(), number)) {
            return null;
        }
        return Stream.of(DigitalCurrencyEnum.values()).filter(e -> Objects.equals(e.getNumber(), number)).findAny().orElse(null);
    }

}
