package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/29 21:01
 **/
@Getter
@AllArgsConstructor
public enum RoomPartyStatusEnum {

    /**
     * 1-进行中;
     */
    GOING(1),

    /**
     * 2-未开始;
     */
    NOT_STARTED(2),

    /**
     * 3-已订阅;
     */
    SUBSCRIBED(3),

    /**
     * 4-我的创建
     */
    MY_CREATION(4),
    ;
    final Integer event;

    public static RoomPartyStatusEnum getByEvent(Integer event) {
        return Arrays.stream(RoomPartyStatusEnum.values())
                .filter(e -> Objects.equals(e.event, event))
                .findAny().orElse(null);
    }
}
