package com.simi.constant;

import cn.hutool.core.util.StrUtil;
import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityRedisKey implements BaseRedisKey {

    /**
     * 主播达标活动钻石记录
     */
    invite_diamond_anchor,
    /**
     * 主播达标活动等级记录
     */
    invite_diamond_anchor_level_record,
    /**
     * 主播达标活动领奖记录
     */
    invite_diamond_anchor_claim_record,
    /**
     * 代理达标活动钻石记录
     */
    invite_diamond_agent,
    /**
     * 代理达标活动等级记录
     */
    invite_diamond_agent_level_record,
    /**
     * 代理达标活动领奖记录
     */
    invite_diamond_agent_claim_record,
    /**
     * 代理达标活动领奖锁
     */
    invite_diamond_claim_lock,


    /**
     * 女性支持者活动送礼数量
     */
    female_supporters_send_gift,
    /**
     * 女性支持者活动收礼数量
     */
    female_supporters_receive_gift,
    /**
     * 女性支持者活动上麦时间
     */
    female_supporters_up_mic_time,
    /**
     * 女性支持者活动结算以及领取结果
     */
    female_supporters_balance,
    female_supporters_claim_lock,

    /**
     * 2024宰牲节送礼计数
     */
    corban_send_gift,
    /**
     * 2024宰牲节上麦时间
     */
    corban_user_upmic,
    /**
     * 2024宰牲节领奖记录
     */
    corban_user_claim_record,
    /**
     * 2024宰牲节活动用户任务达成记录
     */
    corban_user_finish_record,
    corban_user_claim_lock,

    /**
     * 疯狂triple 开启游戏lock
     */
    crazy_triple_start_game_lock,
    /**
     * 疯狂triple 开启下一局游戏lock
     */
    crazy_triple_start_next_game_lock,
    /**
     * 疯狂triple 抽奖lock
     */
    crazy_triple_to_lottery_lock,
    /**
     * 疯狂triple 加入游戏lock
     */
    crazy_triple_join_lock,
    /**
     * 疯狂triple 正在进行的游戏id
     */
    crazy_triple_cur_game_id,
    /**
     * 疯狂triple 游戏信息
     */
    crazy_triple_game_info,
    /**
     * 疯狂triple 预生成的奖品池子
     */
    crazy_triple_game_prize_pool,

    /**
     * 疯狂triple 游戏历史数据（结果历史和top榜单用户信息)
     */
    crazy_triple_game_history_data,

    /**
     * 疯狂triple 游戏参与金额(一局游戏分产品分用户)
     */
    crazy_triple_join_amount,

    /**
     * 疯狂triple 游戏参与金额(一局游戏分产品所有用户总和)
     */
    crazy_triple_join_amount_total,

    /**
     * 疯狂triple 参与金币消耗榜单
     */
    crazy_triple_rank_join,
    /**
     * 疯狂triple 截止上一局的日榜top3
     */
    crazy_triple_rank_join_top3,

    /**
     * 疯狂triple 金币奖励榜单
     */
    crazy_triple_rank_reward,
    /**
     * 疯狂triple 游戏参与记录(用于推送，推送完清除)
     */
    crazy_triple_join_list,
    /**
     * 疯狂triple 游戏参与记录top3(用于推送，推送完清除)
     */
    crazy_triple_join_list_top3,

    /**
     * 疯狂triple 游戏投注记录(用于查询)
     */
    crazy_triple_bet_list,

    /**
     * 疯狂triple 每隔一段时间 全服用户下注事件 lock
     */
    crazy_triple_push_all_user_bet_lock,

    /**
     * 疯狂triple 每隔一段时间 top3用户下注事件 lock
     */
    crazy_triple_push_top3_user_bet_lock,


    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }


    public static String crazyTripleStartGameLock() {
        return crazy_triple_start_game_lock.getKey();
    }

    public static String crazyTripleStartNextGameLock() {
        return crazy_triple_start_next_game_lock.getKey();
    }

    public static String crazyTripleToLotteryLock(long gameId) {
        return crazy_triple_to_lottery_lock.getKey(gameId);
    }

    public static String crazyTripleJoinLock(long userId) {
        return crazy_triple_join_lock.getKey(userId);
    }


    public static String crazyTripleCurGameId() {
        return crazy_triple_cur_game_id.getKey();
    }

    public static String crazyTripleGameInfo(long gameId) {
        return crazy_triple_game_info.getKey(gameId);
    }

    public static String crazyTripleGamePrizePool(int drawType) {
        return crazy_triple_game_prize_pool.getKey(drawType);
    }

    public static String crazyTripleGameHistoryData(String lang) {
        return crazy_triple_game_history_data.getKey(lang);
    }

    public static String crazyTripleJoinAmount(long gameId, int productType, long userId) {
        return crazy_triple_join_amount.getKey(StrUtil.format("{}_{}_{}", gameId, productType, userId));
    }

    public static String crazyTripleJoinAmountTotal(long gameId, int productType) {
        return crazy_triple_join_amount_total.getKey(StrUtil.format("{}_{}", gameId, productType));
    }

    public static String crazyTripleRankJoin() {
        return crazy_triple_rank_join.getKey();
    }

    public static String crazyTripleRankJoinTop3() {
        return crazy_triple_rank_join_top3.getKey();
    }

    public static String crazyTripleRankReward() {
        return crazy_triple_rank_reward.getKey();
    }


    public static String crazyTripleJoinList(long gameId) {
        return crazy_triple_join_list.getKey(gameId);
    }

    public static String crazyTripleJoinListTop3(long gameId) {
        return crazy_triple_join_list_top3.getKey(gameId);
    }

    public static String crazyTripleBetList(long gameId) {
        return crazy_triple_bet_list.getKey(gameId);
    }

    public static String crazyTriplePushAllUserBetLock() {
        return crazy_triple_push_all_user_bet_lock.getKey();
    }

    public static String crazyTriplePushTop3UserBetLock() {
        return crazy_triple_push_top3_user_bet_lock.getKey();
    }
}
