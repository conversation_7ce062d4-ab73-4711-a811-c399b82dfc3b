package com.simi.constant;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum BillDetailEnum {

    BILL_DETAIL_NONE(0,"占位", "", ""),
    EXCHANGE_INCOME(1,"钻石兑换金币收入", "Exchange to coins", "الصرف إلى عملات معدنية"),
    EXCHANGE_EXPENSE(2,"钻石兑换金币支出", "Exchange to coins", "الصرف إلى عملات معدنية"),
    SENDING_GIFT_IN_ROOM(3,"房间送礼", "Sending gifts", "إرسال الهدايا"),
    SENDING_GIFT_BY_CHAT(4,"私聊送礼", "Sending gifts", "إرسال الهدايا"),
    CMS_REWARD_COIN(5,"CMS奖励金币", "Official rewards", "المكافآت الرسمية"),
    CMS_REWARD_DIAMOND(6,"CMS奖励钻石", "Official rewards", "المكافآت الرسمية"),
    REWARD_PACK_COIN(7,"奖励包奖励金币", "Reward package", "حزمة المكافآت"),
    REWARD_PACK_DIAMOND(8,"奖励包奖励钻石", "Reward package", "حزمة المكافآت"),
    OFFICIAL_DEDUCT_COIN(9,"官方扣除金币", "Official Deduction", "الخصم الإدارة"),
    OFFICIAL_DEDUCT_DIAMOND(10,"官方扣除钻石", "Official Deduction", "الخصم الإدارة"),
    CMS_DEDUCT_COIN(11,"CMS扣除金币", "Official Deduction", "الخصم الإدارة"),
    CMS_DEDUCT_DIAMOND(12,"CMS扣除钻石", "Official Deduction", "الخصم الإدارة"),
    CMS_TOP_UP_REFUND(13,"CMS充值补单", "Recharge", "اعادة الشحن"),
    APPLE_STORE_TOP_UP(14,"Apple Store充值", "Recharge", "اعادة الشحن"),
    GOOGLE_PAY_TOP_UP(15,"Google pay充值", "Recharge", "اعادة الشحن"),
    APPLE_STORE_REFUND(16,"Apple Store退款", "Refund", "استرداد الأموال"),
    GOOGLE_PAY_REFUND(17,"Google pay退款", "Refund", "استرداد الأموال"),
    GIFT_COMMISSION_COIN(18,"礼物分成金币", "Received gifts", "الهدايا المستلمة"),
    SENDING_GIFT_BY_POST(19,"动态送礼", "Sending gifts", "إرسال الهديا"),
    PURCHASE_COSTUME_TO_SELF(21,"自购装扮", "Purchased {}", "تم شراؤها {}"),
    PURCHASE_COSTUME_TO_OTHER(22,"赠送装扮", "Sent {}", "مرسلة {}"),
    COIN_DEALER_FOR_GOLD(23,"金票兑换金币收入","Receive coins from Recharge Agent","حصل على كوينز من وكيل الشحن"),
    USD_FOR_COIN_DEALER(24,"美元兑换金票收入","transfer Usd to Recharge Agent","تحويل الدولار إلى وكيل الشحن"),
    DIAMONDS_FOR_DOLLARS(25,"钻石兑换美金收入","Diamonds to US Dollars","تبادل الماس بالدولار"),
    DIAMONDS_FOR_DOLLAR(26,"钻石兑换美金收入（代理）","diamonds for dollar","الماس مقابل الدولار"),
    DIAMONDS_FOR_DOLLARS_DEDUCT(27,"钻石兑换美金支出","Diamonds exchange for US Dollars","تبادل الدولار بالماس"),
    DOLLAR_FOR_GOLD_EXPEND(28,"美金兑换金币支出","US Dollars Exchange Coins","تبادل الكوينز بالدولار"),
    DOLLAR_FOR_GOLD_INCOME(29,"美金兑换金币收入","US Dollars Exchange Coins","تبادل الدولار بالكوينز"),
    CMS_REWARD_USD(30,"CMS奖励美金", "Official rewards", "المكافآت الرسمية"),
    CMS_DEDUCT_USD(31,"CMS扣除美金", "Official Deduction", "الخصم الرسمي"),

    REWARD_PACK_USD(32,"奖励包奖励美金", "Reward package", "حزمة المكافآت"),
    CMS_REWARD_GOLDEN_TICKET(33,"CMS奖励金票", "Official rewards", "المكافآت الرسمية"),
    CMS_DEDUCT_GOLDEN_TICKET(34,"CMS扣除金票", "Official Deduction", "الخصم الإدارة"),
    REWARD_PACK_GOLDEN_TICKET(35,"奖励包奖励金票", "Reward package", "باقة المكافآت"),
    PLATFORM_INCREASE_GOLDEN_TICKET(36,"后台增加金票", "Recharge from the official", "شحن من الإدارة"),
    PLATFORM_DEDUCTION_GOLDEN_TICKET(37,"后台扣减金票", "platform deduction golden ticket", "التذكرة الذهبية للخصم من المنصة"),
    COIN_DEALER_FOR_COIN_DEALER(38,"一级币商转赠二级币商", "Transferring coins to distributors", "تحويل التذكرة إلى الفرعي"),
    USER_FOR_COIN_DEALER(39,"用户转赠币商", "Transfer from user", "تحويل من المستخدم"),
    COIN_DEALER_FOR_USER(40,"币商转赠用户", "Send Coins to user", "تحويل الكوينز الي للمستخدم"),
    BULLET_CHAT_DEDUCT_GOLD(41,"发送弹幕扣除金币","Send barrage","إرسال بث الإذاعي"),
    USD_WITHDRAW_SPONSOR(42,"美元提现发起","Withdraw through the official","سحب الأموال من الإدارة"),
    USD_WITHDRAW_LOSE(43,"美元提现失败","Withdrawal rejected","تم رفض الانسحاب"),
    GIFT_COMMISSION_DIAMOND(44,"用户收到礼物转化为钻石","gift commission diamond","الهدايا المستلمة"),
    JUNIOR_COIN_DEALER_HARVEST(45,"二级币商收到一级币商的转赠", "Received gold ticket from recharge agent", "استلمت التذكرة الذهبية من وكيل إعادة الشحن"),
    SENDING_GIFT_IN_ROOM_ALL_MIC(46,"房间送礼-全麦", "Send gifts to all mic (x{})", "إرسال الهدايا إلى جميع الميكروفونات (x{})"),
    SENDING_GIFT_IN_ROOM_ALL_ROOM(47,"房间送礼-全房", "Send gifts to all room(x{})", "إرسال الهدايا إلى جميع الغرف(x{})"),
    FRUIT_PARTY_DEDUCT_GOLD(48,"水果派对消耗金币","fruit party","حفلة فواكه"),
    FRUIT_PARTY_RECEIVE_AWARD(49,"水果派对获得奖励","Fruit machine award","حفلة فواكه"),
    OFFICIAL_WEBSITE_CHARGE_COIN(50,"官网payerMax充值获得金币","Recharge", "اعادة الشحن"),
    OFFICIAL_WEBSITE_CHARGE_GOLDEN_TICKET(51,"官网payerMax充值获得金票","Recharge", "اعادة الشحن"),
    GAME_DEDUCT_COIN_CRAZY_TRIPLE(52,"疯狂Triple消耗金币","Crazy Triple","ثلاثي مجنون"),
    GAME_REWARD_COIN_CRAZY_TRIPLE(53,"疯狂Triple奖励金币","Crazy Triple","ثلاثي مجنون"),
    PURCHASE_PROP_FOR_ONESELF(54,"自购装扮","Purchase","شراء"),
    PURCHASE_PROP_FOR_OTHERS(55,"赠送装扮","Sending","إرسال"),
    LUCK_777_DEDUCT_GOLD(56,"幸运777下注消耗金币", "luck 777", "luck 777"),
    LUCK_777_RECEIVE_AWARD(57,"幸运777下注获得金币", "luck 777", "luck 777"),
    /**
     * ARISTOCRACY
     */
    ACTIVATE_ARISTOCRACY(60,"购买贵族","Buy aristocracy","شراء الأرستقراطية"),
    /**
     * 贵族回退金币
     */
    ARISTOCRACY_REWARD_GOLDEN_TICKET(61,"回退金币","Return coins","إرجاع الكوينز"),

    ROOM_PARTY_REWARD_GOLD(65,"房间party奖励金币","Room Party Reward Coin ","غرفة الطرف مكافأة"),
    /**
     * 贵族签到
     */
    ARISTOCRACY_CHECK_IN(66,"贵族-每日任务签到","Aristocracy check in","إرجاع الكوينز"),
    /**
     * 购买VIP
     */
    ARISTOCRACY_BUY_VIP(70,"购买VIP","Buy VIP","شراء الأرستقراطية"),

    LUDO_GAME_DEDUCT_COIN(67,"ludo游戏扣除金币","ludo","ludo"),

    BA_LOOT_GAME_DEDUCT_COIN(68,"baLoot游戏扣除金币","baLoot","baLoot"),

    UMO_GAME_DEDUCT_COIN(69,"UMO游戏扣除金币","UMO","UMO"),

    LUDO_GAME_INCREASE_COIN(71,"ludo游戏增加金币","ludo","ludo"),

    BA_LOOT_GAME_INCREASE_COIN(72,"baLoot游戏增加金币","baLoot","baLoot"),

    UMO_GAME_INCREASE_COIN(73,"UMO游戏增加金币","UMO","UMO"),

    PAYDAPAY_RECHARGE_COIN(75,"pandaPay充值金币","Recharge", "اعادة الشحن"),
    PAYDAPAY_RECHARGE_GOLDEN_TICKET(76,"pandaPay充值金票","Recharge", "اعادة الشحن"),
    PAYDAPAY_REFUND(77,"pandaPay退款","Official Deduction", "الخصم الإدارة"),

    THREE_CARD_BRAG_DEDUCT_GOLD(78,"THREE_CARD_BRAG_下注消耗金币", "King Battle", "King Battle"),
    THREE_CARD_BRAG_RECEIVE_AWARD(79,"THREE_CARD_BRAG_下注获得金币", "King Battle", "King Battle"),

    SEND_REDPACKET_DEDUCT_COIN(80, "发红包扣除金币", "Lucky Box deduct coin", "صندوق الحظ يخصم الكوينز"),
    REDPACKET_REFUND_INCREASE_COIN(81, "红包到期退款", "Lucky Box refund", "استرداد أموال صندوق الحظ"),
    GRAB_REDPACKET_INCREASE_COIN(82, "抢红包", "Grab Lucky Box", "احصل على صندوق الحظ"),

    LUCKY_WHEEL_DEDUCT_COIN(85,"幸运转盘扣减金币","Lucky Wheel" ,"عجلة الحظ"),
    LUCKY_WHEEL_REFUND_COIN(86,"幸运转盘退金币","Lucky Wheel","عجلة الحظ"),
    LUCKY_WHEEL_WIN_COIN(87,"幸运转盘赢金币","Lucky Wheel","عجلة الحظ"),

    HUAWEI_RECHARGE_COIN(90,"华为充值金币","Recharge", "اعادة الشحن"),
    HUAWEI_RECHARGE_GOLDEN_TICKET(91,"华为充值金票","Recharge", "اعادة الشحن"),
    HUAWEI_REFUND(92,"华为退款","Official Deduction", "الخصم الإدارة"),
    //VIP 花金币解封
    VIP_UNBLOCK(93,"VIP花金币解封","Unblock VIP by spending gold coins ", "قم بإلغاء حظر VIP عن طريق إنفاق العملات الذهبية موضح "),

    PAYER_MAX_REFUND(94,"payerMax 退款", "Refund","استرداد الأموال")
    ;

    final Integer number;

    final String desc;

    final String enDesc;

    final String arDesc;


    public static BillDetailEnum getByNum(Integer number){
        return Stream.of(BillDetailEnum.values()).filter(e -> Objects.equals(e.getNumber(), number)).findAny().orElse(null);
    }

    public static String getDescByNum(Integer number, String lang) {
        BillDetailEnum billDetailEnum = getByNum(number);
        if (Objects.isNull(billDetailEnum)) {
            return StrUtil.EMPTY;
        }
        if (StrUtil.endWithIgnoreCase(lang, "en")) {
            return billDetailEnum.getEnDesc();
        } else if (StrUtil.endWithIgnoreCase(lang, "ar")) {
            return billDetailEnum.getArDesc();
        } else {
            return StrUtil.EMPTY;
        }
    }
}
