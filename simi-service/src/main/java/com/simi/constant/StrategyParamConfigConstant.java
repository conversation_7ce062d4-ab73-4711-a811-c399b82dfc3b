package com.simi.constant;

public class StrategyParamConfigConstant {

    //钻石兑美金比例原价
    public static final String DIAMONDS_FOR_DOLLARS_ORIGINAL = "diamonds_for_dollars_original";

    //钻石兑美金比例现价
    public static final String DIAMONDS_FOR_DOLLARS_CURRENT = "diamonds_for_dollars_current";

    //金币兑换美金比例现价
    public static final String GOLD_FOR_DOLLAR_ORIGINAL = "gold_for_dollar_original";

    //金币兑换美金比例现价
    public static final String GOLD_FOR_DOLLAR_CURRENT = "gold_for_dollar_current";

    //美金兑换金币比例
    public static final String DOLLAR_FOR_GOLD = "dollar_for_gold";

    //美金兑换金票比例
    public static final String DOLLAR_FOR_TICKET = "dollar_for_ticket";

    //金票兑换金币比例
    public static final String COIN_DEALER_FOR_GOLD = "coin_dealer_for_gold";

    //金币兑换金票比例
    public static final String GOLD_FOR_COIN_DEALER = "gold_for_coin_dealer";

    //美金兑换金币最小可兑换数
    public static final String USD_FOR_GOLD_MIN = "usd_for_gold_min";

    //钻石兑换美金最小值
    public static final String DIAMONDS_FOR_USD_MIN = "diamonds_for_usd_min";

    //平台兑换最小魅力等级
    public static final String PLATFORM_TRANSFERS_CHARM_LEVEL_MIN = "platform_transfers_charm_level_min";

    //转赠给币商的最小魅力等级
    public static final String TO_COIN_DEALER_CHARM_LEVEL_MIN = "to_coin_dealer_charm_level_min";

    //转赠给币商最小美金余额
    public static final String TO_COIN_DEALER_USD_MIN = "to_coin_dealer_usd_min";

    //平台兑换最小美金余额
    public static final String PLATFORM_TRANSFERS_USD_MIN = "platform_transfers_usd_min";


}
