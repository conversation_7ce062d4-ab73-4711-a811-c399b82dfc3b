package com.simi.constant;


import com.google.common.collect.Lists;

import java.util.List;

/**
 * <AUTHOR>
 * @Date: 2023/11/9
 */
public class RoomConstant {

    public static class RoomType{
        public final static byte KTV_TYPE = 1;  //KTV房间
    }

    /**
     * 房间配置，在缓存中以map结构存储
     */
    public static class RoomConfKey{
        /**
         * 房间语言，string类型
         */
        public final static String APP_LANGUAGE = "app_language";
        /**
         * 上麦限制，int类型
         */
        public final static String MIC_UP_TYPE = "mic_up_type";
        /**
         * 一键禁麦，boolean类型
         */
        public final static String IS_ALL_MUTE = "is_all_mute";
        /**
         * 一键禁麦类型，int类型
         */
        public final static String ALL_MUTE_TYPE = "all_mute_type";
    }

    // 房间舞台演唱间歇时长列表
    public static final List<Integer> INTERLUDE_TIME_LIST = Lists.newArrayList(0, 30000, 60000, 120000);

    public static final int NORMAL_ROOM_MIC_COUNT = 20;
}
