package com.simi.constant;

import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 举报枚举
 */
@Getter
@AllArgsConstructor
public enum ReportTypeEnum {

    /**
     * 举报房间
     */
    REPORT_TYPE_ROOM(1),

    /**
     * 举报用户
     */
    REPORT_TYPE_USER(2),

    ;
    final Integer type;

    public static ReportTypeEnum getByType(Integer type) {
        return Stream.of(ReportTypeEnum.values()).filter(e -> Objects.equals(e.getType(), type))
                .findAny().orElseThrow(() -> new ApiException(CodeEnum.PARAM_ILLEGAL));
    }
}
