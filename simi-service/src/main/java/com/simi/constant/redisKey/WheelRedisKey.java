package com.simi.constant.redisKey;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum WheelRedisKey implements BaseRedisKey {
    /**
     * 创建转盘
     */
    create_wheel,

    /**
     * 修改转盘数据
     */
    modify_wheel_data,

    /**
     * 操作转盘状态
     */
    handle_lucky_wheel_status,

    /**
     * 加入转盘
     */
    join_lucky_wheel,

    /**
     * 转盘详情
     */
    lucky_wheel_detail,
    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }

}
