package com.simi.constant.redisKey;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RoomWelcomeContentRedisKey implements BaseRedisKey {

    /**
     * 进房公告
     */
    room_welcome_content_list,
    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
