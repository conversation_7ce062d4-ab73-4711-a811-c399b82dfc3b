package com.simi.constant.redisKey;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RedPacketRedisKey implements BaseRedisKey {

    /**
     * 红包余量
     */
    red_packet_remain,
    /**
     * 红包
     */
    red_packet,
    /**
     * 锁
     */
    red_packet_push_lock,
    red_packet_grab_lock,
    /**
     * 记录
     */
    red_packet_record,
    /**
     * 消费锁
     */
    red_packet_mq_msg_lock;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
