package com.simi.constant.redisKey;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MedalRedisKey implements BaseRedisKey {

    medal_info,

    /**
     * 用户佩戴的勋章
     */
    user_wear_medal,

    /**
     * 用户拥有的勋章
     */
    user_hava_medal,

    /**
     * 任务进度
     */
    medal_task_rate,

    give_medal_lock,

    /**
     * 勋章任务头像框统计flag
     */
    medal_avatar_frame_census_flag,

    medal_notify_lock,
    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }

}
