package com.simi.constant.redisKey;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AgentRedisKey implements BaseRedisKey {

    anchor_income,  //主播收益
    agent_income,   //代理收益

    /**
     * 代理邀请验证码
     */
    generate_invite_code_to_,
    generate_invite_code_fo_,

    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
