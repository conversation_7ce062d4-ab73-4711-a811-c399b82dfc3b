package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SmsChannelEnum {

    SMS(1,"sms"),
    Whatsapp(2,"Whatsapp"),
    TapsCloud(3,"TapsCloud"),

    ;

    private Integer number;

    private String desc;

    public static String getByNumber(Integer number){
        for (SmsChannelEnum value : values()) {
            if (value.getNumber().equals(number)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
