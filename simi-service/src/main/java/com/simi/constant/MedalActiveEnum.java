package com.simi.constant;

import com.simi.config.MedalConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MedalActiveEnum {

    active_Lv01(1, MedalConfig.active_Lv01),
    active_Lv02(2, MedalConfig.active_Lv02),
    active_Lv03(3, MedalConfig.active_Lv03),
    active_Lv04(4, MedalConfig.active_Lv04),
    active_Lv05(5, MedalConfig.active_Lv05),
    active_Lv06(6, MedalConfig.active_Lv06),
    active_Lv07(7, MedalConfig.active_Lv07),
    active_Lv08(8, MedalConfig.active_Lv08),
    active_Lv09(9, MedalConfig.active_Lv09),
    active_Lv10(10, MedalConfig.active_Lv10),
    ;

    private Integer number;

    private String desc;

    public static String getDesc(Integer number){
        for (MedalActiveEnum value : values()) {
            if (value.getNumber().equals(number)){
                return value.getDesc();
            }
        }
        return "";
    }
}
