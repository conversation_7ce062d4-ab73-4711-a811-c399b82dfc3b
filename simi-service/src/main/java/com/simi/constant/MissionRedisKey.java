package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;


@Getter
@AllArgsConstructor
public enum MissionRedisKey implements BaseRedisKey {

    /**
     * 任务每日签到
     */
    daily_sign,

    daily_sign_lock,

    mission_complete_message_lock,

    /**
     * 任务状态
     */
    user_mission_status,

    /**
     * 任务消费mq状态
     */
    mission_complete_mq_status,

    /**
     * 用户日常任务
     */
    user_daily_mission,

    /**
     * 任务完成锁
     */
    mission_complete_lock,

    /**
     * 用户关注任务记录
     */
    user_follow_historical,

    /**
     * 水果有戏任务统计
     */
    user_fruit_party_game_win_mission,

    /**
     * 用户进房任务记录
     */
    user_stay_room_mission
    ;

    public static String userDailyMissionKey(Long uid) {
        return user_daily_mission.getKey(uid);
    }

    public static String missionCompleteLockKey(final long uid, final int missionId){
        return mission_complete_lock.getKey(uid, missionId);
    }

    @Override
    public String getName() {
        return this.name();
    }

}
