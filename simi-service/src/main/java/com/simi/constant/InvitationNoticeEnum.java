package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum InvitationNoticeEnum {

    COMMENT(1, "comment"),
    INVITATION_REPLY(2, "invitation_reply"),
    WHAT_IS_UP(3, "what_is_up"),
    INVITATION_LIKE(4,"invitation_like"),
    ;

    private final int drawType;
    private final String name;


    public static String getValueByType(int drawType){
        for (InvitationNoticeEnum value : values()) {
            if (value.drawType == drawType) {
                return value.getName();
            }
        }
        return null;
    }
}
