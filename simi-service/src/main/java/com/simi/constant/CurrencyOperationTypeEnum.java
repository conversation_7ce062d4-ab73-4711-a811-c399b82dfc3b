package com.simi.constant;

import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum CurrencyOperationTypeEnum {

    /**
     * CMS充值补单
     */
    CMS_TOP_UP_REFUND(CurrencyOperationEnum.CMS_TOP_UP_FUND, BillEnum.CMS_TOP_UP_REFUND),
    /**
     * CMS奖励金币
     */
    CMS_REWARD_COIN(CurrencyOperationEnum.CMS_REWARD_COIN, BillEnum.CMS_REWARD_COIN),
    /**
     * CMS奖励钻石
     */
    CMS_REWARD_DIAMOND(CurrencyOperationEnum.CMS_REWARD_DIAMOND, BillEnum.CMS_REWARD_DIAMOND),
    /**
     * CMS扣除金币
     */
    CMS_DEDUCT_COIN(CurrencyOperationEnum.CMS_DEDUCT_COIN, BillEnum.CMS_DEDUCT_COIN),
    /**
     * CMS扣除钻石
     */
    CMS_DEDUCT_DIAMOND(CurrencyOperationEnum.CMS_DEDUCT_DIAMOND, BillEnum.CMS_DEDUCT_DIAMOND),
    /**
     * CMS奖励美元
     */
    CMS_REWARD_USD(CurrencyOperationEnum.CMS_REWARD_USD, BillEnum.CMS_REWARD_USD),
    /**
     * CMS扣除美元
     */
    CMS_DEDUCT_USD(CurrencyOperationEnum.CMS_DEDUCT_USD, BillEnum.CMS_DEDUCT_USD),
    /**
     * CMS奖励金票
     */
    CMS_REWARD_GOLDEN_TICKET(CurrencyOperationEnum.CMS_REWARD_GOLDEN_TICKET, BillEnum.CMS_REWARD_GOLDEN_TICKET),
    /**
     * CMS扣除金票
     */
    CMS_DEDUCT_GOLDEN_TICKET(CurrencyOperationEnum.CMS_DEDUCT_GOLDEN_TICKET, BillEnum.CMS_DEDUCT_GOLDEN_TICKET),
    ;

    private final CurrencyOperationEnum currencyOperation;
    private final BillEnum billEnum;

    public static BillEnum getByOperation(CurrencyOperationEnum operation){
        return Stream.of(CurrencyOperationTypeEnum.values()).filter(e -> e.currencyOperation == operation).findAny().orElseThrow(()->new ApiException(CodeEnum.PARAM_ILLEGAL)).billEnum;
    }
}
