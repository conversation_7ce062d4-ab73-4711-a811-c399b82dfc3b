package com.simi.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/29 21:01
 **/
@Getter
@AllArgsConstructor
public enum RoomPartyMsgEventEnum {

    BEGIN_SOON_NOTIFY(1),
    HAS_BEGUN_NOTIFY(2),
    END_NOTIFY(3),
    IMAGE_VIOLATION(4),
    ;
    final Integer event;

    public static RoomPartyMsgEventEnum getByEvent(Integer event) {
        return Arrays.stream(RoomPartyMsgEventEnum.values())
                .filter(e -> Objects.equals(e.event, event))
                .findAny().orElse(null);
    }
}
