package com.simi.constant;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 房间 redis key
 *
 * <AUTHOR>
 * @Date: 2023/11/8
 */
@Getter
@AllArgsConstructor
public enum RoomRedisKey implements BaseRedisKey {

    /**
     * 房间更新
     */
    room_update_lock,
    /**
     * 房间信息更新锁
     */
    room_update_info_lock,

    /**
     * 房间配置map，对应key在RoomConfKey
     */
    room_conf,
    room,
    room_wall,
    room_uid_id,  //房间uid-id关系
    room_top_100, //房间前100

    room_in_uid_id,//房间公告提示

    /**
     * 房间观众锁
     */
    room_audience_lock,
    room_audience_update_lock,
    room_audience,  //房间观众
    // 房间行为
    room_action,

    room_user_in,  //用户所在房间
    room_user_in_time,  //用户进房时间
    room_in_live,  //直播中房间

    /**
     * 麦位列表
     */
    room_mic_time,
    room_mic_list,
    room_mic_invite_time,  //邀请间隔
    room_mic_lock,
    room_mic_user_lock,  //麦位用户间隔
    room_mic_user_limit,  //麦位用户间隔
    room_mic_mute,

    /**
     * 麦位申请列表
     */
    room_mic_apply_list,
    room_mic_apply_list_lock,

    room_manager,  //房间管理员
    room_manager_update_lock,

    room_user_conf,  //房间内用户配置

    /**
     * 数据埋点-每日有效房间
     */
    event_tracking_day_room_effective,
    event_tracking_day_room_viewer_peak,
    /**
     * 房间历史记录
     */
    room_history,
    /**
     * 首页推荐房
     */
    home_room,
    /**
     * 房间上麦人数统计
     */
    up_mic_census,
    /**
     * 房间公屏发言统计
     */
    speech_census,
    /**
     * 快速进房
     */
    room_quick,
    room_send_gift_num,

    /**
     * 当天的房间管理员
     */
    room_manager_day,
    /**
     * 当周的房间管理员
     */
    room_manager_week,

    //有效房间，麦位上有人
    room_effective,

    /**
     * meilisearch 房间信息更新时间戳
     */
    meilisearch_room_info_update_flag,

    /**
     * 房间送礼横幅
     */
    room_gift_send_banner,

    /**
     * 房间发送消息
     */
    room_send_msg,

    /**
     * 房间发送消息间隔
     */
    room_send_interval,

    /**
     * 房间黑名单列表
     */
    room_blacklist,

    /**
     * 房间禁言列表
     */
    room_silence_list,
    /**
     * 房间禁言消息状态
     */
    room_silence_message_status,
    room_silence_expire_lock,
    /**
     * 房间 自定义背景使用状态
     */
    room_custom_background_status,

    /**
     * 房间公屏数量
     */
    room_screen_count,

    /**
     * 进行中房间批次id
     */
    room_running_batch_id,

    /**
     * 直播过的房间
     */
    room_batch_id_list,

    /**
     * 处理过的房间集合
     */
    handle_room_batch_id_list,

    /**
     * 房间开播时间
     */
    room_run_time,

    /**
     * 房间收礼值
     */
    room_batch_id_receive_coin,

    /**
     * 房间送礼值
     */
    room_batch_id_send_coin,

    /**
     * 房间收礼uv
     */
    room_batch_id_receive_uv_list,

    /**
     * 房间收礼pv
     */
    room_batch_id_receive_pv,

    /**
     * 收礼送礼去重，用于一次送多个人的情况
     */
    room_batch_id_remove_duplicates,

    /**
     * 房间送礼uv
     */
    room_batch_id_send_uv_list,

    /**
     * 房间送礼pv
     */
    room_batch_id_send_pv,

    /**
     * 最大房间数量
     */
    room_batch_id_max_audience,

    room_mic_charm,

    room_send_mic_charm,

    room_mic_charm_reveal,

    send_room,//发生送礼的房间

    broadcast_cast_time,//房间开播时间戳

    broadcast_validity,

    room_rank_pool,

    room_score_details,

    room_friends_are_play,

    rome_owner_maintenance_lobby,

    game_all_win_list,

    ;

    @Override
    public String getName() {
        return this.name();
    }

    @Override
    public String getKey(Object... args) {
        return BaseRedisKey.super.getKey(args);
    }
}
