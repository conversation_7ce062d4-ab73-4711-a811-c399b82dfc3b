package com.simi.service.temporary;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.InvitationExamineEnum;
import com.simi.common.constant.YesOrNoEnums;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.util.TimeUtils;
import com.simi.dto.CommentCacheDTO;
import com.simi.dto.CommentResp;
import com.simi.dto.MapInvitationDTO;
import com.simi.entity.temporary.MapInvitation2;
import com.simi.entity.user.User;
import com.simi.mapper.MapInvitation2Mapper;
import com.simi.service.user.UserServerService;
import io.micrometer.common.util.StringUtils;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MapInvitation2Service extends ServiceImpl<MapInvitation2Mapper, MapInvitation2> {


    private final MapInvitation2Mapper MapInvitation2Mapper;

    public void addInvitation(MapInvitation2 MapInvitation2) {
        save(MapInvitation2);
    }

    public ListWithTotal<MapInvitationDTO> getList(Integer pageSize, Integer pageNum, List<Long> uids, List<Long> ids) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MapInvitation2::getStatus, 1);
        if (CollectionUtils.isNotEmpty(uids)) {
            wrapper.notIn(MapInvitation2::getUid, uids);
        }
        if (CollectionUtils.isNotEmpty(ids)) {
            wrapper.notIn(MapInvitation2::getId, ids);
        }
        wrapper.eq(MapInvitation2::getExamine, InvitationExamineEnum.APPROVED.getType());
        wrapper.orderByDesc(MapInvitation2::getCreateTime);
        PageHelper.startPage(pageNum, pageSize);
        List<MapInvitation2> list = list(wrapper);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }


    public ListWithTotal<MapInvitationDTO> getMapList(Double longitude, Double latitude, Integer pageSize, Integer pageNum, List<Long> uids, List<Long> ids) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        String uidStr = uids.stream().map(String::valueOf).collect(Collectors.joining(","));
        String idStr = ids.stream().map(String::valueOf).collect(Collectors.joining(","));
        if (StringUtils.isBlank(uidStr)) {
            uidStr = null;
        }
        if (StringUtils.isBlank(idStr)) {
            idStr = null;
        }
        List<MapInvitation2> list = MapInvitation2Mapper.getMapList(longitude, latitude, uidStr, idStr);
        PageHelper.startPage(pageNum, pageSize);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public List<Date> getDayList(String dayStr) {
        PageHelper.startPage(1, 31);
        List<Date> dates = MapInvitation2Mapper.getDayList(dayStr);

        // 完全重新创建日期对象，避免时区问题
        if (dates != null && !dates.isEmpty()) {
            List<Date> correctedDates = new ArrayList<>(dates.size());

            for (Date date : dates) {
                // 从原始日期中提取年、月、日
                Calendar cal = Calendar.getInstance();
                cal.setTime(date);
                int year = cal.get(Calendar.YEAR);
                int month = cal.get(Calendar.MONTH);
                int day = cal.get(Calendar.DAY_OF_MONTH);

                // 创建新的日期对象，使用当前时区
                Calendar newCal = Calendar.getInstance();
                newCal.set(year, month, day, 0, 0, 0); // 设置为当天的8点，确保显示为当天
                newCal.set(Calendar.MILLISECOND, 0);

                correctedDates.add(newCal.getTime());
            }

            return correctedDates;
        }

        return dates;
    }

    public ListWithTotal<MapInvitationDTO> getCollect(List<Long> ids, Integer page, Integer size) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MapInvitation2::getId, ids);
        wrapper.orderByDesc(MapInvitation2::getCreateTime);
        List<MapInvitation2> list = list(wrapper);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public ListWithTotal<MapInvitationDTO> getLabel(Integer label, Integer page, Integer size) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MapInvitation2::getLabel, label);
        wrapper.orderByDesc(MapInvitation2::getCreateTime);
        List<MapInvitation2> list = list(wrapper);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }


    public ListWithTotal<MapInvitationDTO> getDayList(Date day, Integer pageNum, Integer pageSize) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        if (day != null) {
            Date firstSecond = TimeUtils.getFirstSecond(day);
            Date lastSecond = TimeUtils.getLastSecond(day);
            wrapper.ge(MapInvitation2::getCreateTime, firstSecond);
            wrapper.le(MapInvitation2::getCreateTime, lastSecond);
        }
        wrapper.eq(MapInvitation2::getLabel, 6);
        wrapper.eq(MapInvitation2::getExamine,2);
        List<MapInvitation2> list = list(wrapper);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public ListWithTotal<MapInvitationDTO> official(Integer pageNum, Integer pageSize) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MapInvitation2::getUid, 100001L);
        wrapper.eq(MapInvitation2::getLabel, 6);
        wrapper.orderByDesc(MapInvitation2::getCreateTime);
        List<MapInvitation2> list = list(wrapper);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public ListWithTotal<MapInvitationDTO> getSignUp(List<Long> ids, Integer pageNum, Integer pageSize) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        if (ids != null) {
            wrapper.in(MapInvitation2::getId, ids);
            wrapper.orderByDesc(MapInvitation2::getCreateTime);
            wrapper.eq(MapInvitation2::getLabel, 6);
            List<MapInvitation2> list = list(wrapper);
            PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
            List<MapInvitationDTO> dtos = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(list)) {
                dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
            }
            dtoListWithTotal.setTotal(pageInfo.getTotal());
            dtoListWithTotal.setList(dtos);
            return dtoListWithTotal;
        }
        return dtoListWithTotal;

    }


    public ListWithTotal<MapInvitationDTO> getInvitationList(List<Long> id, Integer pageSize, Integer pageNum) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<MapInvitation2> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MapInvitation2::getId, id);
        wrapper.orderByDesc(MapInvitation2::getCreateTime);
        List<MapInvitation2> list = list(wrapper);
        PageInfo<MapInvitation2> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public void delete(Long id) {
        removeById(id);
    }


    private final UserServerService userServerService;

    public static Map<Long, UserBaseInfoDTO> userBaseInfoDTOMap = new ConcurrentHashMap();

    public void init() {
        List<User> userList = userServerService.getUsers();
        List<Long> uids = userList.stream().map(User::getUid).toList();
        userBaseInfoDTOMap = userServerService.batchUserSummary(uids);
    }
}
