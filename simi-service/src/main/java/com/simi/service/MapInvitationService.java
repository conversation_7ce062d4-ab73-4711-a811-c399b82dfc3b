package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.BusiResult;
import com.simi.common.base.SessionUtil;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.*;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.user.UserFriendDTO;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.exception.AdminException;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.util.ClientRouteUtil;
import com.simi.constant.DigitalCurrencyEnum;
import com.simi.constant.InvitationNoticeEnum;
import com.simi.dto.InteractionDTO;
import com.simi.dto.MapInvitationDTO;
import com.simi.dto.MapInvitationReq;
import com.simi.entity.MapInvitation;
import com.simi.mapper.MapInvitationMapper;
import com.simi.mapper.expand.UserMapperExpand;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.user.UserServerService;
import com.simi.util.PushMsgUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MapInvitationService extends ServiceImpl<MapInvitationMapper, MapInvitation> {

    private final  MapInvitationMapper mapInvitationMapper;

    private final NotifyMessageComponent notifyMessageComponent;

    private final UserServerService userServerService;

    private final UserMapperExpand userMapperExpand;


    public List<MapInvitation> getListByUid(Long uid) {
        LambdaQueryWrapper<MapInvitation> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MapInvitation::getUid,uid);
        wrapper.eq(MapInvitation::getStatus,StatusEnum.normal.getStatus());
        wrapper.eq(MapInvitation::getIsDel,StatusEnum.invalid.getStatus());
        return list(wrapper);
    }

    public MapInvitation getInvitationById(Long id,Long uid) {
        PageHelper.startPage(1, 1);
        return mapInvitationMapper.getInvitationById(id, uid);
    }


    public Long addInvitation(Long currentUid, MapInvitationReq req,String country,Integer invitationType){
        //字段判空
        if (StringUtils.isBlank(req.getMaterial()) && StringUtils.isBlank(req.getContext())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        MapInvitation mapInvitation = new MapInvitation();
        mapInvitation.setMaterial(req.getMaterial());
        mapInvitation.setType(req.getType());
        mapInvitation.setLatitude(req.getLatitude());
        mapInvitation.setUid(currentUid);
        mapInvitation.setLongitude(req.getLongitude());
        mapInvitation.setCreateTime(new Date());
        mapInvitation.setAddress(req.getAddress());
        mapInvitation.setContext(req.getContext());
        mapInvitation.setTargets(req.getTargets());
        mapInvitation.setStatus(req.getStatus());
        mapInvitation.setCreateTime(new Date());
        mapInvitation.setCountry(country);
        mapInvitation.setIsDel(YesOrNoEnums.NO.getType());
        save(mapInvitation);
        if (StringUtils.isNotBlank(mapInvitation.getTargets())) {
            List<Long> uids = Arrays.stream(mapInvitation.getTargets().split(","))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .map(Long::parseLong)
                    .toList();
            for (Long uid : uids) {
                CommonIMMessage imMessageChat = new CommonIMMessage();
                imMessageChat.setType(IMMsgType.InvitationIMMsg);
                imMessageChat.setPayload(JSONUtil.toJsonStr(mapInvitation));
                imMessageChat.setTimestamp(new Date().getTime());
                String copywriting = MessageSourceUtil.i18nByCode(CopywritingEnum.INTERACTIVE_NOTIFICATION.getKey(), LanguageEnum.en);
                String copywritingAr = MessageSourceUtil.i18nByCode(CopywritingEnum.INTERACTIVE_NOTIFICATION.getKey(), LanguageEnum.ar);
                LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
                OfflinePushInfo offlinePushInfo = null;
                try {
                    String offlineTitle = Objects.equals(languageEnum, LanguageEnum.en) ? copywriting : copywritingAr;
                    offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.interactionUid),
                            offlineTitle,
                            offlineTitle,
                            null,
                            ClientRouteUtil.toPurse(DigitalCurrencyEnum.GOLDEN_TICKET.getNumber() - 1));
                } catch (Exception e) {
                    // ignore
                }
                notifyMessageComponent.publishPrivateChatMessage(imMessageChat, mapInvitation.getUid(),uid,offlinePushInfo);
                sendInvitation(mapInvitation,mapInvitation.getUid(),uid,invitationType);
            }
        }
        return mapInvitation.getId();
    }

    public ListWithTotal<MapInvitationDTO> getList(Long uid,Long targetUid,Integer pageSize,Integer pageNum,Date startTime,Date endTime,Integer status,List<Long> blockUid,List<Long>  ids) {
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        String blockStr = null;
        if (CollectionUtils.isNotEmpty(blockUid)) {
            blockStr = blockUid.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        String idStr = null;
        if (CollectionUtils.isNotEmpty(ids)) {
            idStr = ids.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            LambdaQueryWrapper<MapInvitation> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(MapInvitation::getId, ids);
            wrapper.eq(MapInvitation::getIsDel, StatusEnum.invalid.getStatus());
            remove(wrapper);
        }
        List<MapInvitation> list = mapInvitationMapper.getMapInvitationList(uid, targetUid, startTime, endTime, status, blockStr, idStr);
        PageHelper.startPage(pageNum, pageSize);
        PageInfo<MapInvitation> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }


    public ListWithTotal<MapInvitationDTO> getMapList(Double longitude,Double latitude,Integer pageSize,Integer pageNum, List<Long> blockUid,List<Long>  ids){
        String blockStr = null;
        if (CollectionUtils.isNotEmpty(blockUid)) {
            blockStr = blockUid.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
        }
        String idStr = null;
        if (CollectionUtils.isNotEmpty(ids)) {
            idStr = ids.stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            LambdaQueryWrapper<MapInvitation> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(MapInvitation::getId, ids);
            wrapper.eq(MapInvitation::getIsDel, StatusEnum.invalid.getStatus());
            remove(wrapper);
        }
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        List<MapInvitation> list = mapInvitationMapper.getMapList(longitude, latitude, blockStr, idStr);
        PageHelper.startPage(pageNum, pageSize);
        PageInfo<MapInvitation> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public ListWithTotal<MapInvitationDTO> getMeOfFriend(Double longitude,Double latitude,Integer range,Integer mapType,Long uid){
        String uids = null;
        if (mapType.equals(MapMeOrFriendType.Friend.getNumber())) {
            List<UserFriendDTO> friends = userMapperExpand.listUserFriends(uid, null);
            if (CollectionUtils.isNotEmpty(friends)) {
                List<Long> uidList = friends.stream().map(UserFriendDTO::getUid).toList();
                uids = uidList.stream().map(String::valueOf).collect(Collectors.joining(","));
            }
        }
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(1, 100);
        List<MapInvitation> list = mapInvitationMapper.getMeOfFriend(longitude, latitude,uids,uid,range);
        PageInfo<MapInvitation> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }



    public ListWithTotal<MapInvitationDTO> getInvitationList(List<Long> id,Integer pageSize,Integer pageNum){
        ListWithTotal<MapInvitationDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(pageNum, pageSize);
        LambdaQueryWrapper<MapInvitation> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MapInvitation::getId,id);
        wrapper.orderByDesc(MapInvitation::getCreateTime);
        List<MapInvitation> list = list(wrapper);
        PageInfo<MapInvitation> pageInfo = new PageInfo<>(list);
        List<MapInvitationDTO> dtos = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(list)) {
            dtos = BeanUtil.copyToList(list, MapInvitationDTO.class);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }

    public void delete(Long id){
        removeById(id);
    }

    public void sendInvitation(MapInvitation mapInvitation,Long uid,Long toUid,Integer invitationType) {
        CommonIMMessage invitation = new CommonIMMessage();
        invitation.setType(IMMsgType.InvitationNoticeMsg);
        InteractionDTO dto = new InteractionDTO();
        dto.setInteractionId(mapInvitation.getId());
        dto.setUserBaseInfoDTO(userServerService.getUserBaseInfo(uid));
        dto.setContent(mapInvitation.getContext());
        dto.setMaterial(mapInvitation.getMaterial());
        dto.setInteractionType(mapInvitation.getType());
        dto.setType(invitationType);
        dto.setCreateTime(new Date());
        LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
        String i18nByCode = MessageSourceUtil.i18nByCode(InvitationNoticeEnum.getValueByType(invitationType), languageEnum);
        dto.setContent(i18nByCode);
        invitation.setPayload(JSONUtil.toJsonStr(dto));
        invitation.setTimestamp(new Date().getTime());
        String copywriting = MessageSourceUtil.i18nByCode(CopywritingEnum.INTERACTIVE_NOTIFICATION.getKey(), LanguageEnum.en);
        String copywritingAr = MessageSourceUtil.i18nByCode(CopywritingEnum.INTERACTIVE_NOTIFICATION.getKey(), LanguageEnum.ar);
        OfflinePushInfo offlinePushInfo = null;
        try {
            String offlineTitle = Objects.equals(languageEnum, LanguageEnum.en) ? copywriting : copywritingAr;
            offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.interactionUid),
                    offlineTitle,
                    offlineTitle,
                    null,
                    ClientRouteUtil.toPurse(DigitalCurrencyEnum.GOLDEN_TICKET.getNumber() - 1));
        } catch (Exception e) {
            // ignore
        }
        notifyMessageComponent.publishInteractionMessage(invitation,toUid.toString(),offlinePushInfo);
    }

}
