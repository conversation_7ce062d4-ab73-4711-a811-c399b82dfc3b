package com.simi.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.dto.StrategyParamConfigDTO;
import com.simi.entity.StrategyParamConfig;
import com.simi.mapper.StrategyParamConfigMapper;
import com.simi.service.cache.StrategyParamConfigCache;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class StrategyParamConfigService extends ServiceImpl<StrategyParamConfigMapper, StrategyParamConfig> {

    @Autowired
    private StrategyParamConfigCache strategyParamConfigCache;

    public void saveParamConfig(StrategyParamConfigDTO dto){
        if (dto != null) {
            StrategyParamConfig config = new StrategyParamConfig();
            config.setId(dto.getId());
            config.setConfigKey(dto.getConfigKey());
            config.setConfigName(dto.getConfigName());
            config.setRemark(dto.getRemark());
            config.setNum(dto.getNum());
            saveOrUpdate(config);
            strategyParamConfigCache.setParamConfig(dto.getConfigKey(), JSONUtil.toJsonStr(config));
        }
    }


    public List<StrategyParamConfigDTO> queryList(String key){
        List<StrategyParamConfigDTO> dtos = new ArrayList<>();
        LambdaQueryWrapper<StrategyParamConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StringUtils.isNotBlank(key),StrategyParamConfig::getConfigKey, key);
        List<StrategyParamConfig> list = list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return dtos;
        }
        for (StrategyParamConfig config : list) {
            StrategyParamConfigDTO dto = new StrategyParamConfigDTO();
            dto.setId(config.getId());
            dto.setConfigKey(config.getConfigKey());
            dto.setConfigName(config.getConfigName());
            dto.setRemark(config.getRemark());
            dto.setNum(config.getNum());
            dto.setCreateTime(config.getCreateTime());
            dtos.add(dto);
        }
        return dtos;
    }

    public StrategyParamConfigDTO getByKey(String key){
        String paramConfig = strategyParamConfigCache.getParamConfig(key);
        if (StringUtils.isBlank(paramConfig)) {
            LambdaQueryWrapper<StrategyParamConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(StringUtils.isNotBlank(key),StrategyParamConfig::getConfigKey, key);
            StrategyParamConfig strategyParamConfig = getOne(wrapper);
            if (strategyParamConfig != null) {
                StrategyParamConfigDTO dto = new StrategyParamConfigDTO();
                dto.setConfigKey(strategyParamConfig.getConfigKey());
                dto.setConfigName(strategyParamConfig.getConfigName());
                dto.setRemark(strategyParamConfig.getRemark());
                dto.setNum(strategyParamConfig.getNum());
                dto.setCreateTime(strategyParamConfig.getCreateTime());
                strategyParamConfigCache.setParamConfig(key,JSONUtil.toJsonStr(dto));
                return dto;
            }
        }
        return JSONUtil.toBean(paramConfig, StrategyParamConfigDTO.class);
    }



}
