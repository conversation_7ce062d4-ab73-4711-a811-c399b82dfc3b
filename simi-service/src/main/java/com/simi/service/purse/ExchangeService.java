package com.simi.service.purse;


import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.CommonUtil;
import com.simi.common.vo.req.ExchangeReq;
import com.simi.common.vo.resp.ExchangeResp;
import com.simi.constant.RevenueRedisKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ExchangeService {

    @Autowired
    private ExchangeRecordService exchangeRecordService;
    @Autowired
    private PurseManageService purseManageService;
    @Autowired
    private RedissonDistributionLocker distributionLocker;
    @Autowired
    private BillService billService;


    /**
     * 钻石兑换金币
     * @param param
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ExchangeResp exchange(final long uid, ExchangeReq param){
        if(param.getAmount() < 1){
            log.warn("exchange amount must lager than 0,current is:{}", param.getAmount());
            throw new ApiException(CodeEnum.EXCHANGE_AMOUNT_MUST_LARGER_THAN_ZERO);
        }
        try(Locker lock = distributionLocker.lock(exchangeLockKey(uid))){
            if(Objects.isNull(lock)){
                log.error("Fail to get exchange lock.");
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            PurseDTO purse = purseManageService.getPurse(uid);
            if(param.getAmount() > purse.getDiamond()){
                log.warn("exchange amount:{}, purse diamond balance is:{}", param.getAmount(), purse.getDiamond());
                throw new ApiException(CodeEnum.EXCHANGE_AMOUNT_CANNOT_LAGER_THAN_DIAMOND_BALANCE);
            }
            // 操作钱包
            PurseDTO dto = purseManageService.diamond2Coin(uid, param.getAmount(), PurseRoleTypeEnum.USER.getType());
            log.info("handle user purse success:{}", dto);
            // 生成兑换记录
            Date now = new Date();
            String recordId = CommonUtil.genId();
            exchangeRecordService.createRecord(recordId, uid, param.getAmount(), now);
            // 生成账单
            billService.createExchangeBill(uid, recordId, param.getAmount(), dto, now);
            ExchangeResp exchangeResp = new ExchangeResp();
            exchangeResp.setCoin(dto.getCoin());
            exchangeResp.setDiamond(dto.getDiamond());
            return exchangeResp;
        } catch (Exception e) {
            if(e instanceof ApiException){
                throw (ApiException)e;
            }
            log.error("diamond exchange to coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }

    }

    /**
     * 兑换锁
     * @param uid
     * @return
     */
    private static String exchangeLockKey(final long uid){
        return RevenueRedisKey.exchange_lock.getKey(uid);
    }
}
