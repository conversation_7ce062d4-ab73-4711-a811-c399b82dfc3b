package com.simi.service.purse;

import cn.hutool.core.util.StrUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PackageCurrencyType;
import com.simi.common.constant.RechargeRecordStatusEnum;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.constant.vip.VIPConstant;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.RevenueRedisKey;
import com.simi.entity.purse.RechargeRecord;
import com.simi.entity.recharge.RechargePackage;
import com.simi.message.RechargeSuccessMessage;
import com.simi.service.EventTrackingService;
import com.simi.service.activity.handle.ActivityBaseHandler;
//import com.simi.service.coinDealer.CoinDealerService;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class PayerMAXService {

    private final RechargePackageService rechargePackageService;
    private final PurseManageService purseManageService;
    private final RedissonDistributionLocker distributionLocker;
    private final RechargeRecordService rechargeRecordService;
    //private final CoinDealerService coinDealerService;
    private final ActivityBaseHandler activityBaseHandler;
    private final TaskExecutor taskExecutor;
    private final EventTrackingService eventTrackingService;
    private final UserServerService userServerService;
    private final UserVipService userVipService;
    private final MedalTaskService medalTaskService;


    @Transactional(rollbackFor = Exception.class)
    public void handleResult(String recordId,String channelVerifyId, RechargeRecordStatusEnum statusEnum){
        try (Locker lock = distributionLocker.lock(RevenueRedisKey.rechargeResultLockKey(recordId))){

            RechargeRecord record = rechargeRecordService.getById(recordId);
            if (record != null && RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType().equals(record.getStatus())) {
                return;
            }
            if(Objects.isNull(lock)){
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            log.info("handleResult record:{}",record);
            if(Objects.isNull(record)){
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            RechargePackage aPackage = rechargePackageService.getById(record.getPackageId());
            log.info("handleResult aPackage:{}",aPackage);
            if (aPackage == null) {
                return;
            }
            if(Objects.equals(RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType(), record.getStatus())){
                log.info("Recharge {} had been success.", recordId);
                return;
            }
            if(!Objects.equals(RechargeRecordStatusEnum.RECHARGE_CREATE.getType(), record.getStatus())){
                log.info("Recharge {} status is{}, had been handled.", recordId, record.getStatus());
                return;
            }
            Date date= new Date();
            switch (statusEnum){
                case RECHARGE_SUCCESS:
                    if (StrUtil.isNotBlank(channelVerifyId)){
                        record.setChannelVerifyId(channelVerifyId);
                    }
                    record.setStatus(statusEnum.getType());
                    record.setSuccessTime(date);
                    record.setUpdateTime(date);
                    rechargeRecordService.updateById(record);
                    break;
                case RECHARGE_REFUND:
                    record.setStatus(statusEnum.getType());
                    record.setUpdateTime(date);
                    rechargeRecordService.updateById(record);
                    break;
            }
            if (statusEnum.getType().equals(RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType())) {
                log.info("handleResult record:{}",record);
                if (PackageCurrencyType.COIN.getNumber().equals(aPackage.getPlatformCurrencyType())){
                    purseManageService.addCoin(record.getUid(), record.getCoinAmount(), BillEnum.OFFICIAL_WEBSITE_CHARGE_COIN, record.getId(), "payerMax recharge", Collections.emptyMap(),record.getTargetUid());
                }
                if (PackageCurrencyType.GOLDEN_TICKET.getNumber().equals(aPackage.getPlatformCurrencyType())){
                    //coinDealerService.recharge(record.getUid(), record.getCoinAmount(), BillEnum.OFFICIAL_WEBSITE_CHARGE_GOLDEN_TICKET,record.getTargetUid());
                }
                UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(record.getUid());
                RechargeSuccessMessage message = new RechargeSuccessMessage();
                message.setRechargeRecord(record);
                message.setNewBie(userBaseInfo.getNewBie());
                eventTrackingService.handleRechargeService(message);
                // 充值活动额外处理
                taskExecutor.execute(() -> {
                    try {
                        //判断是否达到
                        purseManageService.invitationReward(record.getUid(),record.getCoinAmount());
                        activityBaseHandler.handleRechargeData(record.getUid(), aPackage);
                    } catch (Exception e) {
                        log.error("payerMax attach activity handle fail:[{}]", e.getMessage(), e);
                    }
                    userVipService.addExperience(record.getUid(), record.getCoinAmount().intValue(), VIPConstant.ExperienceSource.PAYERMAX);

                    log.info("Execute paymax medal task, uid:[{}] amount:[{}]", record.getUid(), record.getCoinAmount().intValue());
                    medalTaskService.executeMedalTask(MedalTaskEnum.RECHARGE_GOLD, record.getUid(), record.getCoinAmount().intValue());
                });
            }

        }catch (ApiException ae){
            throw ae;
        }catch (Exception e){
            log.error("Handle recharge result error:{}", ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }
}
