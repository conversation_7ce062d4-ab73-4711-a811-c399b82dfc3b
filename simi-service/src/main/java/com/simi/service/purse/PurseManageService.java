package com.simi.service.purse;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.FirstRechargeDTO;
import com.simi.common.dto.StrategyParamConfigDTO;
import com.simi.common.dto.pures.ConvertProportionDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.exception.ApiException;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.req.ManagePurse;
import com.simi.common.vo.resp.PurseResp;
import com.simi.config.UserLevelConfig;
import com.simi.constant.*;
import com.simi.entity.LevelInfo;
import com.simi.entity.purse.Purse;
import com.simi.entity.user.UserPriceConfig;
import com.simi.mapper.PurserMapperExpand;
import com.simi.message.PresentImMessage;
import com.simi.service.EventTrackingService;
import com.simi.service.StrategyParamConfigService;
import com.simi.service.UserPriceConfigService;
import com.simi.service.agent.AgentCache;
import com.simi.service.cache.RechargePackageCache;
import com.simi.service.invite.InviteServerService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.vip.UserVipService;
import com.simi.util.PushMsgUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class PurseManageService {
    private final PurseService purseService;
    private final RedissonDistributionLocker distributionLocker;
    private final PurserMapperExpand purserMapperExpand;
    private final StrategyParamConfigService strategyParamConfigService;
    private final BillService billService;
    private final RedissonManager redissonManager;
    private final SystemConfigService systemConfigService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final InviteServerService inviteServerService;
    private final UserPriceConfigService userPriceConfigService;
    private final EventTrackingService eventTrackingService;
    private final RechargePackageCache rechargePackageCache;
    private final UserVipService userVipService;
    private final AgentCache agentCache;

    /**
     * 获取用户钱包，不存在则创建
     *
     * @param uid
     * @return
     */
    public PurseResp getUserPurse(Long uid) {
        PurseDTO purseDTO = getPurse(uid);
        PurseResp resp = new PurseResp();
        resp.setUid(purseDTO.getUid());
        resp.setCoin(purseDTO.getCoin());
        resp.setDiamond(purseDTO.getDiamond());
        return resp;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void currencySwitch(Integer currencyType, BigDecimal usdNum, Long uid,String lang,String deviceid,Boolean newBie) {
        if (uid == null || uid == 0L || currencyType == null) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        if (usdNum == null || usdNum.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException(CodeEnum.INPUT_VALUE_ERROR);
        }
        if (DigitalCurrencyEnum.COIN.getNumber().equals(currencyType)) {
            StrategyParamConfigDTO configDTO = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DOLLAR_FOR_GOLD);
            long coinNum = usdNum.longValue() * configDTO.getNum();
            deductCoin(uid, coinNum);
            addUSD(uid, usdNum, BillEnum.GOLD_FOR_DOLLAR, CommonUtil.genId(), "", Collections.emptyMap(),0L);
        } else if (DigitalCurrencyEnum.DIAMOND.getNumber().equals(currencyType)) {
            StrategyParamConfigDTO diamondsForUsdMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DIAMONDS_FOR_USD_MIN);
            if (diamondsForUsdMin != null) {
                if (usdNum.compareTo(new BigDecimal(diamondsForUsdMin.getNum())) < 0) {
                    throw new ApiException(CodeEnum.MINIMUM_EXCHANGE_AMOUNT, diamondsForUsdMin.getNum());
                }
            }

            LambdaQueryWrapper<UserPriceConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserPriceConfig::getUid, uid);
            wrapper.eq(UserPriceConfig::getConfigKey, StrategyParamConfigConstant.DIAMONDS_FOR_DOLLARS_CURRENT);
            UserPriceConfig userPriceConfig = userPriceConfigService.getOne(wrapper);
            long coinNum = 0;
            if (userPriceConfig != null) {
                coinNum = usdNum.longValue() * userPriceConfig.getCurrentPrice().intValue();
            }else{
                StrategyParamConfigDTO configDTO = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DIAMONDS_FOR_DOLLARS_CURRENT);

                Optional<UserCurVipInfo> vipOptional = userVipService.getUserVipCache(uid);
                if (vipOptional.isPresent()) {
                    UserCurVipInfo userCurVipInfo = vipOptional.get();
                    int diamondsForUsd = userCurVipInfo.getPropInfo().getDiamondsForUsd();
                    if (diamondsForUsd < configDTO.getNum()) {
                        coinNum = usdNum.longValue() * diamondsForUsd;
                        log.info("vip 钻石兑换美元，使用VIP 比例，uid:{},coinNum:{},diamondsForUsd:{}", uid, coinNum, diamondsForUsd);
                    }
                }else {
                    coinNum = usdNum.longValue() * configDTO.getNum();
                }

            }
            PurseDTO purse = getPurse(uid);
            Integer diamond = purse.getDiamond();
            if (diamond < coinNum) {
                throw new ApiException(CodeEnum.MAXIMUM_EXCHANGE_VALUE);
            }
            deductDiamond(uid, coinNum, BillEnum.DIAMONDS_FOR_DOLLARS_DEDUCT, CommonUtil.genId(), "", Collections.emptyMap(),0L);
            addUSD(uid, usdNum, BillEnum.DIAMONDS_FOR_DOLLARS, CommonUtil.genId(), "", Collections.emptyMap(),0L);
            eventTrackingService.handleDiamondForUsd(uid,coinNum,usdNum.longValue(),new Date(),deviceid,newBie);
            Long superiorUid = inviteServerService.superiorInvite(uid);
            if (superiorUid != null) {
                String constant = systemConfigService.getSysConfValueById(SystemConfigConstant.INVITE_USD_SCALE);

                BigDecimal usdDivide = new BigDecimal(constant).multiply(usdNum);
                addUSD(superiorUid, usdDivide, BillEnum.GOLD_FOR_DOLLAR, CommonUtil.genId(), "", Collections.emptyMap(),uid);

                // 上级累计收益记录 (美分入库)
                Long cents = usdDivide.multiply(new BigDecimal(100)).longValue();
                inviteServerService.updateInviteAgentIncome(uid, superiorUid, cents);

                CommonIMMessage imMessage = new CommonIMMessage();
                imMessage.setType(IMMsgType.purseTransferAccountsImMsg);
                PresentImMessage presentImMessage = new PresentImMessage();
                String copywriting = MessageSourceUtil.i18nByCode(CopywritingEnum.FRIEND_SETTLEMENT_REWARDS.getKey(), LanguageEnum.en);
                String copywritingAr = MessageSourceUtil.i18nByCode(CopywritingEnum.FRIEND_SETTLEMENT_REWARDS.getKey(), LanguageEnum.ar);
                presentImMessage.setUid(uid);
                presentImMessage.setTitle(copywriting);
                presentImMessage.setTitleAr(copywritingAr);
                presentImMessage.setDigitalCurrency(DigitalCurrencyEnum.USD.getNumber());
                presentImMessage.setNum(cents);
                presentImMessage.setSendTime(new Date().getTime());
                presentImMessage.setLinkUrl(ClientRouteUtil.toHomePage(uid, Boolean.TRUE));
                imMessage.setPayload(JSONUtil.toJsonStr(presentImMessage));
                imMessage.setTimestamp(new Date().getTime());

                OfflinePushInfo offlinePushInfo = null;
                try {
                    LanguageEnum languageEnum = this.userAppLanguage(superiorUid);
                    String offlineTitle = Objects.equals(languageEnum, LanguageEnum.en) ? copywriting : copywritingAr;
                    offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemPuresUid),
                            offlineTitle,
                            offlineTitle,
                            null,
                            ClientRouteUtil.toPurse(DigitalCurrencyEnum.USD.getNumber() - 1));
                } catch (Exception e) {
                    // ignore
                }

                notifyMessageComponent.publishPuresIMDefineMessage(imMessage, superiorUid.toString(), offlinePushInfo);
            }
        }

    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void usdConvertGold(BigDecimal usdNum, Long uid,String deviceid,Boolean newBie) {
        if (uid == null || uid == 0L || usdNum == null || usdNum.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        StrategyParamConfigDTO configDTO = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DOLLAR_FOR_GOLD);
        StrategyParamConfigDTO usdForGoldMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.USD_FOR_GOLD_MIN);
        if (usdNum.compareTo(new BigDecimal(usdForGoldMin.getNum())) < 0) {
            throw new ApiException(CodeEnum.MINIMUM_EXCHANGE_AMOUNT,usdForGoldMin.getNum());
        }
        long coinNum = Math.abs(usdNum.longValue()) * configDTO.getNum();
        deductUSD(uid, usdNum, BillEnum.DOLLAR_FOR_GOLD_EXPEND, CommonUtil.genId(), "", Collections.emptyMap(),0L);
        addCoin(uid, coinNum, BillEnum.DOLLAR_FOR_GOLD_INCOME, CommonUtil.genId(), "", Collections.emptyMap(),0L);
        //handleExchangeSuccess(uid,)
        eventTrackingService.handleExchangeSuccess(uid,coinNum,usdNum.longValue(),new Date(),deviceid,newBie);
    }



    /**
     * 获取用户钱包，不存在则创建
     *
     * @param uid
     * @return
     */
    public PurseDTO getPurse(Long uid) {
        PurseDTO purseDTO = purseService.getPurse(uid);
        if (Objects.isNull(purseDTO)) {
            Purse purse = purseService.getById(uid);
            if (Objects.isNull(purse)) {
                log.info("user[{}] purse not exist, create it.", uid);
                Date now = new Date();
                purse = Purse.builder().uid(uid).coin(0L).diamond(0).usd(0L).createTime(now).updateTime(now).build();
                purseService.save(purse);
            }
            purseDTO = PurseDTO.builder().uid(uid).coin(purse.getCoin()).diamond(purse.getDiamond()).usd(purse.getUsd()).build();
            purseDTO.setIsFirst(isFirst(uid));
            purseService.setPurse(purseDTO);
        }
        purseDTO.setIsFirst(isFirst(uid));
        return purseDTO;
    }


    private Boolean isFirst(Long uid) {
        String firstRechargeStr = systemConfigService.getSysConfValueById(SystemConfigConstant.FIRST_RECHARGE_REWARD);
        if (StringUtils.isNotBlank(firstRechargeStr)) {
            List<FirstRechargeDTO> dtos = JSONUtil.toList(firstRechargeStr, FirstRechargeDTO.class);
            if (CollectionUtils.isNotEmpty(dtos)) {
                for (FirstRechargeDTO dto : dtos) {
                    if (!rechargePackageCache.getUserPackage(uid,dto.getGears())){
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 重新加载用户钱包至缓存
     *
     * @param uid
     */
    public PurseDTO reloadPurse(Long uid) {
        Purse purse = purseService.getById(uid);
        PurseDTO purseDTO = PurseDTO.builder().uid(uid).coin(purse.getCoin()).diamond(purse.getDiamond()).usd(purse.getUsd()).build();
        purseService.setPurse(purseDTO);
        return purseDTO;
    }

    /**
     * 钻石兑换金币
     *
     * @param uid
     * @param amount
     * @return
     */
    public PurseDTO diamond2Coin(Long uid, Long amount) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            int i = purserMapperExpand.diamond2Coin(uid, amount, amount);
            if (i != 1) {
                throw new ApiException(CodeEnum.DIAMOND_BALANCE_NOT_ENOUGH);
            }
            return reloadPurse(uid);
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("diamond exchange to coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    /**
     * 扣除金币
     *
     * @param uid
     * @param coin
     * @return
     */
    public PurseDTO deductCoin(long uid, Long coin) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            PurseDTO purseDTO = getPurse(uid);
            if (purseDTO.getCoin() < coin) {
                log.error("user[{}] current coin is {}, spend coin:{}", uid, purseDTO.getCoin(), coin);
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            int i = purserMapperExpand.deductCoin(uid, coin);
            if (i != 1) {
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            return reloadPurse(uid);
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("deduct coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }


    /**
     * 扣减金币
     *
     * @param uid
     * @param coin
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO deductCoin(long uid, Long coin, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid) {
        String lockKey = PurseService.purseLockKey(uid);
        long start0 = System.currentTimeMillis();
        RLock rLock = redissonManager.lock(lockKey, 3, 10);
        log.info("deductCoin methon start0, billEnum:[{}] uid:[{}] cost:[{}]", billEnum, uid, System.currentTimeMillis() - start0);
        if (rLock == null || !rLock.isLocked()) {
            log.error("Fail to get user purse lock:" + lockKey);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        try {
            long start1 = System.currentTimeMillis();
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            log.info("deductCoin methon start1, billEnum:[{}] uid:[{}] cost:[{}]", billEnum, uid, System.currentTimeMillis() - start1);
            long start2 = System.currentTimeMillis();
            PurseDTO purseDTO = getPurse(uid);
            if (purseDTO.getCoin() < coin) {
                log.error("user[{}] current coin is {}, spend coin:{}", uid, purseDTO.getCoin(), coin);
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            int i = purserMapperExpand.deductCoin(uid, coin);
            log.info("deductCoin methon start2, billEnum:[{}] uid:[{}] cost:[{}]", billEnum, uid, System.currentTimeMillis() - start2);
            long start3 = System.currentTimeMillis();
            if (i != 1) {
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, coin, afterPurse, billEnum, new Date(), introduce, remark, null);
            log.info("deductCoin methon start3, billEnum:[{}] uid:[{}] cost:[{}]", billEnum, uid, System.currentTimeMillis() - start3);
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("deduct coin uid:{},billEnum:{},error:{},e:{}", uid, billEnum, e.getMessage(), ExceptionUtil.formatEx(e), e);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 扣减金币
     *
     * @param uid
     * @param coin
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO deductSystemCoin(long uid, Long coin, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid, boolean negative) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            PurseDTO purseDTO = getPurse(uid);
            int i;
            if (negative) {
                // 允许负数
                i = purserMapperExpand.deductCoinAllowNegative(uid, coin);
            } else {
                i = purserMapperExpand.deductCoin(uid, coin);
            }
            if (i != 1) {
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, coin, afterPurse, billEnum, new Date(), introduce, remark, null);
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("deduct coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    /**
     * 扣减美金
     *
     * @param uid
     * @param usd
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO deductUSD(long uid, BigDecimal usd, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            // 换算成美分
            long coin = usd.multiply(new BigDecimal(100)).longValue();
            PurseDTO purseDTO = getPurse(uid);
            if (purseDTO.getUsd() < coin) {
                log.error("user[{}] current usd is {}, spend usd:{}", uid, purseDTO.getCoin(), coin);
                throw new ApiException(CodeEnum.INSUFFICIENT_BALANCE);
            }
            int i = purserMapperExpand.deductUsd(uid, coin);
            if (i != 1) {
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, coin, afterPurse, billEnum, new Date(), introduce, remark, null);
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("deduct coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    /**
     * 增加美金
     *
     * @param uid
     * @param usd
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO addUSD(long uid, BigDecimal usd, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            // 换算成美分
            long coin = usd.multiply(new BigDecimal(100)).longValue();
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct usd duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            int i = purserMapperExpand.addUsd(uid, coin);
            if (i != 1) {
                throw new ApiException(CodeEnum.ADD_COINS_FAIL);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, coin, afterPurse, billEnum, new Date(), introduce, remark, null);
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("add coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    /**
     * 增加金币
     *
     * @param uid
     * @param coin
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO addCoin(long uid, Long coin, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            int i = purserMapperExpand.addCoin(uid, coin);
            if (i != 1) {
                throw new ApiException(CodeEnum.ADD_COINS_FAIL);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, coin, afterPurse, billEnum, new Date(), introduce, remark, null);
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("add coin error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    /**
     * 扣除钻石
     *
     * @param uid
     * @param diamond
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO deductDiamond(long uid, Long diamond, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            PurseDTO purseDTO = getPurse(uid);
            if (purseDTO.getDiamond() < diamond) {
                log.error("user[{}] current diamond is {}, spend diamond:{}", uid, purseDTO.getDiamond(), diamond);
                throw new ApiException(CodeEnum.DIAMOND_BALANCE_NOT_ENOUGH);
            }
            int i = purserMapperExpand.deductDiamond(uid, diamond);
            if (i != 1) {
                throw new ApiException(CodeEnum.DIAMOND_BALANCE_NOT_ENOUGH);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, diamond, afterPurse, billEnum, new Date(), introduce, remark, null);
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("deduct diamond error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }


    /**
     * 增加钻石
     *
     * @param uid
     * @param diamond
     * @return
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public PurseDTO addDiamond(long uid, Long diamond, BillEnum billEnum, String bizId, String remark, Map<String, String> introduce,Long targetUid) {
        String lockKey = PurseService.purseLockKey(uid);
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            boolean duplicat = billService.existBill(uid, billEnum, bizId);
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            int i = purserMapperExpand.addDiamond(uid, diamond);
            if (i != 1) {
                throw new ApiException(CodeEnum.ADD_DIAMOND_FAIL);
            }
            PurseDTO afterPurse = reloadPurse(uid);
            afterPurse.setTargetUid(targetUid);
            billService.createBill(uid, bizId, diamond, afterPurse, billEnum, new Date(), introduce, remark, null);
            agentCache.anchorIncome(uid, diamond.intValue());
            return afterPurse;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("deduct diamond error:{}", e.getMessage());
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public PurseDTO managePurse(ManagePurse param) {
        String lockKey = PurseService.purseLockKey(param.getUid());
        BillEnum billEnum = BillEnum.getByDetailEnum(BillDetailEnum.getByNum(param.getBillDetailType()));
        try (Locker lock = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get user purse lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            boolean duplicat = billService.existBill(param.getUid(), billEnum, param.getBizId());
            if (duplicat) {
                log.warn("The operation to deduct coin duplicate, skip it.");
                throw new ApiException(CodeEnum.DUPLICATE_OPERATION);
            }
            int i = switch (billEnum) {
                case COIN_EXCHANGE_TO_COINS, CMS_REWARD_COIN, REWARD_PACK_COIN, CMS_TOP_UP_REFUND,
                        APPLE_STORE_TOP_UP, GOOGLE_PAY_TOP_UP ->
                        purserMapperExpand.addCoin(param.getUid(), param.getAmount());
                case SEND_IN_ROOM, SEND_IN_CHAT, OFFICIAL_DEDUCT_COIN, CMS_DEDUCT_COIN,
                        APPLE_STORE_REFUND, GOOGLE_PAY_REFUND ->
                        purserMapperExpand.deductCoin(param.getUid(), param.getAmount());
                case CMS_REWARD_DIAMOND, REWARD_PACK_DIAMOND, GIFT_COMMISSION_DIAMOND ->
                        purserMapperExpand.addDiamond(param.getUid(), param.getAmount());
                case DIAMOND_EXCHANGE_TO_COINS, CMS_DEDUCT_DIAMOND, OFFICIAL_DEDUCT_DIAMOND ->
                        purserMapperExpand.deductDiamond(param.getUid(), param.getAmount());
                case CMS_REWARD_USD, REWARD_PACK_USD ->
                        purserMapperExpand.addUsd(param.getUid(), param.getAmount() * 100);
                case CMS_DEDUCT_USD -> purserMapperExpand.deductUsd(param.getUid(), param.getAmount() * 100);
                default -> throw new ApiException(CodeEnum.UNKNOWN_BILL_ITEM);
            };
            if (i != 1) {
                throw new ApiException(CodeEnum.MANAGE_ERROR);
            }
            PurseDTO afterPurse = reloadPurse(param.getUid());
            billService.createBill(param.getUid(), param.getBizId(), param.getAmount(), afterPurse, billEnum,
                    new Date(), param.getIntroduce(), param.getRemark(), null);
            return afterPurse;
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("manage user purse error:{}", ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    /**
     * 创建用户钱包
     *
     * @param uid
     */
    public void createPurse(final long uid) {
        try (Locker lock = distributionLocker.lock(PurseService.purseLockKey(uid))) {
            if (Objects.isNull(lock)) {
                log.warn("create user {} purse get locker timeout.", uid);
                return;
            }
            Purse purse = purseService.getById(uid);
            if (Objects.isNull(purse)) {
                Date now = new Date();
                purse = Purse.builder().uid(uid).coin(0L).diamond(0).createTime(now).updateTime(now).build();
                purseService.save(purse);
            }
            PurseDTO purseDTO = PurseDTO.builder().uid(uid).coin(purse.getCoin()).diamond(purse.getDiamond()).build();
            purseService.setPurse(purseDTO);
        } catch (Exception e) {
            log.error("create user {} purse error:{}", uid, ExceptionUtil.formatEx(e));
        }
    }

    public ConvertProportionDTO convertProportion(Long uid, Integer currencyType, String language) {
        ConvertProportionDTO dto = new ConvertProportionDTO();
        if (DigitalCurrencyEnum.COIN.getNumber().equals(currencyType)) {
            StrategyParamConfigDTO configDTO = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DOLLAR_FOR_GOLD);
            dto.setCurrent(configDTO.getNum());
        } else if (DigitalCurrencyEnum.DIAMOND.getNumber().equals(currencyType)) {
            StrategyParamConfigDTO original = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DIAMONDS_FOR_DOLLARS_ORIGINAL);
            StrategyParamConfigDTO current = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DIAMONDS_FOR_DOLLARS_CURRENT);
            StrategyParamConfigDTO diamondsForUsdMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DIAMONDS_FOR_USD_MIN);
            dto.setCurrent(current.getNum());
            dto.setOriginal(original.getNum());
            dto.setDiamondsForUsdMin(diamondsForUsdMin.getNum());
            // 白名单
            LambdaQueryWrapper<UserPriceConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserPriceConfig::getUid, uid);
            wrapper.eq(UserPriceConfig::getConfigKey, StrategyParamConfigConstant.DIAMONDS_FOR_DOLLARS_CURRENT);
            UserPriceConfig userPriceConfig = userPriceConfigService.getOne(wrapper);
            // VIP
            Optional<UserCurVipInfo> vipInfoOptional = userVipService.getUserVipCache(uid);
            if (userPriceConfig != null) {
                dto.setCurrent(userPriceConfig.getCurrentPrice().intValue());
                vipInfoOptional.ifPresent(k -> {
                    int diamondsForUsd = k.getPropInfo().getDiamondsForUsd();
                    int currentDiamonds = dto.getCurrent();
                    // 判断 VIP 与 白名单相等
                    if (diamondsForUsd == currentDiamonds) {
                        dto.setCurrent(diamondsForUsd);
                        dto.setVipMedal(k.getPropInfo().getVipMedal());
                    }
                });
            } else {
                vipInfoOptional.ifPresent(k -> {
                    int diamondsForUsd = k.getPropInfo().getDiamondsForUsd();
                    int currentDiamonds = dto.getCurrent();
                    // 判断 VIP 小于当前价格
                    if (diamondsForUsd < currentDiamonds) {
                        dto.setCurrent(diamondsForUsd);
                        dto.setVipMedal(k.getPropInfo().getVipMedal());
                    }
                });
            }
            String receiveGifts = "";
            if (LanguageEnum.ar.name().equals(language)) {
                receiveGifts = systemConfigService.getSysConfValueById(SystemConfigConstant.USER_RECEIVE_GIFTS_AR);
            } else {
                receiveGifts = systemConfigService.getSysConfValueById(SystemConfigConstant.USER_RECEIVE_GIFTS_EN);
            }
            dto.setReminder(receiveGifts);
        } else if (DigitalCurrencyEnum.USD.getNumber().equals(currencyType)) {
            StrategyParamConfigDTO current = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DOLLAR_FOR_GOLD);
            StrategyParamConfigDTO usdForGoldMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.USD_FOR_GOLD_MIN);
            StrategyParamConfigDTO platformTransfersCharmLevelMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.PLATFORM_TRANSFERS_CHARM_LEVEL_MIN);
            StrategyParamConfigDTO toCoinDealerCharmLevelMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.TO_COIN_DEALER_CHARM_LEVEL_MIN);
            StrategyParamConfigDTO toCoinDealerUsdMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.TO_COIN_DEALER_USD_MIN);
            StrategyParamConfigDTO platformTransfersUsdMin = strategyParamConfigService.getByKey(StrategyParamConfigConstant.PLATFORM_TRANSFERS_USD_MIN);

            dto.setUsdForGoldMin(usdForGoldMin.getNum());
            dto.setPlatformTransfersCharmMin(platformTransfersCharmLevelMin.getNum());
            dto.setPlatformTransfersUsdMin(platformTransfersUsdMin.getNum());
            dto.setToCoinDealerUsdMin(toCoinDealerUsdMin.getNum());
            dto.setToCoinDealerCharmMin(toCoinDealerCharmLevelMin.getNum());
            dto.setCurrent(current.getNum());
            dto.setOriginal(0);
            String charmStr = redissonManager.hGet(UserRedisKey.user_charm_level.getKey(), String.valueOf(uid));
            long charmAmount = 0;
            if (StringUtils.isNotBlank(charmStr)) {
                charmAmount = Long.parseLong(charmStr);
            }
            LevelInfo levelInfo = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_CHARM, charmAmount);
            dto.setCharmLevel(levelInfo.getLevel());
            if (LanguageEnum.ar.name().equals(language)) {
                String titleAr = systemConfigService.getSysConfValueById(SystemConfigConstant.WALLET_USD_TITLE_AR);
                String reminderAr = systemConfigService.getSysConfValueById(SystemConfigConstant.WALLET_USD_REMINDER_AR);
                dto.setUsdTitle(titleAr);
                dto.setUsdReminder(reminderAr);
            } else {
                String titleEn = systemConfigService.getSysConfValueById(SystemConfigConstant.WALLET_USD_TITLE_EN);
                String reminderEn = systemConfigService.getSysConfValueById(SystemConfigConstant.WALLET_USD_REMINDER_EN);
                dto.setUsdTitle(titleEn);
                dto.setUsdReminder(reminderEn);
            }
        } else if (DigitalCurrencyEnum.GOLDEN_TICKET.getNumber().equals(currencyType)) {
            StrategyParamConfigDTO coinDealerForGold = strategyParamConfigService.getByKey(StrategyParamConfigConstant.COIN_DEALER_FOR_GOLD);
            dto.setCurrent(coinDealerForGold.getNum());
        }
        return dto;
    }

    public LanguageEnum userAppLanguage(Long uid) {
        String appLanguage = redissonManager.hGet(UserRedisKey.user_lang.getKey(), String.valueOf(uid));
        if (StrUtil.isBlank(appLanguage)) {
            return LanguageEnum.ar;
        }
        return LanguageEnum.getLang(appLanguage);
    }
}
