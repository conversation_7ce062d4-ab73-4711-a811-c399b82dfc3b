package com.simi.service.purse;


import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.constant.RevenueRedisKey;
import com.simi.entity.purse.Purse;
import com.simi.mapper.purse.PurseMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class PurseService extends ServiceImpl<PurseMapper, Purse> {

    @Autowired
    private RedissonClient redissonClient;

    /**
     * 设置钱包
     * @param dto
     */
    public void setPurse(PurseDTO dto){
        redissonClient.getMap(purseKey()).put(dto.getUid().toString(), JSONUtil.toJsonStr(dto));
    }

    /**
     * 获取用户钱包信息
     * @param uid
     * @return
     */
    public PurseDTO getPurse(Long uid){
        String purseStr = redissonClient.<String, String>getMap(purseKey()).get(uid.toString());
        if (StrUtil.isNotBlank(purseStr)) {
            return JSONUtil.toBean(purseStr, PurseDTO.class);
        }
        return null;
    }

    public static String purseKey(){
        return RevenueRedisKey.purse.getKey();
    }

    public static String agentPurseKey(){
        return RevenueRedisKey.agent_purse.getKey();
    }

    public Map<Long,PurseDTO> getBatchPurseFromCache(Set<Long> uids){
        Set<String> uidSet = uids.stream().map(e -> String.valueOf(e)).collect(Collectors.toSet());
        Map<String, String> purseMap = redissonClient.<String, String>getMap(purseKey()).getAll(uidSet);
        Map<Long,PurseDTO> map = new HashMap<>();
        for (String uid : uidSet) {
            String purse = purseMap.get(uid);
            if (StringUtils.isNotBlank(purse)){
                PurseDTO purseDTO = JSONUtil.toBean(purse, PurseDTO.class);
                map.put(Long.valueOf(uid),purseDTO);
            }
        }
        return map;
    }

    /**
     * 用户钱包锁
     * @param uid
     * @return
     */
    public static String purseLockKey(Long uid){
        return RevenueRedisKey.purse_lock.getKey(uid);
    }

    public PurseDTO getAgentPurse(Long uid) {
        String purseStr = redissonClient.<String, String>getMap(agentPurseKey()).get(uid.toString());
        if (StrUtil.isNotBlank(purseStr)) {
            return JSONUtil.toBean(purseStr, PurseDTO.class);
        }
        return null;
    }


    public void setAgentPurse(PurseDTO dto){
        redissonClient.getMap(agentPurseKey()).put(dto.getUid().toString(), JSONUtil.toJsonStr(dto));
    }
}
