package com.simi.service.purse;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.apple.itunes.storekit.client.APIException;
import com.apple.itunes.storekit.client.AppStoreServerAPIClient;
import com.apple.itunes.storekit.model.SendTestNotificationResponse;
import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.DecodedJWT;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.*;
import com.simi.common.constant.http.Constant;
import com.simi.common.constant.http.HttpPostSendTypeEnums;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.constant.vip.VIPConstant;
import com.simi.common.dto.AccessToken;
import com.simi.common.dto.FirstRechargeDTO;
import com.simi.common.dto.RechargeRecordListReq;
import com.simi.common.dto.RechargeReq;
import com.simi.common.dto.request.PayerMaxChargeReq;
import com.simi.common.dto.resp.PayerMaxRechargeResp;
import com.simi.common.dto.resp.RefundReq;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.*;
import com.simi.common.vo.resp.HttpPostReq;
import com.simi.config.PayerMAXConfig;
import com.simi.constant.BillEnum;
import com.simi.constant.RevenueRedisKey;
import com.simi.entity.ApplePayRecord;
import com.simi.entity.GooglePayRecord;
import com.simi.entity.PayerMaxRefund;
import com.simi.entity.purse.RechargeRecord;
import com.simi.entity.recharge.RechargePackage;
import com.simi.mapper.PayerMaxRefundMapper;
import com.simi.mapper.purse.RechargeRecordMapper;
import com.simi.message.RechargeSuccessMessage;
import com.simi.message.recharge.OneTimeProductNotification;
import com.simi.service.ApplePayRecordService;
import com.simi.service.EventTrackingService;
import com.simi.service.GooglePayRecordService;
import com.simi.service.PayerMaxRefundService;
import com.simi.service.activity.handle.ActivityBaseHandler;
import com.simi.service.cache.RechargePackageCache;
import com.simi.service.cache.RechargeRecordCache;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.rewardpack.RewardPackServerService;
import com.simi.service.user.BlockServerService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import com.payermax.sdk.client.DefaultPayermaxClient;
import com.payermax.sdk.client.PayermaxClient;
import com.payermax.sdk.config.GlobalMerchantConfig;
import com.payermax.sdk.config.MerchantConfig;
import com.payermax.sdk.enums.Env;
import com.sun.tools.javac.Main;
import io.jsonwebtoken.Header;
import io.jsonwebtoken.JwsHeader;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.json.JSONObject;
import org.springframework.core.env.Environment;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.CertificateException;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.security.interfaces.ECPublicKey;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.*;


/**
 *
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RechargeRecordService extends ServiceImpl<RechargeRecordMapper, RechargeRecord> {

    private final RechargePackageService rechargePackageService;
    private final PurseManageService purseManageService;
    private final RechargeRecordCache rechargeRecordCache;
    private final GooglePayRecordService googlePayRecordService;
    private final ApplePayRecordService applePayRecordService;
    private final ActivityBaseHandler activityBaseHandler;
    private final TaskExecutor taskExecutor;
    private final Environment environment;
    private final RechargeRecordMapper rechargeRecordMapper;
    private final EventTrackingService eventTrackingService;
    private final UserServerService userServerService;
    private final SystemConfigService systemConfigService;
    private final RechargePackageCache rechargePackageCache;
    private final RewardPackServerService rewardPackServerService;
    private final UserVipService userVipService;
    private final MedalTaskService medalTaskService;
    private final BlockServerService blockServerService;
    private final RedissonManager redissonManager;
    private final PayerMaxRefundService payerMaxRefundService;


    public List<RechargeRecord> getByChannelOrderId(String channel, String channelOrderId){
        final List<RechargeRecord> list = this.lambdaQuery().eq(RechargeRecord::getChannel, channel)
                .eq(RechargeRecord::getChannelOrderId, channelOrderId).list();
        return list;
    }

    public List<RechargeRecord> getByChannelVerifyId(String channel, String channelVerifyId){
        final List<RechargeRecord> list = this.lambdaQuery().eq(RechargeRecord::getChannel, channel)
                .eq(RechargeRecord::getChannelVerifyId, channelVerifyId).list();
        return list;
    }

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void rechargeSuccess(OneTimeProductNotification oneTimeProductNotification) {
        //成功
        String purchaseToken = oneTimeProductNotification.getPurchaseToken();
        LambdaQueryWrapper<RechargeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RechargeRecord::getChannelVerifyId, HashUtil.sha1(purchaseToken));
        RechargeRecord record = getOne(wrapper);
        log.info("method rechargeSuccess~ creation RechargeRecord:{}",JSONUtil.toJsonStr(record));
        if (record == null) {
            //缓存 记录
            googlePayRecordService.creation(oneTimeProductNotification.getSku(),oneTimeProductNotification.getVersion(),HashUtil.sha1(purchaseToken),oneTimeProductNotification.getNotificationType());
            rechargeRecordCache.setRechargeRecord(HashUtil.sha1(purchaseToken),JSONUtil.toJsonStr(oneTimeProductNotification));
            return;
        }
        GooglePayRecord googlePayRecord = googlePayRecordService.getGooglePayRecord(HashUtil.sha1(purchaseToken));
        if (googlePayRecord == null) {
            googlePayRecordService.creation(oneTimeProductNotification.getSku(),oneTimeProductNotification.getVersion(),HashUtil.sha1(purchaseToken),oneTimeProductNotification.getNotificationType());
        }
        log.info("recharge message listener rechargeRecord[{}]", JSONUtil.toJsonStr(record));
        RechargePackage aPackage = rechargePackageService.getById(oneTimeProductNotification.getSku());
        Integer purchasesState = purchasesState(aPackage.getChannel(), aPackage.getMerchant(), aPackage.getId(), purchaseToken);
        log.info("recharge message listener aPackage:{} purchasesState:{} uid:[{}]", JSONUtil.toJsonStr(aPackage), purchasesState, record.getUid());
        if (!PurchasesState.succeed.getNumber().equals(purchasesState)) {
            throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
        }
        if (!aPackage.getStatus()){
            throw new ApiException(CodeEnum.RECHARGE_PACKAGE_ERROR);
        }

        if (!Objects.equals(oneTimeProductNotification.getNotificationType(), NotificationType.ONE_TIME_PRODUCT_PURCHASED.getNumber())){
            throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
        }

        if (!Objects.equals(RechargeRecordStatusEnum.RECHARGE_CREATE.getType(), record.getStatus())){
            log.error("RechargeGoogleService checkOrder record error, recordId:{}", record);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
/*
        List<RechargeRecord> channelVerifyIdList = getByChannelVerifyId(RechargeChannelEnum.google.name(),purchaseToken);
        if (CollectionUtils.isNotEmpty(channelVerifyIdList)){
            log.info("RechargeGoogleService checkOrder purchaseToken used, purchaseToken:{}", purchaseToken);
            return;
        }

       ProductPurchase productPurchase = null;
        try {
            productPurchase = checkOrder(GoogleConfig.payPackageName, record.getPackageId(), purchaseToken);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        if (productPurchase.getPurchaseState() != 0){
            throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
        }
        List<RechargeRecord> channelOrderIdList = rechargeRecordService.getByChannelOrderId(RechargeChannelEnum.google.name(), productPurchase.getOrderId());
        if (CollectionUtils.isNotEmpty(channelOrderIdList)){
            log.info("RechargeGoogleService checkOrder channel_order_id used, orderId:{}", productPurchase.getOrderId());
            return;
        }

        if (Objects.nonNull(productPurchase.getPurchaseType()) && productPurchase.getPurchaseType() == 0){
            //沙盒
            record.setIsSandbox(true);
        }
*/
        Date now = new Date();
        //record.setChannelOrderId(productPurchase.getOrderId());
        record.setChannelVerifyId(HashUtil.sha1(purchaseToken));
        record.setStatus(RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType());
        record.setUpdateTime(now);
        record.setSuccessTime(now);
        updateById(record);
        purseManageService.addCoin(record.getUid(), record.getCoinAmount(), BillEnum.GOOGLE_PAY_TOP_UP, HashUtil.sha1(purchaseToken), "google recharge", Collections.emptyMap(),0L,PurseRoleTypeEnum.USER.getType());
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(record.getUid());
        RechargeSuccessMessage message = new RechargeSuccessMessage();
        message.setRechargeRecord(record);
        message.setNewBie(userBaseInfo.getNewBie());
        eventTrackingService.handleRechargeService(message);
        log.info("RechargeGoogleService checkOrder success, uid:{}, recordId:{}, coinAmount:{}", record.getUid(), record.getId(), record.getCoinAmount());

        // 充值活动额外处理
        taskExecutor.execute(() -> {
            try {
                activityBaseHandler.handleRechargeData(record.getUid(), aPackage);
            } catch (Exception e) {
                log.error("Recharge success Activity handle fail:[{}]", e.getMessage(), e);
            }
            userVipService.addExperience(record.getUid(), record.getCoinAmount().intValue(), VIPConstant.ExperienceSource.GOOGLE);

            log.info("Execute googlepay medal task, uid:[{}] amount:[{}]", record.getUid(), record.getCoinAmount().intValue());
            medalTaskService.executeMedalTask(MedalTaskEnum.RECHARGE_GOLD, record.getUid(), record.getCoinAmount().intValue());
        });
        firstRecharge(record.getUid(), oneTimeProductNotification.getSku());
    }

    public void firstRecharge(Long uid, String sku) {
        FirstRechargeDTO firstRechargeDTO = rechargeConfig(sku);
        if (firstRechargeDTO != null) {
            rewardPackServerService.sendFirstRecharge(uid, String.valueOf(firstRechargeDTO.getRewardPackId()));
            rechargePackageCache.setUserPackage(uid, firstRechargeDTO.getGears());
        }
    }

    private FirstRechargeDTO rechargeConfig(String sku) {
        String firstRechargeStr = systemConfigService.getSysConfValueById(SystemConfigConstant.FIRST_RECHARGE_REWARD);
        if (StringUtils.isNotBlank(firstRechargeStr)) {
            List<FirstRechargeDTO> dtos = JSONUtil.toList(firstRechargeStr, FirstRechargeDTO.class);
            if (CollectionUtils.isNotEmpty(dtos)) {
                for (FirstRechargeDTO dto : dtos) {
                    if (dto.getSkuIdList().contains(sku)){
                        return dto;
                    }
                }
            }
        }
        return null;
    }


    public static Integer purchasesState(String channel,String merchant,String productId,String purchaseToken){
        log.info("RechargeAppleService channel:{} merchant:{} merchant:{} purchaseToken:{}",channel,merchant,productId,purchaseToken);
        HttpPostReq req = new HttpPostReq();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        req.setHeader(header);
        Map<String, String> param = new HashMap<>();
        param.put("channel", channel);
        param.put("merchant", merchant);
        param.put("product_id", productId);
        param.put("purchase_token", purchaseToken);
        req.setParam(param);
        req.setUrl("http://simi-internal-services:8089/payment/purchase-state");
        req.setSendType(HttpPostSendTypeEnums.JSON.getType());
        String post = HttpUtils.sendPost(req);
        log.info("RechargeAppleService post:{}",post);
        JSONObject json = new JSONObject(post);
        return json.getInt("purchase_state");
    }


    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void appleRechargeSuccess(RechargeReq req,RechargePackage aPackage,String appVersionCode) {
        log.info("RechargeAppleService req:{} aPackage:{} appVersionCode:{}",JSONUtil.toJsonStr(req),JSONUtil.toJsonStr(aPackage),appVersionCode);
        String purchaseToken = req.getPurchaseToken();
        LambdaQueryWrapper<RechargeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RechargeRecord::getChannelVerifyId,HashUtil.sha1(purchaseToken));
        RechargeRecord record = getOne(wrapper);

        Integer purchasesState = purchasesState(aPackage.getChannel(), aPackage.getMerchant(), aPackage.getId(), purchaseToken);
        log.info("RechargeAppleService purchasesState:{}",purchasesState);
        ApplePayRecord applePayRecord = applePayRecordService.getApplePayRecord(HashUtil.sha1(purchaseToken));
        if (applePayRecord == null) {
            applePayRecordService.creation(req.getProductId(),appVersionCode,HashUtil.sha1(purchaseToken),purchasesState);
        }
        log.info("RechargeAppleService applePayRecord:{}",JSONUtil.toJsonStr(applePayRecord));
        if (!PurchasesState.succeed.getNumber().equals(purchasesState)) {
            throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
        }
        if (!aPackage.getStatus()){
            throw new ApiException(CodeEnum.RECHARGE_PACKAGE_ERROR);
        }

        Date now = new Date();
        //record.setChannelOrderId(productPurchase.getOrderId());
        record.setChannelVerifyId(HashUtil.sha1(purchaseToken));
        record.setStatus(RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType());
        record.setUpdateTime(now);
        record.setSuccessTime(now);
        updateById(record);
        purseManageService.addCoin(record.getUid(), record.getCoinAmount(), BillEnum.APPLE_STORE_TOP_UP, record.getId(), "apple recharge", Collections.emptyMap(),0L,PurseRoleTypeEnum.USER.getType());
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(record.getUid());
        RechargeSuccessMessage message = new RechargeSuccessMessage();
        message.setRechargeRecord(record);
        message.setNewBie(userBaseInfo.getNewBie());
        eventTrackingService.handleRechargeService(message);
        log.info("RechargeAppleService checkOrder success, uid:{}, recordId:{}, coinAmount:{}", record.getUid(), record.getId(), record.getCoinAmount());

        // 充值活动额外处理
        taskExecutor.execute(() -> {
            try {
                activityBaseHandler.handleRechargeData(record.getUid(), aPackage);
            } catch (Exception e) {
                log.error("Recharge success Activity handle fail:[{}]", e.getMessage(), e);
            }
            userVipService.addExperience(record.getUid(), record.getCoinAmount().intValue(), VIPConstant.ExperienceSource.IOS);

            log.info("Execute applepay medal task, uid:[{}] amount:[{}]", record.getUid(), record.getCoinAmount().intValue());
            medalTaskService.executeMedalTask(MedalTaskEnum.RECHARGE_GOLD, record.getUid(), record.getCoinAmount().intValue());
        });
        firstRecharge(record.getUid(), aPackage.getId());
    }




    @Transactional(rollbackFor = Exception.class)
    public PayerMaxRechargeResp payerMAXCharge(PayerMaxChargeReq req){
        PayermaxClient client = DefaultPayermaxClient.getInstance();

        String curEnv = EnvUtil.curEnv(environment);
        if (StrUtil.endWithIgnoreCase(curEnv, EnvUtil.ENV_PROD)) {
            client.setEnv(Env.PROD);
        } else {
            client.setEnv(Env.UAT);
        }

        // 设置商户配置信息

        String merchantPrivateKey = PayerMAXConfig.merchantPrivateKey;
        String payermaxPublicKey = PayerMAXConfig.payermaxPublicKey;
        String merchantNo = PayerMAXConfig.merchantNo;
        String appId = PayerMAXConfig.appId;

        MerchantConfig merchantConfig = MerchantConfig.Builder.builder()
                .merchantPrivateKey(merchantPrivateKey)
                .payermaxPublicKey(payermaxPublicKey)
                .merchantNo(merchantNo)
                .appId(appId)
                .build();

        GlobalMerchantConfig.setDefaultConfig(merchantConfig);
        String jsonStr = JSONUtil.toJsonStr(req);
        log.info("payerMAXCharge jsonStr:{}",jsonStr);
        String result = client.send("orderAndPay",JSON.parseObject(jsonStr));
        log.info("payerMAXCharge result:{}",result);
        // 将JSON数据解析为JSONObject
        JSONObject jsonObject = new JSONObject(result);
        // 提取"data"字段的值
        JSONObject dataObject = jsonObject.getJSONObject("data");
        log.info("payerMAXCharge data:{}",dataObject);
        String redirectUrl = dataObject.getString("redirectUrl");
        String outTradeNo = dataObject.getString("outTradeNo");
        String tradeToken = dataObject.getString("tradeToken");
        String status = dataObject.getString("status");
        PayerMaxRechargeResp resp = new PayerMaxRechargeResp();
        resp.setStatus(status);
        resp.setRedirectUrl(redirectUrl);
        resp.setOutTradeNo(outTradeNo);
        resp.setTradeToken(tradeToken);
        return resp;
    }

    public double sumAmount(RechargeRecordListReq req){
        return rechargeRecordMapper.sumAmount(req);
    }


    public void refund(String latest_receipt, String latest_receipt_info, String notification_type, String params) throws Exception {
        log.info("callback refund: {}, {}, {}, {}", latest_receipt_info, latest_receipt, notification_type, params);
        if (StrUtil.isNotBlank(params)) {
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(params);
            com.alibaba.fastjson.JSONObject unifiedReceipt = jsonObject.containsKey("unified_receipt")? jsonObject.getJSONObject("unified_receipt"):null;
            if(unifiedReceipt!=null){
                JSONArray latestReceiptInfo = unifiedReceipt.containsKey("latest_receipt_info")? unifiedReceipt.getJSONArray("latest_receipt_info"):null;
                if(latestReceiptInfo !=null && !latestReceiptInfo.isEmpty()){

                    if(latestReceiptInfo.getJSONObject(0).containsKey("original_transaction_id")){
                        Date refundTime = new Date();
                        if(latestReceiptInfo.getJSONObject(0).containsKey("cancellation_date_ms")){
                            try {
                                refundTime = new Date(Long.valueOf(latestReceiptInfo.getJSONObject(0).getString("cancellation_date_ms")));
                            }catch (Exception ignored){
                            }
                        }
                        String orderId = latestReceiptInfo.getJSONObject(0).getString("original_transaction_id");

                        LambdaQueryWrapper<RechargeRecord> wrapperQuery = new LambdaQueryWrapper<>();
                        wrapperQuery.eq(RechargeRecord::getChannelOrderId, orderId);
                        wrapperQuery.eq(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType());
                        RechargeRecord record = this.getOne(wrapperQuery);
                        if (Objects.isNull(record)) {
                            log.info("callback refund check record is empty, orderId[{}] {}, {}, {}, {}",
                                    orderId, latest_receipt_info, latest_receipt, notification_type, params);
                            return;
                        }

                        try {
                            purseManageService.deductSystemCoin(record.getUid(), record.getCoinAmount(), BillEnum.GOOGLE_PAY_REFUND, orderId, "apple refund", Collections.emptyMap(),0L, true,PurseRoleTypeEnum.USER.getType());
                            // 封号
                            blockServerService.block(null,record.getUid(), BlockReasonEnum.GOOGLE_REFUND, null);
                        } catch (Exception e) {
                            log.info("callback refund block user failed, orderId[{}] {}, {}, {}, {}",
                                    orderId, latest_receipt_info, latest_receipt, notification_type, params);
                        }

                        try{
                            LambdaUpdateWrapper<RechargeRecord> wrapper = new LambdaUpdateWrapper<>();
                            wrapper.set(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_REFUND.getType());
                            wrapper.set(RechargeRecord::getRefundTime, refundTime);
                            wrapper.eq(RechargeRecord::getChannelOrderId, orderId);
                            this.update(wrapper);
                        }catch (Exception e){
                            log.info("callback refund failed, orderId[{}] {}, {}, {}, {}",
                                    orderId, latest_receipt_info, latest_receipt, notification_type, params);
                        }
                    }
                }
            }
        }
    }

    public void notification(String params) {
        try {
            log.info("apple-notification-params={}", params);
            com.alibaba.fastjson.JSONObject jsonParams = com.alibaba.fastjson.JSONObject.parseObject(params);
            String signedPayload = this.verify(jsonParams.containsKey("signedPayload") ? jsonParams.get("signedPayload").toString() : null);
            if (org.apache.commons.lang.StringUtils.isBlank(signedPayload)) {
                return;
            }
            String fromBASE64 = new String(Base64.getUrlDecoder().decode(signedPayload));
            fromBASE64 = fromBASE64.substring(fromBASE64.indexOf("{"), fromBASE64.lastIndexOf("}") + 1);
            com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(fromBASE64);
            log.info("apple-notification-data={}", jsonObject);

            com.alibaba.fastjson.JSONObject data = com.alibaba.fastjson.JSONObject.parseObject(jsonObject.get("data").toString());
            String s = String.valueOf(Optional.ofNullable(data.get("signedTransactionInfo")).orElse(""));
            if (org.apache.commons.lang.StringUtils.isBlank(s)) {
                return;
            }
            String verify = this.verify(s);

            String fromBASE641 = new String(Base64.getUrlDecoder().decode(verify));
            fromBASE641 = fromBASE641.substring(fromBASE641.indexOf("{"), fromBASE641.lastIndexOf("}") + 1);
            com.alibaba.fastjson.JSONObject signedTransactionInfo = com.alibaba.fastjson.JSONObject.parseObject(fromBASE641);
            log.info("apple-notification-signedTransactionInfo={}", signedTransactionInfo.toJSONString());
            String notificationType = String.valueOf(Optional.ofNullable(jsonObject.get("notificationType")).orElse(""));
            if (org.apache.commons.lang.StringUtils.isBlank(notificationType)) {
                return;
            }

            String originalTransactionId = signedTransactionInfo.get("originalTransactionId").toString();

            String type = notificationType;

            if (StrUtil.equals(Constant.AppleConfKey.REFUND, notificationType)) {
                try {
                    if (StrUtil.isNotBlank(originalTransactionId)) {

                        log.info("apple-notification-REFUND-originalTransactionId{}", originalTransactionId);
                        LambdaQueryWrapper<RechargeRecord> wrapperQuery = new LambdaQueryWrapper<>();
                        wrapperQuery.eq(RechargeRecord::getChannelOrderId, originalTransactionId);
                        wrapperQuery.eq(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType());
                        RechargeRecord record = this.getOne(wrapperQuery);
                        if (Objects.isNull(record)) {
                            log.info("callback refund check record is empty, orderId[{}]", originalTransactionId);
                            return;
                        }

                        try {
                            purseManageService.deductSystemCoin(record.getUid(), record.getCoinAmount(), BillEnum.GOOGLE_PAY_REFUND,
                                    originalTransactionId, "apple refund", Collections.emptyMap(),0L, true,PurseRoleTypeEnum.USER.getType());
                            // 封号
                            blockServerService.block(null,record.getUid(), BlockReasonEnum.GOOGLE_REFUND, null);
                        } catch (Exception e) {
                            log.info("callback refund block user failed, orderId[{}]",
                                    originalTransactionId);
                        }

                        try{
                            LambdaUpdateWrapper<RechargeRecord> wrapper = new LambdaUpdateWrapper<>();
                            wrapper.set(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_REFUND.getType());
                            wrapper.set(RechargeRecord::getRefundTime, new Date());
                            wrapper.eq(RechargeRecord::getChannelOrderId, originalTransactionId);
                            this.update(wrapper);
                        }catch (Exception e){
                            log.info("callback refund failed, orderId[{}]",
                                    originalTransactionId);
                        }

                    }
                } catch (Exception e) {
                    log.error("apple-notification-REFUND error originalTransactionId={},异常=", originalTransactionId, e);
                }
            }
        } catch (Exception e) {
            log.error("apple notification failed:[{}] param:[{}]", ExceptionUtil.formatEx(e), params);
            throw e;
        }
    }

    public String verify(String jwt) {
        if (StrUtil.isBlank(jwt)) {
            return null;
        }
        try {

            DecodedJWT decodedJWT = JWT.decode(jwt);
            String header = new String(Base64.getDecoder().decode(decodedJWT.getHeader()));
            String x5c0 = com.alibaba.fastjson.JSONObject.parseArray(com.alibaba.fastjson.JSONObject.parseObject(header).get("x5c").toString()).get(0).toString();
            PublicKey publicKey = getPublicKeyByX5c(x5c0);
            Algorithm algorithm = Algorithm.ECDSA256((ECPublicKey) publicKey, null);
            algorithm.verify(decodedJWT);
            String payload = decodedJWT.getPayload();

            return payload;
        } catch (Exception e) {
        }
        return null;
    }

    public PublicKey getPublicKeyByX5c(String x5c) throws CertificateException {
        byte[] x5c0Bytes = Base64.getDecoder().decode(x5c);
        CertificateFactory fact = CertificateFactory.getInstance("X.509");
        X509Certificate cer = (X509Certificate) fact.generateCertificate(new ByteArrayInputStream(x5c0Bytes));
        return cer.getPublicKey();
    }

    public void sendTest() throws Exception {
        String accessToken = accessToken();
        Map<String, String> headers = new HashMap<>();
        headers.put("Authorization", "Bearer " + accessToken);
        HttpResponse execute = HttpUtil.createPost(Constant.AppleConfKey.SANDBOX_ITUNES_HOST + Constant.AppleConfKey.NOTIFICATIONS_TEST_PATH)
                .addHeaders(headers)
                .execute();
        log.error("req apple，host={}, path={},httpResponse={}",
                Constant.AppleConfKey.SANDBOX_ITUNES_HOST,
                Constant.AppleConfKey.NOTIFICATIONS_TEST_PATH,
                execute.getStatus());
    }

    private String accessToken()
            throws Exception {

        String accessToken = Optional
                .ofNullable(
                        Optional.ofNullable(redissonManager.get(RevenueRedisKey.apple_token.getKey(Constant.AppleConfKey.packageName))).filter(json -> {
                            AccessToken token1 = JSONUtil.toBean(json, AccessToken.class);
                            return token1.getExp() > System.currentTimeMillis();
                        }).orElseGet(() -> {
                            try {
                                AccessToken token = buildKey(Constant.AppleConfKey.packageName, Constant.AppleConfKey.keyId, Constant.AppleConfKey.issuer);
                                String jsonStr = JSONUtil.toJsonStr(token);
                                redissonManager.set(RevenueRedisKey.apple_token.getKey(Constant.AppleConfKey.packageName), jsonStr);
                                return jsonStr;
                            } catch (Exception e) {
                                e.printStackTrace();
                                return null;
                            }
                        })
                ).orElseThrow(() -> new Exception("accessToken 解密异常"));
        // return gson.fromJson(accessToken, AccessToken.class).getToken();
        return JSONUtil.toBean(accessToken, AccessToken.class).getToken();
    }

    private AccessToken buildKey(String packageName, String keyId, String issuer)
            throws Exception {
        Map<String, Object> header = new HashMap<>();
        header.put(JwsHeader.ALGORITHM, SignatureAlgorithm.ES256.getValue());
        header.put(JwsHeader.KEY_ID, keyId);
        header.put(Header.TYPE, Header.JWT_TYPE);

        Map<String, Object> claims = new HashMap<>();
        claims.put("iss", issuer);
        claims.put("iat", System.currentTimeMillis() / 1000);

        long exp = System.currentTimeMillis() + 30 * 60 * 1000;
        claims.put("exp", exp / 1000);
        claims.put("aud", "AppStoreConnect-v1");
        claims.put("bid", packageName);

        String token = Jwts.builder()
                .setHeader(header)
                .setClaims(claims)
                .signWith(SignatureAlgorithm.ES256, getPrivateKey(packageName))
                .compact();

        AccessToken key = new AccessToken();
        key.setToken(token);
        key.setExp(exp);
        return key;
    }

    private static PrivateKey getPrivateKey(String filename) throws IOException {
        try {
            String privateKey = Constant.AppleConfKey.p8_privateKey;

            KeyFactory kf = KeyFactory.getInstance("EC");
            return kf.generatePrivate(new PKCS8EncodedKeySpec(Base64.getDecoder().decode(privateKey)));
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("Java did not support the algorithm:" + "EC", e);
        } catch (InvalidKeySpecException e) {
            throw new RuntimeException("Invalid key format");
        }
    }

    /*public static void main(String[] args) throws Exception {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("EC");
        keyPairGenerator.initialize(256);
        KeyPair keyPair = keyPairGenerator.generateKeyPair();

        PrivateKey privateKey = keyPair.getPrivate();

        PKCS8EncodedKeySpec pkcs8EncodedKeySpec = new PKCS8EncodedKeySpec(privateKey.getEncoded());
        byte[] pkcs8EncodedBytes = pkcs8EncodedKeySpec.getEncoded();
        String base64PrivateKey = Base64.getEncoder().encodeToString(pkcs8EncodedBytes);

        System.out.println("Base64 Encoded PKCS#8 Private Key:");
        System.out.println(base64PrivateKey);

        try (FileOutputStream fos = new FileOutputStream("private_key.txt")) {
            fos.write(base64PrivateKey.getBytes());
        }
    }*/

    public void sign() throws IOException {
        String issuerId = Constant.AppleConfKey.issuer;
        String keyId = Constant.AppleConfKey.keyId;
        String bundleId = Constant.AppleConfKey.packageName;


        // 使用类路径加载密钥文件
        InputStream inputStream = Main.class.getClassLoader().getResourceAsStream("AuthKey_BUK95478PA.p8");

        if (inputStream == null) {
            System.out.println("密钥文件未找到");
            return;
        }

        String encodedKey;
        try {
            encodedKey = new String(inputStream.readAllBytes(), StandardCharsets.UTF_8);
        } catch (IOException e) {
            e.printStackTrace();
            return;
        }

        com.apple.itunes.storekit.model.Environment environment = com.apple.itunes.storekit.model.Environment.SANDBOX;

        AppStoreServerAPIClient client =
                new AppStoreServerAPIClient(encodedKey, keyId, issuerId, bundleId, environment);

        try {
            SendTestNotificationResponse response = client.requestTestNotification();
            System.out.println(response);
        } catch (APIException | IOException e) {
            e.printStackTrace();
        }
    }

    public void payerMaxRefund(RefundReq data) {
        PayerMaxRefund refund = BeanUtil.copyProperties(data, PayerMaxRefund.class);
        RechargeRecord record = getById(refund.getOutTradeNo());
        if (record != null) {
            refund.setUid(record.getUid());
        }
        payerMaxRefundService.save(refund);
        try {
            purseManageService.deductSystemCoin(record.getUid(), record.getCoinAmount(), BillEnum.PAYER_MAX_REFUND,
                    data.getOutTradeNo(), "apple refund", Collections.emptyMap(),0L, true,PurseRoleTypeEnum.USER.getType());
            // 封号
            blockServerService.block(null,record.getUid(), BlockReasonEnum.GOOGLE_REFUND, null);
        } catch (Exception e) {
            log.info("callback refund block user failed, orderId[{}]",
                    data.getOutTradeNo());
        }
    }
}




