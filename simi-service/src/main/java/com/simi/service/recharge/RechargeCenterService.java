package com.simi.service.recharge;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.constant.*;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.constant.vip.VIPConstant;
import com.simi.common.dto.recharge.RechargeOrderResp;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.req.recharge.PandaPayCallbackRefundReq;
import com.simi.common.vo.req.recharge.PandaPayCallbackReq;
import com.simi.common.vo.req.recharge.PandaPayRefundReq;
import com.simi.config.PandaPayConfig;
import com.simi.constant.BillEnum;
import com.simi.constant.RevenueRedisKey;
import com.simi.dto.recharge.RechargeContext;
import com.simi.entity.account.Account;
import com.simi.entity.purse.RechargeRecord;
import com.simi.entity.recharge.RechargePackage;
import com.simi.message.RechargeSuccessMessage;
import com.simi.service.EventTrackingService;
import com.simi.service.activity.handle.ActivityBaseHandler;
//import com.simi.service.coinDealer.CoinDealerService;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.oauth2.AccountService;
import com.simi.service.purse.BillService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.purse.RechargePackageService;
import com.simi.service.purse.RechargeRecordService;
import com.simi.service.user.BlockServerService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class RechargeCenterService {

    private final RechargeStrategyFactory factory;
    private final RechargeRecordService rechargeRecordService;
    private final PurseManageService purseManageService;
    //private final CoinDealerService coinDealerService;
    private final AccountService accountService;
    private final UserServerService userServerService;
    private final PandaPayConfig pandaPayConfig;
    private final PandaPayComponent pandaPayComponent;
    private final ActivityBaseHandler activityBaseHandler;
    private final TaskExecutor taskExecutor;
    private final EventTrackingService eventTrackingService;
    private final UserVipService userVipService;
    private final MedalTaskService medalTaskService;
    private final RechargePackageService rechargePackageService;
    private final SystemConfigService systemConfigService;
    private final RedissonManager redissonManager;
    private final BillService billService;
    private final BlockServerService blockServerService;


    /**
     * 统一下单接口
     */
    public RechargeOrderResp createOrder(Long uid, Long paymentUid, String clientIp,
                                         RechargePackage rechargePackage, XAuthToken xAuthToken) throws Exception {
        UserBaseInfoDTO userBaseInfoDTO = userServerService.getFromCache(paymentUid);
        // 官网的充值允许, 无用户状态替别人充值, 但是请求头入参还是写死了某个无效uid. 仅以此补偿
        if (Objects.isNull(userBaseInfoDTO)) {
            paymentUid = null;
        }

        String chargeRecordId = pandaPayConfig.getMerchantId();
        chargeRecordId = chargeRecordId + genChargeRecordId();
        RechargeRecord rechargeRecord = buildRechargeRecord(chargeRecordId, uid, paymentUid, rechargePackage, clientIp, xAuthToken);
        RechargeContext context = buildRechargeContext(rechargePackage, rechargeRecord, paymentUid, uid);
        RechargeStrategy strategy = factory.getStrategy(rechargePackage.getMerchant());
        if (Objects.isNull(strategy)) {
            log.error("Cannot find the {} recharge channel.", rechargePackage.getMerchant());
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
        RechargeOrderResp result = strategy.order(context);
        rechargeRecordService.save(rechargeRecord);
        return result;
    }


    /**
     * 创建充值记录
     */
    public RechargeRecord buildRechargeRecord(String chargeRecordId, Long uid, Long paymentUid, RechargePackage rechargePackage, String clientIp, XAuthToken xAuthToken) {
        try {
            Date date = new Date();
            String os = StrUtil.EMPTY;
            String deviceId = StrUtil.EMPTY;
            if (Objects.nonNull(xAuthToken)) {
                os = xAuthToken.getOs();
                deviceId = xAuthToken.getDeviceID();
            }
            return RechargeRecord.builder()
                    .id(chargeRecordId)
                    .uid(uid)
                    .packageId(rechargePackage.getId())
                    .channel(rechargePackage.getChannel())
                    .currency(rechargePackage.getCurrency())
                    .currencyAmount(BigDecimal.valueOf(rechargePackage.getCurrencyAmount()))
                    .coinAmount(rechargePackage.getCoinAmount().longValue())
                    .status(RechargeRecordStatusEnum.RECHARGE_CREATE.getType())
                    .createTime(date)
                    .os(os)
                    .deviceId(deviceId)
                    .ip(clientIp)
                    .updateTime(date)
                    .isSandbox(false)
                    .dollarAmount(new BigDecimal(rechargePackage.getDollarAmount()))
                    .platformCurrencyType(rechargePackage.getPlatformCurrencyType())
                    .targetUid(paymentUid)
                    .build();
        } catch (Exception e) {
            log.error("RechargeCenterService buildRechargeRecord error, uid:{}, error:{}", uid, ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    private RechargeContext buildRechargeContext(RechargePackage rechargePackage, RechargeRecord record, Long paymentUid, Long targetUid) {
        String userName = "{}_{}";
        String phone = systemConfigService.getSysConfValueById(SystemConfigConstant.PANDAPAY_DEFAULT_INFO);
        if (Objects.nonNull(paymentUid)) {
            Account account = accountService.lambdaQuery().eq(Account::getUid, paymentUid).one();
            if (Objects.nonNull(account)) {
                phone = StrUtil.format("{}{}", StrUtil.isNotBlank(account.getAreaCode()) ? account.getAreaCode() : StrUtil.EMPTY,
                        StrUtil.isNotBlank(account.getPhone()) ? account.getPhone() : StrUtil.EMPTY);
            }
        }
        LanguageEnum languageEnum = userServerService.userAppLanguage(paymentUid);
        return RechargeContext.builder()
                .rechargePackage(rechargePackage)
                .record(record)
                .nick(StrUtil.format(userName, Objects.nonNull(paymentUid) ? paymentUid : StrUtil.EMPTY, targetUid))
                .phoneNumber(phone)
                .languageEnum(languageEnum)
                .build();
    }

    public static String genChargeRecordId() {
        String dateTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String str = RandomUtil.randomNumbers(8);
        return dateTime + str;
    }


    @Transactional(rollbackFor = Exception.class)
    public void pandaPayCallback(Object objReq, String authParam) throws NoSuchAlgorithmException, InvalidKeyException {
        try {
            String reqJson = JSONUtil.toJsonStr(objReq);
            PandaPayCallbackReq pandaPayCallbackReq = JSONUtil.toBean(reqJson, PandaPayCallbackReq.class);
            String authorization = pandaPayComponent.auth(reqJson, pandaPayConfig.getApiKey());
            log.info("pandaPay authParam:[{}] authorization:[{}]", authParam, authorization);
            if (!StrUtil.equals(authParam, authorization)) {
                log.error("pandaPay callback auth sign failed, req:[{}] authParam:[{}] authorization:[{}]", reqJson, authParam, authorization);
                return;
            }
            String recordId = pandaPayCallbackReq.getExternalId();
            RechargeRecord record = rechargeRecordService.getById(recordId);
            if (Objects.isNull(record)) {
                log.info("Recharge Record is empty, pandaPayCallbackReq:[{}] authParam:[{}]",
                        reqJson, authParam);
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            RechargeRecordStatusEnum statusEnum = RechargeRecordStatusEnum.getByType(record.getStatus());
            if (Objects.isNull(statusEnum)) {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            if (Objects.equals(statusEnum, RechargeRecordStatusEnum.RECHARGE_SUCCESS)) {
                log.info("Pandapay Duplicate transactions, pandaPayCallbackReq:[{}] authParam:[{}] record:[{}]",
                        reqJson, authParam, JSONUtil.toJsonStr(record));
                return;
            }
            Date now = new Date();
            switch (pandaPayCallbackReq.getStatus()) {
                case "Success":
                    if (Objects.equals(record.getPlatformCurrencyType(), PackageCurrencyType.COIN.getNumber())) {
                        purseManageService.addCoin(record.getUid(), record.getCoinAmount(), BillEnum.PANDAPAY_RECHARGE_COIN, record.getId(), "pandaPay recharge", Collections.emptyMap(), record.getTargetUid(),PurseRoleTypeEnum.USER.getType());
                    } else if (Objects.equals(record.getPlatformCurrencyType(), PackageCurrencyType.GOLDEN_TICKET.getNumber())) {
                        //coinDealerService.recharge(record.getUid(), record.getCoinAmount(), BillEnum.PANDAPAY_RECHARGE_GOLDEN_TICKET, record.getTargetUid());
                    }
                    record.setStatus(RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType());
                    record.setSuccessTime(now);
                    record.setUpdateTime(now);
                    rechargeRecordService.updateById(record);

                    try {
                        extracted(record);
                    } catch (Exception e) {
                        log.error("PandaPay Recharge successful, subsequent processing failed, reqJson:[{}] record:[{}]",
                                reqJson, JSONUtil.toJsonStr(reqJson));
                    }
                    break;
                case "Fail":
                    log.info("PandaPay Transaction failed, pandaPayCallbackReq[{}] record:[{}]", reqJson, JSONUtil.toJsonStr(record));
                    record.setStatus(RechargeRecordStatusEnum.RECHARGE_FAILED.getType());
                    record.setUpdateTime(now);
                    rechargeRecordService.updateById(record);
                    break;
                case "Pending":
                default:
                    break;
            }
        } catch (Exception e) {
            log.error("pandaPay Success Callback error, req:[{}] auth:[{}] errorMsg:[{}]",
                    JSONUtil.toJsonStr(objReq), authParam, ExceptionUtil.formatEx(e));
        }
    }

    private void extracted(RechargeRecord record) {
        RechargePackage aPackage = rechargePackageService.getById(record.getPackageId());
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(record.getUid());
        RechargeSuccessMessage message = new RechargeSuccessMessage();
        message.setRechargeRecord(record);
        message.setNewBie(userBaseInfo.getNewBie());
        eventTrackingService.handleRechargeService(message);
        // 充值活动额外处理
        taskExecutor.execute(() -> {
            try {
                activityBaseHandler.handleRechargeData(record.getUid(), aPackage);
            } catch (Exception e) {
                log.error("payerMax attach activity handle fail:[{}]", e.getMessage(), e);
            }
            userVipService.addExperience(record.getUid(), record.getCoinAmount().intValue(), VIPConstant.ExperienceSource.PAYERMAX);

            log.info("Execute paymax medal task, uid:[{}] amount:[{}]", record.getUid(), record.getCoinAmount().intValue());
            medalTaskService.executeMedalTask(MedalTaskEnum.RECHARGE_GOLD, record.getUid(), record.getCoinAmount().intValue());
        });
    }


    @Transactional(rollbackFor = Exception.class)
    public void pandaPayCallbackRefund(Object objReq, String authParam) throws NoSuchAlgorithmException, InvalidKeyException {
        try {
            String reqJson = JSONUtil.toJsonStr(objReq);
            //PandaPayCallbackRefundReq refundReq = (PandaPayCallbackRefundReq) objReq;
            PandaPayCallbackRefundReq refundReq = JSONUtil.toBean(reqJson, PandaPayCallbackRefundReq.class);
            String authorization = pandaPayComponent.auth(reqJson, pandaPayConfig.getApiKey());
            log.info("pandaPay refund authParam:[{}] authorization:[{}]", authParam, authorization);
            if (!StrUtil.equals(authParam, authorization)) {
                log.error("pandaPay refund callback auth sign failed, authParam:[{}] authorization:[{}]", authParam, authorization);
                return;
            }
            String recordId = refundReq.getExternalId();
            RechargeRecord record = rechargeRecordService.getById(recordId);
            if (Objects.isNull(record)) {
                log.info("Recharge refund Record is empty, pandaPayCallbackReq:[{}] authParam:[{}]",
                        reqJson, authParam);
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            RechargeRecordStatusEnum statusEnum = RechargeRecordStatusEnum.getByType(record.getStatus());
            if (Objects.isNull(statusEnum)) {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            if (!Objects.equals(statusEnum, RechargeRecordStatusEnum.RECHARGE_SUCCESS)) {
                log.info("Pandapay Refund callback failed, record status is not successful req:[{}] authParam:[{}] record:[{}]",
                        reqJson, authParam, JSONUtil.toJsonStr(record));
                return;
            }
            record.setStatus(RechargeRecordStatusEnum.RECHARGE_REFUND.getType());
            if (Objects.nonNull(refundReq.getCreatedTime())) {
                DateTime refundTime = DateUtil.parse(refundReq.getCreatedTime());
                record.setRefundTime(refundTime);
            } else {
                record.setRefundTime(new Date());
            }
            record.setUpdateTime(new Date());
            rechargeRecordService.updateById(record);


            if (Objects.equals(record.getPlatformCurrencyType(), PackageCurrencyType.COIN.getNumber())) {
                PurseDTO purseDTO = purseManageService.deductCoin(record.getUid(), record.getCoinAmount(),PurseRoleTypeEnum.USER.getType());
                BillEnum pandapayRefundCoin = BillEnum.PANDAPAY_REFUND_COIN;
                billService.createBill(record.getUid(), recordId, record.getCoinAmount(), purseDTO, pandapayRefundCoin,
                        new Date(), Collections.emptyMap(), StrUtil.EMPTY, 0L);
            } else if (Objects.equals(record.getPlatformCurrencyType(), PackageCurrencyType.GOLDEN_TICKET.getNumber())) {
                //coinDealerService.modifyGoldenTicketByPlatform(record.getUid(), - record.getCoinAmount());
            }

            blockServerService.block(null, record.getUid(), BlockReasonEnum.PANDAPAY_REFUND, null);
        } catch (Exception e) {
            log.error("pandaPay Callback Refund error, req:[{}] auth:[{}] errorMsg:[{}]",
                    JSONUtil.toJsonStr(objReq), authParam, ExceptionUtil.formatEx(e));
        }
    }

    public void pandaPayRefund(PandaPayRefundReq req) {
        RechargeRecord one = rechargeRecordService.lambdaQuery()
                .eq(RechargeRecord::getId, req.getExternalId())
                .eq(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType())
                .one();
        if (Objects.isNull(one)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        pandaPayComponent.payOut(req, one.getCurrencyAmount());
        redissonManager.hSet(RevenueRedisKey.pandapay_refund_req.getKey(), req.getExternalId(), JSONUtil.toJsonStr(req));
    }

    public String pandaPayAuth(Object reqObj) {
        try {
            return pandaPayComponent.getAuthKey(reqObj);
        } catch (Exception e) {
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }
}
