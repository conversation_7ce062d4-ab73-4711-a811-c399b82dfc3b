package com.simi.service.recharge;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.recharge.PandaPayInContentDTO;
import com.simi.common.dto.recharge.PaydaPayOrderDetailDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.vo.req.recharge.PandaIraqPayInReq;
import com.simi.common.vo.req.recharge.PandaIraqPayOutReq;
import com.simi.common.vo.req.recharge.PandaIraqPayOutResp;
import com.simi.common.vo.req.recharge.PandaPayRefundReq;
import com.simi.config.PandaPayConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.HashMap;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/09/03 11:43
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PandaPayComponent {

    private static final int SUCCESS_CODE = 0;
    private static final String SUCCESS_DESC = "success";

    private final PandaPayConfig pandaPayConfig;

    public PandaPayInContentDTO payIn(String externalId, Long amount, String userName, String phoneNumber, String lang) {
        String responseBody = null;
        try {
            String url = pandaPayConfig.getDomain() + "/api/payment/payIn";
            PandaIraqPayInReq reqBody = PandaIraqPayInReq.builder()
                    .externalId(externalId)
                    .name(userName)
                    .phoneNumber(phoneNumber)
                    .amount(String.valueOf(amount))
                    .channel("Zaincash")
                    .lang(lang)
                    .redirectUrl(pandaPayConfig.getRedirectUrl())
                    .build();

            String reqBodyJson = JSONUtil.toJsonStr(reqBody);
            String authorization = auth(reqBodyJson, pandaPayConfig.getApiKey());
            HashMap<String, String> headers = MapUtil.newHashMap();
            headers.put("Authorization", authorization);
            headers.put("Content-Type", "Application/Json");
            headers.put("Encoding", "UTF-8");
            headers.put("Method", "POST");
            headers.put("appId", pandaPayConfig.getAppId());
            log.info("send pandaPay request reqBodyJson:[{}] externalId:[{}] amount:[{}]", reqBodyJson, externalId, amount);
            HttpResponse response = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(reqBodyJson)
                    .execute();
            responseBody = response.body();
            log.info("pandaPay execute payment response:[{}] externalId:[{}] amount:[{}]", responseBody, externalId, amount);
            PandaPayInContentDTO responseDTO = JSONUtil.toBean(responseBody, PandaPayInContentDTO.class);
            if (!Objects.equals(responseDTO.getCode(), SUCCESS_CODE)
                    && !StrUtil.endWithIgnoreCase(responseDTO.getDescription(), SUCCESS_DESC)) {
                log.info("pandaPay payIn failed payment response:[{}] externalId:[{}] amount:[{}]", responseBody, externalId, amount);
                throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
            }
            return responseDTO;
        } catch (Exception e) {
            log.info("pandaPay payIn error response:[{}] externalId:[{}] amount:[{}] msg:[{}]", responseBody, externalId, amount, ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
        }
    }

    public PaydaPayOrderDetailDTO queryPayInOrder(String externalId) {
        String responseBody = null;
        try {
            String url = pandaPayConfig.getDomain() + "/api/pay/queryPayInOrder";
            JSONObject req = new JSONObject();
            req.set("externalId", externalId);
            req.set("channel", "Zaincash");

            String reqBodyJson = JSONUtil.toJsonStr(req);
            String authorization = auth(reqBodyJson, pandaPayConfig.getApiKey());
            HashMap<String, String> headers = MapUtil.newHashMap();
            headers.put("Authorization", authorization);
            headers.put("Content-Type", "Application/Json");
            headers.put("Encoding", "UTF-8");
            headers.put("Method", "POST");
            headers.put("appId", pandaPayConfig.getAppId());
            log.info("PaydaPay queryPayInOrder reqBodyJson:[{}] externalId:[{}] ", reqBodyJson, externalId);
            HttpResponse response = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(reqBodyJson)
                    .execute();
            responseBody = response.body();
            PaydaPayOrderDetailDTO responseDTO = JSONUtil.toBean(responseBody, PaydaPayOrderDetailDTO.class);
            if (!Objects.equals(responseDTO.getCode(), SUCCESS_CODE)
                    && !StrUtil.endWithIgnoreCase(responseDTO.getDescription(), SUCCESS_DESC)) {
                log.info("Query PaydaPay queryPayInOrder reqBodyJson response:[{}] externalId:[{}]", responseBody, externalId);
                throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
            }
            return responseDTO;
        } catch (Exception e) {
            log.info("Query PaydaPay queryPayInOrder error response:[{}] externalId:[{}]", responseBody, externalId);
            throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
        }
    }

    public String auth(String parameter, String apiKey) throws NoSuchAlgorithmException, InvalidKeyException {
        SecretKeySpec signingKey = new SecretKeySpec(apiKey.getBytes(), "HmacSHA256");
        Mac mac = Mac.getInstance("HmacSHA256");
        mac.init(signingKey);
        byte[] rawHmac = mac.doFinal(parameter.getBytes());
        return Base64.getEncoder().encodeToString(rawHmac);
    }

    public void payOut(PandaPayRefundReq req, BigDecimal amount) {
        String responseBody = null;
        try {
            String url = pandaPayConfig.getDomain() + "/api/pay/payout";
            PandaIraqPayOutReq reqBody = PandaIraqPayOutReq.builder()
                    .externalId(req.getExternalId())
                    .channel("Zaincash")
                    .name(req.getName())
                    .phoneNumber(req.getPhoneNumber())
                    .amount(amount.toString())
                    .type("Wallet")
                    .build();
            String reqBodyJson = JSONUtil.toJsonStr(reqBody);
            String authorization = auth(reqBodyJson, pandaPayConfig.getApiKey());
            HashMap<String, String> headers = MapUtil.newHashMap();
            headers.put("Authorization", authorization);
            headers.put("Content-Type", "Application/Json");
            headers.put("Encoding", "UTF-8");
            headers.put("Method", "POST");
            headers.put("appId", pandaPayConfig.getAppId());
            log.info("pandaPay payOut request reqBodyJson:[{}] externalId:[{}] amount:[{}]", reqBodyJson, req.getExternalId(), amount);
            HttpResponse response = HttpRequest.post(url)
                    .addHeaders(headers)
                    .body(reqBodyJson)
                    .execute();
            responseBody = response.body();
            log.info("pandaPay payOut execute payment response:[{}] externalId:[{}] amount:[{}]", responseBody, req.getExternalId(), amount);
            PandaIraqPayOutResp responseDTO = JSONUtil.toBean(responseBody, PandaIraqPayOutResp.class);
            if (!StrUtil.equals(responseDTO.getCode(), String.valueOf(SUCCESS_CODE))
                    && !StrUtil.endWithIgnoreCase(responseDTO.getDescription(), SUCCESS_DESC)) {
                log.info("pandaPay payOut failed payment response:[{}] externalId:[{}] amount:[{}]", responseBody, req.getExternalId(), amount);
                throw new ApiException(CodeEnum.RECHARGE_PAY_ERROR);
            }
        } catch (Exception e) {
            log.info("pandaPay payOut error response:[{}] externalId:[{}] amount:[{}]", responseBody, req.getExternalId(), amount);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    public String getAuthKey(Object reqObj) throws NoSuchAlgorithmException, InvalidKeyException {
        return auth(JSONUtil.toJsonStr(reqObj), pandaPayConfig.getApiKey());
    }
}
