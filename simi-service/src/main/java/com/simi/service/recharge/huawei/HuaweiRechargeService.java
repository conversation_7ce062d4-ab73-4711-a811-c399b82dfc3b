package com.simi.service.recharge.huawei;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PackageCurrencyType;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.RechargeRecordStatusEnum;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.UserRedisKey;
import com.simi.dto.huawei.AtRet;
import com.simi.entity.purse.RechargeRecord;
import com.simi.entity.recharge.RechargePackage;
//import com.simi.service.coinDealer.CoinDealerService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.purse.RechargePackageService;
import com.simi.service.purse.RechargeRecordService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/10/11 17:48
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class HuaweiRechargeService {

    public static final String TOC_SITE_URL = "https://orders-dre.iap.hicloud.eu";
    public static final String TOBTOC_SITE_URL = "https://orders-at-dre.iap.dbankcloud.com";

    private final UserServerService userServerService;
    private final RechargePackageService rechargePackageService;
    private final RedissonDistributionLocker distributionLocker;
    private final RechargeRecordService rechargeRecordService;
    private final PurseManageService purseManageService;
//    private final CoinDealerService coinDealerService;

    private String getRootUrl(Integer accountFlag) {
        if (accountFlag != null && accountFlag == 1) {
            return TOBTOC_SITE_URL;
        }
        return TOC_SITE_URL;
    }

    public void confirm(Long uid, String purchaseToken, String packageId, XAuthToken xAuthToken, String clientIp) throws Exception {
        UserBaseInfoDTO userDto = userServerService.getFromCache(uid);
        if (Objects.isNull(userDto)) {
            throw new ApiException(CodeEnum.ACCOUNT_NOT_EXIST);
        }

        RechargePackage rechargePackage = rechargePackageService.getById(packageId);
        if (Objects.isNull(rechargePackage) || !rechargePackage.getStatus()) {
            throw new ApiException(CodeEnum.RECHARGE_PACKAGE_ERROR);
        }

        //校验通过逻辑处理
        try (Locker lock = distributionLocker.lock(UserRedisKey.huawei_recharge_lock.getKey(uid))) {
            if (Objects.isNull(lock)) {
                log.error("Fail to get huawei_recharge_lock:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            AtRet atRet;
            try {
                atRet = this.verifyToken(purchaseToken, packageId);
            } catch (Exception e) {
                log.error("huawei verifyToken err: ", e);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            if (!"0".equals(atRet.getResponseCode())) {
                log.error("huawei verifyToken err: {}", JSONUtil.toJsonStr(atRet));
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            // todo sign atRet

            Map<String, Object> tokenData = JSONUtil.toBean(atRet.getPurchaseTokenData(), Map.class);
            String orderId = tokenData.get("orderId").toString();
            Object purchaseType = tokenData.get("purchaseType");

            LambdaQueryWrapper<RechargeRecord> wrapper= new LambdaQueryWrapper<>();
            RechargeRecord chargeRecordDto = rechargeRecordService.lambdaQuery().eq(RechargeRecord::getChannelOrderId, orderId).one();
            //3.判断该通知是否已经处理过(兼容谷歌支付消耗,直接返回,不做处理)
            if (Objects.nonNull(chargeRecordDto)) {
                log.info("huawei recharge verifyToken chargeRecordDto is not empty, chargeRecordDto:[{}]", JSONUtil.toJsonStr(chargeRecordDto));
                throw new ApiException(CodeEnum.SERVER_ERROR);
            } else {
                RechargeRecord rechargeRecord = buildRechargeRecord(uid, orderId, purchaseType, purchaseToken, rechargePackage, clientIp, xAuthToken);
                //写入数据库
                try {
                    rechargeRecordService.save(rechargeRecord);
                } catch (Exception e) {
                    log.error("huawei recharge saveDb failed, rechargeRecord:[{}] uid:[{}] e:[{}]",
                            JSONUtil.toJsonStr(rechargeRecord), uid, ExceptionUtil.formatEx(e));
                    throw new ApiException(CodeEnum.SERVER_ERROR);
                }

                if (Objects.equals(rechargeRecord.getPlatformCurrencyType(), PackageCurrencyType.COIN.getNumber())) {
                    purseManageService.addCoin(rechargeRecord.getUid(), rechargeRecord.getCoinAmount(),
                            BillEnum.HUAWEI_RECHARGE_COIN, rechargeRecord.getId(), "huawei IAP", Collections.emptyMap(), rechargeRecord.getTargetUid(), PurseRoleTypeEnum.USER.getType());
                } //else if (Objects.equals(rechargeRecord.getPlatformCurrencyType(), PackageCurrencyType.GOLDEN_TICKET.getNumber())) {
//                    coinDealerService.recharge(rechargeRecord.getUid(), rechargeRecord.getCoinAmount(),
//                            BillEnum.HUAWEI_RECHARGE_GOLDEN_TICKET, rechargeRecord.getTargetUid());
//                }
                // 确认订单, 转由客户端处理
                // this.confirmPurchase(purchaseToken, packageId, accountFlag);
            }
        }  catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("Huawei create recharge error:{}", ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    public RechargeRecord buildRechargeRecord(Long uid, String orderId, Object purchaseType, String purchaseToken, RechargePackage rechargePackage, String clientIp, XAuthToken xAuthToken) {
        try {
            Date date = new Date();
            String os = StrUtil.EMPTY;
            String deviceId = StrUtil.EMPTY;
            if (Objects.nonNull(xAuthToken)) {
                os = xAuthToken.getOs();
                deviceId = xAuthToken.getDeviceID();
            }
            return RechargeRecord.builder()
                    .id(genChargeRecordId())
                    .uid(uid)
                    .packageId(rechargePackage.getId())
                    .channel(rechargePackage.getChannel())
                    .channelOrderId(orderId)
                    .channelVerifyId(purchaseToken)
                    .currency(rechargePackage.getCurrency())
                    .currencyAmount(BigDecimal.valueOf(rechargePackage.getCurrencyAmount()))
                    .coinAmount(rechargePackage.getCoinAmount().longValue())
                    .status(RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType())
                    .createTime(date)
                    .os(os)
                    .deviceId(deviceId)
                    .ip(clientIp)
                    .updateTime(date)
                    .isSandbox(Objects.nonNull(purchaseType))
                    .dollarAmount(new BigDecimal(rechargePackage.getDollarAmount()))
                    .platformCurrencyType(rechargePackage.getPlatformCurrencyType())
                    .targetUid(uid)
                    .build();
        } catch (Exception e) {
            log.error("HuaweiRecharge buildRechargeRecord error, uid:{}, error:{}", uid, ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    private static String genChargeRecordId() {
        String dateTime = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        String str = RandomUtil.randomNumbers(8);
        return dateTime + str;
    }

    public AtRet verifyToken(String purchaseToken, String productId) throws Exception {
        // fetch the App Level AccessToken
        String appAt = AtUtil.getAppAT();
        // construct the Authorization in Header
        Map<String, String> headers = AtUtil.buildAuthorization(appAt);

        // pack the request body
        Map<String, String> bodyMap = new HashMap<>();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String msgBody = JSONObject.toJSONString(bodyMap);

        String response = AtUtil.httpPost(TOC_SITE_URL + "/applications/purchases/tokens/verify",
                "application/json; charset=UTF-8", msgBody, 5000, 5000, headers, true);
        log.info("huawei verifyToken response: {}", response);
        return JSONUtil.toBean(response, AtRet.class);
    }

    public void confirmPurchase(String purchaseToken, String productId) throws Exception {
        // fetch the App Level AccessToken
        String appAt = AtUtil.getAppAT();
        // construct the Authorization in Header
        Map<String, String> headers = AtUtil.buildAuthorization(appAt);

        // pack the request body
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("purchaseToken", purchaseToken);
        bodyMap.put("productId", productId);

        String msgBody = JSONObject.toJSONString(bodyMap);

        String response = AtUtil.httpPost(TOC_SITE_URL + "/applications/v2/purchases/confirm",
                "application/json; charset=UTF-8", msgBody, 5000, 5000, headers, true);
        log.info("huawei confirmPurchase response: {}", response);
        AtRet atRet = JSONUtil.toBean(response, AtRet.class);
        if (!"0".equals(atRet.getResponseCode())) {
            log.error("huawei confirmPurchase err: {}", response);
        }
    }

    public AtRet cancelledListPurchase(Long endAt, Long startAt, Integer maxRows, Integer type, String continuationToken) throws Exception {
        // 获取应用级AccessToken
        String appAt = AtUtil.getAppAT();
        // 在请求头中构建Authorization
        Map<String, String> headers = AtUtil.buildAuthorization(appAt);

        // 包装request的body信息
        Map<String, Object> bodyMap = new HashMap<>();
        bodyMap.put("endAt", endAt);
        bodyMap.put("startAt", startAt);
        bodyMap.put("maxRows", maxRows);
        bodyMap.put("type", type);
        bodyMap.put("continuationToken", continuationToken);

        String msgBody = JSONObject.toJSONString(bodyMap);
        // demo中使用5000作为超时时间，您可以按照需要调整
        String response = AtUtil.httpPost(TOC_SITE_URL + "/applications/v2/purchases/cancelledList",
                "application/json; charset=UTF-8", msgBody, 5000, 5000, headers, true);
        log.info("huawei cancelledListPurchase response: {}", response);
        return JSONUtil.toBean(response, AtRet.class);
    }
}
