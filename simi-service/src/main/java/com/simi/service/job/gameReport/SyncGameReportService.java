package com.simi.service.job.gameReport;


import com.simi.common.dto.xxl.GameReportDTO;
import com.simi.service.oauth2.login.LoginService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class SyncGameReportService {
    private final LoginService loginService;
    private final List<IGameDataSyncService> gameDataSyncServices;
    /**
     *测试uid
     */
    private final static Long TEST_UID = 36826760L;

    public void doSyncData(GameReportDTO gameReportDTO){
        setToken(gameReportDTO);

        for (IGameDataSyncService gameDataSyncService : gameDataSyncServices) {
            gameDataSyncService.doSync(gameReportDTO);
        }
    }

    private void setToken(GameReportDTO gameReportDTO) {
       /* LoginResp loginResp = new LoginResp();
        loginResp.setToken(TEST_UID.toString());
        loginResp.setUid(TEST_UID);
        loginService.cacheToken(loginResp);*/

        gameReportDTO.setToken(TEST_UID.toString());
        gameReportDTO.setUid(TEST_UID);
    }
}
