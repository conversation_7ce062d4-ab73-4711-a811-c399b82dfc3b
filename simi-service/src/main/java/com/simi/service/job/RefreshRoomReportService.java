package com.simi.service.job;


import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import com.simi.common.annotation.DS;
import com.simi.common.dto.dataReport.MicRecordDTO;
import com.simi.common.dto.dataReport.RoomRecordDTO;
import com.simi.common.dto.dataReport.SearchRoomReportDTO;
import com.simi.common.dto.room.RoomRunTimeDTO;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.RedissonManager;
import com.simi.constant.RoomRedisKey;
import com.simi.entity.room.Room;
import com.simi.entity.user.User;
//import com.simi.service.meiliSearch.dataReport.RoomReportService;
import com.simi.service.room.RoomService;
import com.simi.service.room.RoomUserInRecordService;
import com.simi.service.room.cache.RoomMsgRecordRedisService;
import com.simi.service.room.roommic.RoomMicRecordService;
import com.simi.service.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBatch;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class RefreshRoomReportService {
    private final RoomService roomService;
    private final UserService userService;
    private final RoomUserInRecordService roomUserInRecordService;
    private final RoomMicRecordService roomMicRecordService;
    private final RedissonClient  redissonClient;
    private final RoomMsgRecordRedisService roomMsgRecordRedisService;
    //private final RoomReportService roomReportService;
    private final RedissonManager redissonManager;

    @DS("slave")
    public void doRefresh(String date,boolean updateAll) {
        //获取room数据
        List<SearchRoomReportDTO> roomInfos = getRoomList(date,updateAll);
        log.info("RefreshRoomReportService doRefresh roomInfos: {}", JSON.toJSONString(roomInfos));
        if (CollectionUtils.isEmpty(roomInfos)){
            return;
        }

        //将数据写入meilisearch
        //roomReportService.batchSave(roomInfos);
        //将处理过的房间进行转移
        removeHistoryRoomBatchIds(roomInfos,date);

    }

    private void removeHistoryRoomBatchIds(List<SearchRoomReportDTO> roomInfos, String date) {
        Date targetDate = getTargetDate(date);
        String roomBatchIdListKey = roomMsgRecordRedisService.getRoomBatchIdListKey(targetDate);
        List<String> batchIds = roomInfos.stream().map(SearchRoomReportDTO::getRoomBatchId).collect(Collectors.toList());
        //将数据转移
        roomMsgRecordRedisService.addHandleList(targetDate,batchIds);
        //原数据删除
        roomMsgRecordRedisService.removeList(roomBatchIdListKey,batchIds);
    }

    private List<SearchRoomReportDTO> getRoomList(String date, boolean updateAll) {
        Date targetDate = getTargetDate(date);
        //获取当天结束直播的房间数据
        List<SearchRoomReportDTO> currentDayHistoryRoomList = getCurrentDayHistoryRoomList(targetDate,updateAll);
        if (CollectionUtils.isEmpty(currentDayHistoryRoomList)){
            return Collections.emptyList();
        }

        Set<String> roomIds = currentDayHistoryRoomList.stream().map(SearchRoomReportDTO::getRoomId).collect(Collectors.toSet());
        Set<String> roomBatchIds = currentDayHistoryRoomList.stream().map(SearchRoomReportDTO::getRoomBatchId).collect(Collectors.toSet());
        Map<String, Room> roomMap = roomService.getRoomByRoomIds(roomIds);

        List<Long> uidList = roomMap.values().stream().map(Room::getUid).collect(Collectors.toList());
        //用户数据
        Map<Long, User> userMap = userService.getUserByUidList(uidList);

        //送礼
        Map<String, SearchRoomReportDTO> roomSendGiftMap = getRoomSendGift(roomBatchIds);

        //进房记录
        Map<String, RoomRecordDTO> userInRoomRecordMap = getUserInRoomRecord(currentDayHistoryRoomList);

        //麦位数据
        Map<String, MicRecordDTO> micRecord = getMicRecord(currentDayHistoryRoomList);

        //公屏数量
        Map<String, Integer> screenMsgMap = getBatchRedisMsg(new ArrayList<>(roomBatchIds),RoomRedisKey.room_screen_count);

        //在线主播最长的主播
        Map<String, Long> longestBroadcastUidMap = getLongestBroadcastUid(currentDayHistoryRoomList,roomMap);

        String day = DateTimeUtil.formatUTCZeroToUTCThree(targetDate, DateTimeUtil.DAY_PATTERN);
        //最高在线人数
        Map<String, String> maxAudienceMap = getBatchHashRedisValue(roomBatchIds,RoomRedisKey.room_batch_id_max_audience.getKey(day));

        List<SearchRoomReportDTO> result = new ArrayList<>();
        for (SearchRoomReportDTO searchRoomReportDTO : currentDayHistoryRoomList) {
            String roomBatchId = searchRoomReportDTO.getRoomBatchId();
            searchRoomReportDTO.setReportDate(day);
            //房间靓号
            handleRoom(searchRoomReportDTO,searchRoomReportDTO.getRoomId(),roomMap);
            //用户靓号
            handleUser(searchRoomReportDTO,searchRoomReportDTO.getUid(),userMap);
            //送礼
            handlePartyGiftMap(searchRoomReportDTO,roomBatchId,roomSendGiftMap);
            //进房
            handleUserInRoomRecord(searchRoomReportDTO,roomBatchId,userInRoomRecordMap);
            //上麦
            handleMicRecord(searchRoomReportDTO,roomBatchId,micRecord);
            //公屏数量
            handleScreenMsg(searchRoomReportDTO,roomBatchId,screenMsgMap);
            //在线最长
            handleLongestBroadcastUid(searchRoomReportDTO,roomBatchId,longestBroadcastUidMap);

            //最高在线人数
            handleMaxAudience(searchRoomReportDTO,roomBatchId,maxAudienceMap);

            result.add(searchRoomReportDTO);
        }

        return result;
    }

    private Map<String, String> getBatchHashRedisValue(Set<String> fields, String key) {
        Map<String, String> map = redissonManager.hMGet(key, fields);
        return map;
    }

    private void handleMaxAudience(SearchRoomReportDTO searchRoomReportDTO, String roomBatchId, Map<String, String> maxAudienceMap) {
        String maxAudience = maxAudienceMap.get(roomBatchId);
        if (StringUtils.isNotBlank(maxAudience)){
            searchRoomReportDTO.setHighestOnline(Integer.valueOf(maxAudience));
        }
    }

    private Map<String,SearchRoomReportDTO> getRoomSendGift(Set<String> roomBatchIds) {
        List<List<String>> partition = Lists.partition(new ArrayList<>(roomBatchIds), 20);
        Map<String, Integer> coinMap = new HashMap<>();
        Map<String, Integer> pvMap = new HashMap<>();
        Map<String, Integer> receiveUvMap = new HashMap<>();
        Map<String, Integer> sendUvMap = new HashMap<>();
        Map<String,SearchRoomReportDTO> result = new HashMap<>();
        for (List<String> partitionIds : partition) {
            //送礼
            coinMap.putAll(getBatchRedisMsg(partitionIds, RoomRedisKey.room_batch_id_send_coin));
            //pv
            pvMap.putAll(getBatchRedisMsg(partitionIds, RoomRedisKey.room_batch_id_send_pv));
            //收礼uv
            receiveUvMap.putAll(getBatchUvCount(partitionIds, RoomRedisKey.room_batch_id_receive_uv_list));
            //送礼uv
            sendUvMap.putAll(getBatchUvCount(partitionIds, RoomRedisKey.room_batch_id_send_uv_list));
        }
        for (String roomBatchId : roomBatchIds) {
            SearchRoomReportDTO searchRoomReportDTO = new SearchRoomReportDTO();
            Integer coin = coinMap.get(roomBatchId);
            searchRoomReportDTO.setReceiveGiftGold(coin);
            searchRoomReportDTO.setSendGiftGold(coin);

            Integer pv = pvMap.get(roomBatchId);
            searchRoomReportDTO.setSendGiftPv(pv);
            searchRoomReportDTO.setReceiveGiftPv(pv);

            Integer receiveUv = receiveUvMap.get(roomBatchId);
            searchRoomReportDTO.setReceiveGiftUv(receiveUv);

            Integer sendUv = sendUvMap.get(roomBatchId);
            searchRoomReportDTO.setSendGiftUv(sendUv);
            result.put(roomBatchId,searchRoomReportDTO);
        }
        return result;
    }

    private List<SearchRoomReportDTO> getCurrentDayHistoryRoomList(Date targetDate, boolean updateAll) {
        //获取当天开播的房间
        String roomBatchIdListKey = roomMsgRecordRedisService.getRoomBatchIdListKey(targetDate);
        Set<String> runRoomList = roomMsgRecordRedisService.getRunRoomList(roomBatchIdListKey);
        if (updateAll){
            String handleRoomKey = roomMsgRecordRedisService.getHandleRoomKey(targetDate);
            Set<String> handleRoom = roomMsgRecordRedisService.getRunRoomList(handleRoomKey);
            runRoomList.addAll(handleRoom);
        }
        if (CollectionUtils.isEmpty(runRoomList)){
            return Collections.emptyList();
        }

        Map<String, String> roomTimeMap = roomMsgRecordRedisService.getRoomTimeMap(targetDate, runRoomList);
        Set<Map.Entry<String, String>> entries = roomTimeMap.entrySet();
        List<SearchRoomReportDTO> result = new ArrayList<>();
        for (Map.Entry<String, String> entry : entries) {
            String roomBatchId = entry.getKey();
            String value = entry.getValue();
            if (StringUtils.isNotBlank(value)){
                RoomRunTimeDTO roomRunTimeDTO = JSON.parseObject(value, RoomRunTimeDTO.class);
                Long startTime = roomRunTimeDTO.getStartTime();
                Long endTime = roomRunTimeDTO.getEndTime();
                if (Objects.isNull(startTime) || Objects.isNull(endTime)){
                    continue;
                }
                SearchRoomReportDTO searchRoomReportDTO = new SearchRoomReportDTO();
                //毫秒
                long totalTime = endTime - startTime;
                int totalMin = (int) (totalTime / 60000l);
                searchRoomReportDTO.setBroadcastMinutes(totalMin);
                searchRoomReportDTO.setBeginTimestamp(startTime);
                searchRoomReportDTO.setEndTimestamp(endTime);
                searchRoomReportDTO.setBeginTimeDesc(DateTimeUtil.formatTimeMillis(startTime,DateTimeUtil.DATE_TIME_PATTERN,DateTimeUtil.GMT_3));
                searchRoomReportDTO.setEndTimeDesc(DateTimeUtil.formatTimeMillis(endTime,DateTimeUtil.DATE_TIME_PATTERN,DateTimeUtil.GMT_3));
                searchRoomReportDTO.setRoomBatchId(roomBatchId);
                String roomId = roomBatchId.split(RoomMsgRecordRedisService.SPLIT_CHAR)[0];
                searchRoomReportDTO.setRoomId(roomId);

                result.add(searchRoomReportDTO);
            }
        }
        return result;
    }

    private Date getTargetDate(String date) {
        if (StringUtils.isBlank(date)){
            return new Date();
        }
        return DateTimeUtil.parseDate(DateTimeUtil.GMT_3, date, DateTimeUtil.DAY_PATTERN);
    }

    private void handleLongestBroadcastUid(SearchRoomReportDTO searchRoomReportDTO, String id, Map<String, Long> longestBroadcastUidMap) {
        Long uid = longestBroadcastUidMap.get(id);
        if (Objects.nonNull(uid)){
            searchRoomReportDTO.setLongestBroadcastUid(uid);
        }
    }

    private void handleScreenMsg(SearchRoomReportDTO searchRoomReportDTO, String id, Map<String, Integer> screenMsgMap) {
        Integer count = screenMsgMap.get(id);
        if (Objects.nonNull(count)){
            searchRoomReportDTO.setScreenMsgNum(count);
        }
    }

    private void handleMicRecord(SearchRoomReportDTO searchRoomReportDTO, String id, Map<String, MicRecordDTO> micRecord) {
        MicRecordDTO micRecordDTO = micRecord.get(id);
        if (Objects.nonNull(micRecordDTO)){
            searchRoomReportDTO.setUpMicPv(micRecordDTO.getInMicPv());
            searchRoomReportDTO.setUpMicUv(micRecordDTO.getInMicUv());
            searchRoomReportDTO.setAvgUpMicMin(micRecordDTO.getAvgUpMicMin());
            searchRoomReportDTO.setEffectiveUpMicNum(micRecordDTO.getEffectiveUpMicNum());
        }
    }

    private void handleUserInRoomRecord(SearchRoomReportDTO searchRoomReportDTO, String id, Map<String, RoomRecordDTO> userInRoomRecordMap) {
        RoomRecordDTO roomRecordDTO = userInRoomRecordMap.get(id);
        if (Objects.nonNull(roomRecordDTO)){
            searchRoomReportDTO.setInRoomPv(roomRecordDTO.getInRoomPv());
            searchRoomReportDTO.setInRoomUv(roomRecordDTO.getInRoomUv());
            searchRoomReportDTO.setEffectiveNumber(roomRecordDTO.getEffectiveNumber());
            searchRoomReportDTO.setAvgStayMinutes(roomRecordDTO.getAvgStayMinutes());
        }
    }

    private void handlePartyGiftMap(SearchRoomReportDTO searchRoomReportDTO, String id, Map<String, SearchRoomReportDTO> giftMap) {
        SearchRoomReportDTO gift = giftMap.get(id);
        if (Objects.nonNull(gift)){
            searchRoomReportDTO.setSendGiftUv(gift.getSendGiftUv());
            searchRoomReportDTO.setSendGiftPv(gift.getSendGiftPv());
            searchRoomReportDTO.setReceiveGiftPv(gift.getReceiveGiftPv());
            searchRoomReportDTO.setReceiveGiftUv(gift.getReceiveGiftUv());

            searchRoomReportDTO.setSendGiftGold(gift.getSendGiftGold());
            searchRoomReportDTO.setReceiveGiftGold(gift.getReceiveGiftGold());
        }
    }

    private void handleUser(SearchRoomReportDTO searchRoomReportDTO, Long id, Map<Long, User> userMap) {
        if (Objects.isNull(id)){
            return;
        }

        User user = userMap.get(id);
        if (Objects.nonNull(user)){
            searchRoomReportDTO.setUserNo(user.getUserNo());
        }
    }

    private void handleRoom(SearchRoomReportDTO searchRoomReportDTO, String roomId, Map<String, Room> roomMap) {
        Room room = roomMap.get(roomId);
        if (Objects.nonNull(room)){
            searchRoomReportDTO.setRoomNo(room.getRoomNo());
            searchRoomReportDTO.setUid(room.getUid());
        }
    }

    private  Map<String,Long> getLongestBroadcastUid(List<SearchRoomReportDTO> currentDayHistoryRoomList, Map<String, Room> roomMap) {
        Map<String,Long> map = new HashMap<>();
        for (SearchRoomReportDTO searchRoomReportDTO : currentDayHistoryRoomList) {
            Long beginTimestamp = searchRoomReportDTO.getBeginTimestamp();
            Long endTimestamp = searchRoomReportDTO.getEndTimestamp();
            Date beginTime = new Date(beginTimestamp);
            Date endTime = new Date(endTimestamp);
            String roomId = searchRoomReportDTO.getRoomId();

            Set<Long> allManagerMembers = getAllMembers(roomId, roomMap);
            long limitTimestamp = (endTimestamp - beginTimestamp) / 1000l;
            Long longestBroadcastUid = roomMicRecordService.getMaxRoomMicUid(beginTime, endTime, roomId, limitTimestamp,allManagerMembers);
            map.put(searchRoomReportDTO.getRoomBatchId(),longestBroadcastUid);
        }
        return map;
    }

    public Set<Long> getAllMembers(String roomId, Map<String, Room> roomMap) {
        Set<Long> allMembers = new HashSet<>();
        //管理员
        Set<String> members = redissonManager.sMembers(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId)));
        if (CollectionUtils.isNotEmpty(members)){
            Set<Long> uidSet = members.stream().map(e->Long.valueOf(e)).collect(Collectors.toSet());
            allMembers.addAll(uidSet);
        }
        Room room = roomMap.get(roomId);
        if (Objects.nonNull(room)){
            Long uid = room.getUid();
            allMembers.add(uid);
        }
        return allMembers;
    }

    private Map<String,Integer> getBatchRedisMsg(List<String> roomBatchIds,RoomRedisKey roomRedisKey) {
        List<List<String>> partition = Lists.partition(roomBatchIds, 20);
        Map<String,Integer> map = new HashMap<>();
        for (List<String> partitionRoomBatchIds : partition) {
            // 批量活跃时间
            RBatch batch = redissonClient.createBatch();
            partitionRoomBatchIds.forEach(batchId -> batch.getBucket(roomRedisKey.getKey(String.valueOf(batchId))).getAsync());
            List<?> responses = batch.execute().getResponses();

            int uidIndex = 0;
            for (String roomBatchId : partitionRoomBatchIds) {
                Object count = responses.get(uidIndex);
                uidIndex++;
                if (Objects.nonNull(count)){
                    Integer countInt = Integer.valueOf(count.toString());
                    map.put(roomBatchId,countInt);
                }
            }
        }

        return map;
    }

    private Map<String,Integer> getBatchUvCount(List<String> roomBatchIds,RoomRedisKey roomRedisKey) {
        Map<String,Integer> map = new HashMap<>();
        for (String roomBatchId : roomBatchIds) {
            int count = redissonManager.sCard(roomRedisKey.getKey(roomBatchId));
            map.put(roomBatchId,count);
        }
        return map;
    }

    private Map<String,MicRecordDTO> getMicRecord(List<SearchRoomReportDTO> currentDayHistoryRoomList) {
        Map<String,MicRecordDTO> upMicRecordMap = new HashMap<>();
        for (SearchRoomReportDTO searchRoomReportDTO : currentDayHistoryRoomList) {
            Long beginTimestamp = searchRoomReportDTO.getBeginTimestamp();
            Long endTimestamp = searchRoomReportDTO.getEndTimestamp();
            Date beginTime = DateTimeUtil.getDateByTimestamp(beginTimestamp,TimeZone.getTimeZone("GMT"));
            Date endTime = DateTimeUtil.getDateByTimestamp(endTimestamp,TimeZone.getTimeZone("GMT"));

            String roomId = searchRoomReportDTO.getRoomId();
            long limitTimestamp = (endTimestamp - beginTimestamp) / 1000l;
            MicRecordDTO micRecordDTO = roomMicRecordService.getRoomMicRecord(beginTime, endTime, roomId, limitTimestamp);
            if (Objects.isNull(micRecordDTO)){
                continue;
            }

            Long totalTime = micRecordDTO.getTotalTime();
            if (Objects.nonNull(totalTime)){
                Integer inMicUv = micRecordDTO.getInMicUv();
                BigDecimal totalTimeVar = BigDecimal.valueOf(totalTime);
                BigDecimal inMicUvVar = BigDecimal.valueOf(inMicUv);
                long oneMinuteTimestamp = 60l;
                Double avgStayMinutes = totalTimeVar.divide(inMicUvVar, 1, BigDecimal.ROUND_HALF_UP)
                        .divide(BigDecimal.valueOf(oneMinuteTimestamp), 1, BigDecimal.ROUND_HALF_UP).doubleValue();
                micRecordDTO.setAvgUpMicMin(avgStayMinutes);
            }
            upMicRecordMap.put(searchRoomReportDTO.getRoomBatchId(), micRecordDTO);
        }
        return upMicRecordMap;
    }

    private Map<String,RoomRecordDTO> getUserInRoomRecord(List<SearchRoomReportDTO> currentDayHistoryRoomList) {
        Map<String,RoomRecordDTO> inRoomRecordMap = new HashMap<>();
        for (SearchRoomReportDTO searchRoomReportDTO : currentDayHistoryRoomList) {
            Long beginTimestamp = searchRoomReportDTO.getBeginTimestamp();
            Long endTimestamp = searchRoomReportDTO.getEndTimestamp();
            Long limitTimestamp =  (endTimestamp - beginTimestamp) / 1000l;
            Date beginTime = DateTimeUtil.getDateByTimestamp(beginTimestamp,TimeZone.getTimeZone("GMT"));
            Date endTime = DateTimeUtil.getDateByTimestamp(endTimestamp,TimeZone.getTimeZone("GMT"));
            String roomId = searchRoomReportDTO.getRoomId();
            RoomRecordDTO inRoomRecord = roomUserInRecordService.getInRoomRecord(beginTime, endTime, roomId, limitTimestamp);
            if (Objects.isNull(inRoomRecord)){
                continue;
            }

            Long totalTime = inRoomRecord.getTotalTime();
            if (Objects.nonNull(totalTime)){
                Integer inRoomUv = inRoomRecord.getInRoomUv();
                BigDecimal totalTimeVar = BigDecimal.valueOf(totalTime);
                BigDecimal inRoomUvVar = BigDecimal.valueOf(inRoomUv);
                long oneMinuteTimestamp = 60l;
                Double avgStayMinutes = totalTimeVar.divide(inRoomUvVar, 1, BigDecimal.ROUND_HALF_UP)
                        .divide(BigDecimal.valueOf(oneMinuteTimestamp), 1, BigDecimal.ROUND_HALF_UP).doubleValue();
                inRoomRecord.setAvgStayMinutes(avgStayMinutes);
            }
            inRoomRecordMap.put(searchRoomReportDTO.getRoomBatchId(), inRoomRecord);
        }
        return inRoomRecordMap;
    }



}
