package com.simi.service.agora;

import io.agora.media.RtcTokenBuilder2;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AgoraService {

    public String getAgoraToken(String channel,Long uid) {
        //服务器需要配置环境变量
        String appId = System.getenv("AGORA_APP_ID");
        String appCertificate = System.getenv("AGORA_APP_CERTIFICATE");
        // Token 的有效时间，单位秒
        int tokenExpirationInSeconds = 3600;
        // 所有的权限的有效时间，单位秒，声网建议你将该参数和 Token 的有效时间设为一致
        int privilegeExpirationInSeconds = 3600;
        log.info("App Id: {}", appId);
        log.info("App Certificate: {}", appCertificate);
        if (appId == null || appId.isEmpty() || appCertificate == null || appCertificate.isEmpty()) {
            log.info("Need to set environment variable AGORA_APP_ID and AGORA_APP_CERTIFICATE");
            return null;
        }
        // 生成 Token
        RtcTokenBuilder2 token = new RtcTokenBuilder2();
        String result = token.buildTokenWithUid(appId, appCertificate, channel, uid.intValue(), RtcTokenBuilder2.Role.ROLE_SUBSCRIBER, tokenExpirationInSeconds, privilegeExpirationInSeconds);
        log.info("Token with uid: {}", result);
        return result;
    }

}
