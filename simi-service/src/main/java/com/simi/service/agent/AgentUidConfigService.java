package com.simi.service.agent;

import com.alibaba.excel.util.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.agent.AgentCommissionConfig;
import com.simi.entity.agent.AgentUidConfig;
import com.simi.mapper.agent.AgentUidConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 代理UID配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentUidConfigService extends ServiceImpl<AgentUidConfigMapper, AgentUidConfig> {

    private final AgentCommissionConfigService agentCommissionConfigService;

    public void save(Long uid, String configIds) {
        if (StringUtils.isEmpty(configIds)) {
            List<AgentCommissionConfig> agentConfigs = agentCommissionConfigService.getAgentConfigs(0L);
            List<Long> configIdList = agentConfigs.stream().map(AgentCommissionConfig::getId).toList();
            configIds = configIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        }
        AgentUidConfig agentUidConfig = getById(uid);
        if (agentUidConfig == null) {
            AgentUidConfig config = new AgentUidConfig();
            config.setUid(uid);
            config.setConfigIds(configIds);
            config.setCreateTime(new Date());
            config.setUpdateTime(new Date());
            save(config);
        } else {
            agentUidConfig.setConfigIds(configIds);
            agentUidConfig.setUpdateTime(new Date());
            updateById(agentUidConfig);
        }
    }


    public List<Long> getConfigIdsByUid(Long uid){
        AgentUidConfig agentUidConfig = getById(uid);
        if (agentUidConfig == null) {
            return Collections.emptyList();
        }
        return Arrays.stream(agentUidConfig.getConfigIds().split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());
    }

    /**
     * 获取代理的配置信息
     * @param uid
     * @return
     */
    public List<AgentCommissionConfig> getConfigs(Long uid) {
        List<Long> configIdsByUid = getConfigIdsByUid(uid);
        return agentCommissionConfigService.getConfigs(configIdsByUid);
    }




}
