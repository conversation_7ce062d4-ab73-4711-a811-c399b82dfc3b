package com.simi.service.agent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.agent.AgentCommissionConfig;
import com.simi.mapper.agent.AgentCommissionConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 代理分佣配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentCommissionConfigService extends ServiceImpl<AgentCommissionConfigMapper, AgentCommissionConfig> {


}
