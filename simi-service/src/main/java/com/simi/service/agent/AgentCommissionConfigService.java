package com.simi.service.agent;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.agent.AgentCommissionConfig;
import com.simi.mapper.agent.AgentCommissionConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;


/**
 * 代理分佣配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentCommissionConfigService extends ServiceImpl<AgentCommissionConfigMapper, AgentCommissionConfig> {

    /**
     * 根据代理UID和金额查询最匹配的佣金配置
     *
     * @param agentUid 代理UID
     * @param amount 金额
     * @return 最匹配的佣金配置，如果没有找到则返回null
     */
    public AgentCommissionConfig findBestMatchConfig(Long agentUid, BigDecimal amount) {
        if (agentUid == null || amount == null) {
            log.warn("查询佣金配置参数不能为空: agentUid={}, amount={}", agentUid, amount);
            return null;
        }

        try {
            // 1. 首先尝试直接查找匹配的配置
            AgentCommissionConfig matchedConfig = baseMapper.findMatchingConfig(agentUid, amount);
            if (matchedConfig != null) {
                return matchedConfig;
            }
            // 2. 如果没有找到匹配的配置，说明金额超过了所有配置的最大值
            // 查找该代理下佣金比例最高的配置
            matchedConfig = baseMapper.findHighestRateConfig(agentUid);
            return matchedConfig;

        } catch (Exception e) {
            log.error("查询代理佣金配置失败: agentUid={}, amount={}", agentUid, amount, e);
            return null;
        }
    }

    /**
     * 根据代理UID和金额计算佣金
     *
     * @param agentUid 代理UID
     * @param amount 金额
     * @return 计算出的佣金金额，如果没有匹配的配置则返回0
     */
    public BigDecimal calculateCommission(Long agentUid, BigDecimal amount) {
        AgentCommissionConfig config = findBestMatchConfig(agentUid, amount);
        if (config == null || config.getCommissionRate() == null) {
            return BigDecimal.ZERO;
        }

        // 计算佣金：金额 * 佣金比例
        BigDecimal commission = amount.multiply(config.getCommissionRate());
        log.info("代理UID: {} 金额: {} 佣金比例: {}% 计算佣金: {}",
            agentUid, amount, config.getCommissionRate().multiply(BigDecimal.valueOf(100)), commission);

        return commission;
    }

    /**
     * 获取代理的最高佣金比例配置
     *
     * @param agentUid 代理UID
     * @return 最高佣金比例的配置，如果没有找到则返回null
     */
    public AgentCommissionConfig getHighestRateConfig(Long agentUid) {
        if (agentUid == null) {
            return null;
        }

        return baseMapper.findHighestRateConfig(agentUid);
    }

    /**
     * 获取代理的所有佣金配置（按最小金额排序）
     *
     * @param agentUid 代理UID
     * @return 佣金配置列表
     */
    public List<AgentCommissionConfig> getAgentConfigs(Long agentUid) {
        if (agentUid == null) {
            return List.of();
        }

        return baseMapper.findAgentConfigs(agentUid);
    }

    /**
     * 根据配置ID列表和金额查询所有匹配的配置（保留原方法，用于兼容）
     *
     * @param ids 配置ID列表
     * @param amount 金额（暂未使用，保留用于扩展）
     * @return 配置列表
     */
    public AgentCommissionConfig getConfigs(List<Long> ids, BigDecimal amount) {
        List<AgentCommissionConfig> agentCommissionConfigs = new ArrayList<>();
        if (CollectionUtils.isEmpty(ids)) {
            LambdaQueryWrapper<AgentCommissionConfig> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AgentCommissionConfig::getAgentUid, 0L);
            agentCommissionConfigs = list(wrapper);
        }else {
            agentCommissionConfigs = listByIds(ids);
        }

        if (CollectionUtils.isEmpty(agentCommissionConfigs)) {
            return null;
        }

        for (AgentCommissionConfig agentCommissionConfig : agentCommissionConfigs) {
            if (agentCommissionConfig.getMinAmount().compareTo(amount) <= 0 && agentCommissionConfig.getMaxAmount().compareTo(amount) >= 0) {
                return  agentCommissionConfig;
            }
        }
        //获取比例最大的一条
        return agentCommissionConfigs.stream()
                .filter(config -> config.getCommissionRate() != null)
                .max(Comparator.comparing(AgentCommissionConfig::getCommissionRate))
                .orElse(null);


    }

}
