package com.simi.service.agent.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.agent.AgentCommissionConfig;
import com.simi.mapper.agent.AgentCommissionConfigMapper;
import com.simi.service.agent.AgentCommissionConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 代理分佣配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AgentCommissionConfigService extends ServiceImpl<AgentCommissionConfigMapper, AgentCommissionConfig> 

    private final AgentCommissionConfigMapper agentCommissionConfigMapper;

    @Override
    public List<AgentCommissionConfig> getByAgentUid(Long agentUid) {
        if (agentUid == null) {
            return List.of();
        }
        return agentCommissionConfigMapper.selectByAgentUid(agentUid);
    }

    @Override
    public BigDecimal getCommissionRate(Long agentUid, BigDecimal amount) {
        if (agentUid == null || amount == null) {
            return null;
        }
        
        AgentCommissionConfig config = agentCommissionConfigMapper.selectByAgentUidAndAmount(agentUid, amount);
        return config != null ? config.getCommissionRate() : null;
    }

    @Override
    public BigDecimal calculateCommission(Long agentUid, BigDecimal amount) {
        BigDecimal rate = getCommissionRate(agentUid, amount);
        if (rate == null || amount == null) {
            return BigDecimal.ZERO;
        }
        
        return amount.multiply(rate).setScale(2, BigDecimal.ROUND_HALF_UP);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addConfig(AgentCommissionConfig config) {
        if (config == null) {
            log.warn("分佣配置不能为空");
            return false;
        }
        
        // 验证必要字段
        if (config.getAgentUid() == null || config.getMinAmount() == null || config.getCommissionRate() == null) {
            log.warn("分佣配置必要字段不能为空");
            return false;
        }
        
        // 验证分佣比例范围
        if (config.getCommissionRate().compareTo(BigDecimal.ZERO) <= 0 || 
            config.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
            log.warn("分佣比例必须在0-1之间");
            return false;
        }
        
        // 验证金额区间
        if (!validateAmountRange(config.getAgentUid(), config.getMinAmount(), config.getMaxAmount(), null)) {
            log.warn("金额区间重叠或无效");
            return false;
        }
        
        // 设置创建时间
        config.setCreateTime(new Date());
        
        return save(config);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateConfig(AgentCommissionConfig config) {
        if (config == null || config.getId() == null) {
            log.warn("分佣配置或ID不能为空");
            return false;
        }
        
        // 验证必要字段
        if (config.getAgentUid() == null || config.getMinAmount() == null || config.getCommissionRate() == null) {
            log.warn("分佣配置必要字段不能为空");
            return false;
        }
        
        // 验证分佣比例范围
        if (config.getCommissionRate().compareTo(BigDecimal.ZERO) <= 0 || 
            config.getCommissionRate().compareTo(BigDecimal.ONE) > 0) {
            log.warn("分佣比例必须在0-1之间");
            return false;
        }
        
        // 验证金额区间（排除自己）
        if (!validateAmountRange(config.getAgentUid(), config.getMinAmount(), config.getMaxAmount(), config.getId())) {
            log.warn("金额区间重叠或无效");
            return false;
        }
        
        return updateById(config);
    }

    @Override
    public boolean validateAmountRange(Long agentUid, BigDecimal minAmount, BigDecimal maxAmount, Long excludeId) {
        if (agentUid == null || minAmount == null) {
            return false;
        }
        
        // 验证最小金额不能为负数
        if (minAmount.compareTo(BigDecimal.ZERO) < 0) {
            return false;
        }
        
        // 验证最大金额必须大于最小金额
        if (maxAmount != null && maxAmount.compareTo(minAmount) < 0) {
            return false;
        }
        
        // 检查是否与现有配置重叠
        int overlappingCount = agentCommissionConfigMapper.countOverlappingRanges(agentUid, minAmount, maxAmount, excludeId);
        return overlappingCount == 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteByAgentUid(Long agentUid) {
        if (agentUid == null) {
            return false;
        }
        
        LambdaQueryWrapper<AgentCommissionConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AgentCommissionConfig::getAgentUid, agentUid);
        
        return remove(wrapper);
    }
}
