package com.simi.service.agent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.agent.AgentAffiliation;
import com.simi.mapper.agent.AgentAffiliationMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class AgentAffiliationService extends ServiceImpl<AgentAffiliationMapper, AgentAffiliation> {

    public AgentAffiliation getAgentAffiliation(Long uid,Integer roleType) {
        LambdaQueryWrapper<AgentAffiliation> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AgentAffiliation::getUid, uid)
                .eq(AgentAffiliation::getRoleType, roleType);
        return getOne(queryWrapper);
    }
}
