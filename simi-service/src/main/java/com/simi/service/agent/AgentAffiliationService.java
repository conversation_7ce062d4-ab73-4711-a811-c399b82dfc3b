package com.simi.service.agent;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.agent.AgentAffiliation;
import com.simi.mapper.agent.AgentAffiliationMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

@Slf4j
@Component
@RequiredArgsConstructor
public class AgentAffiliationService extends ServiceImpl<AgentAffiliationMapper, AgentAffiliation> {

    /**
     * 获取当前代理的直属主播 + 所有下一级代理的直属主播 UID 列表（仅 2 层）
     * A（当前代理）
     * ├─ 主播1       ← 第1层
     * ├─ B（代理）
     * │  └─ 主播2    ← 第2层
     */
    public List<Long> getAgentAll2(Long agentUid) {
        Set<Long> allHostUids = new HashSet<>();

        // === 第1层：当前代理的直属主播 ===
        List<Long> directHostUids = list(
                Wrappers.<AgentAffiliation>lambdaQuery()
                        .eq(AgentAffiliation::getParentUid, agentUid)
                        .eq(AgentAffiliation::getRoleType, 2)
        ).stream().map(AgentAffiliation::getUid).toList();

        allHostUids.addAll(directHostUids);

        // === 查询当前代理的直属子代理 ===
        List<Long> childAgentUids = list(
                Wrappers.<AgentAffiliation>lambdaQuery()
                        .eq(AgentAffiliation::getParentUid, agentUid)
                        .eq(AgentAffiliation::getRoleType, 1)
        ).stream().map(AgentAffiliation::getUid).toList();

        // === 第2层：子代理的直属主播 ===
        if (!childAgentUids.isEmpty()) {
            List<Long> secondLevelHostUids = list(
                    Wrappers.<AgentAffiliation>lambdaQuery()
                            .in(AgentAffiliation::getParentUid, childAgentUids)
                            .eq(AgentAffiliation::getRoleType, 2)
            ).stream().map(AgentAffiliation::getUid).toList();

            allHostUids.addAll(secondLevelHostUids);
        }

        return new ArrayList<>(allHostUids);
    }

    /**
     * 获取当前代理的直属主播 + 所有下级代理的直属主播 UID 列表
     * A（当前代理）
     * ├─ 主播1
     * ├─ B（代理）
     * │  ├─ 主播2
     * │  └─ C（代理）
     * │     └─ 主播3
     */
    public List<Long> getAgentAll(Long agentUid) {
        Set<Long> allHostUids = new HashSet<>();      // 所有主播UID
        Set<Long> visitedAgents = new HashSet<>();    // 防止代理重复
        Queue<Long> queue = new LinkedList<>();

        // 初始化队列和访问记录
        queue.add(agentUid);
        visitedAgents.add(agentUid);

        while (!queue.isEmpty()) {
            Long currentAgentUid = queue.poll();

            // 查询当前代理直属主播（role_type = 2）
            List<Long> hostUids = list(
                    Wrappers.<AgentAffiliation>lambdaQuery()
                            .eq(AgentAffiliation::getParentUid, currentAgentUid)
                            .eq(AgentAffiliation::getRoleType, 2)
            ).stream().map(AgentAffiliation::getUid).toList();

            allHostUids.addAll(hostUids);

            // 查询当前代理的下级代理（role_type = 1）
            List<Long> childAgentBids = list(
                    Wrappers.<AgentAffiliation>lambdaQuery()
                            .eq(AgentAffiliation::getParentUid, currentAgentUid)
                            .eq(AgentAffiliation::getRoleType, 1)
            ).stream().map(AgentAffiliation::getUid).toList();

            for (Long childUid : childAgentBids) {
                if (visitedAgents.add(childUid)) {
                    queue.add(childUid);
                }
            }
        }

        return new ArrayList<>(allHostUids);
    }

}
