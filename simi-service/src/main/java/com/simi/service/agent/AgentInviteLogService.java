package com.simi.service.agent;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.util.RandomCode;
import com.simi.common.vo.AgentSendsInvite;
import com.simi.common.vo.AgentSendsVerificationCode;
import com.simi.common.vo.agent.AgentDataResp;
import com.simi.common.vo.agent.InviteJoinReq;
import com.simi.common.vo.agent.InviteOperationReq;
import com.simi.common.vo.agent.SendInviteReq;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.redisKey.AgentRedisKey;
import com.simi.entity.agent.AgentAffiliation;
import com.simi.entity.agent.AgentInviteLog;
import com.simi.entity.invite.InviteBinding;
import com.simi.mapper.agent.AgentInviteLogMapper;
import com.simi.service.LongLinkService;
import com.simi.service.invite.InviteBindingService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class AgentInviteLogService extends ServiceImpl<AgentInviteLogMapper, AgentInviteLog> {

    private final AgentCache agentCache;
    private final LongLinkService longLinkService;
    private final AgentAffiliationService agentAffiliationService;
    private final InviteBindingService inviteBindingService;

    public AgentDataResp data(Long currentUid, Date startTime, Date endTime, Integer page) {
        AgentDataResp agentDataResp = new AgentDataResp();
        int pageSize = 20;
        int offset = (page != null && page > 0) ? (page - 1) * pageSize : 0;

        try {
            // === 查询邀请的普通用户 ===
            LambdaQueryWrapper<InviteBinding> queryWrapper = Wrappers.<InviteBinding>lambdaQuery()
                    .eq(InviteBinding::getInviteUid, currentUid)
                    .eq(InviteBinding::getStatus, 2)
                    .isNotNull(InviteBinding::getInviteUid)
                    .ge(startTime != null, InviteBinding::getCreateTime, startTime)
                    .le(endTime != null, InviteBinding::getCreateTime, endTime)
                    .orderByDesc(InviteBinding::getCreateTime) // 可选：按时间倒序
                    .last("LIMIT " + offset + ", " + pageSize); // 手动分页
            List<InviteBinding> inviteBindings = inviteBindingService.list(queryWrapper);
            for (InviteBinding inviteBinding : inviteBindings) {
                //包装用户数据
                UserBaseInfoDTO userBaseInfoDTO = agentCache.userServerService.getUserBaseInfo(inviteBinding.getInvitedUid());
                AgentDataResp.InviteDataResp inviteDataResp = new AgentDataResp.InviteDataResp();
                inviteDataResp.setNick(userBaseInfoDTO.getNick());
                inviteDataResp.setUid(userBaseInfoDTO.getUid());
                inviteDataResp.setAvatar(userBaseInfoDTO.getAvatar());
                agentDataResp.getInviteDataRespList().add(inviteDataResp);
            }
            LambdaQueryWrapper<InviteBinding> queryWrapper2 = Wrappers.<InviteBinding>lambdaQuery()
                    .eq(InviteBinding::getInviteUid, currentUid)
                    .eq(InviteBinding::getStatus, 2)
                    .isNotNull(InviteBinding::getInviteUid);
            List<InviteBinding> inviteBindings2 = inviteBindingService.list(queryWrapper2);
            agentDataResp.setUserNum(inviteBindings2.size());

            // === 查询邀主播数据 ===
            LambdaQueryWrapper<AgentAffiliation> queryWrapper3 = Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getParentUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, 2)
                    .ge(startTime != null, AgentAffiliation::getCreateTime, startTime)
                    .le(endTime != null, AgentAffiliation::getCreateTime, endTime)
                    .orderByDesc(AgentAffiliation::getCreateTime) // 可选：按时间倒序
                    .last("LIMIT " + offset + ", " + pageSize); // 手动分页
            List<AgentAffiliation> agentAffiliations = agentAffiliationService.list(queryWrapper3);
            for (AgentAffiliation agentAffiliation : agentAffiliations) {
                //包装用户数据
                UserBaseInfoDTO userBaseInfoDTO = agentCache.userServerService.getUserBaseInfo(agentAffiliation.getUid());
                AgentDataResp.HostDataResp hostDataResp = new AgentDataResp.HostDataResp();
                hostDataResp.setNick(userBaseInfoDTO.getNick());
                hostDataResp.setUid(userBaseInfoDTO.getUid());
                hostDataResp.setAvatar(userBaseInfoDTO.getAvatar());
                agentDataResp.getHostDataRespList().add(hostDataResp);
            }
            LambdaQueryWrapper<AgentAffiliation> queryWrapper4 = Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getParentUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, 2); // 手动分页
            List<AgentAffiliation> agentAffiliations1 = agentAffiliationService.list(queryWrapper4);
            agentDataResp.setHostNum(agentAffiliations1.size());

            // === 查询代理数据 ===
            LambdaQueryWrapper<AgentAffiliation> queryWrapper5 = Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getParentUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, 1)
                    .ge(startTime != null, AgentAffiliation::getCreateTime, startTime)
                    .le(endTime != null, AgentAffiliation::getCreateTime, endTime)
                    .orderByDesc(AgentAffiliation::getCreateTime) // 可选：按时间倒序
                    .last("LIMIT " + offset + ", " + pageSize); // 手动分页
            List<AgentAffiliation> agentAffiliations3 = agentAffiliationService.list(queryWrapper5);
            for (AgentAffiliation agentAffiliation : agentAffiliations3) {
                //包装用户数据
                UserBaseInfoDTO userBaseInfoDTO = agentCache.userServerService.getUserBaseInfo(agentAffiliation.getUid());
                AgentDataResp.HostDataResp hostDataResp = new AgentDataResp.HostDataResp();
                hostDataResp.setNick(userBaseInfoDTO.getNick());
                hostDataResp.setUid(userBaseInfoDTO.getUid());
                hostDataResp.setAvatar(userBaseInfoDTO.getAvatar());
                agentDataResp.getHostDataRespList().add(hostDataResp);
            }
            LambdaQueryWrapper<AgentAffiliation> queryWrapper6 = Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getParentUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, 1); // 手动分页
            List<AgentAffiliation> agentAffiliations4 = agentAffiliationService.list(queryWrapper6);
            agentDataResp.setAgentNum(agentAffiliations4.size());

            // === 查询申请数据 ===
            LambdaQueryWrapper<AgentInviteLog> queryWrapper7 = Wrappers.<AgentInviteLog>lambdaQuery()
                    .eq(AgentInviteLog::getInviterUid, currentUid)
                    .ge(startTime != null, AgentInviteLog::getCreateTime, startTime)
                    .le(endTime != null, AgentInviteLog::getCreateTime, endTime)
                    .orderByDesc(AgentInviteLog::getCreateTime) // 可选：按时间倒序
                    .last("LIMIT " + offset + ", " + pageSize); // 手动分页
            List<AgentInviteLog> agentInviteLogs = this.list(queryWrapper7);
            for (AgentInviteLog agentInviteLog : agentInviteLogs) {
                //包装用户数据
                UserBaseInfoDTO userBaseInfoDTO = agentCache.userServerService.getUserBaseInfo(agentInviteLog.getInviteeUid());
                AgentDataResp.ApplyDataResp applyDataResp = new AgentDataResp.ApplyDataResp();
                applyDataResp.setNick(userBaseInfoDTO.getNick());
                applyDataResp.setUid(userBaseInfoDTO.getUid());
                applyDataResp.setAvatar(userBaseInfoDTO.getAvatar());
                agentDataResp.getApplyDataRespList().add(applyDataResp);
            }

        } catch (Exception e) {
            log.error("异常 {}", e.getMessage());
        }

        return agentDataResp;
    }

    public void operation(Long currentUid, InviteOperationReq req) {
        if (req.getType() == 2) {
            //如果拒绝的话
            String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + req.getFoUserId();
            String toKey = AgentRedisKey.generate_invite_code_to_.getName() + currentUid;
            agentCache.redissonManager.hDel(fromKey, currentUid.toString());
            agentCache.redissonManager.hDel(toKey, req.getFoUserId().toString());
            //更新存在的状态
            this.lambdaUpdate()
                    .eq(AgentInviteLog::getInviterUid, req.getFoUserId())
                    .eq(AgentInviteLog::getInviteeUid, currentUid)
                    .eq(AgentInviteLog::getInviteStatus, 0)
                    .set(AgentInviteLog::getConfirmTime, LocalDateTime.now())
                    .set(AgentInviteLog::getInviteStatus, 2)
                    .update();
            return;
        }
        AgentAffiliation agentAffiliation = agentAffiliationService.getOne(Wrappers.<AgentAffiliation>lambdaQuery()
                .eq(AgentAffiliation::getUid, currentUid));
        if (agentAffiliation != null) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS4);
        }
    }

    public void join(Long currentUid, InviteJoinReq req) {
        //更新存在的状态
        this.lambdaUpdate()
                .eq(AgentInviteLog::getInviterUid, req.getFoUserId())
                .eq(AgentInviteLog::getInviteeUid, currentUid)
                .eq(AgentInviteLog::getInviteStatus, 0)
                .set(AgentInviteLog::getConfirmTime, LocalDateTime.now())
                .set(AgentInviteLog::getInviteStatus, 1)
                .update();

        //绑定关系
        AgentAffiliation agentAffiliation2 = agentAffiliationService.getOne(Wrappers.<AgentAffiliation>lambdaQuery()
                .eq(AgentAffiliation::getUid, req.getFoUserId()));
        AgentAffiliation agentAffiliation = new AgentAffiliation();
        agentAffiliation.setUid(currentUid);
        agentAffiliation.setRoleType(req.getRoleType());
        agentAffiliation.setParentUid(req.getFoUserId());
        agentAffiliation.setLevel(agentAffiliation2 == null ? 1 : agentAffiliation2.getLevel() + 1);
        agentAffiliation.setCreateTime(LocalDateTime.now());
        agentAffiliationService.save(agentAffiliation);
    }

    public void sendCode(Long currentUid, Long uid) {
        if (currentUid.equals(uid)) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS3);
        }

        if (uid == null) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS2);
        }

        String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + currentUid;
        String toKey = AgentRedisKey.generate_invite_code_to_.getName() + uid;

        // 判断当前用户是否已经邀请过该用户
        if (agentCache.redissonManager.hGet(fromKey, uid.toString()) != null) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS);
        }

        // 生成随机邀请码
        String inviteCode = RandomCode.getCode();

        // 存储当前用户邀请记录，设置有效期24小时
        agentCache.redissonManager.hSet(fromKey, uid.toString(), inviteCode, 1, TimeUnit.DAYS);

        // 存储被邀请人对应的记录，设置有效期24小时
        agentCache.redissonManager.hSet(toKey, currentUid.toString(), inviteCode, 1, TimeUnit.DAYS);

        //发送长连接给客户端
        AgentSendsVerificationCode inviteeDTO = new AgentSendsVerificationCode();
        inviteeDTO.setCode(inviteCode);
        inviteeDTO.setTime(System.currentTimeMillis());
        longLinkService.pushCustomerGlobalMsg(inviteeDTO, String.valueOf(uid), PushEvent.AgentSendsVerificationCode, PushToType.EXCLUDE_OTHER);
    }

    public void sendInvite(Long currentUid, SendInviteReq req) {

        // 判断当前用户是否已经邀请过该用户
        String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + currentUid;
        if (agentCache.redissonManager.hGet(fromKey, req.getUserId().toString()) != null) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS);
        }

        //更新存在的状态
        this.lambdaUpdate()
                .eq(AgentInviteLog::getInviterUid, currentUid)
                .eq(AgentInviteLog::getInviteeUid, req.getUserId())
                .eq(AgentInviteLog::getInviteStatus, 0)
                .set(AgentInviteLog::getConfirmTime, LocalDateTime.now())
                .set(AgentInviteLog::getInviteStatus, 3)
                .update();

        // 发送新的邀请逻辑
        AgentInviteLog newInvite = new AgentInviteLog();
        newInvite.setInviterUid(currentUid);
        newInvite.setInviteeUid(req.getUserId());
        newInvite.setInviteType(req.getType());
        newInvite.setInviteChannel(req.getChannel());
        newInvite.setInviteStatus(0); // 设置为“已发送”
        newInvite.setCreateTime(LocalDateTime.now());
        this.save(newInvite);

        //推送长连接给客户端
        UserBaseInfoDTO userBaseInfoDTO = agentCache.userServerService.getUserBaseInfo(currentUid);
        AgentSendsInvite inviteeDTO = new AgentSendsInvite();
        inviteeDTO.setAvatar(userBaseInfoDTO.getAvatar());
        inviteeDTO.setNick(userBaseInfoDTO.getNick());
        inviteeDTO.setUid(currentUid);
        inviteeDTO.setType(req.getType());
        inviteeDTO.setTime(System.currentTimeMillis());
        longLinkService.pushCustomerGlobalMsg(inviteeDTO, String.valueOf(req.getUserId()), PushEvent.AgentSendsInvite, PushToType.EXCLUDE_OTHER);
    }
}
