package com.simi.service.agent;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.util.RandomCode;
import com.simi.common.vo.AgentSendsInvite;
import com.simi.common.vo.agent.*;
import com.simi.constant.redisKey.AgentRedisKey;
import com.simi.entity.agent.AgentAffiliation;
import com.simi.entity.agent.AgentInviteLog;
import com.simi.entity.invite.InviteBinding;
import com.simi.mapper.agent.AgentInviteLogMapper;
import com.simi.service.LongLinkService;
import com.simi.service.invite.InviteBindingService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@RequiredArgsConstructor
public class AgentInviteLogService extends ServiceImpl<AgentInviteLogMapper, AgentInviteLog> {

    private final AgentCache agentCache;
    private final AgentAffiliationService agentAffiliationService;
    private final InviteBindingService inviteBindingService;
    private final UserServerService userServerService;
    private final NotifyMessageComponent notifyMessageComponent;

    public AgentDataResp data(Long currentUid) {
        AgentDataResp agentDataResp = new AgentDataResp();

        LambdaQueryWrapper<InviteBinding> queryWrapper2 = Wrappers.<InviteBinding>lambdaQuery()
                .eq(InviteBinding::getInviteUid, currentUid)
                .in(InviteBinding::getStatus, 2, 4)
                .isNotNull(InviteBinding::getInviteUid);
        List<InviteBinding> inviteBindings2 = inviteBindingService.list(queryWrapper2);
        agentDataResp.setUserNum(inviteBindings2.size());


        LambdaQueryWrapper<AgentAffiliation> queryWrapper6 = Wrappers.<AgentAffiliation>lambdaQuery()
                .eq(AgentAffiliation::getParentUid, currentUid)
                .eq(AgentAffiliation::getRoleType, 1);
        List<AgentAffiliation> agentAffiliations4 = agentAffiliationService.list(queryWrapper6);
        agentDataResp.setAgentNum(agentAffiliations4.size());


        LambdaQueryWrapper<AgentAffiliation> queryWrapper4 = Wrappers.<AgentAffiliation>lambdaQuery()
                .eq(AgentAffiliation::getParentUid, currentUid)
                .eq(AgentAffiliation::getRoleType, 2);
        List<AgentAffiliation> agentAffiliations1 = agentAffiliationService.list(queryWrapper4);
        agentDataResp.setHostNum(agentAffiliations1.size());


        return agentDataResp;
    }

    public AgentDataResp2 dataList(Long currentUid, Date startTime, Date endTime, Integer page, Integer type) {
        AgentDataResp2 agentDataResp = new AgentDataResp2();
        int pageSize = 20;
        int offset = (page != null && page > 0) ? (page - 1) * pageSize : 0;
        try {
            switch (type) {
                case 1:
                    // === 查询邀请的普通用户 ===
                    LambdaQueryWrapper<InviteBinding> queryWrapper = Wrappers.<InviteBinding>lambdaQuery()
                            .eq(InviteBinding::getInviteUid, currentUid)
                            .in(InviteBinding::getStatus, 2, 4)//状态: 1-未绑定; 2-已绑定; 3-已解绑; 4-手动绑定
                            .isNotNull(InviteBinding::getInviteUid)
                            .ge(startTime != null, InviteBinding::getCreateTime, startTime)
                            .le(endTime != null, InviteBinding::getCreateTime, endTime)
                            .orderByDesc(InviteBinding::getCreateTime) // 可选：按时间倒序
                            .last("LIMIT " + offset + ", " + pageSize); // 手动分页
                    List<InviteBinding> inviteBindings = inviteBindingService.list(queryWrapper);
                    for (InviteBinding inviteBinding : inviteBindings) {
                        //包装用户数据
                        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(inviteBinding.getInvitedUid());
                        AgentDataResp2.InviteDataResp dataResp = new AgentDataResp2.InviteDataResp();
                        dataResp.setNick(userBaseInfoDTO.getNick());
                        dataResp.setUid(userBaseInfoDTO.getUid());
                        dataResp.setAvatar(userBaseInfoDTO.getAvatar());
                        dataResp.setGender(userBaseInfoDTO.getGender());
                        agentDataResp.getInviteDataRespList().add(dataResp);
                    }
                    break;
                case 2:
                    // === 查询邀主播数据 ===
                    LambdaQueryWrapper<AgentAffiliation> queryWrapper3 = Wrappers.<AgentAffiliation>lambdaQuery()
                            .eq(AgentAffiliation::getParentUid, currentUid)
                            .eq(AgentAffiliation::getRoleType, 2)
                            .ge(startTime != null, AgentAffiliation::getCreateTime, startTime)
                            .le(endTime != null, AgentAffiliation::getCreateTime, endTime)
                            .orderByDesc(AgentAffiliation::getCreateTime) // 可选：按时间倒序
                            .last("LIMIT " + offset + ", " + pageSize); // 手动分页
                    List<AgentAffiliation> agentAffiliations = agentAffiliationService.list(queryWrapper3);
                    for (AgentAffiliation agentAffiliation : agentAffiliations) {
                        //包装用户数据
                        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(agentAffiliation.getUid());
                        AgentDataResp2.HostDataResp dataResp = new AgentDataResp2.HostDataResp();
                        dataResp.setNick(userBaseInfoDTO.getNick());
                        dataResp.setUid(userBaseInfoDTO.getUid());
                        dataResp.setAvatar(userBaseInfoDTO.getAvatar());
                        dataResp.setGender(userBaseInfoDTO.getGender());
                        agentDataResp.getHostDataRespList().add(dataResp);
                    }
                    break;
                case 3:
                    // === 查询代理数据 ===
                    LambdaQueryWrapper<AgentAffiliation> queryWrapper5 = Wrappers.<AgentAffiliation>lambdaQuery()
                            .eq(AgentAffiliation::getParentUid, currentUid)
                            .eq(AgentAffiliation::getRoleType, 1)
                            .ge(startTime != null, AgentAffiliation::getCreateTime, startTime)
                            .le(endTime != null, AgentAffiliation::getCreateTime, endTime)
                            .orderByDesc(AgentAffiliation::getCreateTime) // 可选：按时间倒序
                            .last("LIMIT " + offset + ", " + pageSize); // 手动分页
                    List<AgentAffiliation> agentAffiliations3 = agentAffiliationService.list(queryWrapper5);
                    for (AgentAffiliation agentAffiliation : agentAffiliations3) {
                        //包装用户数据
                        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(agentAffiliation.getUid());
                        AgentDataResp2.HostDataResp dataResp = new AgentDataResp2.HostDataResp();
                        dataResp.setNick(userBaseInfoDTO.getNick());
                        dataResp.setUid(userBaseInfoDTO.getUid());
                        dataResp.setAvatar(userBaseInfoDTO.getAvatar());
                        dataResp.setGender(userBaseInfoDTO.getGender());
                        agentDataResp.getHostDataRespList().add(dataResp);
                    }
                    break;
                case 4:
                    // === 查询申请数据 ===
                    LambdaQueryWrapper<AgentInviteLog> queryWrapper7 = Wrappers.<AgentInviteLog>lambdaQuery()
                            .eq(AgentInviteLog::getInviterUid, currentUid)
                            .ge(startTime != null, AgentInviteLog::getCreateTime, startTime)
                            .le(endTime != null, AgentInviteLog::getCreateTime, endTime)
                            .orderByDesc(AgentInviteLog::getCreateTime) // 可选：按时间倒序
                            .last("LIMIT " + offset + ", " + pageSize); // 手动分页
                    List<AgentInviteLog> agentInviteLogs = this.list(queryWrapper7);
                    for (AgentInviteLog agentInviteLog : agentInviteLogs) {
                        //包装用户数据
                        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(agentInviteLog.getInviteeUid());
                        AgentDataResp2.ApplyDataResp dataResp = new AgentDataResp2.ApplyDataResp();
                        dataResp.setId(agentInviteLog.getId());
                        dataResp.setNick(userBaseInfoDTO.getNick());
                        dataResp.setUid(userBaseInfoDTO.getUid());
                        dataResp.setAvatar(userBaseInfoDTO.getAvatar());
                        dataResp.setGender(userBaseInfoDTO.getGender());
                        dataResp.setChannel(agentInviteLog.getInviteChannel());
                        dataResp.setStatus(agentInviteLog.getInviteStatus());
                        agentDataResp.getApplyDataRespList().add(dataResp);
                    }

                    break;
            }
        } catch (Exception e) {
            log.error("异常 {}", e.getMessage());
        }

        return agentDataResp;
    }

    //端外
    public void operation2(Long currentUid, InviteOperationReq2 req) {

        String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + req.getFoUserId();
        String toKey = AgentRedisKey.generate_invite_code_to_.getName() + currentUid;
        agentCache.redissonManager.hDel(fromKey, currentUid.toString());
        agentCache.redissonManager.hDel(toKey, req.getFoUserId().toString());

        //判断是否成为了代理或主播
        agentAffiliation(currentUid, req.getType());

        //更新存在的状态
        this.lambdaUpdate()
                .eq(AgentInviteLog::getInviterUid, req.getFoUserId())
                .eq(AgentInviteLog::getInviteeUid, currentUid)
                .eq(AgentInviteLog::getInviteStatus, 0)
                .set(AgentInviteLog::getConfirmTime, LocalDateTime.now())
                .set(AgentInviteLog::getInviteStatus, 1)
                .update();

        //绑定关系
        AgentAffiliation agentAffiliation = new AgentAffiliation();
        agentAffiliation.setUid(currentUid);
        agentAffiliation.setRoleType(req.getType());
        agentAffiliation.setParentUid(req.getFoUserId());
        agentAffiliation.setLevel(getLevel(currentUid, req.getType()));
        agentAffiliation.setCreateTime(LocalDateTime.now());
        agentAffiliationService.save(agentAffiliation);
    }

    //端内-申请列表
    public void operation3(Long currentUid, InviteOperationReq3 req) {

        AgentInviteLog agentInviteLog = this.getOne(Wrappers.<AgentInviteLog>lambdaQuery()
                .eq(AgentInviteLog::getId, req.getId()));

        String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + currentUid;
        String toKey = AgentRedisKey.generate_invite_code_to_.getName() + agentInviteLog.getInviteeUid();
        agentCache.redissonManager.hDel(fromKey, agentInviteLog.getInviteeUid().toString());
        agentCache.redissonManager.hDel(toKey, currentUid.toString());

        if (req.getOpType() == 2) {
            this.lambdaUpdate()
                    .eq(AgentInviteLog::getId, agentInviteLog.getId())
                    .set(AgentInviteLog::getInviteStatus, 2)
                    .update();
            return;
        }
        //判断是否重复发送-逻辑一
        agentAffiliation(currentUid, agentInviteLog.getInviteType());

        //更新存在的状态
        this.lambdaUpdate()
                .eq(AgentInviteLog::getId, agentInviteLog.getId())
                .set(AgentInviteLog::getInviteStatus, 1)
                .update();

        //绑定关系
        AgentAffiliation agentAffiliation = new AgentAffiliation();
        agentAffiliation.setUid(agentInviteLog.getInviteeUid());
        agentAffiliation.setRoleType(agentInviteLog.getInviteType());
        agentAffiliation.setParentUid(currentUid);
        agentAffiliation.setLevel(getLevel(currentUid, agentAffiliation.getRoleType()));
        agentAffiliation.setCreateTime(LocalDateTime.now());
        agentAffiliationService.save(agentAffiliation);
    }

    //端内-系统界面
    public void operation(Long currentUid, InviteOperationReq req) {

        String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + req.getFoUserId();
        String toKey = AgentRedisKey.generate_invite_code_to_.getName() + currentUid;
        agentCache.redissonManager.hDel(fromKey, currentUid.toString());
        agentCache.redissonManager.hDel(toKey, req.getFoUserId().toString());

        if (req.getOpType() == 2) {
            this.lambdaUpdate()
                    .eq(AgentInviteLog::getInviterUid, req.getFoUserId())
                    .eq(AgentInviteLog::getInviteeUid, currentUid)
                    .eq(AgentInviteLog::getInviteStatus, 0)
                    .set(AgentInviteLog::getConfirmTime, LocalDateTime.now())
                    .set(AgentInviteLog::getInviteStatus, 2)
                    .update();
            return;
        }

        //判断是否成为了代理或主播
        agentAffiliation(currentUid, req.getType());

        //更新存在的状态
        this.lambdaUpdate()
                .eq(AgentInviteLog::getInviterUid, req.getFoUserId())
                .eq(AgentInviteLog::getInviteeUid, currentUid)
                .eq(AgentInviteLog::getInviteStatus, 0)
                .set(AgentInviteLog::getConfirmTime, LocalDateTime.now())
                .set(AgentInviteLog::getInviteStatus, 1)
                .update();

        //绑定关系
        AgentAffiliation agentAffiliation = new AgentAffiliation();
        agentAffiliation.setUid(currentUid);
        agentAffiliation.setRoleType(req.getType());
        agentAffiliation.setParentUid(req.getFoUserId());
        agentAffiliation.setLevel(getLevel(currentUid, req.getType()));
        agentAffiliation.setCreateTime(LocalDateTime.now());
        agentAffiliationService.save(agentAffiliation);
    }

    /**
     * currentUid 当前玩家id
     * uid 被邀请的玩家id
     **/
    public void sendCode(Long currentUid, Long uid) {
        if (currentUid.equals(uid)) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS3);
        }

        if (uid == null) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS2);
        }

        String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + currentUid;
        String toKey = AgentRedisKey.generate_invite_code_to_.getName() + uid;

        // 判断当前用户是否已经邀请过该用户
        if (agentCache.redissonManager.hGet(fromKey, uid.toString()) != null) {
            throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS);
        }

        // 生成随机邀请码
        String inviteCode = RandomCode.getCode();

        // 存储当前用户邀请记录，设置有效期24小时
        agentCache.redissonManager.hSet(fromKey, uid.toString(), inviteCode, 1, TimeUnit.DAYS);

        // 存储被邀请人对应的记录，设置有效期24小时
        agentCache.redissonManager.hSet(toKey, currentUid.toString(), inviteCode, 1, TimeUnit.DAYS);

        //发送系统消息
        String textMsgModelStr = JSONUtil.toJsonStr(inviteCode);
        CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                IMMsgType.AgentSendsVerificationCodeIMMsg,
                System.currentTimeMillis(), textMsgModelStr);
        notifyMessageComponent.publishSystemDefineMessage(imMessage, uid + "", null);
    }

    public void sendInvite(Long currentUid, SendInviteReq req) {

        //如果是端外邀请需要验证码才能邀请
        if (req.getInviteType() == 2) {
            // 判断验证码是否正确
            String fromKey = AgentRedisKey.generate_invite_code_fo_.getName() + currentUid;
            String code = agentCache.redissonManager.hGet(fromKey, req.getUserId().toString());
            if (!req.getCode().equals(Integer.valueOf(code))) {
                throw new ApiException(CodeEnum.SMS_VERIFY_ERROR);
            }
            //删掉验证码
            String fromKey2 = AgentRedisKey.generate_invite_code_to_.getName() + req.getUserId();
            agentCache.redissonManager.hDel(fromKey, req.getUserId().toString());
            agentCache.redissonManager.hDel(fromKey2, currentUid.toString());
        }

        if (req.getType() == 1) {
            //判断层级是否受到限制
            AgentAffiliation agentAffiliation = agentAffiliationService.getOne(Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, req.getType()));
            if (agentAffiliation.getLevel() >= 3) {
                throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS6);
            }

            //判断代理是否受到限制
            List<AgentAffiliation> agentAffiliations = agentAffiliationService.list(Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getParentUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, req.getType()));
            if (agentAffiliations.size() >= 10) {
                throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS7);
            }
        }

        if (req.getType() == 2) {
            //判断代理是否受到限制
            List<AgentAffiliation> agentAffiliations = agentAffiliationService.list(Wrappers.<AgentAffiliation>lambdaQuery()
                    .eq(AgentAffiliation::getParentUid, currentUid)
                    .eq(AgentAffiliation::getRoleType, req.getType()));
            if (agentAffiliations.size() >= 100) {
                throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS8);
            }
        }

        //判断是否成为了代理或主播
        agentAffiliation(currentUid, req.getType());

        //判断是否重复发送-逻辑二
        AgentInviteLog agentAffiliation2 = this.getOne(Wrappers.<AgentInviteLog>lambdaQuery()
                .eq(AgentInviteLog::getInviterUid, currentUid)
                .eq(AgentInviteLog::getInviteeUid, req.getUserId())
                .eq(AgentInviteLog::getInviteType, req.getType())
                .eq(AgentInviteLog::getInviteStatus, 0));
        if (agentAffiliation2 != null) {
            // 当前时间
            LocalDateTime now = LocalDateTime.now();
            // 创建时间
            LocalDateTime createdAt = agentAffiliation2.getCreateTime();
            // 判断是否超过 24 小时
            long hours = ChronoUnit.HOURS.between(createdAt, now);
            if (hours < 24) {
                throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS);
            }
            //成功超过24小时了，重置超时
            else {
                this.lambdaUpdate()
                        .eq(AgentInviteLog::getId, agentAffiliation2.getId())
                        .set(AgentInviteLog::getInviteStatus, 3)
                        .update();
            }
        }

        // 发送新的邀请逻辑
        AgentInviteLog newInvite = new AgentInviteLog();
        newInvite.setInviterUid(currentUid);
        newInvite.setInviteeUid(req.getUserId());
        newInvite.setInviteType(req.getType());
        newInvite.setInviteChannel(req.getChannel());
        newInvite.setInviteStatus(0); // 设置为“已发送”
        newInvite.setCreateTime(LocalDateTime.now());
        this.save(newInvite);

        //发送系统消息
        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(currentUid);
        AgentSendsInvite inviteeDTO = new AgentSendsInvite();
        inviteeDTO.setAvatar(userBaseInfoDTO.getAvatar());
        inviteeDTO.setNick(userBaseInfoDTO.getNick());
        inviteeDTO.setUid(currentUid);
        inviteeDTO.setType(req.getType());
        inviteeDTO.setChannel(req.getChannel());
        String textMsgModelStr = JSONUtil.toJsonStr(inviteeDTO);
        CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                IMMsgType.AgentSendsInviteIMMsg,
                System.currentTimeMillis(), textMsgModelStr);
        notifyMessageComponent.publishSystemDefineMessage(imMessage, req.getUserId() + "", null);
    }

    public void sendApply(Long currentUid, SendApplyReq req) {
        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(currentUid);
        if (userBaseInfoDTO == null) {
            throw new ApiException(CodeEnum.ACCOUNT_NOT_EXIST);
        }
        //判断是否成为了代理或主播
        agentAffiliation(currentUid, req.getType());
        //判断是否重复发送-逻辑二
        AgentInviteLog agentAffiliation2 = this.getOne(Wrappers.<AgentInviteLog>lambdaQuery()
                .eq(AgentInviteLog::getInviterUid, req.getUid())
                .eq(AgentInviteLog::getInviteeUid, currentUid)
                .eq(AgentInviteLog::getInviteType, req.getType())
                .eq(AgentInviteLog::getInviteStatus, 0));
        if (agentAffiliation2 != null) {
            // 当前时间
            LocalDateTime now = LocalDateTime.now();
            // 创建时间
            LocalDateTime createdAt = agentAffiliation2.getCreateTime();
            // 判断是否超过 24 小时
            long hours = ChronoUnit.HOURS.between(createdAt, now);
            if (hours < 24) {
                throw new ApiException(CodeEnum.INVITE_ALREADY_EXISTS9);
            }
            //成功超过24小时了，重置超时
            else {
                this.lambdaUpdate()
                        .eq(AgentInviteLog::getId, agentAffiliation2.getId())
                        .set(AgentInviteLog::getInviteStatus, 3)
                        .update();
            }
        }

        // 发送新的邀请逻辑
        AgentInviteLog newInvite = new AgentInviteLog();
        newInvite.setInviterUid(req.getUid());
        newInvite.setInviteeUid(currentUid);
        newInvite.setInviteType(req.getType());
        newInvite.setInviteChannel(3);
        newInvite.setInviteStatus(0); // 设置为“已发送”
        newInvite.setCreateTime(LocalDateTime.now());
        this.save(newInvite);
    }

    //判断是否存在
    private void agentAffiliation(Long currentUid, Integer type) {
        AgentAffiliation agentAffiliation = agentAffiliationService.getOne(Wrappers.<AgentAffiliation>lambdaQuery()
                .eq(AgentAffiliation::getUid, currentUid)
                .eq(AgentAffiliation::getRoleType, type));
        if (agentAffiliation != null) {
            throw new ApiException(type == 2 ? CodeEnum.INVITE_ALREADY_EXISTS5 : CodeEnum.INVITE_ALREADY_EXISTS4);
        }
    }

    private int getLevel(Long currentUid, Integer type) {
        AgentAffiliation agentAffiliation2 = agentAffiliationService.getOne(Wrappers.<AgentAffiliation>lambdaQuery()
                .eq(AgentAffiliation::getUid, currentUid)
                .eq(AgentAffiliation::getRoleType, type));
        return agentAffiliation2 == null ? 1 : agentAffiliation2.getLevel() + 1;
    }
}