package com.simi.service.agent;

import cn.hutool.core.util.StrUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeUtils;
import com.simi.constant.redisKey.AgentRedisKey;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.stereotype.Component;

import java.util.concurrent.TimeUnit;

@Service
@Component
@RequiredArgsConstructor
public class AgentCache {


    public final RedissonManager redissonManager;


    private String getAgentIncomeKey() {
        String weekFirstDayStr = TimeUtils.getWeekFirstDayStr(TimeUtils.PATTERN_FORMAT_DATE);
        return AgentRedisKey.agent_income.getKey(StrUtil.format("{{}}", weekFirstDayStr));
    }

    private String getAnchorIncomeKey() {
        String weekFirstDayStr = TimeUtils.getWeekFirstDayStr(TimeUtils.PATTERN_FORMAT_DATE);
        return AgentRedisKey.anchor_income.getKey(StrUtil.format("{{}}", weekFirstDayStr));
    }

    /**
     * 主播总收益（存）
     *
     * @param targetUid 主播id
     */
    public void anchorIncome(Long targetUid, Integer amount) {
        redissonManager.hIncrement(getAnchorIncomeKey(), targetUid.toString(), amount);
        redissonManager.expire(getAnchorIncomeKey(), 8, TimeUnit.DAYS);
    }

    /**
     * 获取主播总收益
     *
     * @param targetUid
     */
    public Long getAnchorIncome(Long targetUid) {
        String amount = redissonManager.hGet(getAnchorIncomeKey(), targetUid.toString());
        if (StrUtil.isBlank(amount)) {
            return 0L;
        }
        return Long.parseLong(amount);
    }

    /**
     * 代理总收益（存）
     *
     * @param targetUid 代理id
     */
    public void agentIncome(Long targetUid, Integer amount) {
        redissonManager.hIncrement(getAnchorIncomeKey(), targetUid.toString(), amount);
        redissonManager.expire(getAnchorIncomeKey(), 8, TimeUnit.DAYS);
    }

    /**
     * 获取代理总收益
     *
     * @param targetUid
     * @return
     */
    public Long getAgentIncome(Long targetUid) {
        String amount = redissonManager.hGet(getAgentIncomeKey(), targetUid.toString());
        if (StrUtil.isBlank(amount)) {
            return 0L;
        }
        return Long.parseLong(amount);
    }

}
