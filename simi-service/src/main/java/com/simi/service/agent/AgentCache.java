package com.simi.service.agent;

import cn.hutool.core.util.StrUtil;
import com.simi.common.util.TimeUtils;
import com.simi.constant.redisKey.AgentRedisKey;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class AgentCache {


    private String getAgentIncomeKey(){
        Date date = new Date();
        TimeUtils.secondsBetween()
        return AgentRedisKey.agent_income.getKey(StrUtil.format("{{}}", targetUid));
        //return "agent_income:" + targetUid;
    }

    private String getAnchorIncomeKey(Long targetUid){
        return "anchor_income:" + targetUid;
    }

    /**
     * 主播总收益
     * @param targetUid  主播id
     */
    private void anchorIncome(Long targetUid) {
        //是否主播
    }

    /**
     * 代理总收益
     * @param targetUid  代理id
     */
    private void agentIncome(Long targetUid) {
        //是否代理
    }

}
