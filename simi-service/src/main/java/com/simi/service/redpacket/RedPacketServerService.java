package com.simi.service.redpacket;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.google.common.collect.ImmutableMap;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.audit.AuditManage;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.redpacket.RedPacketEventEnum;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.UserPropInfoDTO;
import com.simi.common.dto.redpacket.RedPacketBannerDTO;
import com.simi.common.dto.redpacket.RedPacketConfigDTO;
import com.simi.common.dto.redpacket.RedPacketLevelConfigDTO;
import com.simi.common.dto.redpacket.RedPacketScreenDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.UserSimpleVO;
import com.simi.common.vo.redpacket.RedPacketGrabVO;
import com.simi.common.vo.redpacket.RedPacketHaveBoxVO;
import com.simi.common.vo.redpacket.RedPacketVO;
import com.simi.common.vo.req.redpacket.SendRedPacketReq;
import com.simi.common.vo.resp.UserLevelBaseVO;
import com.simi.constant.BillEnum;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.redisKey.RedPacketRedisKey;
import com.simi.entity.redpacket.RedPacket;
import com.simi.entity.redpacket.RedPacketMsgDTO;
import com.simi.entity.redpacket.RedPacketRecord;
import com.simi.entity.redpacket.RedPacketRecordMsgDTO;
import com.simi.entity.room.Room;
import com.simi.service.LongLinkService;
import com.simi.service.RocketMqSender;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomService;
import com.simi.service.user.UserServerService;
import com.simi.util.TranslationCopyUtil;
import jakarta.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.alibaba.nacos.client.config.utils.ParamUtils.checkParam;

/**
 * <AUTHOR>
 * @date 2024/09/12 16:01
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RedPacketServerService {

    private final SystemConfigService systemConfigService;
    private final RedissonManager redissonManager;
    private final PurseManageService purseManageService;
    private final RedPacketService redPacketService;
    private final RedPacketRecordService redPacketRecordService;
    private final UserServerService userServerService;
    private final LongLinkService longLinkService;
    private final RocketMqSender rocketMqSender;
    private final NotifyMessageComponent notifyMessageComponent;
    private final RoomService roomService;
    private final AuditManage auditManage;
    private final static String REMAIN_COUNT = "remainCount";
    private final static String REMAIN_MONEY = "remainMoney";
    private final static int ROOM_REDPACKET = 0;
    private final static int WORLD_REDPACKET = 1;

    private final static String SCREEN_MSG_H5 = "<p>{}</p>";
    private final static String NICK_MSG = " <span style=\"color:#17EBAD\">{}</span>";
    private final static String EVENT_MSG = " <a style=\"text-decoration:none\" href=\"nady:///showUserInfo\">";


    public RedPacketConfigDTO configVO(Long uid, XAuthToken xAuthToken, String roomId) {
        boolean visible = false;
        boolean interfere = auditManage.isAudit(uid, xAuthToken);
        //判断是否进入审核数据
        if (interfere) {
            return RedPacketConfigDTO.builder().visible(visible).build();
        }
        //正常返回数据
        else {
            String configStr = systemConfigService.getSysConfValueById(SystemConfigConstant.RED_PACKET_VISIBLE_CONFIG);

            if (StrUtil.isNotBlank(configStr)) {
                UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
                UserLevelBaseVO userLevel = userBaseInfo.getUserLevel();
                RedPacketLevelConfigDTO levelConfigDTO = JSONUtil.toBean(configStr, RedPacketLevelConfigDTO.class);
                if (userLevel.getActiveLevel() >= levelConfigDTO.getActiveLevel()
                        || userLevel.getWealthLevel() >= levelConfigDTO.getWealthLevel()
                        || userLevel.getCharmLevel() >= levelConfigDTO.getCharmLevel()) {
                    visible = true;
                }
            }

            if (!visible) {
                return RedPacketConfigDTO.builder().visible(visible).build();
            }

            RedPacketConfigDTO config = config(roomId);
            config.setVisible(visible);
            return config;
        }
    }

    public RedPacketVO push(SendRedPacketReq req, Long uid) {
        RLock rLock = redissonManager.fairLock(RedPacketRedisKey.red_packet_push_lock.getKey(uid), 3, 10);
        try {
            if (Objects.isNull(rLock) || !rLock.isLocked()) {
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            Date now = new Date();
            DateTime beginTime = DateUtil.offsetSecond(now, req.getCountdown());
            DateTime expireTime = DateUtil.offsetMinute(beginTime, 5);
            String redPacketId = UUID.fastUUID().toString().replace("-", "");

            PurseDTO purse = purseManageService.getPurse(uid);
            if (Objects.isNull(purse) || purse.getCoin() < req.getGold().longValue()) {
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }

            this.checkParam(req, req.getType());

            purseManageService.deductCoin(uid, req.getGold().longValue(), BillEnum.SEND_REDPACKET_DEDUCT_COIN, redPacketId, StrUtil.EMPTY, null, 0L, PurseRoleTypeEnum.USER.getType());

            RedPacket redPacket = RedPacket.builder()
                    .id(redPacketId)
                    .uid(uid)
                    .amount(req.getGold())
                    .type(req.getType())
                    .count(req.getNumberOfRecipients())
                    .roomId(req.getRoomId())
                    .countdown(req.getCountdown())
                    .createTime(now)
                    .beginTime(beginTime)
                    .expireTime(expireTime)
                    .build();
            redPacketService.save(redPacket);

            redissonManager.hSet(RedPacketRedisKey.red_packet.getKey(), redPacketId, JSONUtil.toJsonStr(redPacket));

            ImmutableMap<String, String> map = ImmutableMap.of(REMAIN_COUNT, String.valueOf(req.getNumberOfRecipients()),
                    REMAIN_MONEY, String.valueOf(req.getGold()));

            String key = RedPacketRedisKey.red_packet_remain.getKey(redPacketId);

            redissonManager.hMSet(key, map);

            TranslationCopyDTO translationCopyDTOFirst = TranslationCopyUtil.translationCopy(CopywritingEnum.SEND_LUCKY_BAG_SCREEN_FIRST.getKey(), null);
            TranslationCopyDTO translationCopyDTOSecond = TranslationCopyUtil.translationCopy(CopywritingEnum.SEND_LUCKY_BAG_SCREEN_SECOND.getKey(), null);
            String ar = translationCopyDTOFirst.getAr() + StrUtil.EMPTY + StrUtil.EMPTY + StrUtil.format(NICK_MSG, translationCopyDTOSecond.getAr());
            String en = translationCopyDTOFirst.getEn() + StrUtil.EMPTY + StrUtil.EMPTY + StrUtil.format(NICK_MSG, translationCopyDTOSecond.getEn());

            UserBaseInfoDTO userBaseInfoDTO = userServerService.getFromCache(uid);

            try {
                RedPacketScreenDTO.ScreenData screenData = RedPacketScreenDTO.ScreenData.builder().msgAr(StrUtil.format(SCREEN_MSG_H5, ar)).build();
                UserSimpleVO userSimpleVO = BeanUtil.toBean(userBaseInfoDTO, UserSimpleVO.class);
                RedPacketScreenDTO screenDTO = RedPacketScreenDTO.builder()
                        .from(userSimpleVO)
                        .msg(StrUtil.format(SCREEN_MSG_H5, en))
                        .event(PushEvent.room_screen_lucky_bag_send_event)
                        .msgID(redPacketId)
                        .data(screenData).build();
                longLinkService.pushCustomerRoomMsg(req.getRoomId(), screenDTO, PushEvent.room_screen_lucky_bag_send_event, PushToType.MESSAGE_TO_ALL);
                if (Objects.equals(req.getType(), WORLD_REDPACKET)) {
                    Room room = roomService.getRoom(req.getRoomId());
                    String roomIdStr = null;
                    if (Objects.nonNull(room) && Objects.nonNull(room.getIsLock()) && room.getIsLock()) {
                        roomIdStr = room.getId();
                    }
                    TranslationCopyDTO bannerText = TranslationCopyUtil.translationCopy(CopywritingEnum.SEND_LUCKY_BAG_BANNER.getKey(), null);
                    String effectConfigStr = systemConfigService.getSysConfValueById(SystemConfigConstant.RED_PACKET_EFFECT_CONFIG);
                    Map<String, String> iconMap = MapUtil.empty();
                    if (StrUtil.isNotBlank(effectConfigStr)) {
                        iconMap = JSONUtil.toBean(effectConfigStr, Map.class);
                    }
                    String icon = iconMap.get("icon");
                    String effectUrl = iconMap.get("effectUrl");
                    String upperEffect = iconMap.get("upperEffect");
                    UserSimpleVO userSimple = BeanUtil.toBean(userBaseInfoDTO, UserSimpleVO.class);
                    RedPacketBannerDTO bannerDTO = RedPacketBannerDTO.builder()
                            .userBaseInfo(userSimple)
                            .goldCount(req.getGold().longValue())
                            .desc(bannerText.getEn())
                            .descAr(bannerText.getAr())
                            .effectUrl(effectUrl)
                            .upperEffect(upperEffect)
                            .roomId(req.getRoomId())
                            .icon(icon)
                            .build();
                    longLinkService.pushCustomerRoomBannerList(roomIdStr, bannerDTO, PushEvent.world_lucky_bag_event, PushToType.MESSAGE_TO_ALL);
                }
            } catch (Exception e) {
                log.error("Room push redpacket notify failed, req:[{}] uid:[{}] e:[{}]", JSONUtil.toJsonStr(req), uid, ExceptionUtil.formatEx(e));
            }

            RedPacketMsgDTO settleMsg = RedPacketMsgDTO.builder()
                    .messageId(UUID.fastUUID().toString())
                    .messageTime(System.currentTimeMillis())
                    .redPacketEventEnum(RedPacketEventEnum.SETTLE)
                    .redPacket(redPacket).build();
            long settleMS = DateUtil.between(now, expireTime, DateUnit.MS);
            rocketMqSender.sendDeliverMessage(RocketMQTopic.RED_PACKET_SETTLE_TOPIC, JSONUtil.toJsonStr(settleMsg), settleMS + 2000);

            return RedPacketVO.builder()
                    .id(redPacketId)
                    .deadline(expireTime.getTime())
                    .remainingTime(DateUtil.between(new Date(), expireTime, DateUnit.SECOND))
                    .build();
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("push redpacket failed, req:[{}] uid:[{}] e:[{}]", JSONUtil.toJsonStr(req), uid, ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    private void checkParam(SendRedPacketReq req, Integer type) {
        RedPacketConfigDTO config = config(req.getRoomId());
        List<RedPacketConfigDTO.Configuration> configuration;
        List<Integer> countdownList;
        if (Objects.equals(type, WORLD_REDPACKET)) {
            configuration = config.getWorld().getConfiguration();
            countdownList = config.getWorld().getCountdown();

        } else if (Objects.equals(type, ROOM_REDPACKET)) {
            configuration = config.getRoom().getConfiguration();
            countdownList = config.getRoom().getCountdown();
        } else {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        if (CollUtil.isEmpty(countdownList) || !countdownList.contains(req.getCountdown())) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        Optional<RedPacketConfigDTO.Configuration> con = configuration.stream().filter(e -> Objects.equals(e.getGoldQuantiry(), req.getGold().intValue())).findAny();
        if (con.isEmpty()) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        RedPacketConfigDTO.Configuration configurationTemp = con.get();
        List<Integer> numberOfRecipientsCon = configurationTemp.getNumberOfRecipients();
        if (CollUtil.isEmpty(numberOfRecipientsCon) || !numberOfRecipientsCon.contains(req.getNumberOfRecipients())) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
    }

    public List<RedPacketVO> redPacketVOS(String roomId) {
        LambdaQueryWrapper<RedPacket> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RedPacket::getRoomId, roomId)
                .gt(RedPacket::getBeginTime, new Date());
        List<RedPacket> redPackets = redPacketService.list(wrapper);
        if (CollUtil.isEmpty(redPackets)) {
            return CollUtil.newArrayList();
        }
        List<Long> uids = redPackets.stream().map(RedPacket::getUid).collect(Collectors.toList());
        Map<Long, UserBaseInfoDTO> userBaseInfoDTOMap = userServerService.batchUserSummary(uids);
        return redPackets.stream().map(e -> {
            RedPacketVO redPacketVO = RedPacketVO.builder().id(e.getId())
                    .deadline(e.getBeginTime().getTime())
                    .remainingTime(DateUtil.between(new Date(), e.getBeginTime(), DateUnit.SECOND))
                    .uid(e.getUid()).build();
            UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOMap.get(e.getUid());
            if (Objects.nonNull(userBaseInfoDTO)) {
                redPacketVO.setAvatar(userBaseInfoDTO.getAvatar());
                redPacketVO.setNickName(userBaseInfoDTO.getNick());
                redPacketVO.setGender(userBaseInfoDTO.getGender());
                UserPropInfoDTO avatarWidget = userBaseInfoDTO.getAvatarWidget();
                redPacketVO.setWaveEffectUrl(Objects.nonNull(avatarWidget) ? avatarWidget.getAnimationUrl() : StrUtil.EMPTY);
                redPacketVO.setStaticAvatarFrameUrl(Objects.nonNull(avatarWidget) ? avatarWidget.getIcon() : StrUtil.EMPTY);
            }
            return redPacketVO;
        }).sorted(Comparator.comparing(RedPacketVO::getRemainingTime)).toList();
    }

    public RedPacketGrabVO grab(Long uid, String redPacketId) {
        RLock rLock = redissonManager.fairLock(RedPacketRedisKey.red_packet_grab_lock.getKey(redPacketId), 3, 10);
        try {
            if (Objects.isNull(rLock) || !rLock.isLocked()) {
                log.error("User cannot obtain lock while grabbing redpacket, redPacketId:[{}] uid:[{}]", redPacketId, uid);
                return RedPacketGrabVO.builder().count(0).build();
            }
            RedPacket redPacket = get(redPacketId);
            if (Objects.isNull(redPacket)) {
                throw new ApiException(CodeEnum.RED_PACKET_NOT_EXISTS);
            }
            if (!DateUtil.isIn(new Date(), redPacket.getBeginTime(), redPacket.getExpireTime())) {
                throw new ApiException(CodeEnum.RED_PACKET_EXPIRE);
            }
            Optional<Integer> remainCount = getRemainCount(redPacketId);
            if (remainCount.isEmpty()) {
                throw new ApiException(CodeEnum.RED_PACKET_RECEVICED_OVER);
            }
            if (remainCount.get() == 0) {
                throw new ApiException(CodeEnum.RED_PACKET_RECEVICED_OVER);
            }
            BigDecimal remainMoney = getRemainMoney(redPacketId);
            if (remainMoney.compareTo(BigDecimal.ZERO) <= 0) {
                throw new ApiException(CodeEnum.RED_PACKET_RECEVICED_OVER);
            }
            if (checkIsGrabbed(uid, redPacketId)) {
                throw new ApiException(CodeEnum.RED_PACKET_HAS_RECEVICED);
            }
            BigDecimal grabMoney = getRandomMoney(remainCount.get(), remainMoney);
            Date date = new Date();

            updateRedPacketRemainCache(redPacketId, grabMoney);

            RedPacketRecord redPacketRecord = this.buildRedPacketRecord(redPacketId, uid, redPacket.getUid(), grabMoney, date);
            saveRedPacketRecordCache(redPacketRecord);

            RedPacketRecordMsgDTO packetRecordMsgDTO = RedPacketRecordMsgDTO.builder()
                    .messageId(UUID.fastUUID().toString())
                    .messageTime(System.currentTimeMillis())
                    .redPacketRecord(redPacketRecord).build();
            rocketMqSender.sendAsynchronousMsg(RocketMQTopic.RED_PACKET_GRAB_RECORD_TOPIC, JSON.toJSONString(packetRecordMsgDTO));

            UserBaseInfoDTO userBaseInfoDTO = userServerService.getFromCache(redPacket.getUid());
            UserPropInfoDTO avatarWidget = userBaseInfoDTO.getAvatarWidget();
            return RedPacketGrabVO.builder()
                    .count(grabMoney.intValue())
                    .uid(userBaseInfoDTO.getUid())
                    .avatar(userBaseInfoDTO.getAvatar())
                    .nickName(userBaseInfoDTO.getNick())
                    .gender(userBaseInfoDTO.getGender())
                    .waveEffectUrl(Objects.nonNull(avatarWidget) ? avatarWidget.getAnimationUrl() : StrUtil.EMPTY)
                    .staticAvatarFrameUrl(Objects.nonNull(avatarWidget) ? avatarWidget.getIcon() : StrUtil.EMPTY)
                    .build();
        } catch (Exception e) {
            log.error("User grab redPacket failed, redPacketId:[{}] uid:[{}] msg:[{}]", redPacketId, uid, ExceptionUtil.formatEx(e));
            return RedPacketGrabVO.builder().count(0).build();
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    private RedPacketRecord buildRedPacketRecord(String redPacketId, Long uid, Long sender, BigDecimal grabMoney, Date date) {
        return RedPacketRecord.builder()
                .redPacketId(redPacketId)
                .uid(uid)
                .sender(sender)
                .receiveAmount(grabMoney)
                .createTime(date)
                .build();
    }

    public RedPacketConfigDTO config(String roomId) {
        String configStr = systemConfigService.getSysConfValueById(SystemConfigConstant.RED_PACKET_CONFIG);
        if (StrUtil.isBlank(configStr)) {
            return RedPacketConfigDTO.builder().build();
        }
        return JSONUtil.toBean(configStr, RedPacketConfigDTO.class);
    }

    public RedPacket get(String redPacketId) {
        if (StrUtil.isBlank(redPacketId)) {
            return null;
        }
        RedPacket redPacket = getRedPacketFromCache(redPacketId);
        if (Objects.isNull(redPacket)) {
            redPacket = redPacketService.getById(redPacketId);
            if (Objects.nonNull(redPacket)) {
                redissonManager.hSet(RedPacketRedisKey.red_packet.getKey(), redPacketId, JSONUtil.toJsonStr(redPacket));
            }
        }
        return redPacket;
    }

    private RedPacket getRedPacketFromCache(String redPacketId) {
        String redPacketStr = redissonManager.hGet(RedPacketRedisKey.red_packet.getKey(), String.valueOf(redPacketId));
        if (StrUtil.isBlank(redPacketStr)) {
            return null;
        }
        return JSONUtil.toBean(redPacketStr, RedPacket.class);
    }

    /**
     * 获取红包剩余个数
     *
     * @param redPacketId
     * @return 返回null代表红包过期
     */
    protected Optional<Integer> getRemainCount(String redPacketId) {
        String remainCount = redissonManager.hGet(RedPacketRedisKey.red_packet_remain.getKey(redPacketId), REMAIN_COUNT);
        if (StrUtil.isBlank(remainCount)) {
            return Optional.empty();
        }
        return Optional.of(Integer.parseInt(remainCount));
    }

    public boolean checkIsGrabbed(Long uid, String redPacketId) {
        String hGet = redissonManager.hGet(RedPacketRedisKey.red_packet_record.getKey(redPacketId), uid.toString());
        return StrUtil.isNotBlank(hGet);
    }

    protected BigDecimal getRemainMoney(String redPacketId) {
        String remainStr = redissonManager.hGet(RedPacketRedisKey.red_packet_remain.getKey(redPacketId), REMAIN_MONEY);
        if (StrUtil.isBlank(remainStr)) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(remainStr);
    }

    protected void saveRedPacketRecordCache(RedPacketRecord redPacketRecord) {
        String key = RedPacketRedisKey.red_packet_record.getKey(redPacketRecord.getRedPacketId());
        redissonManager.hSet(key, redPacketRecord.getUid().toString(), JSONUtil.toJsonStr(redPacketRecord));
        redissonManager.expire(key, 7, TimeUnit.DAYS);
    }

    protected void updateRedPacketRemainCache(String redPacketId, BigDecimal amount) {
        String redisKey = RedPacketRedisKey.red_packet_remain.getKey(redPacketId);
        redissonManager.hIncrement(redisKey, REMAIN_MONEY, -amount.doubleValue());
        redissonManager.hIncrement(redisKey, REMAIN_COUNT, -1);
    }

    private BigDecimal getRandomMoney(int remainSize, BigDecimal remainMoney) {
        if (remainSize == 1) {
            return remainMoney.setScale(0, RoundingMode.DOWN);
        }
        BigDecimal min = BigDecimal.ONE;
        BigDecimal max1 = remainMoney.divide(BigDecimal.valueOf(remainSize), RoundingMode.DOWN);
        BigDecimal minRemainAmount = min.multiply(BigDecimal.valueOf(remainSize - 1)).setScale(0, RoundingMode.DOWN);
        BigDecimal max2 = remainMoney.subtract(minRemainAmount);
        BigDecimal max = max1.min(max2);
        BigDecimal random;
        if (max.compareTo(min) > 0) {
            random = min.add(BigDecimal.valueOf(Math.random()).multiply(max.subtract(min))).setScale(0, RoundingMode.DOWN);
        } else {
            random = min;
        }
        return random;
    }


    public void dealRedPacketSettle(RedPacketMsgDTO message) {
        String jsonStr = JSONUtil.toJsonStr(message);
        log.info("deal red packet settle, message:[{}]", jsonStr);
        if (Objects.equals(message.getRedPacketEventEnum(), RedPacketEventEnum.SETTLE)) {
            RedPacket redPacket = message.getRedPacket();
            BigDecimal remainMoney = getRemainMoney(redPacket.getId());
            if (remainMoney.compareTo(BigDecimal.ZERO) <= 0) {
                log.info("The red packet has been consumed, msg:[{}] remain:[{}]", jsonStr, remainMoney);
                return;
            }
            purseManageService.addCoin(redPacket.getUid(), remainMoney.longValue(),
                    BillEnum.REDPACKET_REFUND_INCREASE_COIN, redPacket.getId(), StrUtil.EMPTY, null, 0L,PurseRoleTypeEnum.USER.getType());

            String key = RedPacketRedisKey.red_packet_remain.getKey(String.valueOf(redPacket.getId()));
            redissonManager.del(key);

            TranslationCopyDTO titleCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.LUCKY_BAG_REFUND_TITLE.getKey(), null);
            String[] refund = new String[]{"\n \uD83D\uDCB0 x " + remainMoney.longValue()};
            TranslationCopyDTO textCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.LUCKY_BAG_REFUND_TEXT.getKey(), refund);
            LanguageEnum languageEnum = userServerService.userAppLanguage(redPacket.getUid());
            notifyMessageComponent.pushSystemComplexIMMsgV2(languageEnum, redPacket.getUid(), System.currentTimeMillis(), null,
                    null, titleCopyDTO, textCopyDTO, null, null);
        }
    }

    public void dealGrabRedPacketRecord(RedPacketRecordMsgDTO message) {
        String jsonStr = JSONUtil.toJsonStr(message);
        log.info("deal red packet record, message:[{}]", jsonStr);

        RedPacketRecord redPacketRecord = message.getRedPacketRecord();

        RedPacket redPacket = redPacketService.getById(redPacketRecord.getRedPacketId());

        purseManageService.addCoin(redPacketRecord.getUid(), redPacketRecord.getReceiveAmount().longValue(),
                BillEnum.GRAB_REDPACKET_INCREASE_COIN, redPacketRecord.getRedPacketId(), StrUtil.EMPTY, null, 0L,PurseRoleTypeEnum.USER.getType());

        boolean save = redPacketRecordService.save(redPacketRecord);
        if (!save) {
            log.error("deal red packet record, save record failed, message:[{}]", jsonStr);
        }
        TranslationCopyDTO titleCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.GRAB_LUCKY_BAG_TITLE.getKey(), null);
        String[] grab = new String[]{"\n \uD83D\uDCB0 x " + redPacketRecord.getReceiveAmount().longValue()};
        TranslationCopyDTO textCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.GRAB_LUCKY_BAG_TEXT.getKey(), grab);
        LanguageEnum languageEnum = userServerService.userAppLanguage(redPacketRecord.getUid());
        notifyMessageComponent.pushSystemComplexIMMsgV2(languageEnum, redPacketRecord.getUid(), System.currentTimeMillis(), null,
                null, titleCopyDTO, textCopyDTO, null, null);

        UserBaseInfoDTO userBaseInfoDTOFrom = userServerService.getFromCache(redPacketRecord.getSender());
        TranslationCopyDTO translationCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.GARB_LUCKY_BAG_SCREEN.getKey(), null);
        String nick = StrUtil.format(NICK_MSG, userBaseInfoDTOFrom.getNick());
        String ar = StrUtil.format(SCREEN_MSG_H5, StrUtil.indexedFormat(translationCopyDTO.getAr(), EVENT_MSG + nick));
        String en = StrUtil.format(SCREEN_MSG_H5, StrUtil.indexedFormat(translationCopyDTO.getEn(), EVENT_MSG + nick));

        RedPacketScreenDTO.ScreenData screenData = RedPacketScreenDTO.ScreenData.builder()
                .goldCount(redPacketRecord.getReceiveAmount().longValue())
                .msgAr(ar).build();
        UserBaseInfoDTO userBaseInfoDTOGrabUser = userServerService.getFromCache(redPacketRecord.getUid());
        UserSimpleVO userSimpleVO = BeanUtil.toBean(userBaseInfoDTOGrabUser, UserSimpleVO.class);
        RedPacketScreenDTO screenDTO = RedPacketScreenDTO.builder()
                .from(userSimpleVO)
                .msg(en)
                .event(PushEvent.room_screen_lucky_bag_recive_event)
                .msgID(redPacketRecord.getRedPacketId())
                .targetUid(StrUtil.toString(redPacketRecord.getSender()))
                .data(screenData).build();
        longLinkService.pushCustomerRoomMsg(redPacket.getRoomId(), screenDTO, PushEvent.room_screen_lucky_bag_recive_event, PushToType.MESSAGE_TO_ALL);
    }

    public List<RedPacketHaveBoxVO> haveBox(List<String> roomIds) {
        if (CollUtil.isEmpty(roomIds)) {
            return CollUtil.newArrayList();
        }
        LambdaQueryWrapper<RedPacket> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RedPacket::getRoomId, roomIds)
                .gt(RedPacket::getBeginTime, new Date());
        List<RedPacket> redPackets = redPacketService.list(wrapper);
        List<String> hasPacketIds = redPackets.stream().map(RedPacket::getRoomId).distinct().toList();
        return roomIds.stream().map(e -> RedPacketHaveBoxVO.builder().roomId(e).thereOne(hasPacketIds.contains(e)).build())
                .collect(Collectors.toList());
    }

    public List<String> haveBoxRoomIds(List<String> roomIds) {
        if (CollUtil.isEmpty(roomIds)) {
            return CollUtil.newArrayList();
        }
        LambdaQueryWrapper<RedPacket> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(RedPacket::getRoomId, roomIds)
                .gt(RedPacket::getBeginTime, new Date());
        List<RedPacket> redPackets = redPacketService.list(wrapper);
        return redPackets.stream().map(RedPacket::getRoomId).distinct().toList();
    }
}
