package com.simi.service;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.WithdrawStatus;
import com.simi.common.dto.WithdrawRecordReq;
import com.simi.common.exception.ApiException;
import com.simi.constant.BillEnum;
import com.simi.entity.WithdrawRecord;
import com.simi.mapper.WithdrawRecordMapper;
import com.simi.service.purse.PurseManageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;

@Service
public class WithdrawRecordService  extends ServiceImpl<WithdrawRecordMapper, WithdrawRecord> {

    @Autowired
    private PurseManageService purseManageService;

    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void addWithdrawRecord(WithdrawRecordReq req){
        if (req.getAmount() == null || req.getAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        WithdrawRecord record = new WithdrawRecord();
        String id = IdUtil.getSnowflake().nextIdStr();
        record.setId(id);
        record.setUid(req.getUid());
        record.setStatus(WithdrawStatus.untreated.getCode());
        record.setAmount(req.getAmount().multiply(new BigDecimal("100")).longValue());
        record.setChannel(req.getChannel());
        record.setUserRealId(req.getUserRealId());
        record.setCreatedAt(new Date());
        save(record);
        purseManageService.deductUSD(req.getUid(), req.getAmount()  , BillEnum.USD_WITHDRAW_SPONSOR, id,"", Collections.emptyMap(),0L);

    }

}
