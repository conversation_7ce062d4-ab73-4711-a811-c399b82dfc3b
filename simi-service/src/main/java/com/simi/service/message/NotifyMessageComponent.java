package com.simi.service.message;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.SystemComplexIMMsgEventEnum;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.entity.CommonIMMessage;
import com.simi.dto.push.SystemComplexIMMsgDTO;
import com.simi.util.PushMsgUtil;
import com.simi.util.TranslationCopyUtil;
import com.simi.common.util.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.validation.annotation.Validated;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 通知消息
 * @Author: Andy
 * @Date: 2023/11/21
 */
@Slf4j
@Validated
@Component
@RequiredArgsConstructor
public class NotifyMessageComponent {

    @Autowired
    private TencentMessageComponent tencentMessageComponent;

    /**
     * 构造通用IM消息
     * @param type
     * @param timestamp
     * @param payload
     * @return
     */
    public CommonIMMessage buildCommonIMMessage(IMMsgType type, Long timestamp, String payload){
        return tencentMessageComponent.buildCommonIMMessage(type, timestamp, payload);
    }

    /**
     * 系统消息
     * 发送自定义IM消息
     * 系统账号发送
     *
     * @param imMessage
     * @param toUid
     * @param offlineMsg
     */
    @Async
    public void  publishSystemDefineMessage(CommonIMMessage imMessage, String toUid, OfflinePushInfo offlineMsg) {
        tencentMessageComponent.publishSystemDefineMessage(JSONUtil.toJsonStr(imMessage), PlatformConfig.systemUid, toUid, offlineMsg);
    }




    /**
     * 系统消息
     * 发送自定义IM消息
     * 系统账号发送
     *
     * @param imMessage
     * @param toUid
     * @param offlineMsg
     */
    @Async
    public void  publishInteractionMessage(CommonIMMessage imMessage, String toUid, OfflinePushInfo offlineMsg) {
        tencentMessageComponent.publishSystemDefineMessage(JSONUtil.toJsonStr(imMessage), PlatformConfig.interactionUid, toUid, offlineMsg);
    }
    /**
     * 钱包助手消息
     * 发送自定义钱包IM消息
     * @param imMessage
     * @param toUid
     */
    @Async
    public void publishPuresIMDefineMessage(CommonIMMessage imMessage, String toUid, OfflinePushInfo offlineMsg) {
        tencentMessageComponent.publishSystemDefineMessage(JSONUtil.toJsonStr(imMessage), PlatformConfig.systemPuresUid, toUid, offlineMsg);
    }

    /**
     * 私聊消息
     * @param imMessage
     * @param toUid
     */
    @Async
    public void publishPrivateChatMessage(CommonIMMessage imMessage, Long fromUid, Long toUid, OfflinePushInfo offlineMsg) {
        tencentMessageComponent.publishSystemDefineMessage(JSONUtil.toJsonStr(imMessage), fromUid, String.valueOf(toUid), offlineMsg);
    }

    /**
     * 好友助手消息
     * @param imMessage
     * @param toUid
     */
    @Async
    public void publishFriendAssistantMessage(CommonIMMessage imMessage, Long toUid, OfflinePushInfo offlineMsg) {
        tencentMessageComponent.publishSystemDefineMessage(JSONUtil.toJsonStr(imMessage), PlatformConfig.friendAssistantMsgUid, String.valueOf(toUid), offlineMsg);
    }

    /**
     * 推送tim 系统复杂消息
     * @param uid uid
     * @param messageTime 消息时间
     * @param linkUrl 跳转链接url
     * @param picUrl 图片url
     * @param titleCopywritingEnum 标题文案枚举
     */
    public void pushSystemComplexIMMsg(LanguageEnum languageEnum, Long uid, Long messageTime, String linkUrl, String picUrl,
                                          CopywritingEnum titleCopywritingEnum,
                                          Object[] titleAttachStr,
                                          TranslationCopyDTO textTranslation, Integer event, String eventPayload) {
        TranslationCopyDTO titleTranslation = null;
        if (Objects.nonNull(titleCopywritingEnum)) {
            titleTranslation = TranslationCopyUtil.translationCopy(titleCopywritingEnum.getKey(), titleAttachStr);
        }
        SystemComplexIMMsgDTO imMsgDTO = SystemComplexIMMsgDTO.builder()
                .titleMap(titleTranslation)
                .linkUrl(linkUrl)
                .picUrl(picUrl)
                .textMap(textTranslation)
                .event(event)
                .eventPayload(eventPayload)
                .build();
        CommonIMMessage imMessage = this.buildCommonIMMessage(
                IMMsgType.SystemComplexIMMsg,
                messageTime, JSONUtil.toJsonStr(imMsgDTO));

        OfflinePushInfo offlinePushInfo = null;
        try {
            offlinePushInfo = getOfflinePushInfo(languageEnum, linkUrl, picUrl, textTranslation, titleTranslation, offlinePushInfo);
        } catch (Exception e) {
            // ignore
            log.info("pushSystemComplexIMMsg build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
        }

        this.publishSystemDefineMessage(imMessage, String.valueOf(uid), offlinePushInfo);
    }

    @Nullable
    private OfflinePushInfo getOfflinePushInfo(LanguageEnum languageEnum, String linkUrl, String picUrl, TranslationCopyDTO textTranslation, TranslationCopyDTO titleTranslation, OfflinePushInfo offlinePushInfo) {
        String offlineTitle;
        String offlineText;
        if (Objects.equals(languageEnum, LanguageEnum.en)) {
            offlineTitle = titleTranslation.getEn();
            offlineText = textTranslation.getEn();
        } else {
            offlineTitle = titleTranslation.getAr();
            offlineText = textTranslation.getAr();
        }
        offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                offlineTitle,
                offlineText,
                picUrl,
                linkUrl);
        return offlinePushInfo;
    }

    /**
     *
     * @param uid uid
     * @param messageTime 消息时间
     * @param linkUrl 跳转链接
     * @param picUrl 图片
     * @param titleTranslation 标题
     * @param textTranslation 文案
     * @param eventEnum 附加事件类型
     * @param eventPayload 附加事件荷载
     */
    public void pushSystemComplexIMMsgV2(LanguageEnum languageEnum, Long uid, Long messageTime, String linkUrl, String picUrl,
                                         TranslationCopyDTO titleTranslation,
                                         TranslationCopyDTO textTranslation, SystemComplexIMMsgEventEnum eventEnum, String eventPayload) {
        Integer event = null;
        if (Objects.nonNull(eventEnum)) {
            event = eventEnum.getEvent();
        }
        SystemComplexIMMsgDTO imMsgDTO = SystemComplexIMMsgDTO.builder()
                .titleMap(titleTranslation)
                .linkUrl(linkUrl)
                .picUrl(picUrl)
                .textMap(textTranslation)
                .event(event)
                .eventPayload(eventPayload)
                .build();
        CommonIMMessage imMessage = this.buildCommonIMMessage(
                IMMsgType.SystemComplexIMMsg,
                messageTime, JSONUtil.toJsonStr(imMsgDTO));
        OfflinePushInfo offlinePushInfo = null;
        try {
            offlinePushInfo = getOfflinePushInfo(languageEnum, linkUrl, picUrl, textTranslation, titleTranslation, offlinePushInfo);
        } catch (Exception e) {
            // ignore
            log.info("pushSystemComplexIMMsgV2 build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
        }
        this.publishSystemDefineMessage(imMessage, String.valueOf(uid), offlinePushInfo);
    }


    /**
     * 推送tim
     */
    public void batchPushSystemComplexIMMsg(List<String> uids, String linkUrl,
                                            TranslationCopyDTO titleTranslation,
                                            TranslationCopyDTO textTranslation,
                                            String offlineTitle,
                                            String offlineText,
                                            String picUrl) {

        SystemComplexIMMsgDTO imMsgDTO = SystemComplexIMMsgDTO.builder()
                .titleMap(titleTranslation)
                .linkUrl(linkUrl)
                .picUrl(picUrl)
                .textMap(textTranslation)
                .build();
        CommonIMMessage imMessage = this.buildCommonIMMessage(
                IMMsgType.SystemComplexIMMsg,
                System.currentTimeMillis(), JSONUtil.toJsonStr(imMsgDTO));

        OfflinePushInfo offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                offlineTitle,
                offlineText,
                picUrl,
                linkUrl);
        tencentMessageComponent.batchPublishSystemDefineMessage(JSONUtil.toJsonStr(imMessage), PlatformConfig.systemUid, uids, offlinePushInfo);
    }

    /**
     * TIM 全员PUSH
     */
    public void allStaffPushService(String linkUrl,
                                            TranslationCopyDTO titleTranslation,
                                            TranslationCopyDTO textTranslation) {

        JSONObject jsonObject = new JSONObject();
        jsonObject.set("LinkUrl", linkUrl);
        CommonIMMessage imMessage = this.buildCommonIMMessage(
                IMMsgType.SystemComplexIMMsg,
                System.currentTimeMillis(), jsonObject.toString());

        OfflinePushInfo offlinePushInfo = OfflinePushInfo.builder()
                .PushFlag(0)
                .Title(titleTranslation.getAr())
                .Desc(textTranslation.getAr())
                .build();
        tencentMessageComponent.allStaffPushService(JSONUtil.toJsonStr(imMessage), PlatformConfig.systemUid, offlinePushInfo);
    }


    /**
     * 单推
     */
    public void singleShotPushService(Long fromUid, List<String> uids, String title, String text, String image, String jumpUrl) {
        OfflinePushInfo offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                title,
                text,
                image,
                jumpUrl);
        if (Objects.isNull(fromUid)) {
            fromUid = PlatformConfig.systemUid;
        }
        tencentMessageComponent.singleShotPushService(fromUid, uids, offlinePushInfo);
    }

}
