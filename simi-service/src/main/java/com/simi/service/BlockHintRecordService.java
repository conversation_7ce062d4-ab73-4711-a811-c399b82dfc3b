package com.simi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.BlockHintRecord;
import com.simi.mapper.BlockHintRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class BlockHintRecordService extends ServiceImpl<BlockHintRecordMapper, BlockHintRecord> {

}
