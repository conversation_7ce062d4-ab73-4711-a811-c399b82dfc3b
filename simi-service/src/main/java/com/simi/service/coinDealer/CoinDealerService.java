package com.simi.service.coinDealer;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.github.yulichang.base.MPJBaseServiceImpl;
import com.github.yulichang.wrapper.MPJLambdaWrapper;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.StatusEnum;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.StrategyParamConfigDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.util.CommonUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.CoinDealerStatus;
import com.simi.constant.StrategyParamConfigConstant;
import com.simi.dto.CoinDealerInfoDTO;
import com.simi.entity.coinDealer.CoinDealer;
import com.simi.entity.coinDealer.CoinDealerLatestPresentRecord;
import com.simi.entity.user.User;
import com.simi.mapper.CoinDealerLatestPresentRecordMapper;
import com.simi.mapper.CoinDealerMapper;
import com.simi.service.EventTrackingService;
import com.simi.service.StrategyParamConfigService;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.oauth2.AccountService;
import com.simi.service.purse.BillService;
import com.simi.service.purse.PurseManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class CoinDealerService extends MPJBaseServiceImpl<CoinDealerMapper, CoinDealer> {

    private final CoinDealerLatestPresentRecordMapper coinDealerLatestPresentRecordMapper;
    private final CoinDealerMapper coinDealerMapper;
    private final AccountService accountService;
    private final CoinDealerStatisticService coinDealerStatisticService;
    private final PurseManageService purseManageService;
    private final StrategyParamConfigService strategyParamConfigService;
    private final EventTrackingService eventTrackingService;
    private final MedalTaskService medalTaskService;
    private final BillService billService;
    private final TaskExecutor taskExecutor;


    @Transactional(rollbackFor = Exception.class)
    public void create(Long uid, Long superiorsUid, String remark) {
        if (uid == null || uid == 0L) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        if (superiorsUid == null) {
            superiorsUid = 0L;
        }

        if (!accountService.exist(uid)) {
            throw new ApiException(CodeEnum.ACCOUNT_NOT_EXIST);
        }

        if (superiorsUid != 0 && !accountService.exist(superiorsUid)) {
            throw new ApiException(CodeEnum.COIN_DEALER_SUPERIOR_NOT_EXIST);
        }

        var coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid));

        if (coinDealer != null) {
            coinDealer.setSuperiorsUid(superiorsUid);
            coinDealer.setRemark(remark);
            coinDealer.setLevel(superiorsUid == 0 ? 1 : 2);
            coinDealer.setStatus(CoinDealerStatus.NORMAL);
            coinDealerMapper.updateById(coinDealer);
        } else {
            coinDealer = CoinDealer.builder()
                    .uid(uid)
                    .superiorsUid(superiorsUid)
                    .status(CoinDealerStatus.NORMAL)
                    .level(superiorsUid == 0 ? 1 : 2)
                    .remark(remark).build();
            coinDealerMapper.insert(coinDealer);
        }

        coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid));
        coinDealerStatisticService.createOrUpdate(uid, coinDealer.getUpdatedAt());
    }

    //金票赚赠金币
    @Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRED)
    public void coinDealerPresent(Long targetUid,Long gold,String remark,Long uid){
        purseManageService.addCoin(targetUid,gold, BillEnum.COIN_DEALER_FOR_GOLD, CommonUtil.genId(),remark, Collections.emptyMap(),uid,PurseRoleTypeEnum.USER.getType());

        try {
            log.info("Coin dealer present deal medal task, uid:[{}] gold:[{}]", targetUid, gold);
            taskExecutor.execute(() -> medalTaskService.executeMedalTask(MedalTaskEnum.RECHARGE_GOLD, targetUid, gold.intValue()));
        } catch (Exception e) {
            log.info("Coin dealer present deal medal task failed, uid:[{}] gold:[{}]", targetUid, gold);
        }
    }

    public Boolean isCoinDealer(Long uid) {
        return coinDealerMapper.selectCount(new QueryWrapper<CoinDealer>().eq("uid", uid).eq("status",CoinDealerStatus.NORMAL.getCode())) > 0;
    }

    // 平台奖励新增或者扣减 goldenTicket 可 + 可以 -
    public Date modifyGoldenTicketByPlatform(Long uid, Long goldenTicket) {
        if (goldenTicket == null) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        var coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid).eq("status", StatusEnum.normal.getStatus()));
        if (coinDealer == null) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        coinDealer.setGoldenTicket(coinDealer.getGoldenTicket() + goldenTicket);
        if (coinDealer.getGoldenTicket() < 0) {
            throw new ApiException(CodeEnum.COIN_DEALER_GOLDEN_TICKET_NOT_ENOUGH);
        }
        if (1 != coinDealerMapper.update(null,
                new UpdateWrapper<CoinDealer>()
                        .setSql("golden_ticket = golden_ticket + " + goldenTicket)
                        .eq("uid", uid))) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        BillEnum billItem ;
        if (goldenTicket > 0) {
            billItem = BillEnum.PLATFORM_INCREASE_GOLDEN_TICKET;
        }else {
            billItem = BillEnum.PLATFORM_DEDUCTION_GOLDEN_TICKET;
        }
        Date date = new Date();
        PurseDTO dto = new PurseDTO();
        dto.setUid(uid);
        dto.setGoldenTicket(goldenTicket);
        billService.createBill(uid, CommonUtil.genId(), Math.abs(goldenTicket), dto, billItem,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);
        return date;
    }

    // 充值 goldenTicket +
    public void recharge(Long uid, Long goldenTicket,BillEnum billItem,Long targetUid) {
        if (goldenTicket == null) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        var coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid).eq("status", StatusEnum.normal.getStatus()));
        if (coinDealer == null) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        coinDealer.setGoldenTicket(coinDealer.getGoldenTicket() + goldenTicket);
        if (coinDealer.getGoldenTicket() < 0) {
            throw new ApiException(CodeEnum.COIN_DEALER_GOLDEN_TICKET_NOT_ENOUGH);
        }
        if (1 != coinDealerMapper.update(null,
                new UpdateWrapper<CoinDealer>()
                        .setSql("golden_ticket = golden_ticket + " + goldenTicket)
                        .eq("uid", uid))) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        coinDealerStatisticService.recharge(uid, goldenTicket);
        Date date = new Date();
        PurseDTO dto = new PurseDTO();
        dto.setUid(uid);
        dto.setTargetUid(targetUid);
        dto.setGoldenTicket(coinDealer.getGoldenTicket());
        billService.createBill(uid, CommonUtil.genId(), Math.abs(goldenTicket), dto, billItem,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);
    }

    // 管理后台扣减 goldenTicket +
    public void subGoldenTicketByAdmin(Long uid, Long goldenTicket) {
        if (goldenTicket == null || goldenTicket <= 0) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        var coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid));
        if (coinDealer == null) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        coinDealer.setGoldenTicket(coinDealer.getGoldenTicket() - goldenTicket);
        if (coinDealer.getGoldenTicket() < 0) {
            throw new ApiException(CodeEnum.COIN_DEALER_GOLDEN_TICKET_NOT_ENOUGH);
        }
        if (1 != coinDealerMapper.update(null,
                new UpdateWrapper<CoinDealer>()
                        .setSql("golden_ticket = golden_ticket + " + goldenTicket)
                        .eq("uid", uid))) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        BillEnum billItem = BillEnum.PLATFORM_INCREASE_GOLDEN_TICKET;
        Date date = new Date();
        PurseDTO dto = new PurseDTO();
        dto.setUid(uid);
        dto.setGoldenTicket(goldenTicket);
        billService.createBill(uid, CommonUtil.genId(), coinDealer.getGoldenTicket(), dto, billItem,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);
    }

    // 上级转下级
    @Transactional(rollbackFor = Exception.class)
    public Date transfer(Long uid, Long inferiorUid, Long goldenTicket) {
        if (goldenTicket == null || goldenTicket <= 0) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        var inferior = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", inferiorUid).eq("status", StatusEnum.normal.getStatus()));
        if (Objects.isNull(inferior)) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }

        var superior = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid).eq("status", StatusEnum.normal.getStatus()));
        if (Objects.isNull(superior)) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }

        if (superior.getGoldenTicket() - goldenTicket < 0) {
            throw new ApiException(CodeEnum.COIN_DEALER_GOLDEN_TICKET_NOT_ENOUGH);
        }

        superior.setGoldenTicket(superior.getGoldenTicket() - goldenTicket);
        inferior.setGoldenTicket(inferior.getGoldenTicket() + goldenTicket);

        coinDealerMapper.updateById(superior);
        coinDealerMapper.updateById(inferior);

        coinDealerStatisticService.transfer(uid, inferiorUid, goldenTicket);
        Date date = new Date();
        BillEnum increase = BillEnum.COIN_DEALER_FOR_COIN_DEALER_DEDUCTION;
        PurseDTO dto = new PurseDTO();
        dto.setUid(uid);
        dto.setGoldenTicket(superior.getGoldenTicket());
        dto.setTargetUid(inferiorUid);
        billService.createBill(uid, CommonUtil.genId(), Math.abs(goldenTicket), dto, increase,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);
        BillEnum deduction = BillEnum.COIN_DEALER_FOR_COIN_DEALER_INCREASE;
        PurseDTO inferiorPurse = new PurseDTO();
        inferiorPurse.setUid(inferiorUid);
        inferiorPurse.setGoldenTicket(inferior.getGoldenTicket());
        inferiorPurse.setTargetUid(uid);
        billService.createBill(inferiorUid, CommonUtil.genId(), Math.abs(goldenTicket), inferiorPurse, deduction,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);
        return date;
    }

    // 用户转增 goldenTicket +
    // ( 扣用户美金  加币商金票 )
    public void userPresent(Long uid, Long coinDealerUid, Long usdNum,boolean newBie,String deviceID) {
        if (usdNum == null || usdNum <= 0) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        if (!isCoinDealer(coinDealerUid)) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        //比例
        StrategyParamConfigDTO configDTO = strategyParamConfigService.getByKey(StrategyParamConfigConstant.DOLLAR_FOR_TICKET);
        Long usd= configDTO.getNum() * usdNum;
        purseManageService.deductUSD(uid, new BigDecimal(usdNum) , BillEnum.USD_FOR_COIN_DEALER,CommonUtil.genId(),"", Collections.emptyMap(),coinDealerUid, PurseRoleTypeEnum.USER.getType());

        if (1 != coinDealerMapper.update(null,
                new UpdateWrapper<CoinDealer>()
                        .setSql("golden_ticket = golden_ticket + " + usd)
                        .eq("uid", coinDealerUid))) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }

        var record = coinDealerLatestPresentRecordMapper.selectOne(new QueryWrapper<CoinDealerLatestPresentRecord>()
                .eq("uid", uid)
                .eq("coin_dealer_uid", coinDealerUid));

        if (record != null) {
            record.setLatestPresent(usdNum);
            coinDealerLatestPresentRecordMapper.updateById(record);
        } else {
            record = CoinDealerLatestPresentRecord.builder()
                    .uid(uid)
                    .coinDealerUid(coinDealerUid)
                    .latestPresent(usdNum)
                    .build();
            coinDealerLatestPresentRecordMapper.insert(record);
        }
        CoinDealer coinDealer = get(coinDealerUid);
        coinDealerStatisticService.userPresent(coinDealerUid, usdNum);
        Date date = new Date();
        BillEnum increase = BillEnum.USER_FOR_COIN_DEALER;
        PurseDTO dto = new PurseDTO();
        dto.setUid(coinDealerUid);
        dto.setGoldenTicket(coinDealer.getGoldenTicket());
        dto.setTargetUid(uid);
        billService.createBill(coinDealerUid, CommonUtil.genId(), Math.abs(usd), dto, increase,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);

        eventTrackingService.handleUsdForCoinDealer(uid,usd,usdNum,new Date(),deviceID,newBie);
    }

    // 售出 goldenTicket +
    // (扣币商金票, 加用户金币)
    public Date sell(Long uid, Long goldenTicket, Long targetUid) {
        if (goldenTicket == null || goldenTicket <= 0) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        var coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid).eq("status", StatusEnum.normal.getStatus()));
        if (coinDealer == null) {
            throw new ApiException(CodeEnum.COIN_DEALER_NOT_EXIST);
        }
        coinDealer.setGoldenTicket(coinDealer.getGoldenTicket() - goldenTicket);

        if (coinDealer.getGoldenTicket() < 0) {
            throw new ApiException(CodeEnum.COIN_DEALER_GOLDEN_TICKET_NOT_ENOUGH);
        }
        coinDealerMapper.update(null,
                new UpdateWrapper<CoinDealer>()
                        .setSql("golden_ticket = golden_ticket + " + -goldenTicket)
                        .eq("uid", uid));
        coinDealerStatisticService.sell(uid, goldenTicket);
        Date date = new Date();
        BillEnum increase = BillEnum.COIN_DEALER_FOR_USER;
        PurseDTO dto = new PurseDTO();
        dto.setUid(uid);
        dto.setGoldenTicket(coinDealer.getGoldenTicket());
        dto.setTargetUid(targetUid);
        billService.createBill(uid, CommonUtil.genId(), Math.abs(goldenTicket), dto, increase,
                date, Collections.emptyMap(), StrUtil.EMPTY, null);
        return date;
    }


    public Long delete(Long id) {
        CoinDealer coinDealer = coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("id", id));
        coinDealerMapper.update(CoinDealer.builder().status(CoinDealerStatus.DELETED).build(), new QueryWrapper<CoinDealer>().eq("id", id));
        return coinDealer.getUid();
    }

    public void update(Long uid, String remark) {
        coinDealerMapper.update(CoinDealer.builder().remark(remark).build(), new QueryWrapper<CoinDealer>().eq("uid", uid));
    }

    public CoinDealer get(Long uid) {
        return coinDealerMapper.selectOne(new QueryWrapper<CoinDealer>().eq("uid", uid).eq("status", StatusEnum.normal.getStatus()));
    }


    public Boolean isSubordinate(Long uid,Long subordinateUid) {
        return coinDealerMapper.selectCount(new QueryWrapper<CoinDealer>().eq("uid", subordinateUid).eq("status", StatusEnum.normal.getStatus()).eq("superiors_uid",uid)) > 0;
    }


    // 管理后台用
    public ListWithTotal<CoinDealerInfoDTO> search(Integer pageNum, Integer pageSize, Long uid, Long userNo, Long superiorsUid, Integer level) {
        ListWithTotal<CoinDealerInfoDTO> coinDealerInfoDTO = new ListWithTotal<>();
        var page = new Page<CoinDealerInfoDTO>(pageNum, pageSize);

        var wrapper = new MPJLambdaWrapper<CoinDealer>().leftJoin(
                User.class, User::getUid, CoinDealer::getUid
        );

        if (uid != null && uid > 0) {
            wrapper = wrapper.eq(CoinDealer::getUid, uid);
        }

        if (userNo != null && userNo > 0) {
            wrapper = wrapper.eq(User::getUserNo, userNo);
        }

        if (superiorsUid != null && superiorsUid > 0) {
            wrapper = wrapper.eq(CoinDealer::getSuperiorsUid, superiorsUid);
        }

        if (level != null && level > 0 && level < 3) {
            wrapper = wrapper.eq(CoinDealer::getLevel, level);
        }
        wrapper = wrapper.eq(CoinDealer::getStatus, CoinDealerStatus.NORMAL);

        wrapper = wrapper
                .selectAs(CoinDealer::getId, CoinDealerInfoDTO::getId)
                .selectAs(CoinDealer::getUid, CoinDealerInfoDTO::getUid)
                .selectAs(User::getUserNo, CoinDealerInfoDTO::getUserNo)
                .selectAs(User::getNick, CoinDealerInfoDTO::getNick)
                .selectAs(CoinDealer::getSuperiorsUid, CoinDealerInfoDTO::getSuperiorsUid)
                .selectAs(CoinDealer::getLevel, CoinDealerInfoDTO::getLevel)
                .selectAs(CoinDealer::getRemark, CoinDealerInfoDTO::getRemark)
                .selectAs(CoinDealer::getCreatedAt, CoinDealerInfoDTO::getCreatedAt)
                .selectAs(CoinDealer::getUpdatedAt, CoinDealerInfoDTO::getUpdatedAt);
        Page<CoinDealerInfoDTO> coinDealerInfoDTOPage = coinDealerMapper.selectJoinPage(page, CoinDealerInfoDTO.class, wrapper);

        List<CoinDealerInfoDTO> records = coinDealerInfoDTOPage.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return coinDealerInfoDTO;
        }

        coinDealerInfoDTO.setList(records);
        coinDealerInfoDTO.setTotal(coinDealerInfoDTOPage.getTotal());
        return coinDealerInfoDTO;
    }

    public List<CoinDealer> getCoinDealerByUidList(List<Long> uidList){
        LambdaQueryWrapper<CoinDealer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CoinDealer::getUid, uidList)
                .in(CoinDealer::getLevel,Arrays.asList(1,2))
                .eq(CoinDealer::getStatus,CoinDealerStatus.NORMAL)
                .select(CoinDealer::getUid);
        return list(queryWrapper);
    }

    public List<CoinDealer> getGoldenTicketByUidList(Set<Long> uidList){
        LambdaQueryWrapper<CoinDealer> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(CoinDealer::getUid, uidList)
                .select(CoinDealer::getUid,CoinDealer::getGoldenTicket);
        return list(queryWrapper);
    }
}

