package com.simi.service.shop.pay.impl;

import com.simi.common.util.CommonUtil;
import com.simi.constant.BillEnum;
import com.simi.service.purse.PurseManageService;
import com.simi.service.shop.pay.ShopPayService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 金币支付业务方法
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopCoinPayService implements ShopPayService {
    private final PurseManageService purseManageService;
    @Override
    public int payCurrencyType() {
        return 1;
    }

    @Override
    public String deductionAmountAncCreateBill(long uid,
                                             long coin,
                                             BillEnum billEnum,
                                             String remark,
                                             Map<String, String> introduce,
                                             Long targetUid) {
        //扣减库存 + 创建订单
        long finalCoin = Long.valueOf(coin);
        String bizOrderId = CommonUtil.genId();
        log.info("deductionAmountAncCreateBill uid:{}，coin:{},billEnum:{},remark:{},introduce:{},targetUid:{},bizOrderId:{}",
                uid,coin,billEnum,remark,introduce,targetUid,bizOrderId);
        purseManageService.deductCoin(uid, finalCoin, billEnum, bizOrderId, remark, introduce, targetUid);

        return bizOrderId;
    }


}
