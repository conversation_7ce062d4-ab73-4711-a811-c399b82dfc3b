package com.simi.service.shop;


import com.simi.common.constant.BackpackOperateTypeEnum;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.backPack.BackpackGoodsSourceTypeEnum;
import com.simi.common.constant.prop.BillIntroduceFieldNameConstant;
import com.simi.common.constant.prop.PropTypeEnums;
import com.simi.common.constant.resource.ResourceTypeEnum;
import com.simi.common.exception.ApiException;
import com.simi.constant.BillEnum;
import com.simi.dto.shop.ShopPurchaseDTO;
import com.simi.entity.PropInfo;
import com.simi.service.shop.pay.ShopPayService;
import com.simi.service.shop.ship.ShopShipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import static com.simi.common.constant.CodeEnum.getByNumber;

/**
 * 商城装扮商品购买业务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopPurchaseService {
    private final ShopPayService shopPayService;
    private final ShopShipService shopShipService;
    /**
     * 执行商城购买逻辑
     *
     */
    @Transactional(rollbackFor = Exception.class)
    public void doPurchaseGoods( ShopPurchaseDTO shopPurchaseDTO){
        Long targetUserId = shopPurchaseDTO.getTargetUserId();
        BillEnum billEnum = getBillEnum(targetUserId);

        Map<String, String> stringStringMap = buildIntroduce(shopPurchaseDTO.getPropInfo(), shopPurchaseDTO.getDay(),shopPurchaseDTO.getType());
        //扣除金币，并且生成账单
        String bizOrderId = shopPayService.deductionAmountAncCreateBill(shopPurchaseDTO.getUserId(), shopPurchaseDTO.getPropDayPrice(),
                billEnum, "", stringStringMap, shopPurchaseDTO.getTargetUserId());

        BackpackOperateTypeEnum backpackOperateTypeEnum = getBackpackOperateTypeEnum(targetUserId);
        BackpackGoodsSourceTypeEnum backpackGoodsSourceTypeEnum = getBackpackGoodsSourceTypeEnum(targetUserId);
        ResourceTypeEnum resourceTypeEnum = ResourceTypeEnum.getFrequencyType(shopPurchaseDTO.getType());
        long finalShipUserId = shopPurchaseDTO.getUserId();
        if (buyForOthers(targetUserId)){
            finalShipUserId = targetUserId;
        }
        //商品下放到背包
         int code = shopShipService.increaseBackpackGoodsAndAddLog(backpackOperateTypeEnum, bizOrderId, finalShipUserId, shopPurchaseDTO.getId()
                , shopPurchaseDTO.getDay(), resourceTypeEnum, backpackGoodsSourceTypeEnum);
        if (!CodeEnum.SUCCESS_ZERO.getNumber().equals(code)){
            CodeEnum codeEnum = getByNumber(code);
            throw new ApiException(codeEnum);
        }
    }

    private BackpackGoodsSourceTypeEnum getBackpackGoodsSourceTypeEnum(Long targetUserId) {
        if (buyForOthers(targetUserId)) {
            return BackpackGoodsSourceTypeEnum.CHANGE_SOURCE_SHOP_BUY_FOR_OTHERS;
        }

        return BackpackGoodsSourceTypeEnum.CHANGE_SOURCE_SHOP_BUY_FOR_ONESELF;
    }

    private BackpackOperateTypeEnum getBackpackOperateTypeEnum(Long targetUserId) {
        if (buyForOthers(targetUserId)) {
            return BackpackOperateTypeEnum.BACKPACK_OPERATE_SHOP_BUY_FOR_OTHERS;
        }

        return BackpackOperateTypeEnum.BACKPACK_OPERATE_SHOP_BUY_FOR_ONESELF;
    }

    private Map<String, String> buildIntroduce(PropInfo propInfo, Integer day, Integer type) {
        Map<String, String> infoMap = new HashMap<>(4);
        infoMap.put(BillIntroduceFieldNameConstant.ID,propInfo.getId().toString());
        infoMap.put(BillIntroduceFieldNameConstant.NAME_EN, PropTypeEnums.getEnDescByType(type).toLowerCase());
        infoMap.put(BillIntroduceFieldNameConstant.NAME_AR,PropTypeEnums.getArDescByType(type));
        infoMap.put(BillIntroduceFieldNameConstant.DAY,day.toString());
        return infoMap;
    }

    private BillEnum getBillEnum(Long targetUserId) {
        if (buyForOthers(targetUserId)) {
            return BillEnum.SHOP_PROP_FOR_OTHERS_BY_GOLD;
        }

        return BillEnum.SHOP_PROP_FOR_ONESELF_BY_GOLD;
    }

    private boolean buyForOthers(Long targetUserId) {
        return Objects.nonNull(targetUserId) && targetUserId > 0 ;
    }
}
