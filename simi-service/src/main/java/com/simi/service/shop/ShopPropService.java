package com.simi.service.shop;


import com.alibaba.fastjson.JSON;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.YesOrNoEnums;
import com.simi.common.constant.prop.PropConfigConstant;
import com.simi.common.constant.prop.PropCurrencyTypeEnums;
import com.simi.common.constant.prop.PropTypeEnums;
import com.simi.common.constant.user.UserStatusEnum;
import com.simi.common.dto.shop.prop.PropNewProductTimestampDTO;
import com.simi.common.dto.shop.prop.ShopPropCachePageDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.vo.PageResultInfo;
import com.simi.common.vo.req.shop.prop.OnSalePropListReq;
import com.simi.common.vo.req.shop.prop.PurchaseOnSalePropReq;
import com.simi.constant.RevenueRedisKey;
import com.simi.dto.shop.ShopPurchaseDTO;
import com.simi.entity.PropInfo;
import com.simi.entity.PropPriceDetail;
import com.simi.service.PageService;
import com.simi.service.PropInfoService;
import com.simi.service.PropPriceDetailService;
import com.simi.service.PropServerService;
import com.simi.service.shop.cache.ShopPropCacheService;
import com.simi.service.user.BlockRecordService;
import com.simi.service.user.UserServerService;
import com.simi.common.vo.shop.prop.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.simi.common.constant.CodeEnum.getByNumber;

/**
 * 商城装扮相关业务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ShopPropService {
    private final PropInfoService propInfoService;
    private final PropPriceDetailService propPriceDetailService;
    private final PageService pageService;
    private final ShopPropCacheService shopPropCacheService;
    private final UserServerService userServerService;
    private final ShopPurchaseService shopPurchaseService;
    private final PropServerService propServerService;
    private final BlockRecordService blockRecordService;
    private final RedissonDistributionLocker distributionLocker;

    /**
     * 上新已读列表hash key分隔符
     */
    private final static String PROP_NEW_PRODUCT_READ_LIST_HASH_KEY_SPIT = "_";

    /**
     * 获取上新商品标识列表
     */
    public NewProductFlagVO getNewProductFlag(Long uid) {
        List<NewProductFlagItem> newProductFlagItemList = new ArrayList<>();
        int lastDaysTimestamp = DateTimeUtil.getLastDaysTimestamp(new Date(), PropConfigConstant.NEW_PRODUCT_DAYS);
        try {
            Map<Integer, List<PropNewProductTimestampDTO>> newProductTimestampMap = getNewProductTimestampMap(lastDaysTimestamp, uid);
            List<Integer> typeList = Arrays.stream(PropTypeEnums.values()).map(PropTypeEnums::getType).collect(Collectors.toList());
            int totalFlag = 0;
            for (Integer type : typeList) {
                List<PropNewProductTimestampDTO> items = newProductTimestampMap.get(type);
                Integer newProductFlag = CollectionUtils.isEmpty(items) ? YesOrNoEnums.NO.getType() : YesOrNoEnums.YES.getType();
                NewProductFlagItem item = NewProductFlagItem.builder()
                        .markNewProduct(newProductFlag)
                        .type(type)
                        .build();
                newProductFlagItemList.add(item);
                if (YesOrNoEnums.YES.getType().equals(newProductFlag)) {
                    totalFlag = newProductFlag;
                }
            }
            //总体上新未读情况
            NewProductFlagItem item = NewProductFlagItem.builder()
                    .markNewProduct(totalFlag)
                    .type(0)
                    .build();
            newProductFlagItemList.add(item);
        } catch (Exception e) {
            log.error("getNewProductFlag execute error,exception:{}", ExceptionUtil.formatEx(e));
        }

        return NewProductFlagVO.builder().newProductFlagItemList(newProductFlagItemList).build();
    }


    private Map<Integer, List<PropNewProductTimestampDTO>> getNewProductTimestampMap(Integer newProductTimestampGt, Long uid) {
        List<PropNewProductTimestampDTO> notReadNewPropList = getNotReadNewPropList(newProductTimestampGt, uid);
        return notReadNewPropList.stream().collect(Collectors.groupingBy(PropNewProductTimestampDTO::getType));
    }

    /**
     * 获取用户未读上新列表
     *
     * @param newProductTimestampGt
     * @param uid
     * @return
     */
    private List<PropNewProductTimestampDTO> getNotReadNewPropList(Integer newProductTimestampGt, Long uid) {
        List<PropNewProductTimestampDTO> notFilterReadItems = shopPropCacheService.getMarkNewProductTimestamp(newProductTimestampGt);
        //数据库加载无数据的情况
        if (Objects.isNull(notFilterReadItems)) {
            return Collections.EMPTY_LIST;
        }
        //未加载缓存的情况
        if (CollectionUtils.isEmpty(notFilterReadItems)) {
            notFilterReadItems = propInfoService.getOnSaleNewProductTimestamp(newProductTimestampGt, null);
            log.info("uid:{},newProductTimestampGt:{},get propNewProductTimestamp from db result is:{}",
                    uid, newProductTimestampGt, JSON.toJSONString(notFilterReadItems));
            //过滤价格为空的情况
            if (CollectionUtils.isNotEmpty(notFilterReadItems)) {
                Set<Long> ids = notFilterReadItems.stream().map(PropNewProductTimestampDTO::getId).collect(Collectors.toSet());
                Map<Long, List<PropPriceDetail>> priceList =
                        propPriceDetailService.getPriceListByProIds(ids, null, PropCurrencyTypeEnums.SPECIES.getType());
                notFilterReadItems = notFilterReadItems.stream().filter(e -> {
                    Long id = e.getId();
                    List<PropPriceDetail> propPriceDetails = priceList.get(id);
                    return checkDisplayTimestampPrice(propPriceDetails);
                }).collect(Collectors.toList());
            }

            shopPropCacheService.setMarkNewProductTimestamp(notFilterReadItems);
        }

        if (CollectionUtils.isEmpty(notFilterReadItems)) {
            return Collections.EMPTY_LIST;
        }

        //获取用户已读上新列表
        Set<String> hashKeySet = notFilterReadItems.stream().map(e -> {
            Long propId = e.getId();
            Integer markNewProductTimestamp = e.getMarkNewProductTimestamp();
            return propId + PROP_NEW_PRODUCT_READ_LIST_HASH_KEY_SPIT + markNewProductTimestamp;
        }).collect(Collectors.toSet());
        Map<String, String> readListMap = getReadNewPropMap(uid, hashKeySet);

        List<PropNewProductTimestampDTO> finalItemList = notFilterReadItems.stream().filter(e -> {
            Long id = e.getId();
            Integer timestamp = e.getMarkNewProductTimestamp();
            String hashKey = id + PROP_NEW_PRODUCT_READ_LIST_HASH_KEY_SPIT + timestamp;
            return !readListMap.containsKey(hashKey);
        }).collect(Collectors.toList());

        return finalItemList;
    }

    private Map<String, String> getReadNewPropMap(Long uid, Set<String> hashKeys) {
        if (CollectionUtils.isEmpty(hashKeys)) {
            return Collections.emptyMap();
        }

        return shopPropCacheService.getReadNewPropMap(uid, hashKeys);
    }

    /**
     * 获取商城装扮商品，并且标记上新已读商品
     *
     * @param onSalePropListReq
     * @param uid
     * @param language
     * @return
     */
    public ShopPropListVO getOnSalePropList(OnSalePropListReq onSalePropListReq, Long uid, String language) {
        Integer type = onSalePropListReq.getType();
        ShopPropCachePageDTO cacheOnSalePropDTO =
                shopPropCacheService.getCacheOnSalePropList(uid, onSalePropListReq.getPageNum(), onSalePropListReq.getPageSize(), type);
        List<ShopPropItem> cacheItemList = cacheOnSalePropDTO.getShopPropItemList();
        //请求无结果的情况
        if (Objects.isNull(cacheItemList)) {
            return buildShopPropListVO(Collections.EMPTY_LIST, uid, language, cacheOnSalePropDTO.getPageResultInfo());
        }
        if (CollectionUtils.isNotEmpty(cacheItemList)) {
            return buildShopPropListVO(cacheItemList, uid, language, cacheOnSalePropDTO.getPageResultInfo());
        }

        //从数据库中加载该type所有数据
        List<ShopPropItem> shopPropItems = loadOnSalePropListFromDB(uid, type);
        log.warn("uid :{}, type:{},get getOnSalePropList from db result is :{}", uid, type, JSON.toJSONString(shopPropItems));
        //将价格为空的过滤掉
        shopPropItems = shopPropItems.stream().filter(e -> {
            return checkDisplayPrice(e.getPriceList());
        }).collect(Collectors.toList());

        //更新缓存
        shopPropCacheService.updateOnSalePropCache(shopPropItems, type);

        if (CollectionUtils.isEmpty(shopPropItems)) {
            log.warn("uid:{}, get getOnSalePropList is empty,type:{}", uid, type);
            return ShopPropListVO.getNullResultVO();
        }

        PageResultInfo dbPageResultInfo = pageService.correctOnSalePageInfo(onSalePropListReq.getPageNum(),
                onSalePropListReq.getPageSize(), shopPropItems.size());
        if (dbPageResultInfo.getStart() >= shopPropItems.size()) {
            return buildShopPropListVO(Collections.EMPTY_LIST, uid, language, dbPageResultInfo);
        }
        //截取DB数据
        List<ShopPropItem> pagePropItems = shopPropItems.subList(dbPageResultInfo.getStart(), dbPageResultInfo.getEnd());
        return buildShopPropListVO(pagePropItems, uid, language, dbPageResultInfo);
    }

    private boolean checkDisplayPrice(List<ShopPropPriceItem> priceList) {
        if (CollectionUtils.isEmpty(priceList)) {
            return false;
        }
        //超过一项直接展示
        if (priceList.size() > 1) {
            return true;
        }

        Integer price = priceList.get(0).getPrice();
        //只有一项且价格为0则不展示
        return !price.equals(0);
    }

    private boolean checkDisplayTimestampPrice(List<PropPriceDetail> propPriceDetails) {
        if (CollectionUtils.isEmpty(propPriceDetails)) {
            return false;
        }
        //超过一项直接展示
        if (propPriceDetails.size() > 1) {
            return true;
        }

        //只有一项且价格为0则不展示
        Integer price = propPriceDetails.get(0).getPrice();
        return !price.equals(0);

    }

    private ShopPropListVO buildShopPropListVO(List<ShopPropItem> items,
                                               Long uid,
                                               String language,
                                               PageResultInfo pageResultInfo) {
        int lastDaysTimestamp = DateTimeUtil.getLastDaysTimestamp(new Date(), PropConfigConstant.NEW_PRODUCT_DAYS);
        Set<String> readPropHashKeySet = items.stream()
                .filter(item -> item.getMarkNewProductTimestamp() > lastDaysTimestamp)
                .map(e -> {
                    Long id = e.getId();
                    Integer markNewProductTimestamp = e.getMarkNewProductTimestamp();
                    String hashKey = id + PROP_NEW_PRODUCT_READ_LIST_HASH_KEY_SPIT + markNewProductTimestamp;
                    return hashKey;
                }).collect(Collectors.toSet());

        Map<String, Boolean> readPropMap = batchCheckIsNotReadMarkNewProduct(readPropHashKeySet, uid);
        for (ShopPropItem item : items) {
            if (LanguageEnum.ar.name().equals(language)) {
                item.setName(item.getNameAr());
            } else {
                item.setName(item.getNameEn());
            }
            String readHashKey = item.getId() + PROP_NEW_PRODUCT_READ_LIST_HASH_KEY_SPIT + item.getMarkNewProductTimestamp();
            Boolean readFlag = readPropMap.get(readHashKey);
            //未读的标识
            if (Objects.nonNull(readFlag) && !readFlag) {
                item.setMarkNewProduct(YesOrNoEnums.YES.getType());
                shopPropCacheService.updateNewPropReadList(uid, readHashKey);
            } else {
                item.setMarkNewProduct(YesOrNoEnums.NO.getType());
            }
        }

        ShopPropListVO shopPropListVO = new ShopPropListVO();
        shopPropListVO.setShopPropList(items);
        shopPropListVO.setTotalPage(pageResultInfo.getTotalPage());
        shopPropListVO.setCurrentPage(pageResultInfo.getCurrentPage());
        shopPropListVO.setTotalSize(pageResultInfo.getTotalSize());
        shopPropListVO.setPageSize(pageResultInfo.getPageSize());

        return shopPropListVO;
    }


    private List<ShopPropItem> loadOnSalePropListFromDB(Long uid, Integer type) {
        List<PropInfo> onSaleList = propInfoService.getOnSalePropByType(uid, type);
        if (CollectionUtils.isEmpty(onSaleList)) {
            return Collections.EMPTY_LIST;
        }

        Set<Long> ids = onSaleList.stream().map(PropInfo::getId).collect(Collectors.toSet());
        Map<Long, List<PropPriceDetail>> priceList = propPriceDetailService.getPriceListByProIds(ids, type, PropCurrencyTypeEnums.SPECIES.getType());

        List<ShopPropItem> result = new ArrayList<>();
        for (PropInfo propInfo : onSaleList) {
            List<ShopPropPriceItem> priceDetail = priceList
                    .getOrDefault(propInfo.getId(), Collections.emptyList())
                    .stream()
                    .map(e -> {
                        return ShopPropPriceItem.builder()
                                .price(e.getPrice())
                                .day(e.getDay())
                                .currencyType(e.getCurrencyType())
                                .build();
                    }).collect(Collectors.toList());

            ShopPropItem item = ShopPropItem.builder()
                    .id(propInfo.getId())
                    .animationType(propInfo.getAnimationType())
                    .animationUrl(propInfo.getAnimationUrl())
                    .type(propInfo.getType())
                    .icon(propInfo.getIcon())
                    .nameAr(propInfo.getNameAr())
                    .nameEn(propInfo.getNameEn())
                    .direction(propInfo.getDirection())
                    .circulationUrl(propInfo.getCirculationUrl())
                    .groupIds(propInfo.getGroupIds())
                    .priceList(priceDetail)
                    .markNewProductTimestamp(propInfo.getMarkNewProductTimestamp())
                    .build();
            result.add(item);
        }
        return result;
    }


    /**
     * 批量判断是否在已读列表里面
     *
     * @param readListHashKeys
     * @param uid
     * @return
     */
    private Map<String, Boolean> batchCheckIsNotReadMarkNewProduct(Set<String> readListHashKeys, long uid) {
        if (CollectionUtils.isEmpty(readListHashKeys)) {
            return Collections.emptyMap();
        }

        Map<String, Boolean> checkResultMap = new HashMap<>(readListHashKeys.size());
        Map<String, String> readNewPropMap = getReadNewPropMap(uid, readListHashKeys);
        readListHashKeys.forEach(e -> {
            String readResult = readNewPropMap.get(e);
            boolean readFlag = StringUtils.isNotBlank(readResult) ? true : false;
            checkResultMap.put(e, readFlag);
        });
        return checkResultMap;
    }

    /**
     * 商城购买道具核心接口
     *
     * @param purchaseOnSalePropReq
     * @param uid
     * @return
     */
    public int purchaseOnSaleProp(PurchaseOnSalePropReq purchaseOnSalePropReq, Long uid) {
        if (uid.equals(purchaseOnSalePropReq.getTargetUserId())) {
            log.warn("purchaseOnSaleProp check targetUserId fail,targetUserId :{},uid:{} is equal", purchaseOnSalePropReq.getTargetUserId(), uid);
            throw new ApiException(CodeEnum.UNABLE_TO_SEND_TO_YOURSELF);
        }
        //校验参数，包含赠送用户id
        if (!checkTargetUserId(purchaseOnSalePropReq.getTargetUserId(), uid)) {
            throw new ApiException(CodeEnum.USER_NOT_FOUND);
        }

        //校验商品是否存在
        long propId = purchaseOnSalePropReq.getId();
        PropInfo propInfo = propServerService.propInfoFromCache(propId);
        if (Objects.isNull(propInfo)) {
            throw new ApiException(CodeEnum.THIS_ITEM_IS_SOLD_OUT);
        }

        //校验价格
        Integer propDayPrice = propPriceDetailService.getPropDayPrice(propId, purchaseOnSalePropReq.getType(),
                purchaseOnSalePropReq.getCurrencyType(), purchaseOnSalePropReq.getDay());
        if (Objects.isNull(propDayPrice)) {
            throw new ApiException(CodeEnum.THIS_ITEM_IS_SOLD_OUT);
        }

        ShopPurchaseDTO shopPurchaseDTO = buildShopPurchaseDTO(propInfo, propDayPrice, purchaseOnSalePropReq, uid);
        //加锁，防止重复提交
        String purchaseKey = RevenueRedisKey.shop_purchase.getKey(uid, purchaseOnSalePropReq.getId());
        try (Locker lock = distributionLocker.lock(purchaseKey)) {
            if (Objects.isNull(lock)) {
                throw new ApiException(CodeEnum.BACKPACK_REQ_DUPLICATE);
            }

            shopPurchaseService.doPurchaseGoods(shopPurchaseDTO);
        } catch (ApiException e) {
            log.error("purchaseOnSalePropReq:{},uid:{},purchaseOnSaleProp occur ApiException,exception:{}",
                    JSON.toJSONString(purchaseOnSalePropReq), uid, ExceptionUtil.formatEx(e));
            int responseCode = e.getResponseCode();
            CodeEnum codeEnum = getByNumber(responseCode);
            throw new ApiException(codeEnum);
        } catch (Exception e) {
            log.error("purchaseOnSalePropReq:{},uid:{},purchaseOnSaleProp occur Exception,exception:{}",
                    JSON.toJSONString(purchaseOnSalePropReq), uid, ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }

        return CodeEnum.SUCCESS.getNumber();
    }

    private ShopPurchaseDTO buildShopPurchaseDTO(PropInfo propInfo, Integer propDayPrice, PurchaseOnSalePropReq purchaseOnSalePropReq, Long uid) {
        return ShopPurchaseDTO.builder()
                .propInfo(propInfo)
                .propDayPrice(propDayPrice)
                .id(purchaseOnSalePropReq.getId())
                .targetUserId(purchaseOnSalePropReq.getTargetUserId())
                .userId(uid)
                .type(purchaseOnSalePropReq.getType())
                .currencyType(purchaseOnSalePropReq.getCurrencyType())
                .day(purchaseOnSalePropReq.getDay())
                .build();
    }


    /**
     * 校验赠送的id
     *
     * @param targetUserId
     * @param uid
     */
    private boolean checkTargetUserId(Long targetUserId, Long uid) {
        if (Objects.isNull(targetUserId) || targetUserId <= 0) {
            return true;
        }

        UserBaseInfoDTO targetUserIdInfo = userServerService.getFromCache(targetUserId);
        if (Objects.isNull(targetUserIdInfo)) {
            log.warn("purchaseOnSaleProp check targetUserId fail,targetUserId :{} is not exist", targetUserId);
            return false;
        }

        //用户状态校验
        UserStatusEnum userStatus = targetUserIdInfo.getUserStatus();
        if (!UserStatusEnum.USER_STATUS_NORMAL.equals(userStatus)) {
            log.warn("purchaseOnSaleProp check targetUserId fail,targetUserId :{} is not normal,userStatus :{}",
                    targetUserId, userStatus);
            return false;
        }

        String blackHint = blockRecordService.blockHint(targetUserId);
        if (StringUtils.isNotBlank(blackHint)) {
            log.warn("purchaseOnSaleProp check targetUserId fail,targetUserId :{} is blocked", targetUserId);
            return false;
        }

        return true;
    }
}
