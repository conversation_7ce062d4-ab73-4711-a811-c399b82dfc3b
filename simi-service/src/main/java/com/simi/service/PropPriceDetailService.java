package com.simi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.PropPriceDetail;
import com.simi.mapper.PropPriceDetailMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Service
@RequiredArgsConstructor
public class PropPriceDetailService extends ServiceImpl<PropPriceDetailMapper, PropPriceDetail> {
    private final PropPriceDetailMapper propPriceDetailMapper;

    public void delByPropId(long propId){
        propPriceDetailMapper.delByPropId(propId);
    }

    public void updateIsDeletedByPropId(long propId,int isDeleted){
        propPriceDetailMapper.updateIsDeletedByPropId(propId,isDeleted);
    }

    public Map<Long, List<PropPriceDetail>> getPriceListByProIds(Set<Long> propIds,Integer propType,int currencyType){
        List<PropPriceDetail> priceDetails = propPriceDetailMapper.getPriceListByProIds(propIds, propType, currencyType);
        Map<Long, List<PropPriceDetail>> propIdToPriceMap = priceDetails.stream().collect(Collectors.groupingBy(PropPriceDetail::getPropId));
        //处理排序，按照有效期正续
        Set<Long> keySet = propIdToPriceMap.keySet();
        for (Long key : keySet) {
            List<PropPriceDetail> propPriceDetails = propIdToPriceMap.get(key);
            propPriceDetails = propPriceDetails.stream().sorted(Comparator.comparingInt(PropPriceDetail::getDay)).collect(Collectors.toList());
            propIdToPriceMap.put(key,propPriceDetails);
        }
        return propIdToPriceMap;
    }

    public Integer getPropDayPrice(long propId,int propType,int currencyType,int day){
        return propPriceDetailMapper.selectDayPrice(propId, propType, currencyType, day);
    }
}
