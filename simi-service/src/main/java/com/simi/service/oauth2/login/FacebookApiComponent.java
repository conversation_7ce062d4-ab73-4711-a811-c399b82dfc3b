package com.simi.service.oauth2.login;

import cn.hutool.core.util.StrUtil;
import com.google.common.collect.Maps;
import com.simi.common.config.FacebookConfig;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.baceBook.FacebookUserinfo;
import com.simi.common.dto.baceBook.TokenDebugResp;
import com.simi.common.dto.baceBook.UserinfoResp;
import com.simi.common.dto.fb.FacebookJWK;
import com.simi.common.exception.ApiException;
import com.simi.common.exception.FacebookException;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.GsonUtil;
import com.simi.constant.GoogleConstant;
import com.nimbusds.jose.JOSEException;
import com.nimbusds.jose.JWSVerifier;
import com.nimbusds.jose.crypto.RSASSAVerifier;
import com.nimbusds.jose.jwk.RSAKey;
import com.nimbusds.jwt.JWTClaimsSet;
import com.nimbusds.jwt.SignedJWT;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.security.PublicKey;
import java.security.interfaces.RSAPublicKey;
import java.text.ParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 脸书API组件
 *
 * <AUTHOR>
 * @date 2023/11/4 19:40
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FacebookApiComponent {

    private final FacebookApi facebookApi;

    private final Map<String, JWSVerifier> verifierMap = Maps.newConcurrentMap();

    public String debugToken(String token) {
        TokenDebugResp result;
        try {
            result = facebookApi.debugToken(StrUtil.format("{}|{}", FacebookConfig.appId, FacebookConfig.appSecret), token);
        } catch (Exception e) {
            log.error("request facebook debug token response failed.token:[{}]", token);
            throw new ApiException(CodeEnum.LOGIN_ERROR);
        }
        if (Objects.isNull(result) || Objects.isNull(result.getData())) {
            log.error("debug token response empty.");
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        log.info("debug token[{}] resp:{}", token, GsonUtil.getGson().toJson(result));
        if(!FacebookConfig.appId.equalsIgnoreCase(result.getData().getAppId())){
            log.error("input token {} debug failure, cause app id not equal.{}", token, result.getData().getAppId());
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        return result.getData().getUserId();
    }

    public FacebookUserinfo getUserinfo(String userId, String token) {
        UserinfoResp result;
        try {
            result = facebookApi.getUserinfo(userId, "id,name,picture.type(large)", token);
        } catch (Exception e) {
            log.error("request facebook getUserinfo response failed.token:[{}] userId:[{}]", token, userId);
            throw new ApiException(CodeEnum.LOGIN_ERROR);
        }
        if (Objects.isNull(result)) {
            log.error("get userinfo response empty.");
            throw new FacebookException("get userinfo error.");
        }
        log.info("get userinfo[{}] resp:{}", userId, GsonUtil.getGson().toJson(result));
        String pictureUrl = null;
        if(Objects.nonNull(result.getPicture()) && Objects.nonNull(result.getPicture().getData())){
            pictureUrl = result.getPicture().getData().getUrl();
        }
        return FacebookUserinfo.builder().id(userId).name(result.getName()).picture(pictureUrl).build();
    }


    public FacebookUserinfo parseJWT(String jwt) {
        try {
            SignedJWT signedJWT = SignedJWT.parse(jwt);
            String keyID = signedJWT.getHeader().getKeyID();
            JWSVerifier verifier = verifierMap.get(keyID);
            if (Objects.isNull(verifier)) {
                reloadJwks();
                verifier = verifierMap.get(keyID);
                if(Objects.isNull(verifier)){
                    log.error("JWT {} verify failure, cause key id {} not equal.", jwt, keyID);
                    throw new FacebookException("Cannot get the JWKS.");
                }
            }
            boolean verify = signedJWT.verify(verifier);
            if (!verify) {
                log.error("JWT {} verify failure.", jwt);
                throw new FacebookException("Verify the jwt failure.");
            }
            JWTClaimsSet claimsSet = signedJWT.getJWTClaimsSet();
            List<String> appids = claimsSet.getStringListClaim("aud");
            boolean sameApp = appids.stream()
                    .anyMatch(a -> a.equalsIgnoreCase(FacebookConfig.appId));
            if (!sameApp) {
                log.error("JWT {} error, cause app id not equal.{}={}", jwt, FacebookConfig.appId, appids);
                throw new FacebookException("app id error.");
            }
            return FacebookUserinfo.builder()
                    .id(claimsSet.getStringClaim("sub"))
                    .name(claimsSet.getStringClaim(GoogleConstant.UserinfoConstant.NAME))
                    .picture(claimsSet.getStringClaim(GoogleConstant.UserinfoConstant.PICTURE))
                    .build();
        } catch (ParseException | JOSEException e) {
            log.error("Verify {} token error:{}", jwt, ExceptionUtil.formatEx(e));
            throw new FacebookException("app id error.");
        }
    }

    private void reloadJwks(){
        FacebookJWK jwks = facebookApi.jwks();
        log.info("facebook jwks:{}", GsonUtil.getGson().toJson(jwks));
        jwks.getKeys().forEach(key -> {
            try {

                RSAKey rsaKey = RSAKey.parse(GsonUtil.getGson().toJson(key));
                PublicKey publicKey = rsaKey.toPublicKey();
                JWSVerifier verifier = new RSASSAVerifier((RSAPublicKey) publicKey);
                verifierMap.put(key.getKid(), verifier);
            } catch (ParseException | JOSEException e) {
                log.error("Parse rsa key error:{}", ExceptionUtil.formatEx(e));
            }
        });
    }

    /*@PostConstruct
    public void init() {
        reloadJwks();
    }*/
}
