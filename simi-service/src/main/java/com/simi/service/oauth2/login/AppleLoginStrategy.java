package com.simi.service.oauth2.login;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.SignTypeEnum;
import com.simi.common.constant.login.LoginTypeEnum;
import com.simi.common.dto.ThirdPartyUserinfo;
import com.simi.common.vo.login.LoginReq;
import com.simi.common.vo.resp.AppleUserinfo;
import com.simi.entity.account.Account;
import com.simi.service.oauth2.AccountService;
import com.simi.service.user.UidService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 苹果登录策略
 */
@Slf4j
@Service("apple")
@RequiredArgsConstructor
public class AppleLoginStrategy implements ILoginStrategy {

    private final UidService uidService;
    private final AccountService accountService;
    private final AppleApiComponent appleApiComponent;

    @Override
    public Account getAccountById(String id, String areaCode) {
        return accountService.getByAppleId(id);
    }

    @Override
    public ThirdPartyUserinfo getUserinfo(LoginReq loginReq) {
        log.info("AppleLogin req:{}", loginReq);
        AppleUserinfo userinfo = appleApiComponent.tokeninfo(loginReq.getCode());
        log.info("AppleLogin userinfo:{}", JSONUtil.toJsonStr(userinfo));
        return ThirdPartyUserinfo.builder()
                .id(userinfo.getId())
                /* .name(userinfo.getName())
                .picture(userinfo.getPicture())*/
                .type(LoginTypeEnum.apple.name())
                .build();
    }

    @Override
    public Account create(LoginReq param, ThirdPartyUserinfo userinfo,
                          String clientIp) {
        final long uid = uidService.genUid();
        log.info("generate user id:{}", uid);
        Account account = Account.builder()
                .uid(uid)
                .appleId(userinfo.getId())
                .os(param.getOs())
                .osVersion(param.getOsVersion())
                .app(param.getApp())
                .model(param.getModel())
                .deviceId(param.getDeviceId())
                .signTime(new Date())
                .signType(SignTypeEnum.APPLE.getCode())
                .appVersion(param.getAppVersion())
                .appVersionCode(param.getAppVersionCode())
                .channel(param.getChannel())
                .deviceBrand(param.getDeviceBrand())
                .systemLanguage(param.getSystemLanguage())
                .appLanguage(param.getAppLanguage())
                .isp(param.getIsp())
                .countryCode(param.getCountryCode())
                .createTime(new Date())
//                .ipLocation(ipApiComponent.countryCodeByIp(clientIp))
                .build();
        return accountService.create(account, userinfo.getName(), userinfo.getPicture(), clientIp);
    }

    @Override
    public String getType() {
        return LoginTypeEnum.apple.name();
    }

    @Override
    public void verifyAccount(Account account, LoginReq req) {

    }

    @Override
    public Account bindingAccount(Long uid, String id, String passwd) {
        return accountService.bandingApple(uid, id);
    }

}
