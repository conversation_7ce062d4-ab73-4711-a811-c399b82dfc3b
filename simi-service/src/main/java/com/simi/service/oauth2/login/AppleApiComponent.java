package com.simi.service.oauth2.login;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.simi.common.vo.resp.AppleUserinfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpHeaders;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;

/**
 * 苹果登录api
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AppleApiComponent {

    public AppleUserinfo tokeninfo(String token) {
        AppleUserinfo appleUserinfo = new AppleUserinfo();
        String uniqueId = appleApi(token);
        appleUserinfo.setId(uniqueId);
        return appleUserinfo;
    }


    public static String appleApi(String token){
        HttpClient client = HttpClient.newHttpClient();
        String jsonBody = "{\"code\": \""+ token +"\"}";

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-internal-services:8089/apple/get-unique-id"))
                .header("Content-Type", "application/json")
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                .build();

        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            HttpHeaders headers = response.headers();
            headers.map().forEach((k, v) -> System.out.println(k + ":" + v));
            Gson gson = new Gson();
            JsonObject jsonObject = gson.fromJson(response.body(), JsonObject.class);
            String uniqueId = jsonObject.get("unique_id").getAsString();
            return uniqueId;
        }catch (Exception e) {
            log.error("error..............",e);
        }
        return null;
    }

    public static void main(String[] args) {
        String resp = appleApi("c7b17fa35de584f989db4804733a4b636.0.rrzrq.zwQtV-abLk0E55dh0ijtxQ");
        System.out.println("resp " + resp);
    }
}
