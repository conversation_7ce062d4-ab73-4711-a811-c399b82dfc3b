package com.simi.service.oauth2.login;

import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import com.simi.common.util.ExceptionUtil;
import com.simi.entity.huawei.ResponseInfos;
import com.simi.service.oauth2.login.huawei.HttpClientUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.Charsets;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/09 17:08
 **/
@Slf4j
public class AuthCodeUtil {

    private static final String CODE_GRANT_TYPE = "authorization_code";

    private static final String REFRESH_TOKEN_GRANT_TYPE = "refresh_token";

    private static final String URL_SCOPE = "https://oauth-api.cloud.huawei.com/rest.php";

    private static final String URL_GET_INFO = "https://account.cloud.huawei.com/rest.php";

    private static final String TOKEN_URI = "https://oauth-login.cloud.huawei.com/oauth2/v3/token";

    private static final String OPEN_ID = "OPENID";

    private static final String NSP_SVC_GET_INFO = "GOpen.User.getInfo";

    private static final String NSP_SVC_PERSE_ACCESS_TOKEN = "huawei.oauth2.user.getTokenInfo";

    private static final int HTTP_STATUS_OK = 200;

    /**
     * get access token and refresh token by code
     * if request success,will return as ResponseInfos{body=TokensEntity,nspStatus=0}
     * if request fail,will return as ResponseInfos{body=ErrorInfos,nspStatus=*}
     *
     * @param code
     * @param appId
     * @param appSecret
     * @param redirectUri
     * @return
     */
    public static ResponseInfos getTokensByCode(String code, String appId, String appSecret, String redirectUri) {
        HttpPost httpPost = new HttpPost(TOKEN_URI);
        ResponseInfos responseInfos = null;
        List<NameValuePair> request = new ArrayList<>();
        request.add(new BasicNameValuePair("redirect_uri", redirectUri));
        request.add(new BasicNameValuePair("code", code));
        request.add(new BasicNameValuePair("client_secret", appSecret));
        request.add(new BasicNameValuePair("client_id", appId));
        request.add(new BasicNameValuePair("grant_type", CODE_GRANT_TYPE));
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(request));
            responseInfos = executeHttpRequest(httpPost);
        } catch (Exception e) {
            log.info("huawei getTokensByCode failed:[{}] code:[{}]", ExceptionUtil.formatEx(e), code);
            throw new ApiException(CodeEnum.LOGIN_ERROR);
        }
        return responseInfos;
    }

    /**
     * parse access token
     * if request success,will return as ResponseInfos{body=AccessTokenInfos,nspStatus=0}
     * if request fail,will return as ResponseInfos{body=ErrorInfos,nspStatus=*}
     *
     * @param accessToken
     * @return
     */
    public static ResponseInfos parseAccessToken(String accessToken) {
        ResponseInfos responseInfos = null;
        HttpPost httpPost = new HttpPost(URL_SCOPE);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("nsp_svc=");
        stringBuffer.append(NSP_SVC_PERSE_ACCESS_TOKEN);
        stringBuffer.append("&open_id=");
        stringBuffer.append(OPEN_ID);
        stringBuffer.append("&access_token=");
        try {
            stringBuffer.append(URLEncoder.encode(accessToken, "UTF-8"));
            StringEntity entity = new StringEntity(stringBuffer.toString());
            httpPost.setEntity(entity);
            responseInfos = executeHttpRequest(httpPost);
        } catch (Exception e) {
            log.info("huawei parseAccessToken failed:[{}] accessToken:[{}]", ExceptionUtil.formatEx(e), accessToken);
            throw new ApiException(CodeEnum.LOGIN_ERROR);
        }
        return responseInfos;
    }

    /**
     * update access token by refresh token when access token is expired,
     * if request success,will return as ResponseInfos{body=TokensEntity,nspStatus=0}
     * if request fail,will return as ResponseInfos{body=ErrorInfos,nspStatus=*}
     *
     * @param refreshToken
     * @param appId
     * @param appSecret
     * @return
     */
    public static ResponseInfos updateAccessToken(String refreshToken, String appId, String appSecret) {
        ResponseInfos responseInfos = null;
        HttpPost httpPost = new HttpPost(TOKEN_URI);
        List<NameValuePair> request = new ArrayList<>();
        request.add(new BasicNameValuePair("refresh_token", refreshToken));
        request.add(new BasicNameValuePair("client_secret", appSecret));
        request.add(new BasicNameValuePair("client_id", appId));
        request.add(new BasicNameValuePair("grant_type", REFRESH_TOKEN_GRANT_TYPE));
        try {
            httpPost.setEntity(new UrlEncodedFormEntity(request));
            responseInfos = executeHttpRequest(httpPost);
        } catch (IOException e) {
            log.info("huawei updateAccessToken failed:[{}] refreshToken:[{}]", ExceptionUtil.formatEx(e), refreshToken);
            throw new ApiException(CodeEnum.LOGIN_ERROR);
        }
        return responseInfos;
    }

    /**
     * get user Infos by access token
     * if request success,will return as ResponseInfos{body=UserInfos,nspStatus=0}
     * if request fail,will return as ResponseInfos{body=ErrorInfos,nspStatus=*}
     *
     * @param accessToken
     * @param getNickName
     * @return
     * @throws IOException
     */
    public static ResponseInfos getUserInfos(String accessToken, int getNickName) {
        ResponseInfos responseInfos = null;
        HttpPost httpPost = new HttpPost(URL_GET_INFO);
        httpPost.setHeader("Content-Type", "application/x-www-form-urlencoded");
        StringBuffer stringBuffer = new StringBuffer();
        stringBuffer.append("nsp_fmt=");
        stringBuffer.append("JSON");
        stringBuffer.append("&nsp_svc=");
        stringBuffer.append(NSP_SVC_GET_INFO);
        stringBuffer.append("&access_token=");
        try {
            stringBuffer.append(URLEncoder.encode(accessToken, "UTF-8"));
            stringBuffer.append("&getNickName=");
            stringBuffer.append(getNickName);
            StringEntity entity = new StringEntity(stringBuffer.toString());
            httpPost.setEntity(entity);
            responseInfos = executeHttpRequest(httpPost);
        } catch (Exception e) {
            log.info("huawei getUserInfos failed:[{}] accessToken:[{}]", ExceptionUtil.formatEx(e), accessToken);
            throw new ApiException(CodeEnum.LOGIN_ERROR);
        }
        return responseInfos;
    }

    /**
     * execute http request
     *
     * @param httpPost
     * @return
     */
    private static ResponseInfos executeHttpRequest(HttpPost httpPost) {
        CloseableHttpResponse response = null;
        ResponseInfos responseInfos = null;
        try {
            response = HttpClientUtil.getClient().execute(httpPost);
            HttpEntity responseEntity = response.getEntity();
            Header[] nsp_statuses = response.getHeaders("NSP_STATUS");
            String ret = responseEntity != null ? EntityUtils.toString(responseEntity, Charsets.UTF_8) : null;
            responseInfos = new ResponseInfos();
            responseInfos.setBody(ret);
            if (response.getStatusLine().getStatusCode() != HTTP_STATUS_OK) {
                log.error("huawei executeHttpRequest http request failed,{}", ret);
                throw new ApiException(CodeEnum.REGISTER_ERROR);
            }
            EntityUtils.consume(responseEntity);
            if (nsp_statuses.length == 1) {
                String value = nsp_statuses[0].getValue();
                responseInfos.setNspStatus(Integer.valueOf(value));
                log.info("NSP_STATUS:{}", value);
            }
            return responseInfos;
        } catch (IOException e) {
            log.info("huawei executeHttpRequest failed:[{}] ", ExceptionUtil.formatEx(e));
        } finally {
            try {
                response.close();
            } catch (Exception e) {
                log.info("huawei response.close failed:[{}] ", ExceptionUtil.formatEx(e));
            }
        }
        return responseInfos;
    }
}
