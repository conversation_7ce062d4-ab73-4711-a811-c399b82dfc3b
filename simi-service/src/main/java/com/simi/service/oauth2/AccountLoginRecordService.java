package com.simi.service.oauth2;


import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.constant.login.LoginTypeEnum;
import com.simi.common.constant.user.LoginType;
import com.simi.common.dto.user.AccountLoginRecordDTO;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.vo.login.LoginReq;
import com.simi.constant.PlatformEnum;
import com.simi.entity.account.AccountLoginRecord;
import com.simi.mapper.account.AccountLoginRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 登录记录
 *
 * <AUTHOR>
 * @date 2023/11/7 16:35
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountLoginRecordService extends ServiceImpl<AccountLoginRecordMapper, AccountLoginRecord> {

    /**
     * 创建登录记录
     */
    public void create(Long uid, Long userNo, LoginTypeEnum loginType, LoginReq loginReq, Byte status, String clientIp, XAuthToken xAuthToken,String did){
        AccountLoginRecord record = AccountLoginRecord.builder()
                .uid(uid)
                .userNo(userNo)
                .loginType(loginType.name())
                .os(loginReq.getOs())
                .osVersion(loginReq.getOsVersion())
                .app(loginReq.getApp())
                .appVersion(loginReq.getAppVersion())
                .model(loginReq.getModel())
                .deviceId(did)
                .fingerprint(Objects.nonNull(xAuthToken) ? xAuthToken.getFingerprint() : StrUtil.EMPTY)
                .createTime(new Date())
                .status(status)
                .loginIp(clientIp)
                .build();
        save(record);
    }

    public void createWebLoginRecord(Long uid, LoginTypeEnum loginType, Byte status){
        AccountLoginRecord record = AccountLoginRecord.builder()
                .uid(uid)
                .loginType(loginType.name())
                .createTime(new Date())
                .status(status)
                .build();
        save(record);
    }

    /**
     * 用户最后一次登录记录
     * @param uid
     * @return
     */
    public AccountLoginRecord lastLoginRecord(final long uid){
        List<AccountLoginRecord> list = lambdaQuery().eq(AccountLoginRecord::getUid, uid).orderByDesc(AccountLoginRecord::getCreateTime).list();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    /**
     * 设备最后登录记录
     * @param did
     * @return
     */
    public AccountLoginRecord deviceLastRecord(String did){
        List<AccountLoginRecord> list = lambdaQuery().eq(AccountLoginRecord::getDeviceId, did).orderByDesc(AccountLoginRecord::getCreateTime).list();
        if(CollectionUtils.isEmpty(list)){
            return null;
        }
        return list.get(0);
    }

    public ListWithTotal<AccountLoginRecordDTO> queryList(Long uid, Long userNo, String deviceId, String fingerprint,
                                                          Integer loginType, Integer os, String ip, String model, Long stime, Long etime, Integer pageNum, Integer pageSize){
        String loginTypeStr = null;
        if (Objects.nonNull(loginType)) {
            LoginType loginTypeEnum = LoginType.getByType(loginType);
            if (Objects.nonNull(loginTypeEnum)) {
                loginTypeStr = loginTypeEnum.getDesc();
            }
        }
        String osStr = null;
        if (Objects.nonNull(os)) {
            PlatformEnum platformEnum = PlatformEnum.forNumber(os);
            if (Objects.nonNull(platformEnum)) {
                osStr = platformEnum.getName();
            }
        }
        Date startTime = null;
        if (Objects.nonNull(stime)) {
            startTime = new Date(stime);
        }
        Date endTime = null;
        if (Objects.nonNull(etime)) {
            endTime = new Date(etime);
        }
        LambdaQueryWrapper<AccountLoginRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(uid) ,AccountLoginRecord::getUid, uid);
        wrapper.eq(Objects.nonNull(userNo),AccountLoginRecord::getUserNo,userNo);
        wrapper.like(StrUtil.isNotBlank(deviceId),AccountLoginRecord::getDeviceId,deviceId);
        wrapper.like(StrUtil.isNotBlank(fingerprint),AccountLoginRecord::getFingerprint,fingerprint);
        wrapper.eq(StrUtil.isNotBlank(loginTypeStr), AccountLoginRecord::getLoginType, loginTypeStr);
        wrapper.eq(StrUtil.isNotBlank(osStr), AccountLoginRecord::getOs, osStr);
        wrapper.like(StrUtil.isNotBlank(ip),AccountLoginRecord::getLoginIp,ip);
        wrapper.like(StrUtil.isNotBlank(model),AccountLoginRecord::getModel,model);
        wrapper.gt(Objects.nonNull(startTime), AccountLoginRecord::getCreateTime, startTime);
        wrapper.lt(Objects.nonNull(endTime), AccountLoginRecord::getCreateTime, endTime);
        PageHelper.startPage(pageNum, pageSize, "create_time DESC");
        List<AccountLoginRecord> records = this.list(wrapper);
        PageInfo<AccountLoginRecord> pageInfo = new PageInfo<>(records);
        List<AccountLoginRecordDTO> resultList = pageInfo.getList().stream().map(e -> {
            AccountLoginRecordDTO build = AccountLoginRecordDTO.builder()
                    .id(e.getRecordId())
                    .uid(e.getUid())
                    .userNo(e.getUserNo())
                    .deviceId(e.getDeviceId())
                    .fingerprint(e.getFingerprint())
                    .os(e.getOs())
                    .model(e.getModel())
                    .loginType(e.getLoginType())
                    .ip(e.getLoginIp())
                    .createTime(e.getCreateTime()).build();
            return build;
        }).toList();
        return ListWithTotal.<AccountLoginRecordDTO>builder().total(pageInfo.getTotal()).list(resultList).build();
    }

}
