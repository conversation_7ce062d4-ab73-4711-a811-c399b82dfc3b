package com.simi.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.ResourceStatuEnum;
import com.simi.common.constant.resource.ResourceRedisKey;
import com.simi.common.dto.UserPropInfoDTO;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.util.RedissonManager;
import com.simi.constant.DeleteFlagEnum;
import com.simi.entity.PropInfo;
import com.simi.service.cache.BackPackCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/05/20 21:04
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class PropServerService {

    private final PropInfoService propInfoService;
    private final RedissonManager redissonManager;
    private final BackPackCache backPackCache;

    public PropInfo propInfoFromCache(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }

        String propInfoStr = redissonManager.hGet(ResourceRedisKey.prop_info.getKey(), String.valueOf(id));
        if (StrUtil.isNotBlank(propInfoStr)) {
            return JSONUtil.toBean(propInfoStr, PropInfo.class);
        }

        PropInfo propInfo = propInfoService.lambdaQuery()
                .eq(PropInfo::getId, id)
                .eq(PropInfo::getDeleteFlag, DeleteFlagEnum.UN_DELETED.getType())
                .eq(PropInfo::getStatus, ResourceStatuEnum.LISTING.getStatu())
                .oneOpt().orElse(null);

        if (propInfo != null) {
            redissonManager.hSet(ResourceRedisKey.prop_info.getKey(), String.valueOf(id), JSONUtil.toJsonStr(propInfo));
        }

        return propInfo;
    }

    public Map<Long, PropInfo> batchPropInfoFromCache(List<Long> ids) {
        Map<Long, PropInfo> resultMap = MapUtil.newHashMap();

        Set<String> idsSet = ids.stream().map(String::valueOf).collect(Collectors.toSet());
        Map<String, String> propInfoMap = redissonManager.hMGet(ResourceRedisKey.prop_info.getKey(), idsSet);

        propInfoMap.forEach((goodsId, dtoStr) -> resultMap.put(Long.parseLong(goodsId), JSONUtil.toBean(dtoStr, PropInfo.class)));

        if (ids.size() != resultMap.size()) {
            Set<Long> noCacheIds = ids.stream().filter(e -> !resultMap.containsKey(e)).collect(Collectors.toSet());
            List<PropInfo> propInfos = propInfoService.lambdaQuery()
                    .in(PropInfo::getId, noCacheIds)
                    .eq(PropInfo::getDeleteFlag, DeleteFlagEnum.UN_DELETED.getType())
                    .eq(PropInfo::getStatus, ResourceStatuEnum.LISTING.getStatu())
                    .list();

            if (CollUtil.isNotEmpty(propInfos)) {
                propInfos.forEach(propInfo -> {
                    resultMap.put(propInfo.getId(), propInfo);
                    redissonManager.hSet(ResourceRedisKey.prop_info.getKey(), String.valueOf(propInfo.getId()), JSONUtil.toJsonStr(propInfo));
                });
            }
        }

        return resultMap;
    }


    public Map<Long, Map<Integer, UserPropInfoDTO>> batchUserUsingProp(List<Long> uids) {
        if (CollUtil.isEmpty(uids)) {
            return MapUtil.empty();
        }
        Map<Long, Map<Integer, UserPropInfoDTO>> result = MapUtil.newHashMap(uids.size());
        uids.forEach(uid -> {
            Map<String, String> userGoodsAll = backPackCache.getUserGoodsAll(uid);
            if (MapUtil.isEmpty(userGoodsAll)) {
                return;
            }
            List<Long> goodsIds = userGoodsAll.values().stream()
                    .map(Long::parseLong)
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(goodsIds)) {
                return;
            }
            Map<Long, PropInfo> propInfoMap = this.batchPropInfoFromCache(goodsIds);
            Map<Integer, UserPropInfoDTO> propDTOTypeMap = propInfoMap.values().stream()
                    .map(propInfo -> buildDTO(propInfo, uid))
                    .collect(Collectors.toMap(UserPropInfoDTO::getGoodsType, Function.identity(), (k1, k2) -> k1));
            result.put(uid, propDTOTypeMap);
        });
        return result;
    }

    public UserPropInfoDTO buildDTO(PropInfo info, Long uid) {
        UserPropInfoDTO dto = new UserPropInfoDTO();
        dto.setId(info.getId());
        dto.setGoodsId(info.getId());
        dto.setIcon(info.getIcon());
        if (MessageSourceUtil.getLang().name().equals(LanguageEnum.ar.name())) {
            dto.setName(info.getNameAr());
        } else {
            dto.setName(info.getNameEn());
        }
        dto.setAnimationUrl(info.getAnimationUrl());
        dto.setAnimationType(info.getAnimationType());
        dto.setDuration(info.getDirection());
        dto.setUserId(uid);
        dto.setGoodsType(info.getType());
        dto.setState(info.getStatus());
        dto.setDirection(info.getDirection());
        dto.setCirculationUrl(info.getCirculationUrl());
        return dto;
    }
}
