package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.simi.common.constant.MatchTypeEnum;
import com.simi.common.dto.room.HomeRoomDTO;
import com.simi.common.dto.room.RoomAudienceDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.constant.RoomRedisKey;
import com.simi.dto.*;
import com.simi.entity.MapInvitation;
import com.simi.entity.room.Room;
import com.simi.entity.user.User;
import com.simi.service.cache.CollectCache;
import com.simi.service.cache.MatchCache;
import com.simi.service.room.RoomHighService;
import com.simi.service.user.BlackService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MatchService {

    private final UserServerService userServerService;

    private final RedissonClient redissonClient;

    private final RoomHighService roomHighService;

    private final MatchCache matchCache;

    private final MapInvitationService mapInvitationService;

    private final CollectCache collectCache;

    private final BlackService blackService;

    public MatchDTO match(Long uid, Double longitude,Double latitude){
        List<User> users = userServerService.matchUser(uid, longitude, latitude);
        Collections.shuffle(users);
        MatchDTO matchDTO = new MatchDTO();
        //匹配过的数据
        Map<String, String> match = matchCache.getMatch(uid);
        if (CollectionUtils.isNotEmpty(users)) {
            Set<String> roomIds = redissonClient.<String>getSet(RoomRedisKey.room_in_live.getKey()).readAll();
            Set<User> userSet = new HashSet<>(users);
            for (User user : userSet) {
                boolean blackOrNot = blackService.getBlacklist(uid, user.getUid());
                //房间数据
                Room room = roomHighService.getRoom(user.getUid());
                if (room != null) {
                    String roomId = match.get(user.getUid() + ":" + MatchTypeEnum.ROOM.getNumber());
                    if (roomIds.contains(room.getId()) && StringUtils.isBlank(roomId)) {
                        //拼接房间数据
                        HomeRoomDTO dto = BeanUtil.copyProperties(room, HomeRoomDTO.class);
                        dto.setRoomId(room.getId());
                        RoomAudienceDTO roomAudienceDTO = roomHighService.audienceTop(room.getId(), 5);
                        dto.setAudienceTop5(roomAudienceDTO);
                        matchDTO.setMatchType(MatchTypeEnum.ROOM.getNumber());
                        matchDTO.setHomeRoomDTO(dto);
                        matchCache.setMatchTypeId(uid,MatchTypeEnum.ROOM.getNumber(),user.getUid(),roomId);
                        return matchDTO;
                    }
                }
                String matchUid = match.get(user.getUid() + ":" + MatchTypeEnum.USER.getNumber());
                if (StringUtils.isBlank(matchUid)) {
                    //拼接用户数据
                    UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(user.getUid());
                    matchDTO.setMatchType(MatchTypeEnum.USER.getNumber());
                    matchDTO.setUserBaseInfo(userBaseInfo);
                    matchCache.setMatchTypeId(uid,MatchTypeEnum.USER.getNumber(),user.getUid(),user.getUid().toString());
                    return matchDTO;
                }
                List<MapInvitation> listByUid = mapInvitationService.getListByUid(user.getUid());
                if (CollectionUtils.isNotEmpty(listByUid)) {
                    for (MapInvitation mapInvitation : listByUid) {
                        String invitationId = match.get(user.getUid() + ":" + MatchTypeEnum.INVITATION.getNumber());
                        if (StringUtils.isEmpty(invitationId)){
                            MatchMapInvitationDTO invitation = getMapInvitation(mapInvitation);
                            matchDTO.setMatchType(MatchTypeEnum.INVITATION.getNumber());
                            matchDTO.setInvitation(invitation);
                            matchCache.setMatchTypeId(uid,MatchTypeEnum.INVITATION.getNumber(),user.getUid(),mapInvitation.getId().toString());
                            return matchDTO;
                        }
                    }
                }
            }
        }
        return matchDTO;
    }


    public MatchMapInvitationDTO getMapInvitation(MapInvitation mapInvitation) {
        MatchMapInvitationDTO mapInvitationDTO = BeanUtil.copyProperties(mapInvitation, MatchMapInvitationDTO.class);
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(mapInvitationDTO.getUid());
        if (userBaseInfo != null) {
            mapInvitationDTO.setSendUser(userBaseInfo);
        }
        List<CommentCacheDTO> commentCacheDTOS = collectCache.getComment(mapInvitationDTO.getId());
        mapInvitationDTO.setCommentNum(commentCacheDTOS.size());

        return mapInvitationDTO;
    }
}
