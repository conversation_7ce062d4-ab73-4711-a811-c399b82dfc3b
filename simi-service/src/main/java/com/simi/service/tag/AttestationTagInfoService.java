package com.simi.service.tag;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.tag.AttestationTagInfo;
import com.simi.mapper.tag.AttestationTagInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Slf4j
@RequiredArgsConstructor
public class AttestationTagInfoService  extends ServiceImpl<AttestationTagInfoMapper, AttestationTagInfo> {
    private final AttestationTagInfoMapper attestationTagInfoMapper;
    public List<AttestationTagInfo> getEffectiveTag(Long uid, Integer tagStatus, long timestamp){
        return attestationTagInfoMapper.getEffectiveTag(uid,tagStatus,timestamp);
    }

}
