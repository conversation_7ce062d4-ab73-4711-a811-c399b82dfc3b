package com.simi.service.game;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.RewardPackCopywritingEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.game.FruitPartyConfigDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.room.RoomInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.CommonUtil;
import com.simi.common.vo.req.game.RegularGameBillReq;
import com.simi.constant.BillEnum;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.RegularGameEnum;
import com.simi.dto.UserSimple;
import com.simi.dto.push.JoyEffectBannerDTO;
import com.simi.message.RegularGameMQMessage;
import com.simi.service.LongLinkService;
import com.simi.service.mission.MissionService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomHighService;
import com.simi.service.user.UserServerService;
import com.simi.util.TranslationCopyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class RegularGamesService {

    private final PurseManageService purseManageService;
    private final UserServerService userServerService;
    private final RoomHighService roomHighService;
    private final LongLinkService longLinkService;
    private final SystemConfigService systemConfigService;
    private final MissionService missionService;


    public void reverseWin(RegularGameMQMessage gameMQMessage) {
        Long gold = gameMQMessage.getGold();
        Long specialGold = Optional.ofNullable(gameMQMessage.getSpecialGold()).orElse(0L);

        gold = gold + specialGold;

        Long uid = gameMQMessage.getUid();
        Integer joyType = gameMQMessage.getJoyType();
        RegularGameEnum gameEnum = RegularGameEnum.getByJoyType(joyType);
        String reqJSON = JSONUtil.toJsonStr(gameMQMessage);
        if (Objects.isNull(gameEnum)) {
            log.error("regular game reverseWin joyType empty info:[{}]", reqJSON);
        }
        String commonId = CommonUtil.genId();
        log.info("succ regular game reverseWin message:[{}] commonId:[{}]", reqJSON, commonId);
        BillEnum incomeBill = gameEnum.getIncomeBill();
        purseManageService.addCoin(uid, gold, incomeBill, commonId, StrUtil.EMPTY, Collections.emptyMap(), 0L, PurseRoleTypeEnum.USER.getType());

        // 发横幅
        try {
            joyEffectBanner(gameMQMessage);
        } catch (Exception e) {
            log.error("regular game push effect banner fail msg:[{}]", e.getMessage(), e);
        }

        if (Objects.equals(RegularGameEnum.FRUIT_PARTY, gameEnum)) {
            missionService.fruitPartyGameMission(uid, gold, gameMQMessage.getMessageTime());
        }

    }


    /**
     * 横幅
     */
    public void joyEffectBanner(RegularGameMQMessage gameMQMessage) {
        Long uid = gameMQMessage.getUid();
        Integer winMultiple = gameMQMessage.getWinMultiple();

        String config = systemConfigService.getSysConfValueById(SystemConfigConstant.FRUIT_PARTY_EFFECT_CONFIG);

        String kbConfig = systemConfigService.getSysConfValueById(SystemConfigConstant.KING_BATTLE_EFFECT_CONFIG);

        FruitPartyConfigDTO configDTO = JSONUtil.toBean(config, FruitPartyConfigDTO.class);

        FruitPartyConfigDTO kbConfigDTO = JSONUtil.toBean(kbConfig, FruitPartyConfigDTO.class);

        Integer joyType = gameMQMessage.getJoyType();
        RegularGameEnum gameEnum = RegularGameEnum.getByJoyType(joyType);
        if (Objects.isNull(gameEnum)) {
            log.info("joy effect banner type empty");
            return;
        }
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
        UserSimple userSimple = BeanUtil.toBean(userBaseInfo, UserSimple.class);

        CopywritingEnum copywritingEnum = gameEnum.getCopywritingEnum();

        TranslationCopyDTO translationCopyDTO = TranslationCopyUtil.translationCopy(copywritingEnum.getKey(), null);

        // 房间上锁就仅仅发当前房间, 否则全服
        String roomIdFlag = null;
        RoomInfoDTO userInRoom = roomHighService.getUserInRoom(uid);
        if (Objects.nonNull(userInRoom) && Objects.nonNull(userInRoom.getRoomLock())
                && userInRoom.getRoomLock() && StrUtil.isNotBlank(userInRoom.getRoomPasswd())) {
            roomIdFlag = userInRoom.getRoomId();
        }

        if (StrUtil.isNotBlank(config) && CollUtil.isNotEmpty(configDTO.getEffectMultiple())
                && configDTO.getEffectMultiple().contains(winMultiple)) {
            JoyEffectBannerDTO effectBannerDTO = JoyEffectBannerDTO.builder()
                    .userInfo(userSimple)
                    .icon(configDTO.getIcon())
                    .gold(gameMQMessage.getGold())
                    .effectUrl(configDTO.getEffectUrl())
                    .joyType(joyType)
                    .nameCopywriting(translationCopyDTO)
                    .winMultiple(winMultiple)
                    .roomId(userInRoom == null ? null:userInRoom.getRoomId())
                    .build();
            longLinkService.pushCustomerRoomBannerList(roomIdFlag,effectBannerDTO, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
            if (roomIdFlag == null) {
                longLinkService.pushCustomerRoomMsg(roomIdFlag, effectBannerDTO, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
            }
        }

        if (gameMQMessage.getSpecialGold() != null && gameMQMessage.getSpecialGold() > 0) {
            TranslationCopyDTO unitTranslation = TranslationCopyUtil.translationCopy(RewardPackCopywritingEnum.COINS.getKey(), null);
            JoyEffectBannerDTO effectBannerDTO2 = JoyEffectBannerDTO.builder()
                    .userInfo(userSimple)
                    .icon(configDTO.getIcon())
                    // 特殊横幅金币 = 保底金币 + 原本的金币
                    .gold(gameMQMessage.getSpecialGold() + gameMQMessage.getGold())
                    .effectUrl(configDTO.getEffectUrl())
                    .joyType(joyType)
                    .nameCopywriting(translationCopyDTO)
                    .winMultiple(winMultiple)
                    .unit(unitTranslation)
                    .roomId(userInRoom == null ? null:userInRoom.getRoomId())
                    .build();
            longLinkService.pushCustomerRoomBannerList(roomIdFlag, effectBannerDTO2, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
            if (roomIdFlag == null) {
                longLinkService.pushCustomerRoomMsg(roomIdFlag, effectBannerDTO2, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
            }
        }

        if (Objects.equals(joyType, RegularGameEnum.THREE_CARD_BRAG.getJoyType()) && gameMQMessage.getWinMultiple() == 15) {
            JoyEffectBannerDTO effectBannerDTO2 = JoyEffectBannerDTO.builder()
                    .userInfo(userSimple)
                    .icon(kbConfigDTO.getIcon())
                    // 特殊横幅金币 = 保底金币 + 原本的金币
                    .gold(gameMQMessage.getGold())
                    .effectUrl(kbConfigDTO.getEffectUrl())
                    .joyType(joyType)
                    .nameCopywriting(translationCopyDTO)
                    .winMultiple(winMultiple)
                    .roomId(userInRoom == null ? null:userInRoom.getRoomId())
                    .build();
            longLinkService.pushCustomerRoomBannerList(roomIdFlag,effectBannerDTO2, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
            if (roomIdFlag == null) {
                longLinkService.pushCustomerRoomMsg(roomIdFlag, effectBannerDTO2, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
            }
        }
    }

    /**
     * 账单创建处理
     *
     * @param req
     */
    public void createBill(Long uid, RegularGameBillReq req) {
        if (Objects.isNull(uid)) {
            throw new ApiException(CodeEnum.ACCOUNT_NOT_EXIST);
        }
        long start1 = System.currentTimeMillis();
        UserBaseInfoDTO userInfo = userServerService.getFromCache(uid);
        if (Objects.isNull(userInfo)) {
            throw new ApiException(CodeEnum.ACCOUNT_NOT_EXIST);
        }
        log.info("regular games user create bill uid:[{}]  req:[{}] cost:[{}]",
                uid, JSONUtil.toJsonStr(req), System.currentTimeMillis() - start1);


        if (Objects.isNull(req) || req.getGold() < 1) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        RegularGameEnum gameEnum = RegularGameEnum.getByJoyType(req.getJoyType());
        if (Objects.isNull(gameEnum)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        long start2 = System.currentTimeMillis();
        PurseDTO purse = purseManageService.getPurse(uid);
        if (Objects.isNull(purse) || purse.getCoin() < req.getGold()) {
            throw new ApiException(CodeEnum.BALANCE_NOT_ENOUGH);
        }
        log.info("regular games user purseInfo:[{}]  req:[{}] cost:[{}]",
                uid, JSONUtil.toJsonStr(req), System.currentTimeMillis() - start2);

        long start3 = System.currentTimeMillis();
        /*boolean status = obtainPlayStatus(uid);

        if (!status) {
            log.error("regular games obtain play status fail uid:[{}]  req:[{}]", uid, JSONUtil.toJsonStr(req));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }*/
        String commonId = CommonUtil.genId();
        log.info("succ regular games user create bill uid:[{}] req:[{}] commonId:[{}]", uid, JSONUtil.toJsonStr(req), commonId);
        purseManageService.deductCoin(uid, req.getGold(), gameEnum.getExpenseBill(),
                CommonUtil.genId(), StrUtil.EMPTY, Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
        log.info("regular games user deductCoin,[{}]  req:[{}] cost:[{}]",
                uid, JSONUtil.toJsonStr(req), System.currentTimeMillis() - start3);
    }


    private boolean obtainPlayStatus(Long uid) {
        /*Random rand = new Random();
        int code = rand.nextInt((9999 - 1000) + 1) + 1000;

        HttpPostReq req = new HttpPostReq();
        Map<String, String> param = new HashMap<>();
        param.put("code", code + "");
        req.setParam(param);
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=UTF-8");
        req.setHeader(header);
        req.setUrl("http://simi-internal-services:8089/whatsapp/send-verify-code");
        req.setSendType(HttpPostSendTypeEnums.JSON.getType());
        try {
            //String post = HttpUtils.sendPost(req);
            SmsSendRecord record = new SmsSendRecord();
            record.setCode(code + "");
            record.setChannel(SmsChannelEnum.Whatsapp.getDesc());
        } catch (Exception e) {
            log.error("error..............", e);
        }*/
        return true;
    }
}
