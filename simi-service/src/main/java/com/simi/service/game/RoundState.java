package com.simi.service.game;

import com.simi.common.constant.GamePhase;

public class RoundState {
    // 一轮总时长（单位：秒）
    private static final int ROUND_DURATION = 60;

    // 当前回合开始时间（单位：毫秒），用于计算相对时间
    private static volatile long roundStartTime = System.currentTimeMillis();

    /**
     * 启动新一轮时调用，记录当前系统时间为回合起始时间。
     * 必须在每次开始新回合时调用此方法。
     */
    public static void startNewRound() {
        roundStartTime = System.currentTimeMillis();
    }

    /**
     * 获取当前回合所处的阶段（下注、置灰、开奖、展示）。
     * 根据当前时间与起始时间的间隔秒数判断。
     */
    public static GamePhase getCurrentPhase() {
        return GamePhase.fromElapsedSeconds(getElapsedSeconds());
    }

    /**
     * 获取当前阶段剩余的秒数（当前时间距离该阶段结束还有几秒）。
     * 如果阶段已过，返回 0。
     */
    public static int getCurrentPhaseRemainingSeconds() {
        GamePhase phase = getCurrentPhase();
        return phase.getRemainingSeconds(getElapsedSeconds());
    }

    /**
     * 获取整轮回合剩余的秒数（从当前时刻到20秒轮结束的时间）。
     * 如果时间超过20秒，也会返回0。
     */
    public static int getTotalRemainingSeconds() {
        return Math.max(0, ROUND_DURATION - (int) getElapsedSeconds());
    }

    /**
     * 内部工具方法：计算当前回合已经过了多少秒。
     * 基于系统当前时间与轮次起始时间的差值。
     */
    private static long getElapsedSeconds() {
        return (System.currentTimeMillis() - roundStartTime) / 1000;
    }
}