package com.simi.service;

import cn.hutool.core.util.StrUtil;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.dto.AppVersionGetResp;
import com.simi.entity.client.AppVersion;
import com.simi.service.client.AppVersionService;
import com.simi.common.constant.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 版本控制
 * @Author: Andy
 * @Date: 2023/11/29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionHighService {

    private final RedissonClient redissonClient;
    //private final UserServerService userServerService;
    private final AppVersionService appVersionService;

    /**
     * 获取版本更新信息
     * @param os
     * @param versionCode
     * @return
     */
    public AppVersionGetResp getVersion(String appLanguage, String os, String versionCode) {
        List<AppVersion> appVersions = new ArrayList<>();
        final Collection<String> versionCollection = redissonClient.<String, String>getMap(InfrastructureRedisKey.app_version_valid.getKey()).readAllValues();
        if (CollectionUtils.isEmpty(versionCollection)) {
            LambdaQueryWrapper<AppVersion> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AppVersion::getStatus, StatusEnum.normal.getStatus());
            List<AppVersion> list = appVersionService.list(wrapper);
            if (!CollectionUtils.isEmpty(list)) {
                for (AppVersion appVersion : list) {
                    redissonClient.getMap(InfrastructureRedisKey.app_version_valid.getKey()).put(appVersion.getId().toString(), JSONUtil.toJsonStr(appVersion));
                }
                final Collection<String> version = redissonClient.<String, String>getMap(InfrastructureRedisKey.app_version_valid.getKey()).readAllValues();
                appVersions = version.stream().map(e -> JSONUtil.toBean(e, AppVersion.class)).toList();
            }
        }else {
            appVersions = versionCollection.stream().map(e -> JSONUtil.toBean(e, AppVersion.class)).toList();
        }
        AppVersionGetResp resp = new AppVersionGetResp();
        resp.setNeedUpdate(false);



        final List<AppVersion> versionList = appVersions.stream().filter(x -> StrUtil.equalsIgnoreCase(x.getOs(), os)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(versionList)){
            log.error("app version is null, os:{}", os);
            return resp;
        }
        final Map<Integer, List<AppVersion>> typeMap = versionList.stream().collect(Collectors.groupingBy(AppVersion::getUpdateType));
        final List<AppVersion> forceList = typeMap.get(AppVersionUpdateType.FORCE.getType());
        //优先强制更新
        if (CollectionUtils.isNotEmpty(forceList)){
            final AppVersionGetResp needUpdateResp = checkVersion(appLanguage, versionCode, forceList);
            if (Objects.nonNull(needUpdateResp)){
                return needUpdateResp;
            }
        }
        final List<AppVersion> recommendList = typeMap.get(AppVersionUpdateType.RECOMMEND.getType());
        if (CollectionUtils.isNotEmpty(recommendList)){
            final AppVersionGetResp needUpdateResp = checkVersion(appLanguage, versionCode, recommendList);
            if (Objects.nonNull(needUpdateResp)){
                return needUpdateResp;
            }
        }
        return resp;
    }

    /**
     * 版本比对
     * @param appVersion
     * @param versionList
     * @return
     */
    private AppVersionGetResp checkVersion(String appLanguage, String appVersion, List<AppVersion> versionList) {
        versionList.sort(Comparator.comparing(AppVersion::getUpdateTime).reversed());
        for (AppVersion version : versionList) {
            final Integer condition = version.getCondition();
            final int compare = compareVersion(appVersion, version.getVersion());
            if (compare == 0){
                if (Objects.equals(VersionUpdateConditionEnum.EQ.getType(), condition) || Objects.equals(VersionUpdateConditionEnum.LQ.getType(), condition)){
                    return buildNeedUpdateResp(appLanguage, version);
                }
            } else if (compare == -1){
                if (Objects.equals(VersionUpdateConditionEnum.LQ.getType(), condition) || Objects.equals(VersionUpdateConditionEnum.LT.getType(), condition)){
                    return buildNeedUpdateResp(appLanguage, version);
                }
            }
        }
        return null;
    }

    private AppVersionGetResp buildNeedUpdateResp(String appLanguage, AppVersion version) {
        String explain = version.getEnExplain();
        String title = version.getEnTitle();
        if (LanguageEnum.ar.name().equals(appLanguage)){
            explain = version.getArExplain();
            title = version.getArTitle();
        }
        AppVersionGetResp resp = new AppVersionGetResp();
        resp.setNeedUpdate(true);
        resp.setSkipUrl(version.getSkipUrl());
        resp.setUpdateType(version.getUpdateType());
        resp.setTitle(title);
        resp.setExplain(explain);
        return resp;
    }

    /**
     * 0，version1 == version2
     * -1，version1 < version2
     * 1，version1 > version2
     * @param version1
     * @param version2
     * @return
     */
    private int compareVersion(String version1, String version2) {
        Integer v1 = Integer.parseInt(version1);
        Integer v2 = Integer.parseInt(version2);
        if (v1 > v2){
            return 1;
        } else if (v1 < v2){
            return -1;
        } else {
            return 0;
        }

        /*String s1[] = version1.split("\\.");
        String s2[] = version2.split("\\.");
        int len1 = s1.length;
        int len2 = s2.length;
        for(int i = 0; i < len1 || i < len2; i ++) {
            int xx = 0, yy = 0;//没有则默认0
            if(i < len1) xx = Integer.parseInt(s1[i]);
            if(i < len2) yy = Integer.parseInt(s2[i]);
            if(xx > yy) return 1;
            if(xx < yy) return -1;
        }
        return 0;*/
    }
}
