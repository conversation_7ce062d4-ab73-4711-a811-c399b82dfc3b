package com.simi.service.mission;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.MissionEnum;
import com.simi.common.dto.MissionDTO;
import com.simi.constant.MissionRedisKey;
import com.simi.message.MissionCompleteMessage;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/11 11:56
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class FollowingANewFriendMissionCompleteStrategy extends DailyMissionCompleteService implements MissionCompleteStrategy {


    @Override
    public void complete(MissionCompleteMessage message) {
        log.info("Following a new friend mission complete strategy message:[{}]", JSONUtil.toJsonStr(message));
        MissionDTO.MissionInfo missionInfo = personalMission(message.getUid(), getMission());
        if (!Objects.equals(missionInfo.getMissionId(), getMission().getId())) {
            log.warn("[{}]Cannot find user {} mission:{}, skip it.", message.getMessageId(), message.getUid(), getMission());
            return;
        }

        Long targetUid = message.getTargetUid();
        if (Objects.isNull(message.getTargetUid()) || targetUid < 1) {
            return;
        }

        boolean isMember = redissonManager.sIsMember(followMissionKey(message.getUid()), String.valueOf(targetUid));
        if (isMember) {
            log.warn("[{}]User {} has followed target user {} before.", message.getMessageId(), message.getUid(), targetUid);
            return;
        }
        // 加到历史关注数, ||处理方式, 正负值
        redissonManager.sAdd(followMissionKey(message.getUid()), String.valueOf(targetUid));
        handleComplete(message, missionInfo, new Date(message.getMessageTime()));
    }


    @Override
    public MissionEnum getMission() {
        return MissionEnum.FOLLOW_A_NEW_FRIEND;
    }

    /**
     * 关注任务redis key
     *
     * @return
     */
    private static String followMissionKey(Long uid) {
        return MissionRedisKey.user_follow_historical.getKey(uid);
    }
}
