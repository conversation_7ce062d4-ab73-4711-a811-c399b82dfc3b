package com.simi.service.mission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.MissionConfig;
import com.simi.common.constant.*;
import com.simi.common.dto.MissionDTO;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.constant.EventTrackingConstant;
import com.simi.constant.MissionRedisKey;
import com.simi.dto.event.TaskEventDTO;
import com.simi.entity.MissionRecord;
import com.simi.message.MissionCompleteMessage;
import com.simi.service.EventTrackingService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.user.UserServerService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 通用任务完成策略
 */
@Slf4j
public abstract class AbstractMissionCompleteService {

    @Resource
    protected MissionRecordService missionRecordService;
    @Resource
    protected RedissonManager redissonManager;
    @Resource
    private RedissonDistributionLocker distributionLocker;
    @Resource
    protected MissionService missionService;
    @Resource
    private NotifyMessageComponent notifyMessageComponent;
    @Resource
    protected UserServerService userServerService;

    @Resource
    private EventTrackingService eventTrackingService;

    public void handleComplete(MissionCompleteMessage message, MissionDTO.MissionInfo missionInfo, Date complete) {
        final long uid = message.getUid();
        final int missionId = message.getMissionId();
        MissionEnum missionEnum = message.getType();
        try (Locker lock = distributionLocker.lock(MissionRedisKey.missionCompleteLockKey(uid, missionId))) {
            if (Objects.isNull(lock)) {
                log.warn("Get mission complete locker error.");
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            String timeKey = null;
            TimeZoneEnum timeZoneEnum = userServerService.userTimeZone(uid);
            TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(complete, timeZoneEnum);
            if (Objects.equals(missionEnum.getMissionType(), MissionTypeEnum.DAILY_MISSION)) {
                timeKey = DateUtil.format(timeZoneDateDTO.getRecordTime(), DatePattern.PURE_DATE_PATTERN);
            }
            complete = timeZoneDateDTO.getRecordTime();
            boolean finished = missionService.checkMissionStatus(timeKey, message.getUid(), missionId);
            //检查任务完成状态，已完成直接return
            if (finished) {
                log.info("user {} mission {} has finished.", uid, missionId);
                return;
            }
            Optional<MissionRecord> optional = getMissionRecord(uid, missionId);
            MissionRecord missionRecord;
            List<MissionConfig> missionConfigs = missionService.missionConfigs();
            Map<Integer, MissionConfig.MissionConInfo> missionConInfoMap = missionConfigs.stream().map(MissionConfig::getMissionInfos).flatMap(Collection::stream)
                    .collect(Collectors.toMap(MissionConfig.MissionConInfo::getMissionId, x -> x));
            MissionConfig.MissionConInfo missionConInfo = missionConInfoMap.get(missionId);
            if (optional.isPresent()) {
                missionRecord = optional.get();
                int increment = 1;
                if (Objects.equals(missionEnum, MissionEnum.WIN_1000_COINS_IN_FRUIT_PARTY)) {
                    Double zScore = redissonManager.zScore(MissionRedisKey.user_fruit_party_game_win_mission.getKey(timeKey), String.valueOf(uid));
                    increment = Objects.isNull(zScore) ? 0 : zScore.intValue();
                    increment = increment - (Objects.isNull(missionRecord.getCompleteNum()) ? 0 : missionRecord.getCompleteNum());
                }
                if (Objects.equals(MissionStatusEnum.INCOMPLETE.getStatus(), missionRecord.getStatus())) {
                    // 任务未完成
                    missionRecord.setCompleteNum(missionRecord.getCompleteNum() + increment);
                    missionRecord.setUpdateTime(complete);
                    if (missionRecord.getCompleteNum() >= missionRecord.getTargetNum()) {
                        // 达到目标数
                        missionRecord.setCompleteNum(missionRecord.getTargetNum());
                        missionRecord.setStatus(MissionStatusEnum.COMPLETE_UNCLAIMED.getStatus());
                        missionRecord.setCompleteTime(complete);
                    }
                    missionRecordService.updateById(missionRecord);
                } else {
                    log.info("[{}] user has complete mission, skip it.", message.getMessageId());
                    return;
                }
            } else {
                int completeNum = 1;
                if (Objects.equals(missionEnum, MissionEnum.WIN_1000_COINS_IN_FRUIT_PARTY)) {
                    Double zScore = redissonManager.zScore(MissionRedisKey.user_fruit_party_game_win_mission.getKey(timeKey), String.valueOf(uid));
                    completeNum = Objects.isNull(zScore) ? 0 : zScore.intValue();
                }
                int status = missionConInfo.getTaskVal() == 1 ? MissionStatusEnum.COMPLETE_UNCLAIMED.getStatus()
                        : completeNum >= missionConInfo.getTaskVal() ? MissionStatusEnum.COMPLETE_UNCLAIMED.getStatus() : MissionStatusEnum.INCOMPLETE.getStatus();
                missionRecord = MissionRecord.builder()
                        .uid(uid)
                        .missionId(missionId)
                        .completeNum(completeNum)
                        .targetNum(missionConInfo.getTaskVal())
                        .status(status)
                        .rewardPackId(missionConInfo.getRewardPackId())
                        .createTime(complete)
                        .updateTime(complete)
                        .completeTime(status == MissionStatusEnum.COMPLETE_UNCLAIMED.getStatus() ? complete : null)
                        .type(message.getType().getMissionType().getType())
                        .build();
                missionRecordService.save(missionRecord);
            }

            //更新用户任务缓存
            handleUserMissionCache(missionRecord, missionInfo);
            if (!Objects.equals(missionInfo.getMissionId(), message.getMissionId())) {
                log.warn("Cannot get the user mission,skip it.");
                return;
            }

            if (Objects.equals(missionRecord.getStatus(), MissionStatusEnum.COMPLETE_UNCLAIMED.getStatus())
                    || Objects.equals(missionRecord.getStatus(), MissionStatusEnum.CLAIMED.getStatus())) {
                long expireTime;
                if (Objects.equals(missionEnum.getMissionType(), MissionTypeEnum.DAILY_MISSION)) {
                    expireTime = DateUtil.endOfDay(timeZoneDateDTO.getRecordTime()).getTime();
                } else {
                    expireTime = DateUtil.offsetDay(complete, 7).getTime();
                }
                String missionStatusKey = MissionService.userMissionStatusKey(timeKey, message.getUid());
                redissonManager.sAdd(missionStatusKey, String.valueOf(missionId));
                redissonManager.expireAt(missionStatusKey, expireTime);
            }

            // 系统消息通知
            /*MissionConfig missionConfig = missionConfigs.stream()
                    .filter(e -> Objects.equals(missionEnum.getMissionType().getType(), e.getMissionType()))
                    .findAny().orElseThrow(() -> new ApiException(CodeEnum.THE_RESOURCE_DOES_NOT_EXIST));*/
            LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
            if (Objects.equals(languageEnum, LanguageEnum.ar)) {
                missionInfo.setMissionName(missionConInfo.getNameCopyWriting().getAr());
                missionInfo.setMissionDesc(missionConInfo.getDescCopyWriting().getAr());
            } else if (Objects.equals(languageEnum, LanguageEnum.en)) {
                missionInfo.setMissionName(missionConInfo.getNameCopyWriting().getEn());
                missionInfo.setMissionDesc(missionConInfo.getDescCopyWriting().getEn());
            }
            TranslationCopyDTO textTranslation = missionConInfo.getNameCopyWriting();
            String dailyRewardLinkUrl = ClientRouteUtil.toDailyReward();
            notifyMessageComponent.pushSystemComplexIMMsg(languageEnum, uid, System.currentTimeMillis(), dailyRewardLinkUrl,
                    null, CopywritingEnum.TASK_COMPLETED, null,
                    textTranslation, SystemComplexIMMsgEventEnum.MISSION_COMPLETED.getEvent(), JSONUtil.toJsonStr(missionInfo));
            try {
                taskEvent(missionEnum.getId(),missionEnum.getMissionType().getType());
            } catch (Exception e) {
                log.error("missionEnum:{}", JSONUtil.toJsonStr(missionEnum));
            }


        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("Handle user complete mission message {}, error:{}", JSONUtil.toJsonStr(message), ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    private void taskEvent(Integer taskType,Integer taskNumber) {
        TaskEventDTO dto = new TaskEventDTO();
        dto.setTaskType(taskType);
        dto.setTaskNumber(taskNumber);
        eventTrackingService.handleEvent(dto, EventTrackingConstant.EventName.COMPLETE_THE_TASK);
    }

    /**
     * 更新用户任务缓存
     *
     * @param missionRecord
     */
    private void handleUserMissionCache(MissionRecord missionRecord, MissionDTO.MissionInfo messionInfo) {
        String missionKey = missionKey(missionRecord.getUid());
        messionInfo.setRate(missionRecord.getCompleteNum());
        messionInfo.setStatus(missionRecord.getStatus());
        String jsonStr = JSONUtil.toJsonStr(messionInfo);
        redissonManager.hSet(missionKey, String.valueOf(messionInfo.getMissionId()), JSONUtil.toJsonStr(messionInfo));
        log.info("handle user mission cache. uid:{}, mission:{}", missionRecord.getUid(), jsonStr);
        MissionEnum missionEnum = MissionEnum.getById(messionInfo.getMissionId());
        if (Objects.isNull(missionEnum)) {
            log.info("fail handle mission cache, missionEnum is empty. uid:{}, mission:{}", missionRecord.getUid(), jsonStr);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        redissonManager.expireAt(missionKey, messionInfo.getExpireTime());
    }


    /**
     * 获取任务记录
     *
     * @param uid
     * @param missionId
     * @return
     */
    protected abstract Optional<MissionRecord> getMissionRecord(Long uid, Integer missionId);

    /**
     * 任务键
     *
     * @param uid
     * @return
     */
    protected abstract String missionKey(Long uid);

    protected MissionDTO.MissionInfo personalMission(long uid, MissionEnum missionEnum) {
        try {
            MissionDTO resp = missionService.missionsByType(uid, missionEnum.getMissionType(), missionService.missionConfigs());
            if (CollUtil.isEmpty(resp.getMissionInfos())) {
                return null;
            }
            return resp.getMissionInfos().stream()
                    .filter(c -> c.getMissionId() == missionEnum.getId())
                    .peek(e -> e.setUpperLevelsIcon(resp.getMissionTypeIcon()))
                    .findAny().orElse(null);
        } catch (Exception e) {
            log.error("Get personal mission error:{}", ExceptionUtil.formatEx(e));
            return null;
        }
    }

}
