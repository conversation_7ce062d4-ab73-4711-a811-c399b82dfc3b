package com.simi.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.simi.common.constant.*;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.constant.UserConstant;
import com.simi.constant.UserRedisKey;
import com.simi.dto.push.SystemComplexIMMsgDTO;
import com.simi.entity.user.User;
import com.simi.entity.account.Account;
import com.simi.entity.invite.InviteBinding;
import com.simi.service.invite.InviteBindingService;
import com.simi.service.invite.InviteServerService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.oauth2.AccountService;
import com.simi.service.room.RoomHighService;
import com.simi.service.room.RoomService;
import com.simi.service.tencent.TencentIMComponent;
import com.simi.service.user.UserOnlineService;
import com.simi.service.user.UserServerService;
import com.simi.service.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/06/26 20:27
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class FixedService {

    private final UserServerService userServerService;
    private final RoomHighService roomHighService;
    private final TencentIMComponent tencentIMComponent;
    private final UserService userService;
    private final InviteBindingService inviteBindingService;
    private final InviteServerService inviteServerService;
    private final SystemConfigService systemConfigService;
    private final RedissonManager redissonManager;
    private final RoomService roomService;
    private final AccountService accountService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final UserOnlineService userOnlineService;

    public void fixUserStatus(String targetUids) {
        List<String> uids = StrUtil.split(targetUids, StrUtil.C_COMMA);
        String countryCode = "SA";
        String default_avatar = systemConfigService.getSysConfValueById(SystemConfigConstant.DEFAULT_AVATAR);
        String man_avatar = systemConfigService.getSysConfValueById(SystemConfigConstant.DEFAULT_MALE_AVATAR);
        String woman_avatar = systemConfigService.getSysConfValueById(SystemConfigConstant.DEFAULT_FEMALE_AVATAR);
        uids.forEach(uid -> {
            try {
                UserBaseInfoDTO fromCache = userServerService.getFromCache(Long.valueOf(uid));
                if (Objects.isNull(fromCache)) {
                    log.error("fixUserStatus empty uid:[{}]", uid);
                }
                long uidLong = Long.parseLong(uid);
                redissonManager.hSet(UserRedisKey.user_no.getKey(), fromCache.getUserNo().toString(), uid);

                String Tag_Profile_IM_Gender;
                String avatar;
                if (Objects.equals(fromCache.getGender(), UserConstant.UserGender.MALE)) {
                    Tag_Profile_IM_Gender = "Gender_Type_Male";
                    avatar = man_avatar;
                } else if (Objects.equals(fromCache.getGender(), UserConstant.UserGender.FEMALE)) {
                    Tag_Profile_IM_Gender = "Gender_Type_Female";
                    avatar = woman_avatar;
                } else {
                    Tag_Profile_IM_Gender = "Gender_Type_Unknown";
                    avatar = default_avatar;
                }

                LambdaUpdateWrapper<User> wrapper = new LambdaUpdateWrapper<>();
                wrapper.eq(User::getUid, uid);
                wrapper.set(User::getStatus, StatusEnum.normal.getStatus());
                wrapper.set(User::getUpdateTime, new Date());

                if (StrUtil.isBlank(fromCache.getAvatar())) {
                    wrapper.set(User::getAvatar, avatar);
                    fromCache.setAvatar(avatar);
                }
                if (Objects.isNull(fromCache.getGender())) {
                    wrapper.set(User::getGender, 0);
                }
                if (StrUtil.isBlank(fromCache.getCountryCode())) {
                    wrapper.set(User::getCountryCode, countryCode);
                    accountService.lambdaUpdate().set(Account::getCountryCode, countryCode).eq(Account::getUid, uidLong).update();
                }
                userService.update(wrapper);

                tencentIMComponent.exportAccount(fromCache.getUid(), fromCache.getNick(), fromCache.getAvatar());

                tencentIMComponent.subscriber(fromCache.getUid(), fromCache.getNick(), fromCache.getAvatar(), Tag_Profile_IM_Gender);

                userServerService.clearUserSummaryCache(uidLong);

                LanguageEnum languageEnum = userServerService.userAppLanguage(Long.valueOf(uid));

                redissonManager.hDel(roomService.roomUidIdKey(), uid);
                redissonManager.hDel(roomService.roomKey(), uid);
                roomHighService.openRoom(uidLong, languageEnum.name());

                boolean exists = inviteBindingService.lambdaQuery().eq(InviteBinding::getInvitedUid, uidLong).exists();
                if (!exists) {
                    InviteBindingEnum bindingEnum = InviteBindingEnum.UNBOUND;
                    inviteServerService.inviteBinding(null, uidLong, bindingEnum.getType(), null, Boolean.TRUE);
                }
            } catch (Exception e) {
                log.error("fixUserStatus fail uid:[{}] msg:[{}]", uid, e.getMessage(), e);
            }
        });
    }

    public void pushSystemNotify(Long uid, TranslationCopyDTO titleMap, TranslationCopyDTO textMap,
                                 String linkUrl, String picUrl, String roomId, Boolean checkOnline) {
        try {
            boolean online = userOnlineService.isOnline(uid);
            if (checkOnline && !online) {
                return;
            }
            if (StrUtil.isNotBlank(roomId)) {
                linkUrl = ClientRouteUtil.toRoom(roomId);
            }
            TimeZoneDateDTO zoneDateDTO = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3);
            SystemComplexIMMsgDTO imMsgDTO = SystemComplexIMMsgDTO.builder()
                    .titleMap(titleMap)
                    .picUrl(picUrl)
                    .linkUrl(linkUrl)
                    .textMap(textMap)
                    .event(null)
                    .eventPayload(null)
                    .build();
            CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                    IMMsgType.SystemComplexIMMsg,
                    zoneDateDTO.getRecordTime().getTime(), JSONUtil.toJsonStr(imMsgDTO));
            notifyMessageComponent.publishSystemDefineMessage(imMessage, String.valueOf(uid), null);
        } catch (Exception e) {
            log.error("Push System Notify uid:[{}]", uid);
        }
    }

    public void pushRecentlySystemNotify(TranslationCopyDTO titleMap, TranslationCopyDTO textMap,
                                         String linkUrl, String picUrl, String roomId, Boolean checkOnline, Integer offsetDay, String uids) {
        try {
            if (StrUtil.isBlank(uids)) {
                DateTime dateTime = DateUtil.offsetDay(new Date(), offsetDay);
                List<InviteBinding> activeUsers = inviteBindingService.lambdaQuery().gt(InviteBinding::getActiveTime, dateTime).list();
                activeUsers.forEach(user -> {
                    pushSystemNotify(user.getInvitedUid(), titleMap, textMap, linkUrl, picUrl, roomId, checkOnline);
                });
            } else {
                StrUtil.split(uids, StrUtil.C_COMMA).stream().map(String::trim).forEach(uid -> {
                    pushSystemNotify(Long.parseLong(uid), titleMap, textMap, linkUrl, picUrl, roomId, checkOnline);
                });
            }
        } catch (Exception e) {
            log.error("Push Recently System Notify fail:[{}]", ExceptionUtil.formatEx(e));
        }
    }


}
