package com.simi.service;

import com.simi.common.vo.PageResultInfo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class PageService {
    /**
     * 获取分页
     * @param pageNum
     * @param pageSize
     * @param totalSize
     * @return
     */
    public PageResultInfo correctOnSalePageInfo(Integer pageNum, Integer pageSize, int totalSize) {
        int totalPage = totalSize % pageSize > 0 ? totalSize / pageSize + 1 : totalSize / pageSize;
        int start = pageNum - 1 >= 0 ? (pageNum - 1) * pageSize : 0;
        start = start > totalSize ? totalSize : start;
        int targetEnd = start + pageSize;
        //越界的情况
        int end = targetEnd  > totalSize ? totalSize  : targetEnd;
        return PageResultInfo.builder()
                .totalSize(totalSize)
                .start(start)
                .end(end)
                .totalPage(totalPage)
                .currentPage(pageNum)
                .pageSize(pageSize)
                .build();
    }
}
