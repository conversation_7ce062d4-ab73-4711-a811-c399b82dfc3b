package com.simi.service.invite;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.InviteBindingEnum;
import com.simi.entity.invite.InviteBinding;
import com.simi.mapper.invite.InviteBindingMapper;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2024/05/08 19:37
 **/
@Service
public class InviteBindingService extends ServiceImpl<InviteBindingMapper, InviteBinding> {
    public List<InviteBinding> getInviteInfoByInvitedUidList(List<Long> invitedUidList){
        LambdaQueryWrapper<InviteBinding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InviteBinding::getInvitedUid, invitedUidList)
                        .select(InviteBinding::getActiveTime,InviteBinding::getInvitedUid,InviteBinding::getInviteUid,InviteBinding::getStatus);
        return list(queryWrapper);
    }

    public List<InviteBinding> getAlreadyInviteInfoByUserIds(Set<Long> userIds){
        LambdaQueryWrapper<InviteBinding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InviteBinding::getInvitedUid, userIds)
                .in(InviteBinding::getStatus, Arrays.asList(InviteBindingEnum.ALREADY_BOUND.getType(),
                        InviteBindingEnum.MANUAL_BINDING.getType(),InviteBindingEnum.REBIND.getType()))
                .select(InviteBinding::getInvitedUid,InviteBinding::getInviteUid);
        return list(queryWrapper);
    }

    public List<InviteBinding> getInfoByInvitUidList(List<Long> inviteUidList){
        LambdaQueryWrapper<InviteBinding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InviteBinding::getInviteUid, inviteUidList)
                .select(InviteBinding::getActiveTime,InviteBinding::getInvitedUid,InviteBinding::getInviteUid,InviteBinding::getStatus);
        return list(queryWrapper);
    }

    public List<InviteBinding> getInfoByInvitUidListAndStatus(List<Long> inviteUidList,List<Integer> statusList){
        LambdaQueryWrapper<InviteBinding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InviteBinding::getInviteUid, inviteUidList);
        queryWrapper.in(InviteBinding::getStatus, statusList)
                .select(InviteBinding::getActiveTime,InviteBinding::getInvitedUid,InviteBinding::getInviteUid,InviteBinding::getStatus);
        return list(queryWrapper);
    }

    public List<InviteBinding> getInviteInfoByInvitedUidListAndStatus(List<Long> invitedUidList,List<Integer> statusList){
        LambdaQueryWrapper<InviteBinding> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(InviteBinding::getInvitedUid, invitedUidList);
        queryWrapper.in(InviteBinding::getStatus, statusList)
                .select(InviteBinding::getActiveTime,InviteBinding::getInvitedUid,InviteBinding::getInviteUid,InviteBinding::getStatus);
        return list(queryWrapper);
    }
}
