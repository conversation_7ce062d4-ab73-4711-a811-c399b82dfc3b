package com.simi.service.invite;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.*;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.invite.InviteUserRecordDTO;
import com.simi.common.dto.invite.UserInviteInfoDTO;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.vo.invite.InviteUserRecordVO;
import com.simi.constant.InviteRedisKey;
import com.simi.constant.UserConstant;
import com.simi.dto.UserSimple;
import com.simi.dto.push.TextMsgDTO;
import com.simi.dto.push.UserActiveMsgDTO;
import com.simi.entity.invite.InviteBinding;
import com.simi.entity.invite.InviteBindingLog;
import com.simi.mapper.invite.InviteBindingMapperExpand;
import com.simi.service.following.FollowingLowService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.user.UserServerService;
import com.simi.util.PushMsgUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.IntStream;

/**
 * 邀请服务业务层
 *
 * <AUTHOR>
 * @date 2024/05/08 15:19
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class InviteServerService {

    private static final List<Integer> SUCCESS_INVITE_STATUS = CollUtil.newArrayList(
            InviteBindingEnum.ALREADY_BOUND.getType(),
            InviteBindingEnum.MANUAL_BINDING.getType(),
            InviteBindingEnum.REBIND.getType());
    private final RedissonManager redissonManager;
    private final InviteBindingService inviteBindingService;
    private final InviteBindingLogService inviteBindingLogService;
    private final SystemConfigService systemConfigService;
    private final FollowingLowService followingLowService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final InviteBindingMapperExpand inviteBindingMapperExpand;
    @Lazy
    @Autowired
    private UserServerService userServerService;

    /**
     * 用户邀请相关信息
     *
     * @param uid
     * @return
     */
    public UserInviteInfoDTO userInviteInfo(Long uid) {
        UserInviteInfoDTO result = UserInviteInfoDTO.builder().build();
        String inviteCode = redissonManager.hGet(InviteRedisKey.user_invite_code.getKey(), String.valueOf(uid));
        // 生成code
        if (StrUtil.isBlank(inviteCode)) {
            inviteCode = this.createInviteCode();
            redissonManager.hSet(InviteRedisKey.user_invite_code.getKey(), String.valueOf(uid), inviteCode);
            redissonManager.hSet(InviteRedisKey.user_counter_invite_code.getKey(), inviteCode, String.valueOf(uid));
        }
        long inviteCount = redissonManager.zCard(InviteRedisKey.user_branch_invited.getKey(String.valueOf(uid)));
        result.setInviteCode(inviteCode);
        result.setInviteCount((int) inviteCount);

        Long count = inviteBindingService.lambdaQuery()
                .eq(InviteBinding::getInviteUid, uid)
                .eq(InviteBinding::getStatus, InviteBindingEnum.UNBUNDLING.getType()).count();
        result.setPreviouslyOwned(count > 0);
        String h5LinkUrl = systemConfigService.getSysConfValueById(SystemConfigConstant.INVITE_H5_LINK_URL);
        if (StrUtil.isNotBlank(h5LinkUrl)) {
            result.setLinkUrl(h5LinkUrl.concat(inviteCode));
        }
        String improveInformationFlag = redissonManager.get(InviteRedisKey.improve_information_flag.getKey(uid));
        if (StrUtil.isNotBlank(improveInformationFlag)) {
            String superiorUid = redissonManager.hGet(InviteRedisKey.user_superior_invite.getKey(), String.valueOf(uid));
            if (StrUtil.isBlank(superiorUid)) {
                result.setSecondInvitationEndTime(Long.parseLong(improveInformationFlag));
            }
        }
        return result;
    }

    /**
     * h5触发
     *
     * @param clientIp
     * @param inviteCode
     */
    public UserSimple triggerInviteLink(String clientIp, String inviteCode) {
        String uid = redissonManager.hGet(InviteRedisKey.user_counter_invite_code.getKey(), inviteCode);
        UserSimple result = UserSimple.builder().build();
        if (StrUtil.isBlank(uid) || StrUtil.isBlank(clientIp)) {
            return result;
        }
        DateTime dateTime = DateUtil.offsetDay(new Date(), 1);

        List<String> ipWhiteList = CollUtil.newArrayList();
        String ipWhiteListStr = systemConfigService.getSysConfValueById(SystemConfigConstant.INVITE_LINK_TRIGGER_IP_WHITELIST);
        if (StrUtil.isNotBlank(ipWhiteListStr)) {
            ipWhiteList = StrUtil.split(ipWhiteListStr, StrUtil.C_COMMA);
        }

        if (!ipWhiteList.contains(clientIp)) {
            redissonManager.hSet(InviteRedisKey.invite_code_trigger.getKey(), clientIp, StrUtil.format("{}:{}", inviteCode, dateTime.getTime()));
        }

        UserBaseInfoDTO userBaseInfoDTO = userServerService.getFromCache(Long.valueOf(uid));
        if (Objects.nonNull(userBaseInfoDTO)) {
            result = BeanUtil.toBean(userBaseInfoDTO, UserSimple.class);
        }
        return result;
    }

    /**
     * 校验
     *
     * @param clientIp
     */
    public String inviteLinkCheck(String clientIp) {
        String codeCombin = redissonManager.hGet(InviteRedisKey.invite_code_trigger.getKey(), clientIp);
        if (StrUtil.isBlank(codeCombin)) {
            return StrUtil.EMPTY;
        }
        List<String> split = StrUtil.split(codeCombin, StrPool.C_COLON);
        if (CollUtil.isEmpty(split) || split.size() < 2) {
            log.warn("invite Link Check codeCombin is error[{}].ip:[{}]", codeCombin, clientIp);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        String time = split.get(1);
        if (System.currentTimeMillis() < Long.parseLong(time)) {
            return split.get(0);
        } else {
            redissonManager.hDel(InviteRedisKey.invite_code_trigger.getKey(), clientIp);
        }
        return StrUtil.EMPTY;
    }

    /**
     * 通过邀请码获取用户信息
     *
     * @param inviteCode
     * @return
     */
    public String userInfoByInviteCode(String inviteCode) {
        return redissonManager.hGet(InviteRedisKey.user_counter_invite_code.getKey(), inviteCode);
    }

    public Boolean codeCheck(String inviteCode) {
        if (StrUtil.isNotBlank(userInfoByInviteCode(inviteCode))) {
            return true;
        }
        long maybeUserNo;
        try {
            maybeUserNo = Long.parseLong(inviteCode);
        } catch (Exception e) {
            return false;
        }
        Long uidByUserNo = userServerService.getUidByUserNo(maybeUserNo);
        return Objects.nonNull(uidByUserNo);
    }

    /**
     * 好友邀请-详情页
     *
     * @return
     */
    public ListWithTotal<InviteUserRecordVO> userInviteList(Long uid, String searchKey, Integer pageNum, Integer pageSize) {
        if (Objects.isNull(uid)) {
            return ListWithTotal.empty();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<InviteUserRecordDTO> recordDTOS = inviteBindingMapperExpand.recordSimpleUserInvite(uid, searchKey, SUCCESS_INVITE_STATUS);
        if (CollUtil.isEmpty(recordDTOS)) {
            return ListWithTotal.empty();
        }
        Date now = new Date();
        PageInfo<InviteUserRecordDTO> pageInfo = new PageInfo<>(recordDTOS);
        List<InviteUserRecordVO> vos = recordDTOS.stream().map(dto -> {
            InviteUserRecordVO vo = BeanUtil.toBean(dto, InviteUserRecordVO.class);
            vo.setActiveDays(0);
            if (Objects.nonNull(vo.getActiveTime())) {
                long activeDays = DateUtil.betweenDay(vo.getActiveTime(), now, true);
                vo.setActiveDays((int) activeDays);
            }
            if (StringUtils.isBlank(vo.getAvatar())) {
                String avatarConfStr;
                if (vo.getGender()== UserConstant.UserGender.MALE) {
                    avatarConfStr = SystemConfigConstant.DEFAULT_MALE_AVATAR;
                } else if (UserConstant.UserGender.FEMALE == vo.getGender() ) {
                    avatarConfStr = SystemConfigConstant.DEFAULT_FEMALE_AVATAR;
                } else {
                    avatarConfStr = SystemConfigConstant.DEFAULT_AVATAR;
                }
                vo.setAvatar(avatarConfStr);
            }
            return vo;
        }).toList();
        return ListWithTotal.<InviteUserRecordVO>builder().list(vos).total(pageInfo.getTotal()).build();
    }

    /**
     * 代理收益记录
     * 已解绑的记录也查出来, 附带上状态, (绑定中, 已解绑)
     *
     * @return
     */
    public ListWithTotal<InviteUserRecordVO> inviteIncomeRecord(Long uid, String searchKey, Integer pageNum, Integer pageSize) {
        if (Objects.isNull(uid)) {
            return ListWithTotal.empty();
        }
        PageHelper.startPage(pageNum, pageSize);
        List<InviteUserRecordDTO> recordDTOS = inviteBindingMapperExpand.recordInviteIncome(uid, searchKey);
        if (CollUtil.isEmpty(recordDTOS)) {
            return ListWithTotal.empty();
        }
        PageInfo<InviteUserRecordDTO> pageInfo = new PageInfo<>(recordDTOS);
        List<InviteUserRecordVO> vos = recordDTOS.stream().map(dto -> {
            // 美分直接返回, 客户端转成美金
            /*BigDecimal divide = new BigDecimal(vo.getUsdCount()).divide(bigDecimal, 2, RoundingMode.DOWN);
            vo.setUsdCount(divide.longValue());*/
            return BeanUtil.toBean(dto, InviteUserRecordVO.class);
        }).toList();
        return ListWithTotal.<InviteUserRecordVO>builder().list(vos).total(pageInfo.getTotal()).build();
    }


    /**
     * 绑定邀请关系
     *
     * @param inviteUid 邀请人uid
     * @param targetUid 被邀请人uid
     */
    public void inviteBinding(Long inviteUid, Long targetUid, Integer status, Integer adminId, boolean isActive) {
        log.info("init invite binding inviteUid:[{}]  targetUid:[{}] status:[{}] adminId):[{}]", inviteUid, targetUid, status, adminId);
        if (Objects.isNull(targetUid)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Date now = new Date();
        if (Objects.nonNull(inviteUid)) {
            redissonManager.zIncrement(InviteRedisKey.user_branch_invited.getKey(String.valueOf(inviteUid)),
                    Double.valueOf(now.getTime()),
                    String.valueOf(targetUid));
            redissonManager.hSet(InviteRedisKey.user_superior_invite.getKey(), String.valueOf(targetUid), String.valueOf(inviteUid));
        }
        InviteBinding inviteBinding = InviteBinding.builder()
                .invitedUid(targetUid)
                .inviteUid(inviteUid)
                .status(status)
                .createTime(now).build();
        if (Objects.nonNull(inviteUid) && isActive) {
            inviteBinding.setActiveTime(DateUtil.beginOfDay(now));
        }
        boolean save = inviteBindingService.saveOrUpdate(inviteBinding);
        if (!save) {
            log.info("init invite binding save data error.  inviteUid:[{}]  targetUid:[{}]", inviteUid, targetUid);
            return;
        }
        // create log
        if (!Objects.equals(status, InviteBindingEnum.UNBOUND.getType())) {
            createInviteLog(targetUid, now, status, adminId);
        }

        // 邀请成功互关并且法消息
        if (Objects.nonNull(inviteUid) && SUCCESS_INVITE_STATUS.contains(status)) {
            followingLowService.following(inviteUid, targetUid, Boolean.TRUE);
            followingLowService.following(targetUid, inviteUid, Boolean.TRUE);

            //发送消息
            String greetingTextStr = systemConfigService.getSysConfValueById(SystemConfigConstant.INVITE_SUCCESS_GREETING_TEXT);
            if (StrUtil.isNotBlank(greetingTextStr)) {
                LanguageEnum languageEnum = userServerService.userAppLanguage(inviteUid);
                JSONObject textObj = JSONUtil.parseObj(greetingTextStr);
                Object text = textObj.get(languageEnum.name());
                if (Objects.nonNull(text)) {
                    TextMsgDTO textMsgDTO = TextMsgDTO.builder().text(text.toString()).build();
                    CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                            IMMsgType.TextIMMsg,
                            System.currentTimeMillis(), JSONUtil.toJsonStr(textMsgDTO));

                    OfflinePushInfo offlinePushInfo = null;
                    try {
                        offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(targetUid),
                                text.toString(),
                                text.toString(),
                                null,
                                ClientRouteUtil.toIM(targetUid.toString(), Boolean.FALSE));
                    } catch (Exception e) {
                        // ignore
                    }

                    notifyMessageComponent.publishPrivateChatMessage(imMessage, targetUid, inviteUid, offlinePushInfo);
                }
            }
        }
    }

    public boolean updateInviteBinding(InviteBinding inviteBinding) {
        return inviteBindingService.updateById(inviteBinding);
    }

    public void createInviteLog(Long uid, Date date, Integer status, Integer adminId) {
        InviteBindingLog bindingLog = InviteBindingLog.builder()
                .invitedUid(uid)
                .status(status)
                .adminId(adminId)
                .createTime(date).build();
        inviteBindingLogService.save(bindingLog);
    }


    public void batchCreateInviteLog(List<InviteBindingLog> dtos) {
        inviteBindingLogService.saveBatch(dtos);
    }

    /**
     * 更新邀请活跃状态
     *
     * @param dtos
     * @return
     */
    public boolean updateBatchInviteInfo(List<InviteBinding> dtos) {
        return inviteBindingService.updateBatchById(dtos);
    }

    /**
     * 不活跃用户列表
     *
     * @return
     */
    public List<InviteBinding> recordNotActiveUser(Date expireDay) {
        return inviteBindingService.lambdaQuery()
                .in(InviteBinding::getStatus, SUCCESS_INVITE_STATUS)
                .eq(InviteBinding::getActiveTime, expireDay).list();
    }


    /**
     * 创建邀请码
     *
     * @return
     */
    private String createInviteCode() {
        String inviteCode = null;
        boolean flag = true;
        while (flag) {
            inviteCode = RandomUtil.randomString(6);
            String uid = redissonManager.hGet(InviteRedisKey.user_counter_invite_code.getKey(), inviteCode);
            if (StrUtil.isBlank(uid)) {
                flag = false;
            }
        }
        return inviteCode;
    }

    /**
     * 通过uid获取他的上级邀请者(即代理)
     *
     * @param uid
     * @return
     */
    public Long superiorInvite(Long uid) {
        if (Objects.isNull(uid)) {
            return null;
        }
        String superiorInvite = redissonManager.hGet(InviteRedisKey.user_superior_invite.getKey(), String.valueOf(uid));
        if (StrUtil.isNotBlank(superiorInvite)) {
            return Long.parseLong(superiorInvite);
        }
        return null;
    }

    /**
     * 代理邀请收益汇总
     *
     * @param invitedUid
     * @param inviteUid
     * @param usdCount
     */
    public void updateInviteAgentIncome(Long invitedUid, Long inviteUid, Long usdCount) {
        try {
            int updateAgentIncome = inviteBindingMapperExpand.updateAgentIncome(invitedUid, inviteUid, usdCount);
            if (updateAgentIncome != 1) {
                log.info("update invite agent usdIncome invitedUid:[{}] inviteUid:[{}] usdCount:[{}]",
                        invitedUid, inviteUid, usdCount);
            }
        } catch (Exception e) {
            log.warn("update invite agent usdIncome fail invitedUid:[{}] inviteUid:[{}] usdCount:[{}]",
                    invitedUid, inviteUid, usdCount);
        }
    }

    /**
     * 解绑不活跃用户
     */
    public void unBoundActiveUser(Integer expireDayFlag) {
        DateTime expireDay = DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), expireDayFlag));
        List<InviteBinding> expireList = this.recordNotActiveUser(expireDay);
        log.info("expire active user list size:[{}]", expireList.size());
        if (CollUtil.isEmpty(expireList)) {
            return;
        }
        Date now = new Date();
        String userInactivationNotifyStr = systemConfigService.getSysConfValueById(SystemConfigConstant.USER_INACTIVATION_NOTIFY_TEXT);
        JSONObject textObj = new JSONObject();
        if (StrUtil.isNotBlank(userInactivationNotifyStr)) {
            textObj = JSONUtil.parseObj(userInactivationNotifyStr);
        }
        JSONObject finalTextObj = textObj;
        CollUtil.split(expireList, 200).forEach(dtos -> {
            dtos.forEach(dto -> dto.setStatus(InviteBindingEnum.UNBUNDLING.getType()));
            boolean updated = this.updateBatchInviteInfo(dtos);
            if (updated) {
                List<Long> invitedUids = dtos.stream().map(InviteBinding::getInvitedUid).distinct().toList();
                Map<Long, UserBaseInfoDTO> invitedUserMap = userServerService.batchUserSummary(invitedUids);
                List<InviteBindingLog> logList = dtos.stream().map(dto -> {
                    redissonManager.zRemove(InviteRedisKey.user_branch_invited.getKey(String.valueOf(dto.getInviteUid())), String.valueOf(dto.getInvitedUid()));
                    String superiorUid = redissonManager.hGet(InviteRedisKey.user_superior_invite.getKey(), String.valueOf(dto.getInvitedUid()));
                    // 解绑消息
                    if (StrUtil.isNotBlank(superiorUid)) {
                        LanguageEnum languageEnum = userServerService.userAppLanguage(Long.parseLong(superiorUid));
                        Object msgText = finalTextObj.get(languageEnum.name());
                        if (Objects.nonNull(msgText)) {
                            UserBaseInfoDTO userBaseInfoDTO = invitedUserMap.get(dto.getInvitedUid());
                            UserActiveMsgDTO msgDTO = UserActiveMsgDTO.builder()
                                    .event(UserActiveEventEnum.INACTIVATION_UNBINDING.getType())
                                    .text(msgText.toString())
                                    .userInfo(userBaseInfoDTO)
                                    .linkUrl(ClientRouteUtil.toHomePage(dto.getInvitedUid(), Boolean.FALSE))
                                    .build();
                            CommonIMMessage imMessage = CommonIMMessage.builder()
                                    .type(IMMsgType.InviteActiveMsg)
                                    .payload(JSONUtil.toJsonStr(msgDTO))
                                    .timestamp(System.currentTimeMillis()).build();

                            OfflinePushInfo offlinePushInfo = null;
                            try {
                                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.friendAssistantMsgUid),
                                        msgText.toString(),
                                        msgText.toString(),
                                        null,
                                        ClientRouteUtil.toHomePage(dto.getInvitedUid(), Boolean.FALSE));
                            } catch (Exception e) {
                                // ignore
                                log.info("unBoundActiveUser build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
                            }

                            notifyMessageComponent.publishFriendAssistantMessage(imMessage, Long.parseLong(superiorUid), offlinePushInfo);
                        }
                    }
                    return InviteBindingLog.builder()
                            .invitedUid(dto.getInvitedUid())
                            .status(InviteBindingEnum.UNBUNDLING.getType())
                            .createTime(now).build();
                }).toList();
                // 删掉被邀请者对应邀请者的缓存
                String[] invitedUidArr = invitedUids.stream().map(String::valueOf).toArray(String[]::new);
                redissonManager.hMDel(InviteRedisKey.user_superior_invite.getKey(), invitedUidArr);
                this.batchCreateInviteLog(logList);
            }
        });
    }

    public void expiringSoonActiveUser() {
        Date now = new Date();
        IntStream.of(-3, -7, -15, -25).forEach(days -> {
            DateTime expireDay = DateUtil.beginOfDay(DateUtil.offsetDay(now, days));
            List<InviteBinding> expiringSoonList = this.recordNotActiveUser(expireDay);
            log.info("expiring Soon active user list size:[{}] days:[{}]", expiringSoonList.size(), days);
            if (CollUtil.isEmpty(expiringSoonList)) {
                return;
            }
            String userNotActiveNotifyStr = systemConfigService.getSysConfValueById(SystemConfigConstant.USER_NOT_ACTIVE_NOTIFY_TEXT);
            JSONObject textObj = new JSONObject();
            if (StrUtil.isNotBlank(userNotActiveNotifyStr)) {
                textObj = JSONUtil.parseObj(userNotActiveNotifyStr);
            }
            JSONObject finalTextObj = textObj;
            List<Long> invitedUids = expiringSoonList.stream().map(InviteBinding::getInvitedUid).distinct().toList();
            Map<Long, UserBaseInfoDTO> invitedUserMap = userServerService.batchUserSummary(invitedUids);
            expiringSoonList.forEach(dto -> {
                String superiorUid = redissonManager.hGet(InviteRedisKey.user_superior_invite.getKey(), String.valueOf(dto.getInvitedUid()));
                // 不活跃消息
                if (StrUtil.isNotBlank(superiorUid)) {
                    LanguageEnum languageEnum = userServerService.userAppLanguage(Long.parseLong(superiorUid));
                    Object msgText = finalTextObj.get(languageEnum.name());
                    if (Objects.nonNull(msgText)) {
                        UserBaseInfoDTO userBaseInfoDTO = invitedUserMap.get(dto.getInvitedUid());
                        UserActiveMsgDTO msgDTO = UserActiveMsgDTO.builder()
                                .text(StrUtil.format(msgText.toString(), Math.abs(days)))
                                .event(UserActiveEventEnum.NOT_ACTIVE.getType())
                                .userInfo(userBaseInfoDTO)
                                .linkUrl(ClientRouteUtil.toHomePage(dto.getInvitedUid(), Boolean.FALSE))
                                .build();
                        CommonIMMessage imMessage = CommonIMMessage.builder()
                                .type(IMMsgType.InviteActiveMsg)
                                .payload(JSONUtil.toJsonStr(msgDTO))
                                .timestamp(System.currentTimeMillis()).build();

                        OfflinePushInfo offlinePushInfo = null;
                        try {
                            offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.friendAssistantMsgUid),
                                    msgDTO.getText(),
                                    msgDTO.getText(),
                                    null,
                                    null);
                        } catch (Exception e) {
                            // ignore
                            log.info("expiringSoonActiveUser build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
                        }

                        notifyMessageComponent.publishFriendAssistantMessage(imMessage, Long.parseLong(superiorUid), offlinePushInfo);
                    }
                }
            });
        });
    }

    public Boolean rebind(Long uid, String inviteCode) {
        String improveInformationFlag = redissonManager.get(InviteRedisKey.improve_information_flag.getKey(uid));
        if (StrUtil.isBlank(improveInformationFlag)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        String superiorUid = redissonManager.hGet(InviteRedisKey.user_superior_invite.getKey(), String.valueOf(uid));
        if (StrUtil.isNotBlank(superiorUid)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        String codeUid = this.userInfoByInviteCode(inviteCode);
        if (StrUtil.isBlank(codeUid)) {
            log.info("user invite rebind inviteCode check fail uid:[{}] inviteCode:[{}]", uid, inviteCode);
            long maybeUserNo;
            try {
                maybeUserNo = Long.parseLong(inviteCode);
            } catch (Exception e) {
                return false;
            }
            Long uidByUserNo = userServerService.getUidByUserNo(maybeUserNo);
            if (Objects.isNull(uidByUserNo)) {
                return false;
            }
            codeUid = String.valueOf(uidByUserNo);
        }
        if (Objects.equals(codeUid, String.valueOf(uid))) {
            log.info("invite rebind can not binding self inviteCode:[{}]", inviteCode);
            return false;
        }
        UserBaseInfoDTO inviteUser = userServerService.getFromCache(Long.valueOf(codeUid));
        if (Objects.isNull(inviteUser)) {
            throw new ApiException(CodeEnum.USER_NOT_FOUND);
        }
        log.info("user invite rebind uid:[{}] inviteCode:[{}]", uid, inviteCode);
        this.inviteBinding(inviteUser.getUid(), uid, InviteBindingEnum.REBIND.getType(), null, Boolean.TRUE);
        return true;
    }
}
