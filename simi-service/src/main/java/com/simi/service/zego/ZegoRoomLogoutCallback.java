package com.simi.service.zego;


import com.simi.common.dto.AbstractCallbackBody;
import com.simi.common.dto.room.RoomLogoutCallbackBody;
import com.simi.service.IZegoCallback;
import com.simi.service.room.RoomHighService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * 退出房间
 * <AUTHOR>
 * @date 2024/2/23 16:30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ZegoRoomLogoutCallback implements IZegoCallback<RoomLogoutCallbackBody> {
  private final RoomHighService roomApiComponent;

  @Override
  public void handler(RoomLogoutCallbackBody copyMessage) {
    roomApiComponent.outRoom(copyMessage.getUserAccount(), copyMessage.getRoomId(), new Date(copyMessage.getLogoutTime()));
  }

  @Override
  public String eventType() {
    return AbstractCallbackBody.CALLBACK_ROOM_LOGOUT;
  }
}
