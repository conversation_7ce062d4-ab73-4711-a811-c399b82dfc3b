package com.simi.service.zego;

import com.simi.service.IZegoCallback;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
public class ZegoCallbackFactory {

    private final Map<String, IZegoCallback> zegoCallbackMap = new ConcurrentHashMap<>();


    public IZegoCallback getCallback(String eventType){
        return this.zegoCallbackMap.get(eventType);
    }
}
