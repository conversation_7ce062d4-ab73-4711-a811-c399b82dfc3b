package com.simi.service;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.simi.entity.MsgModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendCallback;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.MessageConst;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.assertj.core.util.Arrays;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 测试消息队列
 */
@Service
@Slf4j
public class RocketMqSender {


    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    /**
     * 同步消息
     */
    public void sendSynchronizationMsg(String topic, String mag) {
        rocketMQTemplate.syncSend(topic, mag);
    }

    /**
     * 异步消息
     */
    public void sendAsynchronousMsg(String topic, String mag) {
        // 异步消息
        rocketMQTemplate.asyncSend(topic, mag, new SendCallback() {
            @Override
            public void onSuccess(SendResult sendResult) {
                log.info("RocketMqSender result success topic:[{}] mag:[{}]  result:[{}]", topic, mag, JSONUtil.toJsonStr(sendResult));
            }

            @Override
            public void onException(Throwable throwable) {
                log.error("RocketMqSender result error topic:[{}] mag:[{}]  result:[{}]", topic, mag, JSONUtil.toJsonStr(throwable.getMessage()));
            }
        });
    }

    /**
     * 单向消息
     * 不等待服务端返回响应且没有回调函数触发，即只发送请求不等待应答
     * @param topic
     * @param mag
     */
    public void sendOneWay(String topic, String mag) {
        rocketMQTemplate.sendOneWay(topic, mag);
    }

    /**
     * 指定交付直接消息
     * @param topic
     * @param msg
     */
    public void sendDeliverMessage(String topic, String msg, Long millisecond ) {
        // 延迟消息，必须创建Message对象，延迟等级在发送参数中
        Message<String> message = MessageBuilder.withPayload(msg).build();
        log.info("rocketMQ send delayed message:[{}] topic:[{}]", JSONUtil.toJsonStr(message), topic);
        rocketMQTemplate.syncSendDeliverTimeMills(topic, message, System.currentTimeMillis() + millisecond);
    }

    public void sendDelayedMessage(String topic, String messageBody, Long delayMilliSecond) {
        Message<?> msg = MessageBuilder.withPayload(messageBody)
                .setHeader(MessageConst.PROPERTY_DELAY_TIME_LEVEL, String.valueOf(delayMilliSecond))
                .build();
        rocketMQTemplate.convertAndSend(topic, msg);
    }


    /**
     * 发送顺序消息
     * 概念：RocketMQ的Topic在创建时默认会生成4个队列，只能保证在一个队列中保证顺序消息，
     *      通过消息的id hash取模获取这条消息应该储存在那个队列中，并且必须使用同步消息，
     *      消费者采用ConsumeMode.ORDERLY（同一个队列单线程消费）模式消费，对于指定的一个Topic，
     *      每个队列的消息按照严格的先入先出（FIFO）的顺序来发布和消费。
     * 适用场景：适用于性能要求不高，所有的消息严格按照FIFO原则来发布和消费的场景。
     */
    public void sendSendOrderly() {
        // 顺序消息，在发送消息时通过参数直接传入hashKey，保证消息顺序在同一队列
        List<MsgModel> msgModels = new ArrayList<>();
        msgModels.add(new MsgModel("qwer", 1, "下单"));
        msgModels.add(new MsgModel("qwer", 1, "短信"));
        msgModels.add(new MsgModel("qwer", 1, "物流"));
        msgModels.add(new MsgModel("zxcv", 2, "下单"));
        msgModels.add(new MsgModel("zxcv", 2, "短信"));
        msgModels.add(new MsgModel("zxcv", 2, "物流"));
        for (MsgModel msgModel : msgModels) {
            rocketMQTemplate.syncSendOrderly("bootTestTopic", JSON.toJSONString(msgModel), JSON.toJSONString(msgModel.getNum()));
        }
    }


}
