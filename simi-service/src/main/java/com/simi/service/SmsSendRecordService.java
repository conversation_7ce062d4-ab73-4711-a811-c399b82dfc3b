package com.simi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.sms.SmsSendRecordDTO;
import com.simi.constant.PurposeEnum;
import com.simi.constant.SmsChannelEnum;
import com.simi.entity.SmsSendRecord;
import com.simi.mapper.SmsSendRecordMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class SmsSendRecordService extends ServiceImpl<SmsSendRecordMapper, SmsSendRecord> {

    public ListWithTotal<SmsSendRecordDTO> queryList(String phone, Integer channel, Integer purpose, Integer pageNo, Integer pageSize){
        List<SmsSendRecordDTO> dtos = new ArrayList<>();
        LambdaQueryWrapper<SmsSendRecord> wrapper = new LambdaQueryWrapper<>();
        if (!StringUtils.isBlank(phone)) {
            wrapper.eq(SmsSendRecord::getPhone,phone);
        }
        if (channel != null && channel > 0) {
            String  desc = SmsChannelEnum.getByNumber(channel);
            wrapper.eq(SmsSendRecord::getChannel,desc);
        }
        if (purpose != null && purpose > 0) {
            String descByNumber = PurposeEnum.getDescByNumber(purpose);
            wrapper.eq(SmsSendRecord::getPurpose,descByNumber);
        }
        wrapper.orderByDesc(SmsSendRecord::getCreateTime);
        ListWithTotal<SmsSendRecordDTO> dtoListWithTotal = new ListWithTotal<>();
        PageHelper.startPage(pageNo, pageSize);
        List<SmsSendRecord> list = list(wrapper);
        PageInfo<SmsSendRecord> pageInfo = new PageInfo<>(list);
        for (SmsSendRecord record : list) {
            SmsSendRecordDTO dto = new SmsSendRecordDTO();
            dto.setCode(record.getCode());
            dto.setId(record.getId());
            dto.setPhone(record.getPhone());
            dto.setPurpose(record.getPurpose());
            dto.setCreateTime(record.getCreateTime());
            dto.setAreaCode(record.getAreaCode());
            dto.setChannel(record.getChannel());
            dtos.add(dto);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }
}
