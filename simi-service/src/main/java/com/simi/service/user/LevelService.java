package com.simi.service.user;


import com.simi.common.constant.PrivilegeEnum;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.common.constant.leven.ActiveLevelEnum;
import com.simi.common.constant.leven.CharmLevelEnum;
import com.simi.common.constant.leven.LevelIconEnum;
import com.simi.common.constant.leven.WealthLevelEnum;
import com.simi.common.dto.aristocracy.AristocracyConfigDTO;
import com.simi.common.dto.aristocracy.UserCurAristocracyInfo;
import com.simi.common.dto.user.*;
import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.config.UserLevelConfig;
import com.simi.constant.MedalActiveEnum;
import com.simi.constant.MedalCharmEnum;
import com.simi.constant.MedalWealthEnum;
import com.simi.constant.UserRedisKey;
import com.simi.dto.MedalDTO;
import com.simi.dto.MedalLevel;
import com.simi.entity.LevelInfo;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.vip.UserVipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;


/**
 * 等级服务
 *
 * <AUTHOR>
 * @date 2024/1/22 16:12
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class LevelService {

    @Resource
    private RedissonClient redissonClient;

    private static final String LEVEL_ROUTE = "everysing://?page=level&type={}";

    @Autowired
    private RedissonManager redissonManager;
    @Autowired
    private UserAristocracyRecordsService userAristocracyRecordsService;
    private final AristocracyConfigService aristocracyConfigService;
    private final UserVipService userVipService;


    /**
     * 获取用户活跃等级信息
     *
     * @param uid
     * @return
     */

    public UserLevelDTO getUserActiveLevel(long uid) {
        String charmStr = redissonManager.hGet(UserRedisKey.user_active_level.getKey(), String.valueOf(uid));
        long charmAmount = 0;
        if (StringUtils.isNotBlank(charmStr)) {
            charmAmount = Long.parseLong(charmStr);
        }
        LevelInfo currentLevel = UserLevelConfig.activeLevel(UserLevelTypeEnum.LEVEL_ACTIVE, charmAmount);
        LevelInfo nextLevel = UserLevelConfig.activeNextLevel(UserLevelTypeEnum.LEVEL_ACTIVE, charmAmount);
        int userActiveLevelMedal = UserLevelConfig.getUserActiveLevelMedal(charmAmount);
        UserLevelDTO builder = new UserLevelDTO();
        builder.setUserCurrentLevelAmount(charmAmount);
        builder.setUid(uid);
        builder.setCurrentLevel(currentLevel.getLevel());
        builder.setNextLevel(nextLevel.getLevel());
        builder.setNextLevelAmount(nextLevel.getGold());
        builder.setIcon(MedalActiveEnum.getDesc(userActiveLevelMedal));
        try {
            // 贵族
            Optional<UserCurAristocracyInfo> aristocracyInfoOptional = userAristocracyRecordsService.getUserAristocracyRecordsCache(uid);
            UserLevelDTO.AristocracyInfo aristocracyInfo = new UserLevelDTO.AristocracyInfo();
            aristocracyInfoOptional.ifPresent(item -> {
                Optional<AristocracyConfigDTO> aristocracyConfigDTOOptional
                        = aristocracyConfigService.aristocracyConfigById(item.getCurAristocracy());
                aristocracyConfigDTOOptional.ifPresent(k->{
                    AristocracyConfigDTO aristocracyConfigDTO = aristocracyConfigDTOOptional.get();
                    aristocracyInfo.setAristocracyId(item.getCurAristocracy());
                    aristocracyInfo.setIconUrl(aristocracyConfigDTO.getIconUrl());
                    aristocracyInfo.setAddition(item.getAristocracyPropInfo().getCharmBonus());
                    builder.setAristocracyInfo(aristocracyInfo);
                });
            });
        } catch (Exception e) {
            log.error("aristocracy getUserActiveLevel  charm level error.{}", ExceptionUtil.formatEx(e), e);
        }
        return builder;
    }

    public UserActiveLevelBase getUserActive(final long uid) {
        Long userActive = redissonClient.<String, Long>getMap(UserRedisKey.user_active_level.getKey()).getOrDefault(String.valueOf(uid), 0L);
        return getUserActiveLevelByUidAndAmount(uid, userActive);
    }

    public UserActiveLevelBase getUserActiveLevelByUidAndAmount(final long uid, final long amount) {
        UserActiveLevelBase userActiveLevelBase = new UserActiveLevelBase();
        if (uid == 0) {
            return userActiveLevelBase;
        }
        userActiveLevelBase.setAmount(amount);
        userActiveLevelBase.setUid(uid);

        setActiveLevel(userActiveLevelBase);
        return userActiveLevelBase;
    }

    private void setActiveLevel(UserActiveLevelBase userActiveLevelBuilder) {
        final long amount = userActiveLevelBuilder.getAmount();
        for (int i = 0; i < ActiveLevelEnum.values().length; i++) {
            ActiveLevelEnum activeLevel = ActiveLevelEnum.values()[i];
            final long curAmount = activeLevel.getAmount();
            if (i >= ActiveLevelEnum.values().length - 1) {
                ActiveLevelEnum maxLevel = ActiveLevelEnum.maxLevel();
                // 如果已经是最后一个等级
                userActiveLevelBuilder.setCurrentLevel(maxLevel.toBase());
                userActiveLevelBuilder.setNextLevel(maxLevel.toBase());
                break;
            } else {
                ActiveLevelEnum nextActiveLevel = ActiveLevelEnum.values()[i + 1];
                if (amount >= curAmount && amount < nextActiveLevel.getAmount()) {
                    userActiveLevelBuilder.setCurrentLevel(activeLevel.toBase());
                    userActiveLevelBuilder.setNextLevel(nextActiveLevel.toBase());
                    break;
                }
            }
        }
    }

    /**
     * 获取用户魅力等级信息
     *
     * @param uid
     * @return
     */
    public UserLevelDTO getUserCharmLevel(final long uid) {
        String charmStr = redissonManager.hGet(UserRedisKey.user_charm_level.getKey(), String.valueOf(uid));
        long charmAmount = 0;
        if (StringUtils.isNotBlank(charmStr)) {
            charmAmount = Long.parseLong(charmStr);
        }
        LevelInfo currentLevel = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_CHARM, charmAmount);
        LevelInfo nextLevel = UserLevelConfig.nextLevel(UserLevelTypeEnum.LEVEL_CHARM, charmAmount);

        int userCharmLevelMedal = UserLevelConfig.getUserCharmLevelMedal(charmAmount);
        UserLevelDTO builder = new UserLevelDTO();
        builder.setUserCurrentLevelAmount(charmAmount);
        builder.setUid(uid);
        builder.setCurrentLevel(currentLevel.getLevel());
        builder.setNextLevel(nextLevel.getLevel());
        builder.setNextLevelAmount(nextLevel.getGold());
        builder.setIcon(MedalCharmEnum.getDesc(userCharmLevelMedal));
        try {
            Optional<UserCurAristocracyInfo> aristocracyInfoOptional = userAristocracyRecordsService.getUserAristocracyRecordsCache(uid);
            UserLevelDTO.AristocracyInfo aristocracyInfo = new UserLevelDTO.AristocracyInfo();
            aristocracyInfoOptional.ifPresent(item -> {
                Optional<AristocracyConfigDTO> aristocracyConfigDTOOptional
                        = aristocracyConfigService.aristocracyConfigById(item.getCurAristocracy());
                aristocracyConfigDTOOptional.ifPresent(k->{
                    AristocracyConfigDTO aristocracyConfigDTO = aristocracyConfigDTOOptional.get();
                    aristocracyInfo.setAristocracyId(item.getCurAristocracy());
                    aristocracyInfo.setIconUrl(aristocracyConfigDTO.getIconUrl());
                    aristocracyInfo.setAddition(item.getAristocracyPropInfo().getCharmBonus());
                    builder.setAristocracyInfo(aristocracyInfo);
                });
            });
        } catch (Exception e) {
            log.error("aristocracy getUserCharmLevel  charm level error.{}", ExceptionUtil.formatEx(e), e);
        }
        return builder;
    }

    public UserCharmLevelBase getUserCharm(Long uid) {
        String amount = redissonManager.hGet(UserRedisKey.user_charm_level.getKey(), String.valueOf(uid));
        return getUserCharmLevelByUidAndAmount(uid, amount != null?Long.parseLong(amount):0L);
    }

    public UserCharmLevelBase getUserCharmLevelByUidAndAmount(Long uid, Long amount) {

        UserCharmLevelBase base = new UserCharmLevelBase();
        if (uid == 0) {
            return base;
        }

        base.setUid(uid);
        base.setAmount(amount);

        setCharmLevel(base);
        return base;
    }

    private void setCharmLevel(UserCharmLevelBase userCharmLevelBuilder) {
        final long amount = userCharmLevelBuilder.getAmount();
        for (int i = 0; i < CharmLevelEnum.values().length; i++) {
            CharmLevelEnum charmLevel = CharmLevelEnum.values()[i];
            final long curAmount = charmLevel.getAmount();
            if (i >= CharmLevelEnum.values().length - 1) {
                CharmLevelEnum maxLevel = CharmLevelEnum.maxLevel();
                // 如果已经是最后一个等级
                userCharmLevelBuilder.setCurrentLevel(maxLevel.toBase());
                userCharmLevelBuilder.setNextLevel(maxLevel.toBase());
                break;
            } else {
                CharmLevelEnum nextCharmLevel = CharmLevelEnum.values()[i + 1];
                if (amount >= curAmount && amount < nextCharmLevel.getAmount()) {
                    userCharmLevelBuilder.setCurrentLevel(charmLevel.toBase());
                    userCharmLevelBuilder.setNextLevel(nextCharmLevel.toBase());
                    break;
                }
            }
        }
    }

    /**
     * 获取用户财富等级信息
     *
     * @param uid
     * @return
     */
    public UserLevelDTO getUserWealthLevel(long uid) {
        String wealthStr = redissonManager.hGet(UserRedisKey.user_wealth_level.getKey(), String.valueOf(uid));
        long wealthAmount = 0;
        if (StringUtils.isNotBlank(wealthStr)) {
            wealthAmount = Long.parseLong(wealthStr);
        }
        LevelInfo currentLevel = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_WEALTH, wealthAmount);
        LevelInfo nextLevel = UserLevelConfig.nextLevel(UserLevelTypeEnum.LEVEL_WEALTH, wealthAmount);

        int userWealthLevelMedal = UserLevelConfig.getUserWealthLevelMedal(wealthAmount);
        UserLevelDTO builder = new UserLevelDTO();
        builder.setUserCurrentLevelAmount(wealthAmount);
        builder.setUid(uid);
        builder.setCurrentLevel(currentLevel.getLevel());

        builder.setNextLevel(nextLevel.getLevel());
        builder.setNextLevelAmount(nextLevel.getGold());
        builder.setIcon(MedalWealthEnum.getDesc(userWealthLevelMedal));
        try {
            Optional<UserCurVipInfo> vipInfoOptional = userVipService.getUserVipCache(uid);
            UserLevelDTO.VIPInfo vipInfo = new UserLevelDTO.VIPInfo();
            vipInfoOptional.ifPresent(item -> {
                UserCurVipInfo.PropInfo propInfo = item.getPropInfo();
                builder.setVipInfo(vipInfo);
                vipInfo.setVipLevel(item.getVipLevel());
                vipInfo.setIconUrl(propInfo.getIcon());
                vipInfo.setAddition(propInfo.getWealthCoefficient());
            });
        } catch (Exception e) {
            log.error("aristocracy getUserCharmLevel  charm level error.{}", ExceptionUtil.formatEx(e), e);
        }
        return builder;
    }

    public UserWealthLevelBase getUserWealthLevelByUidAndAmount(long uid, long amount) {
        UserWealthLevelBase base = new UserWealthLevelBase();
        if (uid == 0) {
            return base;
        }
        base.setUid(uid);
        base.setAmount(amount);
        setWealthLevel(base);
        return base;
    }

    private void setWealthLevel(UserWealthLevelBase userWealthLevelBuilder) {
        final long amount = userWealthLevelBuilder.getAmount();
        for (int i = 0; i < WealthLevelEnum.values().length; i++) {
            WealthLevelEnum wealthLevel = WealthLevelEnum.values()[i];
            final long curAmount = wealthLevel.getAmount();
            if (i >= WealthLevelEnum.values().length - 1) {
                WealthLevelEnum maxLevel = WealthLevelEnum.maxLevel();
                // 如果已经是最后一个等级
                userWealthLevelBuilder.setCurrentLevel(maxLevel.toBase());
                userWealthLevelBuilder.setNextLevel(maxLevel.toBase());
                break;
            } else {
                WealthLevelEnum nextWealthLevel = WealthLevelEnum.values()[i + 1];
                if (amount >= curAmount && amount < nextWealthLevel.getAmount()) {
                    userWealthLevelBuilder.setCurrentLevel(wealthLevel.toBase());
                    userWealthLevelBuilder.setNextLevel(nextWealthLevel.toBase());
                    break;
                }
            }
        }
    }

    /**
     * 处理魅力等级
     *
     * @param user
     * @param userCharmAmount
     * @param coinNum
     * @return
     */
    public int handleCharmLevel(UserBaseInfoDTO user, final long userCharmAmount, final long coinNum) {
        // 获取用户的当前魅力等级
        UserCharmLevelBase curCharm = getUserCharmLevelByUidAndAmount(user.getUid(), userCharmAmount);
        // 处理魅力等级
        final long preCharmAmount = curCharm.getAmount() - coinNum;
        if (preCharmAmount < curCharm.getCurrentLevel().getAmount()) {
            //处理升级逻辑
//            sendUserUpgrade2RTM(user, UserLevelTypeEnum.LEVEL_CHARM, curCharm.getCurrentLevel().getLevel());
//            sendUserUpgrade2IM(user, UserLevelTypeEnum.LEVEL_CHARM, curCharm.getCurrentLevel().getLevel());
            return 1;
        }
        return 0;
    }

    /**
     * 处理财富等级
     *
     * @param user
     * @param userWealthAmount
     * @param coinNum
     * @return
     */
    public int handleWealthLevel(UserBaseInfoDTO user, final long userWealthAmount, final long coinNum) {
        // 获取用户的当前财富等级
        UserWealthLevelBase curWealth = getUserWealthLevelByUidAndAmount(user.getUid(), userWealthAmount);
        // 处理财富等级
        final long preWealthAmount = curWealth.getAmount() - coinNum;
        if (preWealthAmount < curWealth.getCurrentLevel().getAmount()) {
            //处理升级逻辑
//            sendUserUpgrade2RTM(user, UserLevelTypeEnum.LEVEL_WEALTH, curWealth.getCurrentLevel().getLevel());
//            sendUserUpgrade2IM(user, UserLevelTypeEnum.LEVEL_WEALTH, curWealth.getCurrentLevel().getLevel());
            return 1;
        }
        return 0;
    }

    public MedalDTO getMedal(Integer type) {

        if (Objects.equals(type, UserLevelTypeEnum.LEVEL_CHARM.getType())) {
            return userCharm();
        }
        if (Objects.equals(type, UserLevelTypeEnum.LEVEL_WEALTH.getType())) {
            return userWealth();
        }
        if (Objects.equals(type, UserLevelTypeEnum.LEVEL_ACTIVE.getType())) {
            return userActive();
        }
        return null;
    }

    public MedalDTO userWealth() {
        MedalDTO dto = new MedalDTO();
        List<MedalLevel> medalLevels = new ArrayList<>();
        for (MedalWealthEnum value : MedalWealthEnum.values()) {
            MedalLevel medalLevel = new MedalLevel();
            medalLevel.setMedalNum(LevelIconEnum.getByScope(value.getNumber()));
            medalLevel.setMedalPic(value.getDesc());
            medalLevels.add(medalLevel);
        }
        dto.setMedalLevels(medalLevels);
        String levelPrivileges = MessageSourceUtil.i18nByCode(PrivilegeEnum.LEVEL_PRIVILEGES_WEALTH.getCode(), MessageSourceUtil.getLang());
        dto.setLevelPrivileges(levelPrivileges);
        return dto;
    }

    public MedalDTO userActive() {
        MedalDTO dto = new MedalDTO();
        List<MedalLevel> medalLevels = new ArrayList<>();
        for (MedalActiveEnum value : MedalActiveEnum.values()) {
            MedalLevel medalLevel = new MedalLevel();
            medalLevel.setMedalNum(LevelIconEnum.getByScope(value.getNumber()));
            medalLevel.setMedalPic(value.getDesc());
            medalLevels.add(medalLevel);
        }
        dto.setMedalLevels(medalLevels);
        String levelPrivileges = MessageSourceUtil.i18nByCode(PrivilegeEnum.LEVEL_PRIVILEGES_ACTIVE.getCode(), MessageSourceUtil.getLang());
        dto.setLevelPrivileges(levelPrivileges);
        return dto;
    }

    public MedalDTO userCharm() {
        MedalDTO dto = new MedalDTO();
        List<MedalLevel> medalLevels = new ArrayList<>();
        for (MedalCharmEnum value : MedalCharmEnum.values()) {
            MedalLevel medalLevel = new MedalLevel();
            medalLevel.setMedalNum(LevelIconEnum.getByScope(value.getNumber()));
            medalLevel.setMedalPic(value.getDesc());
            medalLevels.add(medalLevel);
        }
        dto.setMedalLevels(medalLevels);
        String levelPrivileges = MessageSourceUtil.i18nByCode(PrivilegeEnum.LEVEL_PRIVILEGES_CHARM.getCode(), MessageSourceUtil.getLang());
        dto.setLevelPrivileges(levelPrivileges);
        return dto;
    }


    /**
     * 推送用户升级消息
     *
     * @param user
     * @param levelType
     * @param level
     */
   /* private void sendUserUpgrade2RTM(User user, UserLevelTypeEnum levelType, final int level) {
        String msgTemplate = messageSourceHandler.getMessage(UserCopywritingEnum.CP_3.getKey(), user.getAppLanguage());
        String levelTypeText = "";
        switch (levelType) {
            case LEVEL_ACTIVE:
                levelTypeText = messageSourceHandler.getMessage(UserCopywritingEnum.CP_4.getKey(), user.getAppLanguage());
                break;
            case LEVEL_CHARM:
                levelTypeText = messageSourceHandler.getMessage(UserCopywritingEnum.CP_5.getKey(), user.getAppLanguage());
                break;
            case LEVEL_WEALTH:
                levelTypeText = messageSourceHandler.getMessage(UserCopywritingEnum.CP_6.getKey(), user.getAppLanguage());
                break;
            default:
                return;
        }
        String message = StrUtil.format(msgTemplate, levelTypeText);
        MessageBasePB.SingleText messageText = MessageBasePB.SingleText.newBuilder().setText(message).build();
        List<MessageBasePB.SingleText> content = Lists.newArrayList(messageText);
        UserLevelEvent levelEvent = new UserLevelEvent(levelType, user.getAvatar(), level, content);
        MessageBasePB.CommonMessage commonMessage = levelEvent.toCommonMessage(String.valueOf(user.getUid()));
        MessageBasePB.NextEventData nextEventData = NextEventDataUtil.buildEventData(commonMessage);
        notifyMessageComponent.publishChatroomDefineMsg(nextEventData);
    }*/

    /**
     * 推送用户升级消息至IM
     *
     * @param user
     */
    /*private void sendUserUpgrade2IM(User user, UserLevelTypeEnum levelType, final int level) {
        String levelTypeText = "";
        String routeParam = "";
        switch (levelType) {
            case LEVEL_ACTIVE:
                levelTypeText = messageSourceHandler.getMessage(UserCopywritingEnum.CP_4.getKey(), user.getAppLanguage());
                routeParam = "active";
                break;
            case LEVEL_CHARM:
                levelTypeText = messageSourceHandler.getMessage(UserCopywritingEnum.CP_5.getKey(), user.getAppLanguage());
                routeParam = "charm";
                break;
            case LEVEL_WEALTH:
                levelTypeText = messageSourceHandler.getMessage(UserCopywritingEnum.CP_6.getKey(), user.getAppLanguage());
                routeParam = "wealth";
                break;
            default:
                return;
        }
        String title = messageSourceHandler.getMessage(UserCopywritingEnum.CP_7.getKey(), user.getAppLanguage());
        title = StrUtil.format(title, levelTypeText);
        String content = messageSourceHandler.getMessage(UserCopywritingEnum.CP_8.getKey(), user.getAppLanguage());
        content = StrUtil.format(content, levelTypeText, level);
        IMBasePB.TitleImageTextLinkIMMsgModel msgModel = IMBasePB.TitleImageTextLinkIMMsgModel.newBuilder()
                .setTitle(title)
                .setText(content)
                .setLinkUrl(StrUtil.format(LEVEL_ROUTE, routeParam))
                .build();
        CommonIMMessage commonIMMessage = CommonIMMessage.newBuilder().setType(IMMsgType.TitleImageTextLinkIMMsg)
                .setPayload(Any.pack(msgModel))
                .setTimestamp(System.currentTimeMillis())
                .build();
        notifyMessageComponent.publishIMDefineMessage(commonIMMessage, String.valueOf(user.getUid()), PlatformConfig.systemUid);
    }*/
}
