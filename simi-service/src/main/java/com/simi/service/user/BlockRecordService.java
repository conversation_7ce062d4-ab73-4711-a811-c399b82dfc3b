package com.simi.service.user;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.StatusEnum;
import com.simi.common.exception.ApiException;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.constant.AccountConstant;
import com.simi.dto.BlackMagDTO;
import com.simi.entity.BlockHintRecord;
import com.simi.entity.BlockRecord;
import com.simi.mapper.BlockRecordMapper;
import com.simi.service.BlockHintRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/2/2 13:40
 */
@Slf4j
@Service
public class BlockRecordService extends ServiceImpl<BlockRecordMapper, BlockRecord> {

    @Autowired
    private BlockRecordMapper blockRecordMapper;

    @Autowired
    private BlockHintRecordService blockHintRecordService;

    public  void blackByUid(Long uid) {
        PageHelper.startPage(1,1);
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BlockRecord::getUid, uid);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.gt(BlockRecord::getEndTime, new Date());
        wrapper.orderByDesc(BlockRecord::getEndTime);
        wrapper.orderByAsc(BlockRecord::getType);
        BlockRecord record = getOne(wrapper);
        verifyAccount(record);
    }

    public  String blockHint(Long uid) {
        PageHelper.startPage(1,1);
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BlockRecord::getUid, uid);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.gt(BlockRecord::getEndTime, new Date());
        wrapper.orderByDesc(BlockRecord::getEndTime);
        wrapper.orderByAsc(BlockRecord::getType);
        BlockRecord record = getOne(wrapper);
        return verifyByUid(record);
    }

    public void isBlack(String deviceBlack, String ipBlack,Long uid) {
        PageHelper.startPage(1,1);
        log.info("block isBlack deviceBlack:{} ipBlack:{} uid:{}",deviceBlack, ipBlack, uid);
        BlockRecord record = blockRecordMapper.getByContent(deviceBlack, ipBlack, uid);
        log.info("block isBlack record:{} ",JSONUtil.toJsonStr(record));
        verifyAccount(record);
    }

    public BlockRecord getByContent(String deviceBlack, String ipBlack,Long uid) {
        PageHelper.startPage(1,1);
        return blockRecordMapper.getByContent(deviceBlack, ipBlack, uid);
    }

    public List<BlockRecord> blockByUid(Long uid) {
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BlockRecord::getUid, uid);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.gt(BlockRecord::getEndTime, new Date());
        return list(wrapper);
    }


    public BlockRecord blockByContent(String content,Integer type) {
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BlockRecord::getContent, content);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.eq(BlockRecord::getType,type);
        wrapper.gt(BlockRecord::getEndTime, new Date());
        wrapper.eq(BlockRecord::getBizId,0);
        return getOne(wrapper);
    }


    public List<BlockRecord> queryList(List<Long> uids){
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BlockRecord::getUid, uids);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.gt(BlockRecord::getEndTime, new Date());
        return list(wrapper);
    }

    public List<BlockRecord> queryDidIn(List<String> dids){
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BlockRecord::getContent, dids);
        wrapper.eq(BlockRecord::getType,AccountConstant.BlockType.device_block);
        wrapper.eq(BlockRecord::getBizId,0);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.gt(BlockRecord::getEndTime, new Date());
        return list(wrapper);
    }

    public List<BlockRecord> queryIpIn(List<String> ips){
        LambdaQueryWrapper<BlockRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(BlockRecord::getContent, ips);
        wrapper.eq(BlockRecord::getType,AccountConstant.BlockType.ip_block);
        wrapper.eq(BlockRecord::getBizId,0);
        wrapper.eq(BlockRecord::getStatus, StatusEnum.normal.getStatus());
        wrapper.gt(BlockRecord::getEndTime, new Date());
        return list(wrapper);
    }

    public void verifyAccount(BlockRecord record){
        if (record != null) {
            BlockHintRecord hintRecord = new BlockHintRecord();
            hintRecord.setContent(record.getContent());
            hintRecord.setType(record.getType());
            String uuidString = UUID.randomUUID().toString();
            String code = uuidString.substring(0, 6);
            hintRecord.setCode(code);
            hintRecord.setCreateTime(new Date());
            blockHintRecordService.save(hintRecord);
            log.info("block isBlack hintRecord:{} ",JSONUtil.toJsonStr(hintRecord));
            BlackMagDTO dto = new BlackMagDTO();
            LanguageEnum lang = MessageSourceUtil.getLang();
            dto.setIsBlack(true);
            String remarkCode = "(" + code + ")";
            if (LanguageEnum.ar.equals(lang)) {
                dto.setBlackRemark(record.getRemarkAr() + remarkCode);
            } else {
                dto.setBlackRemark(record.getRemarkEn() + remarkCode);
            }
            dto.setBlackType(record.getType());
            dto.setBlackSTime(record.getStartTime());
            dto.setBlackETime(record.getEndTime());
            log.info("block isBlack BlackMagDTO:{} ",JSONUtil.toJsonStr(dto));
            throw new ApiException(CodeEnum.ACCOUNT_BLACK_LIST, JSONUtil.toJsonStr(dto));
        }

    }

    private String verifyByUid(BlockRecord record){
        if (record != null) {
            BlockHintRecord hintRecord = new BlockHintRecord();
            hintRecord.setContent(record.getContent());
            hintRecord.setType(record.getType());
            String uuidString = UUID.randomUUID().toString();
            String code = uuidString.substring(0, 6);
            hintRecord.setCode(code);
            hintRecord.setCreateTime(new Date());
            blockHintRecordService.save(hintRecord);
            log.info("block isBlack hintRecord:{} ",JSONUtil.toJsonStr(hintRecord));
            BlackMagDTO dto = new BlackMagDTO();
            LanguageEnum lang = MessageSourceUtil.getLang();
            dto.setIsBlack(true);
            String remarkCode = "(" + code + ")";
            if (LanguageEnum.ar.equals(lang)) {
                dto.setBlackRemark(record.getRemarkAr() + remarkCode);
            } else {
                dto.setBlackRemark(record.getRemarkEn() + remarkCode);
            }
            dto.setBlackType(record.getType());
            dto.setBlackSTime(record.getStartTime());
            dto.setBlackETime(record.getEndTime());
            log.info("block isBlack BlackMagDTO:{} ",JSONUtil.toJsonStr(dto));
            return JSONUtil.toJsonStr(dto);
        }
        return "";
    }

}
