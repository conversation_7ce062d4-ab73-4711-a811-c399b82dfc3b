package com.simi.service.user;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.util.ExceptionUtil;
import com.simi.entity.user.UserLevelRecords;
import com.simi.mapper.UserLevelRecordsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class UserLevelRecordsService extends ServiceImpl<UserLevelRecordsMapper, UserLevelRecords> {
    public List<UserLevelRecords> getUserLevelRecordsByUserIds(List<Long> userIds){
        List<UserLevelRecords> userLevelRecords = lambdaQuery().in(UserLevelRecords::getUid, userIds).list();
        return userLevelRecords;
    }

    /**
     * 持久化用户等级数据
     * @param uid
     * @param amount
     * @param levelType
     */
    @Async
    public void saveOrUpdateUserLevelRecords(Long uid,Long amount,int levelType){
        if (Objects.isNull(uid) || Objects.isNull(amount)){
            return;
        }

        try {
            LambdaQueryWrapper<UserLevelRecords> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserLevelRecords::getUid,uid)
                    .eq(UserLevelRecords::getLevelType,levelType);
            UserLevelRecords userLevelRecords = getOne(wrapper);

            UserLevelRecords updateUserLevelRecords = UserLevelRecords.builder()
                    .uid(uid)
                    .amount(amount)
                    .levelType(levelType)
                    .build();
            if (Objects.nonNull(userLevelRecords)){
                Long id = userLevelRecords.getId();
                updateUserLevelRecords.setId(id);
                updateUserLevelRecords.setUpdateTime(new Date());
                updateById(updateUserLevelRecords);
                return;
            }

            updateUserLevelRecords.setCreateTime(new Date());
            save(updateUserLevelRecords);
        }catch (Exception e){
            log.error("uid:{},amount:{},levelType:{},saveOrUpdateUserLevelRecords error,exception:{}",
                    uid,amount,levelType, ExceptionUtil.formatEx(e));
        }
    }
}
