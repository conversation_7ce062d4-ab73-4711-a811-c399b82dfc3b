package com.simi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.weeklyStar.WeeklyStarGiftRecords;
import com.simi.mapper.weeklyStar.WeeklyStarGiftRecordsMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-22 14:48
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class WeeklyStarGiftRecordsService extends ServiceImpl<WeeklyStarGiftRecordsMapper, WeeklyStarGiftRecords> {


    /**
     * 创建记录
     */
    public WeeklyStarGiftRecords create(Integer configId, Long userId, Integer giftPackageId, Integer giftId) {
        WeeklyStarGiftRecords weeklyStarGiftRecords = new WeeklyStarGiftRecords();
        weeklyStarGiftRecords.setConfigId(configId);
        weeklyStarGiftRecords.setUserId(userId);
        weeklyStarGiftRecords.setGiftPackageId(giftPackageId);
        weeklyStarGiftRecords.setGiftId(giftId);
        weeklyStarGiftRecords.setStatus(0);
        weeklyStarGiftRecords.setCreateTime(new Date());
        weeklyStarGiftRecords.setModifyTime(new Date());
        this.save(weeklyStarGiftRecords);
        return weeklyStarGiftRecords;
    }

    /**
     * 更新状态
     */
    public void updateStatus(Integer id, Integer status) {
        WeeklyStarGiftRecords weeklyStarGiftRecords = new WeeklyStarGiftRecords();
        weeklyStarGiftRecords.setId(id);
        weeklyStarGiftRecords.setStatus(status);
        weeklyStarGiftRecords.setModifyTime(new Date());
        this.updateById(weeklyStarGiftRecords);
    }
}
