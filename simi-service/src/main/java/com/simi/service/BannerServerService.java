package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.common.ConfigGroup;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.PositionEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.aristocracy.AristocracyConfigDTO;
import com.simi.common.dto.banner.*;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.req.BaseCommonCountryGroupBusinessReq;
import com.simi.common.vo.resp.UserLevelBaseVO;
import com.simi.config.UserLevelConfig;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.UserRedisKey;
import com.simi.dto.UserSimple;
import com.simi.dto.push.AristocracyUpgradeMsgDTO;
import com.simi.dto.push.JoyEffectBannerDTO;
import com.simi.dto.resource.BannerDTO;
import com.simi.dto.resource.BannerFilterParam;
import com.simi.entity.Banner;
import com.simi.entity.LevelInfo;
import com.simi.entity.group.AppFunctionsGroupConfig;
import com.simi.entity.user.User;
import com.simi.filter.banner.BannerFilterFactory;
import com.simi.filter.banner.ConditionFilter;
import com.simi.manager.BannerManager;
import com.simi.service.group.UserFunctionCountryGroupService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BannerServerService {

    private final BannerFilterFactory bannerFilterFactory;

    private final BannerService bannerService;

    private final BannerManager bannerManager;

    private final UserServerService userServerService;

    private final SystemConfigService systemConfigService;

    private final LongLinkService longLinkService;

    private final AuditCheckService auditCheckService;

    private final UserFunctionCountryGroupService countryGroupService;

    private final RedissonManager redissonManager;

    public BannerResp normalBanners(PositionEnum position, Long uid, XAuthToken xAuthToken) {
        LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
        String os = xAuthToken.getOs();
        String appVersion = xAuthToken.getAppVersionCode();
        if (position == null || position == PositionEnum.POSITION_NONE) {
            position = PositionEnum.HOME;
        }
        Map<String, ConditionFilter<BannerDTO>> allFilter = bannerFilterFactory.getAllFilter();
        List<BannerDTO> allList = getBannerListCache(uid, position);
        BannerFilterParam param = BannerFilterParam.builder()
                .appLang(languageEnum.name())
                .os(os)
                .appVersion(appVersion).build();
        boolean auditMode = auditCheckService.isAuditMode(xAuthToken);
        boolean auditUser = auditCheckService.isAuditUser(uid);

        List<BannerDTO> list = filterBanner(auditMode, auditUser, param, allList, allFilter, uid);
        boolean auditFilter = Objects.equals(position, PositionEnum.PURSE) && (auditMode || auditUser);
        if (CollectionUtils.isEmpty(list) || auditFilter) {
            return BannerResp.builder()
                    .total(0)
                    .list(CollUtil.newArrayList())
                    .build();
        } else {
            list = list.stream()
                    .sorted(Comparator.comparing(BannerDTO::getSeqNo))
                    .collect(Collectors.toList());
            // 播间Banner内最多显示10个
            if (position == PositionEnum.ROOM && list.size() > 10) {
                list = list.subList(0, 10);
            }
            List<BannerBaseDTO> bannerDTOs = list.stream().map(l -> buildDTO(l, languageEnum.name()))
                    .sorted(Comparator.comparing(BannerBaseDTO::getSeqNo, Comparator.reverseOrder()))
                    .collect(Collectors.toList());
            BannerResp resp = new BannerResp();
            resp.setTotal(list.size());
            resp.setList(bannerDTOs);
            return resp;
        }

    }

    public List<BannerDTO> filterBanner(boolean auditMode, boolean auditUser, BannerFilterParam param, List<BannerDTO> bannerDTOList,
                                        Map<String, ConditionFilter<BannerDTO>> filters, Long uid) {
        if (CollectionUtils.isEmpty(filters)) {
            return bannerDTOList;
        }
        Collection<ConditionFilter<BannerDTO>> filterList = filters.values();
        for (ConditionFilter<BannerDTO> filter : filterList) {
            if (CollectionUtils.isEmpty(bannerDTOList)) {
                break;
            }
            bannerDTOList = filter.doFilter(param, bannerDTOList);
        }

        bannerDTOList = customizationFilter(auditMode, auditUser, param, bannerDTOList, uid);

        return bannerDTOList;
    }

    /**
     * 推送特效 banner
     *
     * @param effectRoomBannerDTO
     */
    public void pushEffectRoomBanner(EffectRoomBannerDTO effectRoomBannerDTO) {
        Long uid = effectRoomBannerDTO.getUid();
        String roomId = effectRoomBannerDTO.getRoomId();
        String configKey = effectRoomBannerDTO.getConfigKey();
        Long gold = effectRoomBannerDTO.getGold();
        int winMultiple = effectRoomBannerDTO.getWinMultiple();
        TranslationCopyDTO translationCopyDTO = effectRoomBannerDTO.getTranslationCopyDTO();
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
        UserSimple userSimple = BeanUtil.toBean(userBaseInfo, UserSimple.class);

        String config = systemConfigService.getSysConfValueById(configKey);
        EffectBannerConfigDTO configDTO = JSONUtil.toBean(config, EffectBannerConfigDTO.class);


        JoyEffectBannerDTO effectBannerDTO = JoyEffectBannerDTO.builder()
                .userInfo(userSimple)
                .icon(configDTO.getIcon())
                .gold(gold)
                .effectUrl(configDTO.getEffectUrl())
                // 客户端没有用到
                .joyType(0)
                .nameCopywriting(translationCopyDTO)
                .winMultiple(winMultiple)
                .roomId(roomId)
                .build();

        longLinkService.pushCustomerRoomMsg(roomId, effectBannerDTO, PushEvent.regular_joy_banner_effect_event, PushToType.MESSAGE_TO_ALL);
    }

    /**
     * 推送贵族升级全服房间通知
     */
    public void pushAristocracyRoomNotice(long uid, AristocracyConfigDTO aristocracyConfigDTO) {

        AristocracyUpgradeMsgDTO dto = new AristocracyUpgradeMsgDTO();
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
        UserSimple userSimple = BeanUtil.toBean(userBaseInfo, UserSimple.class);
        dto.setUserInfo(userSimple);
        dto.setAristocracyIcon(aristocracyConfigDTO.getIconUrl2());
        String en = MessageSourceUtil.i18nByCode("aristocracy_level_up", LanguageEnum.en);
        String ar = MessageSourceUtil.i18nByCode("aristocracy_level_up", LanguageEnum.ar);
        log.info("pushAristocracyRoomNotice ar:{},en:{}", ar, en);

        dto.setEnMsg(String.format(en, aristocracyConfigDTO.getId()));
        dto.setArMsg(String.format(ar, aristocracyConfigDTO.getId()));
        dto.setUpLevelAnimationUrlBg(aristocracyConfigDTO.getUpLevelAnimationUrlBg());
        dto.setUpLevelAnimationUrlForward(aristocracyConfigDTO.getUpLevelAnimationUrlForward());

        longLinkService.pushCustomerRoomBannerList(null, dto, PushEvent.room_aristocracy_upgrade_event, PushToType.MESSAGE_TO_ALL);
    }

    /**
     * 推送贵族上线消息
     */
    public void pushAristocracyOnline(long uid, AristocracyConfigDTO aristocracyConfigDTO) {
        AristocracyUpgradeMsgDTO dto = new AristocracyUpgradeMsgDTO();
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
        UserSimple userSimple = BeanUtil.toBean(userBaseInfo, UserSimple.class);
        dto.setUserInfo(userSimple);
        dto.setAristocracyIcon(aristocracyConfigDTO.getIconUrl2());
        String en = MessageSourceUtil.i18nByCode("aristocracy_up", LanguageEnum.en);
        String ar = MessageSourceUtil.i18nByCode("aristocracy_up", LanguageEnum.ar);

        dto.setEnMsg(String.format(en, aristocracyConfigDTO.getId()));
        dto.setArMsg(String.format(ar, aristocracyConfigDTO.getId()));
        dto.setOnlineAnimationUrlBg(aristocracyConfigDTO.getOnlineAnimationUrlBg());
        dto.setOnlineAnimationUrlForward(aristocracyConfigDTO.getOnlineAnimationUrlForward());

        longLinkService.pushCustomerGlobalNewMsg(dto, null, PushEvent.global_aristocracy_online_event, PushToType.MESSAGE_TO_ALL);
    }


    /**
     * 业务自定义过滤
     *
     * @param bannerDTO
     * @return
     */
    private List<BannerDTO> customizationFilter(boolean auditMode, boolean auditUser, BannerFilterParam param, List<BannerDTO> bannerDTO, Long uid) {

        // 提审版本过滤对应配置banner
        if (auditMode || auditUser) {
            String filterBannerIdStr = systemConfigService.getSysConfValueById(SystemConfigConstant.AUDITING_FILTER_BANNER_IDS);
            if (StrUtil.isNotBlank(filterBannerIdStr)) {
                List<String> ignoreIds = StrUtil.split(filterBannerIdStr, StrUtil.C_COMMA);
                bannerDTO = bannerDTO.stream().filter(e -> !ignoreIds.contains(String.valueOf(e.getId()))).toList();
            }
        }

        // 注册时间和财富等级过滤校验
        if (CollUtil.isNotEmpty(bannerDTO)) {
            try {
                String filterBannerStr = systemConfigService.getSysConfValueById(SystemConfigConstant.CUSTOMIZATION_USER_BANNER_FILTER);
                if (StrUtil.isBlank(filterBannerStr)) {
                    return bannerDTO;
                }
                CustomizationBannerFilterDTO filterConfig = JSONUtil.toBean(filterBannerStr, CustomizationBannerFilterDTO.class);
                Integer minSignDays = filterConfig.getMinSignDays();
                Integer minWealthLevel = filterConfig.getMinWealthLevel();
                String filterBannerIdStr = filterConfig.getFilterBannerIds();

                if (StrUtil.isBlank(filterBannerIdStr)) {
                    return bannerDTO;
                }

                UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
                UserLevelBaseVO userLevel = userBaseInfo.getUserLevel();
                long intervalDays = DateUtil.betweenDay(new Date(userBaseInfo.getCreateTime()), new Date(), true);
                // 不满足条件
                if (intervalDays < minSignDays || Objects.isNull(userLevel) || userLevel.getWealthLevel() < minWealthLevel) {
                    List<String> filterBannerIds = StrUtil.split(filterBannerIdStr, StrUtil.C_COMMA);
                    bannerDTO = bannerDTO.stream().filter(e -> !filterBannerIds.contains(String.valueOf(e.getId()))).toList();
                }
            } catch (Exception e) {
                log.error("customization user banner filter fail:[{}]", e.getMessage(), e);
            }
        }
        return bannerDTO;
    }

    public List<BannerDTO> getBannerListCache(Long uid, PositionEnum position) {
        if (uid == null || position == null) {
            return Collections.emptyList();
        }
        try {
            //通过国家组获取符合的BannerDTO
            AppFunctionsGroupConfig config = countryGroupService.getFunctionsGroup(ConfigGroup.OPERATIONAL_BACKEND_COMMON, uid);
            if (config == null) {
                return Collections.emptyList();
            }
            List<BannerDTO> bannerDTOList = getBannerList(position.getNumber(), config.getGroupId());
            //在通过Banner判断玩家是否符合看得到
            List<BannerDTO> bannerDTOList2 = new ArrayList<>();
            Gson gson = new Gson();
            for (BannerDTO bannerDTO : bannerDTOList) {
                if (bannerDTO.getCommonExt() != null) {
                    BaseCommonCountryGroupBusinessReq baseCommonCountryGroupBusinessReq = gson.fromJson(bannerDTO.getCommonExt(), BaseCommonCountryGroupBusinessReq.class);
                    if (baseCommonCountryGroupBusinessReq.getPackageList().contains("1")) {

                        String wealthStr = redissonManager.hGet(UserRedisKey.user_wealth_level.getKey(), String.valueOf(uid));
                        long wealthAmount = 0;
                        if (StringUtils.isNotBlank(wealthStr)) {
                            wealthAmount = Long.parseLong(wealthStr);
                        }
                        LevelInfo currentLevel = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_WEALTH, wealthAmount);

                        //投放类型
                        //{"packageList":null,"publicType":null,"publicValue":null,"blackList":null,"whiteList":null}
                        Integer publicType = baseCommonCountryGroupBusinessReq.getPublicType();
                        if (publicType != null) {
                            boolean add = false;
                            //投放条件：1：全部用户 2：指定用户id  3：财富等级
                            if (publicType == 1) {
                                // 将 publicValue 解析成列表
                                //{"packageList":"1,2","publicType":2,"publicValue":"73239902,35841156","blackList":"24003920","whiteList":"30519622"}
                                String[] publicValueArray = baseCommonCountryGroupBusinessReq.getPublicValue().split(",");
                                if (publicValueArray.length == 2) {
                                    int id1 = Integer.parseInt(publicValueArray[0]);//新老
                                    int id2 = Integer.parseInt(publicValueArray[1]);//性别
                                    User user = userServerService.getUser(uid);
                                    if (id1 == 0 && id2 == 0) {
                                        add = true;
                                    } else {
                                        //判断老用户
                                        if (id1 == 1) {
                                            add = isType1(user);
                                        }
                                        //判断新用户
                                        if (id1 == 2) {
                                            add = !isType1(user);
                                        }
                                        //男
                                        if (id2 == 1) {
                                            add = user.getGender() == 1;
                                        }
                                        //女
                                        if (id2 == 2) {
                                            add = user.getGender() == 2;
                                        }
                                    }
                                }
                            }
                            //设置用户id
                            if (publicType == 2) {
                                // 将 publicValue 解析成列表
                                String[] publicValueArray = baseCommonCountryGroupBusinessReq.getPublicValue().split(",");
                                // 判断玩家的 UID 是否在列表中
                                boolean isUidInPublicValue = Arrays.asList(publicValueArray).contains(String.valueOf(uid));
                                if (isUidInPublicValue) {
                                    add = true;
                                }
                            }
                            //判断财富等级
                            if (publicType == 3 && currentLevel != null) {
                                int leve = Integer.parseInt(baseCommonCountryGroupBusinessReq.getPublicValue());
                                add = currentLevel.getLevel() >= leve;
                            }

                            //在判断黑白名单设置
                            if (add || publicType == 2) {
                                String[] blackListArray = baseCommonCountryGroupBusinessReq.getBlackList() != null ?
                                        baseCommonCountryGroupBusinessReq.getBlackList().split(",") : new String[]{};
                                String[] whiteListArray = baseCommonCountryGroupBusinessReq.getWhiteList() != null ?
                                        baseCommonCountryGroupBusinessReq.getWhiteList().split(",") : new String[]{};
                                // 使用 Set 去重并方便查找
                                Set<String> blackSet = new HashSet<>(Arrays.asList(blackListArray));
                                Set<String> whiteSet = new HashSet<>(Arrays.asList(whiteListArray));
                                // 遍历所有用户，黑名单优先处理
                                String uidStr = String.valueOf(uid);
                                if (blackSet.contains(uidStr)) {
                                    add = false;
                                }
                                if (!blackSet.contains(uidStr) && whiteSet.contains(uidStr)) {
                                    add = true;
                                }
                            }
                            if (add) {
                                bannerDTOList2.add(bannerDTO);
                            }
                        }
                    }
                }
            }
            return bannerDTOList2;
        } catch (
                Exception e) {
            log.error("Unexpected error in getBannerListCache for uid: {}, position: {}", uid, position.name(), e);
        }
        return Collections.emptyList();
    }

    //是不是今天注册的用户
    private boolean isType1(User user) {
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayStart = calendar.getTime();
        return user.getCreateTime().after(todayStart);
    }

    private List<BannerDTO> getBannerList(int position, int groupId) {
        try {
            List<Integer> positionList = new ArrayList<>();
            positionList.add(position);
            Date now = new Date();
            List<BannerDTO> resultList = new ArrayList<>();

            LambdaQueryWrapper<Banner> queryWrapper = new LambdaQueryWrapper<>();
            String groupIdStr = String.valueOf(groupId);

            // 使用 FIND_IN_SET 函数代替 like 查询
            queryWrapper.in(Banner::getPosition, positionList)
                    .isNotNull(Banner::getGroupIds)//isNotNull(Banner::getGroupIds)：确保 group_ids 字段非 NULL
                    .apply("FIND_IN_SET({0}, group_ids) > 0", groupIdStr)  // 精确匹配 groupId 在 groupIds 中
                    .lt(Banner::getStartTime, now)
                    .gt(Banner::getEndTime, now);

            List<Banner> list = bannerService.list(queryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                return CollUtil.newArrayList();
            }
            for (Banner banner : list) {
                resultList.add(bannerManager.buildDTO(banner));
            }
            return resultList;

        } catch (Exception e) {
            log.error("异常", e);
        }
        return Collections.emptyList();
    }

    /**
     * 构造BannerDTO
     *
     * @param banner
     * @return
     */
    private static BannerBaseDTO buildDTO(BannerDTO banner, String lang) {
        BannerBaseDTO dto = new BannerBaseDTO();
        dto.setId(banner.getId());
        dto.setPic(banner.getBanner().getOrDefault(lang, StrUtil.EMPTY));
        // 图片模糊哈希
        if (MapUtil.isNotEmpty(banner.getBlurHash())) {
            dto.setPicHash(banner.getBlurHash().getOrDefault(lang, StrUtil.EMPTY));
        }
        dto.setSeqNo(banner.getSeqNo());
        dto.setRouteType(banner.getRouteType());
        dto.setStartTime(banner.getStartTime().getTime());
        dto.setEndTime(banner.getEndTime().getTime());
        dto.setSmallIcon(banner.getSmallIcon() == null ? StrUtil.EMPTY : banner.getSmallIcon().getOrDefault(lang, StrUtil.EMPTY));
        dto.setRouteUrl(banner.getRouteUrl());
        return dto;
    }
}
