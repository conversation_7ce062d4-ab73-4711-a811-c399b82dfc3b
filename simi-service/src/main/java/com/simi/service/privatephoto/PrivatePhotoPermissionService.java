package com.simi.service.privatephoto;

import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.util.OssUrlUtil;
import com.simi.common.vo.resp.UserPhotoPermissionsResp;
import com.simi.config.PrivatePhotoConfig;
import com.simi.service.vip.UserVipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.Optional;

/**
 * @Author: Andy
 * @Date: 2024-02-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrivatePhotoPermissionService {
    private final UserVipService userVipService;

    public UserPhotoPermissionsResp getUsrEditPhotoPermissions(Long uid) {
        Optional<UserCurVipInfo> userVipCache = userVipService.getUserVipCache(uid);
        if (!userVipCache.isPresent()){
            return UserPhotoPermissionsResp.notVip();
        }

        UserCurVipInfo userCurVipInfo = userVipCache.get();
        Integer vipLevel = userCurVipInfo.getVipLevel();
        if (Objects.isNull(vipLevel)){
            return UserPhotoPermissionsResp.notVip();
        }

        Integer editGIFAvatarVIPLevel = PrivatePhotoConfig.editGIFAvatarVIPLevel;
        boolean editGIFAvatar = vipLevel >= editGIFAvatarVIPLevel ? true : false;

        Integer editGIFAlbumVIPLevel = PrivatePhotoConfig.editGIFAlbumVIPLevel;
        boolean editGIFAlbum = vipLevel >= editGIFAlbumVIPLevel ? true : false;
        return UserPhotoPermissionsResp.builder().vip(true).editGIFAvatar(editGIFAvatar).editGIFAlbum(editGIFAlbum).build();
    }

    public boolean checkEditGIFAlbumPermission(Long uid) {
        UserPhotoPermissionsResp usrEditPhotoPermissions = getUsrEditPhotoPermissions(uid);
        return usrEditPhotoPermissions.isEditGIFAlbum();
    }

    public String getFinalPrivatePhotoUrl(boolean editGIFAlbumPermission, String url) {
        if (editGIFAlbumPermission){
            return OssUrlUtil.convertUrl(url);
        }
        return OssUrlUtil.convertStaticUrl(url);
    }
}
