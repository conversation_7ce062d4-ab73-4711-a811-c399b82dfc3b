package com.simi.service.privatephoto;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.PrivatePhotoStatusEnum;
import com.simi.common.constant.resource.ResourceAuditObjTypeEnum;
import com.simi.common.constant.resource.ResourceAuditStateEnum;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.shumei.*;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.entity.ResourceAuditRecord;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.OssUrlUtil;
import com.simi.common.vo.req.privatePhote.AddPrivatePhotoReq;
import com.simi.common.vo.req.privatePhote.EditOrDeletePrivatePhotoReq;
import com.simi.common.vo.req.privatePhote.PrivatePhotoItem;
import com.simi.common.vo.resp.PrivatePhotoBuildVO;
import com.simi.common.vo.resp.PrivatePhotoVO;
import com.simi.config.PrivatePhotoConfig;
import com.simi.constant.UserConstant;
import com.simi.constant.UserRedisKey;
import com.simi.dto.push.PrivateMsgDTO;
import com.simi.entity.PrivatePhoto;
import com.simi.service.ResourceAuditRecordService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.thirdparty.ShumeiAuditImgApiComponent;
import com.simi.service.user.UserServerService;
import com.simi.util.PushMsgUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: Andy
 * @Date: 2024-02-28
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrivatePhotoHighService {

    private final RedissonClient redissonClient;
    private final RedissonDistributionLocker distributionLocker;
    private final PrivatePhotoService privatePhotoService;
    private final ResourceAuditRecordService resourceAuditRecordService;
    private final ShumeiAuditImgApiComponent shumeiAuditImgApiComponent;
    private final NotifyMessageComponent notifyMessageComponent;
    private final PrivatePhotoPermissionService privatePhotoPermissionService;
    @Resource
    @Lazy
    private UserServerService userServerService;

    private final static String AR = "ar";
    private final static String EN = "en";
    /**
     * 获取个人相册列表
     * @param uid
     * @param targetUid
     * @return
     */
    public PrivatePhotoBuildVO listPrivatePhoto(Long uid, Long targetUid) {
        List<PrivatePhoto> photoList;
        if (Objects.isNull(targetUid) || Objects.equals(0L, targetUid) || uid.equals(targetUid)){
            photoList = privatePhotoService.lambdaQuery().eq(PrivatePhoto::getUid, uid)
                    .in(PrivatePhoto::getStatus, PrivatePhotoStatusEnum.PUBLISHED.getStatus(),PrivatePhotoStatusEnum.IN_REVIEW.getStatus(),PrivatePhotoStatusEnum.REVIEW_FAILED.getStatus())
                    .orderByAsc(PrivatePhoto::getSort)
                    .list();
        } else {
            photoList = privatePhotoService.lambdaQuery().eq(PrivatePhoto::getUid, targetUid)
                    .eq(PrivatePhoto::getStatus, PrivatePhotoStatusEnum.PUBLISHED.getStatus())
                    .orderByAsc(PrivatePhoto::getSort)
                    .list();
        }

        if (CollectionUtils.isEmpty(photoList)){
            return PrivatePhotoBuildVO.builder().privatePhotoVOS(CollUtil.newArrayList()).photoMaxAmount(PrivatePhotoConfig.limitPhotoAmount).build();
        }
        List<PrivatePhotoVO> privatePhotoVOS = BeanUtil.copyToList(photoList, PrivatePhotoVO.class);
        boolean editGIFAlbumPermission = privatePhotoPermissionService.checkEditGIFAlbumPermission(uid);
        privatePhotoVOS.forEach(e->{
            Integer status = e.getStatus();
            if (PrivatePhotoStatusEnum.REVIEW_FAILED.getStatus().equals(status)) {
                //审核不通过，使用默认图片
                e.setPhotoUrl(PrivatePhotoConfig.defaultPhotoUrl);
            }else if (PrivatePhotoStatusEnum.PUBLISHED.getStatus().equals(status)) {
                //审核通过，需要校验vip权限是否可以展示动图
                e.setPhotoUrl(OssUrlUtil.jointUrl(e.getPhotoUrl()));
            }else if (PrivatePhotoStatusEnum.IN_REVIEW.getStatus().equals(status)) {
                e.setPhotoUrl(OssUrlUtil.jointUrl(e.getPhotoUrl()));
            }
        });
        PrivatePhotoBuildVO privatePhotoBuildVO =  PrivatePhotoBuildVO.builder()
                .privatePhotoVOS(privatePhotoVOS)
                .photoMaxAmount(PrivatePhotoConfig.limitPhotoAmount)
                .build();
        log.info("uid:{},targetUid:{},listPrivatePhoto result:{}", uid,targetUid,JSON.toJSONString(privatePhotoBuildVO));
        return privatePhotoBuildVO;
    }


    /**
     * 添加相片
     * @param uid
     * @param addPrivatePhotoReq
     */
    @Transactional(rollbackFor = Exception.class)
    public void addPrivatePhoto(long uid, AddPrivatePhotoReq addPrivatePhotoReq) {
        List<PrivatePhotoItem> privatePhotoList = addPrivatePhotoReq.getPrivatePhotoList();
        log.info("PrivatePhotoHighService addPrivatePhoto, uid:{}, privatePhotoList:{}", uid, JSONUtil.toJsonStr(privatePhotoList));
        if (CollUtil.isEmpty(privatePhotoList)) {
            return;
        }

        checkMaxSize(privatePhotoList.size());
        try (Locker locker = distributionLocker.lock(UserRedisKey.user_private_photo_lock.getKey(StrUtil.format("{{}}", uid)))){
            if (Objects.isNull(locker)){
                log.error("fail to get user_private_photo_lock, uid:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            List<Integer> statusList = Arrays.asList(PrivatePhotoStatusEnum.PUBLISHED.getStatus(), PrivatePhotoStatusEnum.IN_REVIEW.getStatus(),PrivatePhotoStatusEnum.REVIEW_FAILED.getStatus());
            List<PrivatePhoto> existPrivatePhotoList = privatePhotoService.getPhotoByStatusAndUid(uid, statusList);
            List<Long> needRemovePrivatePhotoIds = getNeedRemovePrivatePhotoIds(existPrivatePhotoList, privatePhotoList);
            privatePhotoService.removeBatchByIds(needRemovePrivatePhotoIds);
            log.info("uid:{},addPrivatePhoto remove ids:{}",uid,needRemovePrivatePhotoIds);

            List<PrivatePhoto> insertPrivatePhotoList = convertPrivatePhotoList(privatePhotoList, uid,existPrivatePhotoList, PrivatePhotoStatusEnum.IN_REVIEW.getStatus());
            //以前没审核通过的需要重新审核
            List<PrivatePhoto> needAuditPhotoList = insertPrivatePhotoList.stream()
                    .filter(e->Objects.isNull(e.getId()) || !PrivatePhotoStatusEnum.PUBLISHED.getStatus().equals(e.getStatus()))
                    .collect(Collectors.toList());
            //更新
            privatePhotoService.saveOrUpdateBatch(insertPrivatePhotoList);
            log.info("uid:{},addPrivatePhoto insert photo list:{}",uid,JSON.toJSONString(insertPrivatePhotoList));

            //图片异步审核
            Map<String, List<String>> objIdUrlMap = needAuditPhotoList.stream()
                    .collect(Collectors.toMap(x -> Convert.toStr(x.getId()),
                            y -> Lists.newArrayList(OssUrlUtil.jointUrl(y.getPhotoUrl()))));
            List<ResourceAuditRecord> recordList = Lists.newArrayList();
            Date now = new Date();
            for (String objId : objIdUrlMap.keySet()) {
                final List<String> urlList = objIdUrlMap.get(objId);
                for (String url : urlList) {
                    ResourceAuditRecord record = new ResourceAuditRecord();
                    record.setUid(uid);
                    record.setObjId(objId);
                    record.setObjType(ResourceAuditObjTypeEnum.RESOURCE_AUDIT_TYPE_PRIVATE_PHOTO.getType());
                    record.setUrl(url);
                    record.setState(ResourceAuditStateEnum.RESOURCE_AUDIT_STATE_AUDIT.getType());
                    record.setCreateTime(now);
                    record.setUpdateTime(now);
                    recordList.add(record);
                }
            }
            log.info("addPrivatePhoto needAuditPhotoList:{}", JSONUtil.toJsonStr(recordList));
            if (CollectionUtils.isEmpty(recordList)) {
                return;
            }

            resourceAuditRecordService.saveBatch(recordList);
            //shumeiAuditImgApiComponent.batchAsyncAuditShowImg(uid, recordList);
        } catch (ApiException ae){
            throw ae;
        } catch (Exception e){
            log.error("PrivatePhotoHighService addPrivatePhoto, uid:{}, addPrivatePhotoReq:{}, msg:{}", uid, JSONUtil.toJsonStr(addPrivatePhotoReq), ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    private void checkMaxSize(int size) {
        Integer limitPhoto = PrivatePhotoConfig.limitPhotoAmount;
        if (size > limitPhoto){
            throw new ApiException(CodeEnum.USER_PRIVATE_PHOTO_MAX);
        }
    }

    @NotNull
    private static List<PrivatePhoto> getPrivatePhotos(Long uid, List<String> photoUrlsList, Long photoCount) {
        if (photoCount >= UserConstant.PRIVATE_PHOTO_MAX){
            throw new ApiException(CodeEnum.USER_PRIVATE_PHOTO_MAX);
        }

        Date now = new Date();
        List<PrivatePhoto> privatePhotoList = Lists.newArrayListWithCapacity(photoUrlsList.size());
        for (String url : photoUrlsList) {
            PrivatePhoto privatePhoto = new PrivatePhoto();
            privatePhoto.setUid(uid);
            privatePhoto.setPhotoUrl(url);
            privatePhoto.setStatus(PrivatePhotoStatusEnum.IN_REVIEW.getStatus());
            privatePhoto.setCreateTime(now);
            privatePhoto.setUpdateTime(now);
            privatePhotoList.add(privatePhoto);
        }
        return privatePhotoList;
    }

    /**
     * 删除相片
     * @param uid
     * @param photoIdsList
     */
    public void deletePrivatePhoto(long uid, List<Long> photoIdsList) {
        log.info("PrivatePhotoHighService deletePrivatePhoto, uid:{}, photoIdsList:{}", uid, JSONUtil.toJsonStr(photoIdsList));
        if (CollUtil.isEmpty(photoIdsList)) {
            return;
        }
        try (Locker locker = distributionLocker.lock(UserRedisKey.user_private_photo_lock.getKey(StrUtil.format("{{}}", uid)))){
            if (Objects.isNull(locker)){
                log.error("fail to get user_private_photo_lock, uid:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            privatePhotoService.lambdaUpdate().eq(PrivatePhoto::getUid, uid).in(PrivatePhoto::getId, photoIdsList).remove();

            updatePhotoTop(Lists.newArrayList(uid));
        } catch (ApiException ae){
            throw ae;
        } catch (Exception e){
            log.error("PrivatePhotoHighService deletePrivatePhoto, uid:{}, photoIdsList:{}, msg:{}", uid, JSONUtil.toJsonStr(photoIdsList), ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    /**
     * 处理相册图片审核结果
     * @param auditIdsList
     */
    public void handleAuditResult(List<String> auditIdsList) {
        final List<ResourceAuditRecord> auditRecordList = resourceAuditRecordService.listByIds(auditIdsList);
        final Map<Long, ResourceAuditRecord> objIdMap = auditRecordList.stream().collect(Collectors.toMap(x -> Convert.toLong(x.getObjId()), Function.identity()));
        final List<PrivatePhoto> privatePhotoList = privatePhotoService.listByIds(objIdMap.keySet());
        Date now = new Date();
        Set<Long> uidSet = Sets.newHashSet();
        for (PrivatePhoto privatePhoto : privatePhotoList) {
            final ResourceAuditRecord auditRecord = objIdMap.get(privatePhoto.getId());
            ResourceAuditStateEnum statusEnum = ResourceAuditStateEnum.getByType(auditRecord.getState());
            if (Objects.nonNull(statusEnum)) {
                privatePhoto.setStatus(statusEnum.getType());
            }
            privatePhoto.setUpdateTime(now);

            uidSet.add(privatePhoto.getUid());
        }

        privatePhotoService.updateBatchById(privatePhotoList);

        updatePhotoTop(Lists.newArrayList(uidSet));
    }

    public ShumeiAuditImgResp auditShowImg(long uid, String imgUrl){
        return shumeiAuditImgApiComponent.auditShowImg(uid, imgUrl);
    }


    private void updatePhotoTop(List<Long> uidList){
        log.info("PrivatePhotoHighService updatePhotoTop, uidList:{}", JSONUtil.toJsonStr(uidList));
        for (Long uid : uidList) {
            try (Locker locker = distributionLocker.lock(UserRedisKey.user_private_photo_lock.getKey(StrUtil.format("{{}}", uid)))){
                if (Objects.isNull(locker)){
                    log.error("fail to get user_private_photo_lock, uid:{}", uid);
                    throw new ApiException(CodeEnum.SERVER_BUSY);
                }

                List<Integer> statusList = Arrays.asList(PrivatePhotoStatusEnum.PUBLISHED.getStatus(), PrivatePhotoStatusEnum.REVIEW_FAILED.getStatus());
                final List<PrivatePhoto> photoList = privatePhotoService.getPhotoByStatusAndUid(uid,statusList);
                final RList<String> topPhotoRList = redissonClient.getList(UserRedisKey.user_private_photo_top.getKey(StrUtil.format("{{}}", uid)));
                topPhotoRList.delete();
                if (CollectionUtils.isEmpty(photoList)){
                    continue;
                }

                List<String> photoJSONList = photoList.stream().map(JSONUtil::toJsonStr).toList();
                topPhotoRList.addAll(photoJSONList);
            } catch (Exception e){
                log.error("PrivatePhotoHighService updatePhotoTop error, uid:{}, msg:{}", uid, ExceptionUtil.formatEx(e));
            }
        }
    }

    /**
     * 回调处理, 审核记录状态
     *
     * @param resultList
     * @param uid
     */
    public void handleAuditCallbackResult(List<AuditResult> resultList, long uid) {
        if (CollectionUtils.isEmpty(resultList)){
            return;
        }
        final Map<String, AuditResult> resultMap = resultList.stream().collect(Collectors.toMap(AuditResult::getId, Function.identity()));
        final List<ResourceAuditRecord> recordList = resourceAuditRecordService.listByIds(resultMap.keySet());
        Map<Integer, List<String>> objTypeIdMap = Maps.newHashMap();
        Date now = new Date();
        for (ResourceAuditRecord record : recordList) {
            final AuditResult result = resultMap.get(record.getId());
            int state = result.isPass() ? ResourceAuditStateEnum.RESOURCE_AUDIT_STATE_PASS.getType() : ResourceAuditStateEnum.RESOURCE_AUDIT_STATE_NOT_PASS.getType();
            record.setState(state);
            record.setExtend(result.getExtend());
            record.setUpdateTime(now);

            objTypeIdMap.computeIfAbsent(record.getObjType(), k -> Lists.newArrayList()).add(record.getId());
        }
        resourceAuditRecordService.updateBatchById(recordList);

        objTypeIdMap.forEach((key, value) -> {
            ResourceAuditObjTypeEnum objTypeEnum = ResourceAuditObjTypeEnum.getByType(key);
            if (Objects.nonNull(objTypeEnum)) {
                // 处理图片状态
                this.handleAuditResult(value);
            }
        });

        //发送系统推送
        pushRejectError(resultList,uid);
    }

    private void pushRejectError(List<AuditResult> resultList, long uid) {
        List<AuditResult> rejectList = resultList.stream().filter(e -> !e.isPass()).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rejectList)){
            return;
        }

        String rejectPrivatePhotoLinkUrl = PrivatePhotoConfig.rejectPrivatePhotoLinkUrl;
        String rejectTitleAr = PrivatePhotoConfig.rejectTitleAr;
        String rejectTitleEn = PrivatePhotoConfig.rejectTitleEn;
        String rejectTextAr = PrivatePhotoConfig.rejectTextAr;
        String rejectTextEn  = PrivatePhotoConfig.rejectTextEn;

        Map<String, String> titleMap = new HashMap<>();
        titleMap.put(AR,rejectTitleAr);
        titleMap.put(EN,rejectTitleEn);

        Map<String, String> textMap = new HashMap<>();
        textMap.put(AR,rejectTextAr);
        textMap.put(EN,rejectTextEn);
        PrivateMsgDTO privateMsgDTO = PrivateMsgDTO.builder()
                .linkUrl(rejectPrivatePhotoLinkUrl)
                .titleMap(titleMap)
                .textMap(textMap)
                .build();

        String privateMsg = JSON.toJSONString(privateMsgDTO);
        CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                IMMsgType.PrivatePhoto,
                new Date().getTime(), privateMsg);

        OfflinePushInfo offlinePushInfo = null;
        try {
            LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
            String offlineTitle = titleMap.get(languageEnum.name());
            String offlineText = textMap.get(languageEnum.name());
            offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                    offlineTitle,
                    offlineText,
                    null,
                    ClientRouteUtil.toUserEdit());
        } catch (Exception e) {
            // ignore
            log.info("pushRejectError build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
        }

        notifyMessageComponent.publishSystemDefineMessage(imMessage, uid + "", offlinePushInfo);
        log.info("pushRejectError send message: [{}]  targetUid: [{}]", privateMsg, uid);
    }

    public void handleBatchSyncAuditResult(ShumeiBatchImgResp shumeiBatchImgResp, long uid){
        List<ShumeiRequestIds> imgList = shumeiBatchImgResp.getImgs();
        List<AuditResult> resultList = Lists.newArrayListWithCapacity(imgList.size());
        for (ShumeiRequestIds img : imgList) {
            if (img.getFinalResult() == 1){
                AuditResult result = new AuditResult();
                result.setId(img.getBtId());
                boolean isPass = !StrUtil.equalsIgnoreCase(ShumeiConstant.RiskLevel.REJECT, img.getRiskLevel());
                result.setPass(isPass);
                result.setExtend(img.getRiskDescription());
                resultList.add(result);
            }
        }

        handleAuditCallbackResult(resultList,uid);
    }

    @Transactional(rollbackFor = Exception.class)
    public void editOrDeletePrivatePhoto(Long uid, EditOrDeletePrivatePhotoReq editOrDeletePrivatePhotoReq,Integer status) {
        List<Integer> statusList = Arrays.asList(PrivatePhotoStatusEnum.PUBLISHED.getStatus(), PrivatePhotoStatusEnum.IN_REVIEW.getStatus(),PrivatePhotoStatusEnum.REVIEW_FAILED.getStatus());
        List<PrivatePhoto> existPrivatePhotoList = privatePhotoService.getPhotoByStatusAndUid(uid, statusList);
        List<PrivatePhotoItem> privatePhotoList = editOrDeletePrivatePhotoReq.getPrivatePhotoList();
        int size = CollectionUtils.isEmpty(privatePhotoList) ? 0 : privatePhotoList.size();
        checkMaxSize(size);

        try (Locker locker = distributionLocker.lock(UserRedisKey.user_private_photo_lock.getKey(StrUtil.format("{{}}", uid)))){
            if (Objects.isNull(locker)){
                log.error("fail to get user_private_photo_lock, uid:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }

            //需要删除的相册
            List<Long> needRemovePrivatePhotoIds = getNeedRemovePrivatePhotoIds(existPrivatePhotoList, privatePhotoList);
            if (CollectionUtils.isNotEmpty(needRemovePrivatePhotoIds)){
                privatePhotoService.removeBatchByIds(needRemovePrivatePhotoIds);
            }
            log.info("uid:{},editOrDeletePrivatePhoto remove photo ids:{}",uid,needRemovePrivatePhotoIds);


            List<PrivatePhoto> changePrivatePhotoList = convertPrivatePhotoList(privatePhotoList, uid, existPrivatePhotoList,status);
            if (CollectionUtils.isNotEmpty(changePrivatePhotoList)){
                privatePhotoService.saveOrUpdateBatch(changePrivatePhotoList);
            }
            log.info("uid:{},editOrDeletePrivatePhoto edit photo list:{}",uid, JSON.toJSONString(changePrivatePhotoList));

            updatePhotoTop(Lists.newArrayList(uid));
        }catch (Exception exception){
            log.error("uid:{},editOrDeletePrivatePhotoReq:{} editOrDeletePrivatePhoto error:{}",
                    uid,JSON.toJSONString(editOrDeletePrivatePhotoReq),ExceptionUtil.formatEx(exception));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    private List<Long> getNeedRemovePrivatePhotoIds(List<PrivatePhoto> existPrivatePhotoList, List<PrivatePhotoItem> privatePhotoList) {
        if (CollectionUtils.isEmpty(privatePhotoList)){
            return existPrivatePhotoList.stream().map(PrivatePhoto::getId).collect(Collectors.toList());
        }

        List<Long> updateIds = privatePhotoList.stream().map(PrivatePhotoItem::getId).collect(Collectors.toList());
        return existPrivatePhotoList.stream()
                .filter(e->!updateIds.contains(e.getId()))
                .map(PrivatePhoto::getId)
                .collect(Collectors.toList());
    }

    private List<PrivatePhoto> convertPrivatePhotoList(List<PrivatePhotoItem> privatePhotoList, Long uid, List<PrivatePhoto> existPrivatePhotoList, Integer status){
        Map<Long, Integer> statusMap = existPrivatePhotoList.stream().collect(Collectors.toMap(PrivatePhoto::getId, PrivatePhoto::getStatus));
        Date now = new Date();
        List<PrivatePhoto> changePhotoList = Lists.newArrayListWithCapacity(privatePhotoList.size());
        for (PrivatePhotoItem privatePhotoItem : privatePhotoList) {
            PrivatePhoto privatePhoto = new PrivatePhoto();
            privatePhoto.setUid(uid);
            privatePhoto.setPhotoUrl(privatePhotoItem.getPhotoUrl());
            Long id = privatePhotoItem.getId();
            //已经存在的情况
            if (Objects.nonNull(id)){
                privatePhoto.setId(id);
                privatePhoto.setStatus(statusMap.get(id));
            }else {
                privatePhoto.setStatus(status);
                privatePhoto.setCreateTime(now);
            }
            privatePhoto.setUpdateTime(now);
            privatePhoto.setSort(privatePhotoItem.getSort());
            changePhotoList.add(privatePhoto);
        }
        return changePhotoList;
    }

}
