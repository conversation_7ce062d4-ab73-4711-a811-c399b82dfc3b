package com.simi.service.privatephoto;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.simi.common.constant.PrivatePhotoStatusEnum;
import com.simi.common.vo.resp.PrivatePhotoVO;
import com.simi.config.PrivatePhotoConfig;
import com.simi.constant.UserRedisKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RList;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Andy
 * @Date: 2024-02-29
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PrivatePhotoLowService {

    private final RedissonClient redissonClient;
    private final PrivatePhotoPermissionService privatePhotoPermissionService;

    public List<PrivatePhotoVO> listUserTopPhoto(long uid){
        RList<String> topPhotoRList = redissonClient.getList(UserRedisKey.user_private_photo_top.getKey(StrUtil.format("{{}}", uid)));
        List<String> privatePhotoList = topPhotoRList.readAll();
        if (CollUtil.isEmpty(privatePhotoList)){
            return Collections.emptyList();
        }

        boolean editGIFAlbumPermission = privatePhotoPermissionService.checkEditGIFAlbumPermission(uid);
        List<PrivatePhotoVO> result = privatePhotoList.stream().map(e -> {
                    PrivatePhotoVO bean = JSONUtil.toBean(e, PrivatePhotoVO.class);
                    if (PrivatePhotoStatusEnum.REVIEW_FAILED.getStatus().equals(bean.getStatus())){
                        bean.setPhotoUrl(PrivatePhotoConfig.defaultPhotoUrl);
                    }else {
                        bean.setPhotoUrl(privatePhotoPermissionService.getFinalPrivatePhotoUrl(editGIFAlbumPermission,bean.getPhotoUrl()));
                    }
                    return bean;
                })
                .collect(Collectors.toList());
        log.info("uid:{},listUserTopPhoto success,result:{}",uid, JSON.toJSONString(result));
        return result;
    }
}
