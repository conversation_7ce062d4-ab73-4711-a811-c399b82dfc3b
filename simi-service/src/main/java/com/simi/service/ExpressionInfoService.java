package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.ConfigGroup;
import com.simi.common.constant.AppVersionEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.aristocracy.AristocracyConfigDTO;
import com.simi.common.dto.aristocracy.UserCurAristocracyInfo;
import com.simi.common.dto.expression.ExpressionInfoAdminDTO;
import com.simi.common.dto.expression.ExpressionInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ExceptionUtil;
import com.simi.entity.ExpressionInfo;
import com.simi.entity.group.AppFunctionsGroupConfig;
import com.simi.mapper.ExpressionInfoMapper;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.group.UserFunctionCountryGroupService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【gift_info(礼物元数据表)】的数据库操作Service实现
 * @createDate 2024-01-04 10:22:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpressionInfoService extends ServiceImpl<ExpressionInfoMapper, ExpressionInfo> {

    private final UserFunctionCountryGroupService countryGroupService;
    private final AuditCheckService auditCheckService;
    private final UserAristocracyRecordsService userAristocracyRecordsService;
    private final AristocracyConfigService aristocracyConfigService;
    private final AppVersionCheckService appVersionCheckService;

    public void saveOrUpdate(ExpressionInfoAdminDTO dto) {
        if (dto != null) {
            ExpressionInfo info = new ExpressionInfo();
            BeanUtil.copyProperties(dto, info);
            info.setUpdateTime(new Date());
            saveOrUpdate(info);
        }
    }

    public ListWithTotal<ExpressionInfoAdminDTO> adminDTOS(Integer page, Integer size) {
        ListWithTotal<ExpressionInfoAdminDTO> dtoListWithTotal = new ListWithTotal<>();
        List<ExpressionInfoAdminDTO> dtos = new ArrayList<>();
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<ExpressionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExpressionInfo::getStatus, 1);
        wrapper.orderByDesc(ExpressionInfo::getCreateTime);
        List<ExpressionInfo> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return dtoListWithTotal;
        }
        for (ExpressionInfo expressionInfo : list) {
            ExpressionInfoAdminDTO dto = new ExpressionInfoAdminDTO();
            dto.setId(expressionInfo.getId());
            dto.setType(expressionInfo.getType());
            dto.setClassify(expressionInfo.getClassify());
            dto.setEnName(expressionInfo.getEnName());
            dto.setArName(expressionInfo.getArName());
            dto.setStaticPic(expressionInfo.getStaticPic());
            dto.setDynamicPic(expressionInfo.getDynamicPic());
            dto.setEndPic(expressionInfo.getEndPic());
            dto.setAdminId(expressionInfo.getAdminId());
            dto.setSort(expressionInfo.getSort());
            dto.setCreateTime(expressionInfo.getCreateTime());
            dto.setUpdateTime(expressionInfo.getUpdateTime());
            dto.setTagEn(expressionInfo.getTagEn());
            dto.setTagAr(expressionInfo.getTagAr());
            dto.setDirection(expressionInfo.getDirection());
            dto.setAristocracyLevel(expressionInfo.getAristocracyLevel());
            dto.setGroupIds(expressionInfo.getGroupIds());
            dtos.add(dto);
        }
        PageInfo<ExpressionInfo> pageInfo = new PageInfo<>(list);
        dtoListWithTotal.setList(dtos);
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        return dtoListWithTotal;
    }

    public List<ExpressionInfoDTO> queryList(String lang, XAuthToken param, Long uid, String appVersion) {
        AppFunctionsGroupConfig config = countryGroupService.getFunctionsGroup(ConfigGroup.OPERATIONAL_BACKEND_COMMON, uid);
        if (config == null) {
            return Collections.emptyList();
        }
        List<ExpressionInfoDTO> dtos = new ArrayList<>();
        LambdaQueryWrapper<ExpressionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExpressionInfo::getStatus, 1);
        wrapper.gt(ExpressionInfo::getSort, -1);
        wrapper.orderByDesc(ExpressionInfo::getSort);
        //通过国家组获取
        wrapper.apply("FIND_IN_SET({0}, group_ids) > 0", config.getGroupId());  // 精确匹配 groupId 在 groupIds 中

        List<ExpressionInfo> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return dtos;
        }
        int curAristocracyLevel = 0;
        Map<Integer, AristocracyConfigDTO> aristocracyConfigDTOMap = new HashMap<>();
        try {
            Optional<UserCurAristocracyInfo> userAristocracyRecordsCache = userAristocracyRecordsService.getUserAristocracyRecordsCache(uid);
            if (userAristocracyRecordsCache.isPresent()) {
                curAristocracyLevel = userAristocracyRecordsCache.get().getCurAristocracy();
            }
            aristocracyConfigDTOMap
                    = aristocracyConfigService.getAristocracyConfig().stream()
                    .collect(Collectors.toMap(AristocracyConfigDTO::getId, k -> k));
        } catch (Exception e) {
            log.error("ExpressionInfoDTO AristocracyConfig error.{}", ExceptionUtil.formatEx(e), e);
        }
        boolean version = appVersionCheckService.checkVersion(AppVersionEnum.ARISTOCRACY, appVersion);
        if (version) {
            log.info("aristocracy 用户版本较低，不显示贵族表情包。uid：{}", uid);
        }

        for (ExpressionInfo expressionInfo : list) {
            try {
                ExpressionInfoDTO dto = new ExpressionInfoDTO();
                dto.setId(expressionInfo.getId());
                dto.setType(expressionInfo.getType());
                dto.setClassify(expressionInfo.getClassify());
                if (Objects.equals(lang, LanguageEnum.ar.name())) {
                    dto.setArName(expressionInfo.getArName());
                    dto.setTagAr(expressionInfo.getTagAr());
                } else {
                    dto.setEnName(expressionInfo.getEnName());
                    dto.setTagEn(expressionInfo.getTagEn());
                }
                dto.setStaticPic(expressionInfo.getStaticPic());
                dto.setDynamicPic(expressionInfo.getDynamicPic());
                dto.setEndPic(expressionInfo.getEndPic());
                dto.setSort(expressionInfo.getSort());
                dto.setDirection(expressionInfo.getDirection());
                Integer aristocracyLevel = expressionInfo.getAristocracyLevel();
                if (Objects.nonNull(aristocracyLevel) && aristocracyLevel > 0) {
                    AristocracyConfigDTO aristocracyConfigDTO = aristocracyConfigDTOMap.get(aristocracyLevel);
                    ExpressionInfoDTO.AristocracyInfo aristocracyInfo = new ExpressionInfoDTO.AristocracyInfo();
                    Map<String, String> nameMap = aristocracyInfo.getNameMap();
                    nameMap.put("ar", aristocracyConfigDTO.getArName());
                    nameMap.put("en", aristocracyConfigDTO.getEnName());
                    aristocracyInfo.setIconUrl(aristocracyConfigDTO.getIconUrl());
                    aristocracyInfo.setAristocracyLevel(aristocracyLevel);
                    aristocracyInfo.setUserCurAristocracyLevel(curAristocracyLevel);
                    dto.setAristocracyInfo(aristocracyInfo);
                    // 如果版本低，不显示贵族表情
                    if (version) {
                        continue;
                    }
                }
                dtos.add(dto);
            } catch (Exception e) {
                log.error("ExpressionInfoService expression error.{}", ExceptionUtil.formatEx(e), e);
            }
        }
        return dtos;
    }


    /**
     * 按组获取表情
     */
    public List<ExpressionInfoDTO> queryListByClassify(Integer classify) {
        LambdaQueryWrapper<ExpressionInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ExpressionInfo::getStatus, 1);
        wrapper.eq(ExpressionInfo::getClassify, classify);
        wrapper.orderByDesc(ExpressionInfo::getSort);
        List<ExpressionInfo> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<ExpressionInfoDTO> dtos = new ArrayList<>();
        for (ExpressionInfo expressionInfo : list) {
            ExpressionInfoDTO dto = new ExpressionInfoDTO();
            BeanUtil.copyProperties(expressionInfo, dto);
            dtos.add(dto);
        }
        return dtos;
    }

    /**
     * 批量更新
     */
    public void updateBatch(List<ExpressionInfoDTO> list) {
        List<ExpressionInfo> expressionInfos = BeanUtil.copyToList(list, ExpressionInfo.class);
        updateBatchById(expressionInfos);
    }
}




