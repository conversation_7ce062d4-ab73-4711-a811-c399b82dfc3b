package com.simi.service.gift;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.vo.host.ContributionListResp;
import com.simi.entity.gift.GiftSendRecord;
import com.simi.entity.user.User;
import com.simi.mapper.UserMapper;
import com.simi.mapper.gift.GiftSendRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class GiftSendRecordService extends ServiceImpl<GiftSendRecordMapper, GiftSendRecord> {

    private final GiftSendRecordMapper giftSendRecordMapper; // 修正为 Mapper
    private final UserMapper userMapper;


    public List<ContributionListResp> contributionList(Long targetUid, Date startTime, Date endTime, Integer page) {
        int pageSize = 20;
        int offset = (page - 1) * pageSize;

        // 1. 查询主播在时间段内收到的送礼记录（带 diamond_num + uid）
        List<GiftSendRecord> records = giftSendRecordMapper.selectList(
                Wrappers.<GiftSendRecord>query()
                        .eq("target_uid", targetUid)
                        .ge("create_time", startTime)
                        .lt("create_time", endTime)
                        .orderByDesc("create_time")
                        .last("LIMIT " + offset + ", " + pageSize)
        );


        if (records.isEmpty()) return Collections.emptyList();

        // 2. 提取 UID 列表
        List<Long> aids = records.stream()
                .map(GiftSendRecord::getUid)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 3. 查询用户信息
        Map<Long, User> userMap = userMapper.selectBatchIds(aids).stream()
                .collect(Collectors.toMap(User::getUid, Function.identity()));

        // 4. 组装结果
        return records.stream().map(record -> {
            User user = userMap.get(record.getUid());
            if (user == null) return null;

            ContributionListResp resp = new ContributionListResp();
            resp.setUid(user.getUid());
            resp.setNick(user.getNick());
            resp.setGender(user.getGender());
            resp.setAvatar(user.getAvatar());
            resp.setDiamond(record.getDiamondNum()); // 每条送礼的 diamond 数量
            return resp;
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
