package com.simi.service.gift;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.simi.constant.RoomRedisKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.concurrent.TimeUnit;


/**
 * @Author: Andy
 * @Date: 2024-01-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomGiftService {

    private final RedissonClient redissonClient;

    public void addRoomSendGiftNum(String roomId, Integer giftNum, Integer comboTimes) {
        Integer totalGiftNum = giftNum * comboTimes;
        final String format = DateUtil.format(new Date(), DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        final RMap<String, Integer> giftNumRMap = redissonClient.getMap(RoomRedisKey.room_send_gift_num.getKey(StrUtil.format("{{}}", roomId)));
        giftNumRMap.addAndGet(format, totalGiftNum);
        giftNumRMap.expire(6, TimeUnit.MINUTES);
    }
}
