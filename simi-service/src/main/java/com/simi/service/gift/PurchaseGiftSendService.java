package com.simi.service.gift;


import cn.hutool.core.util.StrUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.SendTypeEnum;
import com.simi.common.constant.gift.GiftSourceEnum;
import com.simi.common.dto.DateWithMessageDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.entity.room.AudienceReq;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.RevenueRedisKey;
import com.simi.dto.Audience;
import com.simi.entity.GiftInfo;
import com.simi.service.purse.BillService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomHighService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 来源为购买的礼物发送服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class PurchaseGiftSendService extends AbstractGiftSendService {

    private final RoomHighService roomHighService;
    private final PurseManageService purseManageService;
    private final RedissonDistributionLocker distributionLocker;
    private final BillService billService;

    @Override
    public DateWithMessageDTO<String> send(long uid, List<Long> targetUids, String roomId, GiftInfo gift, int giftNum, SendTypeEnum sendType, String comboId, int comboTimes, int start,int giftSource) {
        log.info("purchase gift send param==>>> uid:{},targetUids:{},roomId:{},giftId:{},giftNum:{},sendType:{}", uid, targetUids, roomId, gift.getId(), giftNum, sendType);
        try (Locker lock = distributionLocker.lock(sendGiftLockKey(uid))) {
            if (Objects.isNull(lock)) {
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            PurseDTO purse = purseManageService.getPurse(uid);
            if (SendTypeEnum.ROOM_ALL == sendType) {
                log.info("user send gift to whole room, get the audiences.");
                // 计算当前房间人数
                AudienceReq audienceReq = new AudienceReq(roomId,uid);
                List<Audience> audiences = roomHighService.audienceList(audienceReq);
                if (CollectionUtils.isEmpty(audiences)) {
                    log.warn("no audience in room current.");
                    throw new ApiException(CodeEnum.NO_AUDIENCE_IN_ROOM);
                }
                // 最大可送对象数量：金币/(礼物数量* 礼物价格* 连击次数)
                long count = purse.getCoin() / ((long) giftNum * gift.getPrice() * comboTimes);
                log.info("Limit target size:{}", count);
                if (count < 1) {
                    throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
                }
                long audienceNum = count / comboTimes;
                log.info("The max amount of audience is:{}", audienceNum);
                if (audienceNum < audiences.size()) {
                    List<Audience> targetAudiences = audiences.subList(0, (int)audienceNum);
                    targetUids = targetAudiences.stream().map(Audience::getUid).collect(Collectors.toList());
                } else {
                    targetUids = audiences.stream().map(Audience::getUid).collect(Collectors.toList());
                }
            }
            log.info("target uids:{}", targetUids);
            // 扣钱包
            // 是否连击
            comboId = StringUtils.isBlank(comboId) ? CommonUtil.genId() : comboId;
            long deductAmount = gift.getPrice() * giftNum * targetUids.size() * comboTimes;
            PurseDTO purseDTO = purseManageService.deductCoin(uid, deductAmount, PurseRoleTypeEnum.USER.getType());
            BillEnum billEnum = switch (sendType) {
                case PRIVATE_CHAT -> BillEnum.SEND_IN_CHAT;
                case SEND_TYPE_POST -> BillEnum.SEND_IN_POST;
                case ROOM_MIC_ALL -> BillEnum.SEND_IN_ROOM_ROOM_MIC_ALL;
                case ROOM_ALL -> BillEnum.SEND_IN_ROOM_ROOM_ALL;
                default -> BillEnum.SEND_IN_ROOM;
            };
            Long attachStatistics = (long) targetUids.size();
            billService.createBill(uid, comboId, deductAmount, purseDTO, billEnum,
                    new Date(), Collections.emptyMap(), StrUtil.EMPTY, attachStatistics);
            sendScreenMsg(uid, targetUids, roomId, gift, giftNum,sendType, comboId,comboTimes,giftSource);
            return super.coreSend(uid, targetUids, roomId, gift, giftNum, (int)deductAmount, sendType, GiftSourceEnum.PURCHASE,
                    comboId, comboTimes, start);
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("send gift error:{}", ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }



    @Override
    public Integer getGiftSource() {
        return GiftSourceEnum.PURCHASE.getType();
    }

    /**
     * 送礼锁键
     *
     * @param uid
     * @return
     */
    private static String sendGiftLockKey(Long uid) {
        return RevenueRedisKey.send_gift_lock.getKey(uid);
    }

}
