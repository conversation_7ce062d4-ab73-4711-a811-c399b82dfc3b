package com.simi.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.simi.common.constant.vip.VIPConstant;
import com.simi.common.dto.tracking.*;
import com.simi.common.util.EventTrackingUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.EventTrackingConstant;
import com.simi.constant.RoomRedisKey;
import com.simi.constant.UserRedisKey;
import com.simi.entity.purse.RechargeRecord;
import com.simi.entity.room.Room;
import com.simi.message.RechargeSuccessMessage;
import com.simi.service.kafka.KafkaService;
import com.simi.service.room.RoomService;
import com.simi.service.vip.UserVipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RBitSet;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class EventTrackingService {

    private final RedissonClient redissonClient;
    private final RoomService roomService;
    private final KafkaService kafkaService;
    private final TaskExecutor taskExecutor;
    private final UserVipService userVipService;

    /**
     * 数据埋点
     * @param message
     */
    public void handleRechargeService(RechargeSuccessMessage message) {
        taskExecutor.execute(() -> {
            try {
                final RechargeRecord rechargeRecord = message.getRechargeRecord();
                if (Objects.nonNull(rechargeRecord.getIsSandbox()) && rechargeRecord.getIsSandbox()){
                    return;
                }
                EventRechargeServiceDTO rechargeServiceDTO = new EventRechargeServiceDTO();
                rechargeServiceDTO.setAmount(rechargeRecord.getCurrencyAmount().doubleValue());
                rechargeServiceDTO.setQtyCoin(rechargeRecord.getCoinAmount().intValue());
                rechargeServiceDTO.setRechargeState(true);
                rechargeServiceDTO.setUid(rechargeRecord.getUid().toString());
                rechargeServiceDTO.setDeviceId(rechargeRecord.getDeviceId());
                EventTrackingUtil.fillAdCommonParam(rechargeServiceDTO, message.getNewBie());
                final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.RECHARGE_SERVICE,
                        rechargeRecord.getUpdateTime(), rechargeServiceDTO);
                ////kafkaService.sendKafkaMsg(dataStr);
            } catch (Exception e){
                log.error("RechargeSuccessListener eventTracking error, error:{}", ExceptionUtil.formatEx(e));
            }
        });

    }


    public void handleExchangeSuccess(long uid, long coin, long usd, Date now, String deviceId, Boolean newBie) {
        try {
            taskExecutor.execute(() -> {
                try {
                    EventExchangeServiceDTO exchangeServiceDTO = new EventExchangeServiceDTO();
                    exchangeServiceDTO.setQtyCoin(coin);
                    exchangeServiceDTO.setQtyUsd(usd);
                    exchangeServiceDTO.setExchangeState(true);
                    exchangeServiceDTO.setUid(Convert.toStr(uid));
                    exchangeServiceDTO.setDeviceId(deviceId);

                    EventTrackingUtil.fillAdCommonParam(exchangeServiceDTO,newBie);
                    final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.EXCHANGE_SERVICE,
                            now, exchangeServiceDTO);
                    //kafkaService.sendKafkaMsg(dataStr);
                } catch (Exception e) {
                    log.error("ExchangeService handleExchangeSuccess error, error:{}", ExceptionUtil.formatEx(e), e);
                }
                userVipService.addExperience(uid, (int) coin, VIPConstant.ExperienceSource.USD);
            });
        } catch (Exception e) {
            log.error("ExchangeService handleExchangeSuccess error, error:{}", ExceptionUtil.formatEx(e));
        }
    }

    public void handleDiamondForUsd(long uid, long diamond, long usd, Date now, String deviceId, Boolean newBie) {
        try {
            taskExecutor.execute(() -> {
                EventHandleDiamondForUsdDTO exchangeServiceDTO = new EventHandleDiamondForUsdDTO();
                exchangeServiceDTO.setQeyDiamond(diamond);
                exchangeServiceDTO.setQtyUsd(usd);
                exchangeServiceDTO.setExchangeState(true);
                exchangeServiceDTO.setUid(Convert.toStr(uid));
                exchangeServiceDTO.setDeviceId(deviceId);

                EventTrackingUtil.fillAdCommonParam(exchangeServiceDTO,newBie);
                final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.DIAMOND_FOR_USD,
                        now, exchangeServiceDTO);
                //kafkaService.sendKafkaMsg(dataStr);
            });
        } catch (Exception e) {
            log.error("ExchangeService handleExchangeSuccess error, error:{}", ExceptionUtil.formatEx(e));
        }
    }

    public void handleEvent(Object entity, String eventName){
        taskExecutor.execute(() -> {
            String dataStr = EventTrackingUtil.buildDataStr(eventName, new Date(), entity);
            log.info("Event handleEvent:[{}] eventName:[{}]", JSONUtil.toJsonStr(entity),eventName);
            //kafkaService.sendKafkaMsg(dataStr);
        });
    }

    public void handleUsdForCoinDealer(long uid, long coinDealer, long usd, Date now, String deviceId, Boolean newBie) {
        try {
            EventUsdForCoinDealerDTO exchangeServiceDTO = new EventUsdForCoinDealerDTO();
            exchangeServiceDTO.setQtyCoinDealer(coinDealer);
            exchangeServiceDTO.setQtyUsd(usd);
            exchangeServiceDTO.setExchangeState(true);
            exchangeServiceDTO.setUid(Convert.toStr(uid));
            exchangeServiceDTO.setDeviceId(deviceId);

            EventTrackingUtil.fillAdCommonParam(exchangeServiceDTO,newBie);
            final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.USD_FOR_COIN_DEALER,
                    now, exchangeServiceDTO);
            //kafkaService.sendKafkaMsg(dataStr);
        } catch (Exception e) {
            log.error("ExchangeService handleExchangeSuccess error, error:{}", ExceptionUtil.formatEx(e));
        }
    }

    /**
     * 数据埋点
     * 每日有效房间
     */
    public void eventTrackingForRoomEffective(String roomId,String deviceId){
        try {
            taskExecutor.execute(() -> {
                String today = DateUtil.today();
                RSet<String> roomEffectiveRSet = redissonClient.getSet(RoomRedisKey.event_tracking_day_room_effective.getKey(StrUtil.format("{{}}", today)));
                boolean addResult = roomEffectiveRSet.add(roomId);
                roomEffectiveRSet.expire(3, TimeUnit.DAYS);
                if (addResult){
                    EventRoomEffectiveDTO roomEffectiveDTO = new EventRoomEffectiveDTO();
                    roomEffectiveDTO.setRoomid(roomId);
                    roomEffectiveDTO.setDeviceId(deviceId);
                    String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.ROOM_EFFECTIVE,
                            new Date(), roomEffectiveDTO);
                    //kafkaService.sendKafkaMsg(dataStr);
                }
            });
        } catch (Exception e){
            log.error("RoomLowService eventTrackingForRoomEffective error, error:{}", ExceptionUtil.formatEx(e));
        }
    }

    /**
     * 上报同时在线房间数峰值
     * room_peak_value
     */
    public void reportedRoomPeakValue() {
        taskExecutor.execute(() -> {
            final RSet<String> roomEffectiveRSet = redissonClient.getSet(RoomRedisKey.room_effective.getKey());
            final Date now = new Date();
            final int size = roomEffectiveRSet.size();

            EventRoomPeakValueDTO roomPeakValueDTO = new EventRoomPeakValueDTO(size);
            final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.ROOM_PEAK_VALUE,
                    now, roomPeakValueDTO);
            //kafkaService.sendKafkaMsg(dataStr);
        });
    }

    /**
     * 上报同时在线用户数峰值
     * online_user
     */
    public void reportedOnlineUser() {
        taskExecutor.execute(() -> {
            RBitSet userOnlineRBitSet = redissonClient.getBitSet(UserRedisKey.user_online.getKey());
            final Date now = new Date();
            EventOnlineUserDTO onlineUserDTO = new EventOnlineUserDTO(userOnlineRBitSet.cardinality());
            final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.ONLINE_USER,
                    now, onlineUserDTO);
            //kafkaService.sendKafkaMsg(dataStr);
        });
    }

    public void reportedRoomViewerPeakValue() {
        final DateTime endOfDay = DateUtil.endOfDay(DateUtil.yesterday());
        final String dayStr = DateUtil.format(endOfDay, DatePattern.NORM_DATE_PATTERN);
        final RMap<String, Integer> viewerPeakRMap = redissonClient
                .getMap(RoomRedisKey.event_tracking_day_room_viewer_peak.getKey(dayStr));
        final Map<String, Integer> viewerPeakMap = viewerPeakRMap.readAllMap();
        if (MapUtil.isEmpty(viewerPeakMap)){
            return;
        }

        final Set<String> roomIdSet = viewerPeakMap.keySet();
        final Map<String, Room> roomMap = roomService.batchGet(Lists.newArrayList(roomIdSet));
        for (String roomId : roomIdSet) {
            final Room room = roomMap.get(roomId);
            final Integer peakValue = viewerPeakMap.get(roomId);
            EventRoomViewerPeakValueDTO roomViewerPeakValueDTO = new EventRoomViewerPeakValueDTO(room.getRoomNo(), peakValue);
            final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.ROOM_VIEWER_PEAK_VALUE, endOfDay,
                    roomViewerPeakValueDTO);
            //kafkaService.sendKafkaMsg(dataStr);
        }
    }

    /**
     * 水果机活动-每天数据埋点信息
     */
    public void eventTrackingForFruitMachinePerDay(Map<String, Integer> value) {
        try {
            final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.FRUIT_MACHINE_PER_DATA, new Date(),
                    value);
            //kafkaService.sendKafkaMsg(dataStr);
        } catch (Exception e) {
            log.error("FruitMachineService eventTrackingForFruitMachine error, error:{}", ExceptionUtil.formatEx(e));
        }
    }

    /**
     * 水果机活动总参与人数
     */
    public void eventTrackingForFruitMachineTotal(Map<String, Integer> value) {
        try {
            final String dataStr = EventTrackingUtil.buildDataStr(EventTrackingConstant.EventName.FRUIT_MACHINE_TOTAL_PARTICIPANTS, new Date(),
                    value);
            //kafkaService.sendKafkaMsg(dataStr);
        } catch (Exception e) {
            log.error("FruitMachineService eventTrackingForFruitMachine error, error:{}", ExceptionUtil.formatEx(e));
        }
    }



}
