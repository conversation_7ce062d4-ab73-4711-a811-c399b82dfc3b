package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.simi.dto.weeklyStar.WeekStarConfigDTO;
import com.simi.entity.weeklyStar.WeeklyStarConfig;
import com.simi.mapper.weeklyStar.WeeklyStarConfigMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-15 16:45
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class WeeklyStarConfigService extends ServiceImpl<WeeklyStarConfigMapper, WeeklyStarConfig> {


    private LoadingCache<String, WeekStarConfigDTO> WeeklyStarConfigCache;

    @PostConstruct
    public void refreshCache() {
        WeeklyStarConfigCache = CacheBuilder.newBuilder()
                .maximumSize(50).
                expireAfterWrite(1, TimeUnit.MINUTES)
                .refreshAfterWrite(10, TimeUnit.SECONDS)
                .build(new CacheLoader<String, WeekStarConfigDTO>() {
                    @Override
                    public WeekStarConfigDTO load(String key) throws Exception {
                        try {
                            WeeklyStarConfig weeklyStarConfig = queryWeeklyStarConfig(Integer.parseInt(key));
                            if (weeklyStarConfig == null) {
                                log.error("WeeklyStarConfigCache error,weeklyStarConfig is null,key:{}", key);
                                return null;
                            }
                            WeekStarConfigDTO weekStarConfigDTO = BeanUtil.copyProperties(weeklyStarConfig, WeekStarConfigDTO.class);
                            String giftList = weeklyStarConfig.getGiftList();
                            String rewardList = weeklyStarConfig.getRewardList();
                            if (StringUtils.isNotBlank(giftList)) {
                                weekStarConfigDTO.setRewardList(JSONUtil.toList(giftList, Integer.class));
                            }else {
                                weekStarConfigDTO.setRewardList(new ArrayList<>());
                            }

                            if (StringUtils.isNotBlank(rewardList)) {
                                weekStarConfigDTO.setRewardList(JSONUtil.toList(rewardList, Integer.class));
                            }else {
                                weekStarConfigDTO.setRewardList(new ArrayList<>());
                            }
                            return weekStarConfigDTO;
                        } catch (Exception e) {
                            log.error("WeeklyStarConfigCache error", e);
                        }
                        return null;
                    }
                });
    }


    /**
     * 获取周星榜配置
     * @return WeekStarConfigDTO
     */
    public WeekStarConfigDTO getWeeklyStarConfig(int weekIndex) {
        try {
            return WeeklyStarConfigCache.getUnchecked(weekIndex + "");
        } catch (Exception e) {
            // 有可能是没有命中缓存
            log.warn("getWeeklyStarConfig error", e);
        }
        return null;
    }

    /**
     * 获取最近一周的配置数据
     */
    public WeeklyStarConfig getLastConfig() {
        PageHelper.startPage(1,1);
        LambdaQueryWrapper<WeeklyStarConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(WeeklyStarConfig::getWeekNumber);
        return getOne(wrapper);
    }

    /**
     * 获取当前周的配置记录
     */
    private WeeklyStarConfig queryWeeklyStarConfig(int weekIndex) {
        LambdaQueryWrapper<WeeklyStarConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(WeeklyStarConfig::getWeekNumber, weekIndex);
        return getOne(wrapper);
    }

    /**
     * 更新状态
     */
    public void updateStatus(int id, int status) {
        WeeklyStarConfig weeklyStarConfig = new WeeklyStarConfig();
        weeklyStarConfig.setId(id);
        weeklyStarConfig.setStatus(status);
        updateById(weeklyStarConfig);
    }

}
