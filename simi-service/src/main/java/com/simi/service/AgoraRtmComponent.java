package com.simi.service;

import cn.hutool.core.util.StrUtil;

import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.simi.common.constant.AgoraConstant;
import com.simi.common.constant.AgoraRedisKey;
import com.simi.common.constant.http.Constant;
import com.simi.common.dto.BaseResp;
import com.simi.common.dto.TokenDTO;
import com.simi.common.util.GsonUtil;
import com.simi.config.AgoraConfig;
import io.agora.rtm.RtmTokenBuilder;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.time.Duration;
import java.util.Map;
import java.util.Objects;

/**
 * 声网RTM服务
 *
 * <AUTHOR>
 * @date 2023/11/13 12:03
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AgoraRtmComponent {

    @Resource
    private RedissonClient redissonClient;

    private static final String CHANNEL_MESSAGES = "https://api.agora.io/dev/v2/project/<appid>/rtm/users/<user_id>/channel_messages";

    /**
     * 生成基础鉴权token
     * @param uid
     * @return
     */
    public TokenDTO generateToken(final Long uid) throws Exception {
        return generateToken(uid, true);
    }

    public TokenDTO generateToken(final Long uid, boolean force) throws Exception {
        TokenDTO tokenDTO = null;
        if(!force){
            tokenDTO = tokenFromCache(uid);
        }
        if(force || Objects.isNull(tokenDTO)){
            RtmTokenBuilder tokenBuilder = new RtmTokenBuilder();
            log.info("Force generate user[{}] rtm token", uid);
            String token = tokenBuilder.buildToken(AgoraConfig.appId, AgoraConfig.appCertificate, uid.toString(), RtmTokenBuilder.Role.Rtm_User, 0);
            long current = System.currentTimeMillis();
            long expires = current + Constant.DAY_SEC * 1000;
            tokenDTO = TokenDTO.builder().token(token).expiresIn(Constant.DAY_SEC).expires(expires).build();
            cacheToken(uid, tokenDTO);
        }else {
            int expiresIn = (int)(tokenDTO.getExpires() - System.currentTimeMillis())/1000;
            tokenDTO.setExpiresIn(expiresIn);
        }
        return tokenDTO;
    }
/*



    /**
     * 发送频道消息
     * @param userId
     * @param token
     * @param channelName
     * @param payload
     * @param enableHistoricalMessaging
     * @throws IOException
     */

    public void publishChannelMessage(String userId, String token, String channelName, String payload, boolean enableHistoricalMessaging) throws IOException, IOException {
        String url = CHANNEL_MESSAGES.replaceFirst("<appid>", AgoraConfig.appId).replaceFirst("<user_id>", userId);
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        httpPost.addHeader("x-agora-uid", userId);
        httpPost.addHeader("x-agora-token", token);
        httpPost.addHeader("Content-Type", "application/json");

        Map<String, Object> reqParam = Maps.newHashMap();
        reqParam.put("channel_name", channelName);
        //是否保存为历史消息。默认值是 false
        reqParam.put("enable_historical_messaging", enableHistoricalMessaging);
        reqParam.put("payload", payload);
        httpPost.setEntity(new StringEntity(GsonUtil.getGson().toJson(reqParam), "utf-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)){
            String respContent = EntityUtils.toString(response.getEntity() , "GBK").trim();
            log.info("send msg to agora rtm resp:{}", respContent);
            final BaseResp baseResp = GsonUtil.getGson().fromJson(respContent, BaseResp.class);
            if (!StrUtil.equalsIgnoreCase(AgoraConstant.SUCCESS_RESULT, baseResp.getResult())){
                log.error("AgoraRtmComponent publishChannelMessage error, code:{}, result:{}, request_id:{}",
                        baseResp.getCode(), baseResp.getResult(), baseResp.getRequest_id());
            }
        } finally {
            httpClient.close();
        }
    }


    private TokenDTO tokenFromCache(Long uid) {

        RBucket<String> tokenStr = redissonClient.getBucket(tokenRedisKey(uid));
        return tokenStr.isExists() ? JSONUtil.toBean(tokenStr.get(), TokenDTO.class) : null;
    }

    private String tokenRedisKey(Long uid) {
        return AgoraRedisKey.rtm_token.getKey(StrUtil.format("{{}}", uid));
    }

    private void cacheToken(Long uid, TokenDTO tokenDTO) {
        redissonClient.getBucket(tokenRedisKey(uid)).set(JSONUtil.toJsonStr(tokenDTO), Duration.ofSeconds(tokenDTO.getExpiresIn()));
    }
}
