package com.simi.service.reward.handle;

import cn.hutool.core.map.MapUtil;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.RewardPackCopywritingEnum;
import com.simi.constant.BillDetailEnum;
import com.simi.constant.BillEnum;
import com.simi.dto.reward.PrizeInfoDTO;
import com.simi.dto.reward.RewardContext;
import com.simi.dto.reward.RewardResult;
import com.simi.service.purse.PurseManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * 金币奖励handle
 */
@Slf4j
@Component
public class CoinRewardHandler extends BaseRewardHandler {

    @Autowired
    private PurseManageService purseManageService;

    @Override
    public RewardResult doSendPrize(RewardContext context) {
        PrizeInfoDTO prizeInfo = context.getPrizeInfo();
        BillDetailEnum billDetailEnum = BillDetailEnum.REWARD_PACK_COIN;
        String remark = context.getRewardDesc();
        try {
            purseManageService.addCoin(context.getTargetUid(), prizeInfo.getCount().longValue(),
                    handleBillEnum(context.getCopyWritingEnum()), context.getBizOrderId(), remark, MapUtil.empty(), 0L, PurseRoleTypeEnum.USER.getType());
            return RewardResult.success();
        } catch (Exception e) {
            log.error("coinReward fail uid:{},billEnum:{},amount:{},bizOrderId:{}", context.getTargetUid(), billDetailEnum, prizeInfo.getCount(), context.getBizOrderId());
            return RewardResult.fail();
        }
    }

    private BillEnum handleBillEnum(RewardPackCopywritingEnum copyWritingEnum) {
        if (Objects.isNull(copyWritingEnum)) {
            return BillEnum.REWARD_PACK_COIN;
        }
        switch (copyWritingEnum) {
            case ARISTOCRACY_CHECK_IN:
                return BillEnum.ARISTOCRACY_SIGN_IN;
        }
        return BillEnum.REWARD_PACK_COIN;
    }
}
