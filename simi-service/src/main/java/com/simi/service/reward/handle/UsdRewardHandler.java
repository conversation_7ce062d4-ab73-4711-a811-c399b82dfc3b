package com.simi.service.reward.handle;

import cn.hutool.core.map.MapUtil;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.constant.BillDetailEnum;
import com.simi.constant.BillEnum;
import com.simi.dto.reward.PrizeInfoDTO;
import com.simi.dto.reward.RewardContext;
import com.simi.dto.reward.RewardResult;
import com.simi.service.purse.PurseManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 美金奖励包handle
 */
@Slf4j
@Component
public class UsdRewardHandler extends BaseRewardHandler {

    @Autowired
    private PurseManageService purseManageService;

    @Override
    public RewardResult doSendPrize(RewardContext context) {
        PrizeInfoDTO prizeInfo = context.getPrizeInfo();
        BillDetailEnum billDetailEnum = BillDetailEnum.REWARD_PACK_USD;
        String remark = context.getRewardDesc();
        // 奖励包来的usd数量值也是美分, 转成美金
        BigDecimal usdBigDecimal = new BigDecimal(prizeInfo.getCount())
                .divide(BigDecimal.TEN.multiply(BigDecimal.TEN), 2, RoundingMode.DOWN);
        try {
            purseManageService.addUSD(context.getTargetUid(), usdBigDecimal,
                    BillEnum.REWARD_PACK_USD, context.getBizOrderId(), remark, MapUtil.empty(),0L, PurseRoleTypeEnum.USER.getType());
            return RewardResult.success();
        } catch (Exception e) {
            log.error("usdReward fail uid:{},billEnum:{},amount:{},bizOrderId:{}  errorMsg:[{}]",
                    context.getTargetUid(), billDetailEnum, prizeInfo.getCount(), context.getBizOrderId(), e.getMessage(), e);
            return RewardResult.fail();
        }
    }
}
