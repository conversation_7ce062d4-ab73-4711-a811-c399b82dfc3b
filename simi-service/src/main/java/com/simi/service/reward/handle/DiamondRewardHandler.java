package com.simi.service.reward.handle;


import cn.hutool.core.map.MapUtil;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.constant.BillDetailEnum;
import com.simi.constant.BillEnum;
import com.simi.dto.reward.PrizeInfoDTO;
import com.simi.dto.reward.RewardContext;
import com.simi.dto.reward.RewardResult;
import com.simi.service.purse.PurseManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 */
@Slf4j
@Component
public class DiamondRewardHandler extends BaseRewardHandler {

    @Autowired
    private PurseManageService purseManageService;

    @Override
    public RewardResult doSendPrize(RewardContext context) {
        PrizeInfoDTO prizeInfo = context.getPrizeInfo();
        BillDetailEnum billDetailEnum = BillDetailEnum.REWARD_PACK_DIAMOND;
        String remark = context.getRewardDesc();
        try {
            purseManageService.addDiamond(context.getTargetUid(), prizeInfo.getCount().longValue(),
                    BillEnum.REWARD_PACK_DIAMOND, context.getBizOrderId(), remark, MapUtil.empty(),0L, PurseRoleTypeEnum.USER.getType());
            return RewardResult.success();
        } catch (Exception e) {
            log.error("diamondRewardHandler fail uid:{},billEnum:{},amount:{},bizOrderId:{} errorMsg:[{}]",
                    context.getTargetUid(), billDetailEnum, prizeInfo.getCount(), context.getBizOrderId(), e.getMessage(), e);
            return RewardResult.fail();
        }
    }
}
