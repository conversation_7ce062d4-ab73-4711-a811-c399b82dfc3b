package com.simi.service.reward.handle;


import cn.hutool.json.JSONUtil;
import com.simi.dto.reward.PrizeInfoDTO;
import com.simi.dto.reward.RewardContext;
import com.simi.dto.reward.RewardResult;
import com.simi.service.level.LevelServerService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 财富值
 */
@Slf4j
@Component
public class WealthValueRewardHandler extends BaseRewardHandler {

    @Autowired
    private LevelServerService levelServerService;

    @Override
    public RewardResult doSendPrize(RewardContext context) {
        PrizeInfoDTO prizeInfo = context.getPrizeInfo();

        try {
            levelServerService.sendWealthLevel(context.getTargetUid(), prizeInfo.getCount().longValue());
            return RewardResult.success();
        } catch (Exception e) {
            log.error("Wealth exp reward Handler fail uid:[{}], prizeInfo:[{}]  errorMsg:[{}]",
                    context.getTargetUid(), JSONUtil.toJsonStr(prizeInfo), e.getMessage(), e);
            return RewardResult.fail();
        }
    }
}
