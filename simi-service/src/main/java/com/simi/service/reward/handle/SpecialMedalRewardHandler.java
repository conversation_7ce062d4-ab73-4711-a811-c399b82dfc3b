package com.simi.service.reward.handle;

import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.dto.reward.PrizeInfoDTO;
import com.simi.dto.reward.RewardContext;
import com.simi.dto.reward.RewardResult;
import com.simi.service.medal.MedalServerService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 特殊勋章
 *
 * <AUTHOR>
 * @date 2024/08/21 20:28
 **/
@Slf4j
@Component
@AllArgsConstructor
public class SpecialMedalRewardHandler  extends BaseRewardHandler {

    private final MedalServerService medalServerService;

    @Override
    public RewardResult doSendPrize(RewardContext context) {
        PrizeInfoDTO prizeInfo = context.getPrizeInfo();
        Long targetUid = context.getTargetUid();
        // 分钟
        Integer duration = prizeInfo.getDuration();
        // 计算天数
        int days = duration / 60 / 24;
        // 计算毫秒
        long value = (long) days * DateTimeUtil.SECONDS_PER_DAY * 1000;

        try {
            medalServerService.giveSpecialMedal(prizeInfo.getPrizeId(), targetUid, value);
        } catch (Exception e) {
            log.error("Special Medal reward Handler fail uid:[{}], prizeInfo:[{}]  errorMsg:[{}]",
                    context.getTargetUid(), prizeInfo, ExceptionUtil.formatEx(e), e);
        }
        return RewardResult.success();
    }

}
