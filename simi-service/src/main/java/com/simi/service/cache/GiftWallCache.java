package com.simi.service.cache;

import cn.hutool.core.util.StrUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.redis.GiftRedisKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Service
public class GiftWallCache {

    @Autowired
    private RedissonManager redissonManager;

    private String roomWallRedisKey(Long uid){
        return GiftRedisKey.gift_wall.getKey(StrUtil.format("{{}}", uid));
    }

    public void setGiftWallCache(Long uid, Integer giftId,Integer num){
        String key = roomWallRedisKey(uid);
        redissonManager.hIncrement(key,giftId.toString(),num.intValue());
    }

    public Map<String,String> getGiftWall(Long uid){
        return redissonManager.hGetAll(roomWallRedisKey(uid));
    }
}
