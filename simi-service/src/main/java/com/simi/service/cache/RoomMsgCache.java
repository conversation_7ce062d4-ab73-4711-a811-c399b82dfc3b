package com.simi.service.cache;

import cn.hutool.core.util.StrUtil;
import com.simi.common.util.RedissonManager;
import com.simi.constant.RoomRedisKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.concurrent.TimeUnit;

@Service
public class RoomMsgCache {

    @Autowired
    private RedissonManager redissonManager;

    private String getRoomMsgKey(String key){
        return RoomRedisKey.room_send_msg.getKey(StrUtil.format("{{}}", key));
    }

    private String roomSendInterval(){
        return RoomRedisKey.room_send_interval.getKey();
    }

    public boolean isInterval(Long uid,Integer num){
        String uidStr = redissonManager.get(roomSendInterval());
        if (StrUtil.equals(uidStr, uid.toString())) {
            return true;
        }
        redissonManager.set(roomSendInterval(),uid.toString());
        redissonManager.expire(roomSendInterval(), num, TimeUnit.MILLISECONDS);
        return false;
    }

    public Long incrRoomMsg(Long uid,String msg) throws NoSuchAlgorithmException {
        String key = uid + msg;
        String hashKey = hashKey(key);
        Long increment = redissonManager.increment(getRoomMsgKey(hashKey), 1L);
        if (increment <= 1) {
            redissonManager.expire(getRoomMsgKey(hashKey),1, TimeUnit.MINUTES);
        }
        return increment;
    }



    public static String  hashKey(String plainText) throws NoSuchAlgorithmException {
        MessageDigest messageDigest = MessageDigest.getInstance("SHA-512");
        byte[] hashBytes = messageDigest.digest(plainText.getBytes(StandardCharsets.UTF_8));
        String hashString = Base64.getEncoder().encodeToString(hashBytes);
        return hashString;
    }


}
