package com.simi.service.cache;

import cn.hutool.core.util.StrUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeUtils;
import com.simi.constant.FruitMachineRedisKey;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
@RequiredArgsConstructor
public class FruitMachineCache {

    private final RedissonManager redissonManager;


    public Long fruitMachineRound(String day) {
        Long increment = redissonManager.increment(FruitMachineRedisKey.fruit_machine_round.getKey(StrUtil.format("{{}}", day)), 1L);
        redissonManager.expire(FruitMachineRedisKey.fruit_machine_round.getKey(StrUtil.format("{{}}", day)),1 , TimeUnit.DAYS);
        return increment;
    }

    public Long getFruitMachineRound(String day) {
        String roundStr = redissonManager.get(FruitMachineRedisKey.fruit_machine_round.getKey(StrUtil.format("{{}}", day)));
        if (StringUtils.isNotBlank(roundStr)) {
            return Long.valueOf(roundStr);
        }
        return 0L;
    }
    
    public void setFruitMachineBet(String day,String bet,Long amount){
        Long fruitMachineRound = getFruitMachineRound(day);
        redissonManager.hIncrement(FruitMachineRedisKey.fruit_machine_round_bet.getKey(StrUtil.format("{{}}", fruitMachineRound)),bet, Math.toIntExact(amount));
        redissonManager.expire(FruitMachineRedisKey.fruit_machine_round_bet.getKey(StrUtil.format("{{}}", fruitMachineRound)),10 , TimeUnit.MINUTES);
    }


    public void setFruitMachinePool(List<String> fruits){
        Collections.shuffle(fruits);
        redissonManager.leftPushAll(FruitMachineRedisKey.fruit_machine_pool.getKey(),fruits);
    }

    public String getFruitMachine(){
        return redissonManager.leftPop(FruitMachineRedisKey.fruit_machine_pool.getKey());
    }

    public Integer getFruitMachinePoolSize(){
        return redissonManager.lSize(FruitMachineRedisKey.fruit_machine_pool.getKey());
    }

    public List<String> getFruitMachinePool(){
        return redissonManager.listAll(FruitMachineRedisKey.fruit_machine_pool.getKey());
    }


    public void setFruitMachineNameMultiple(Map<String,String> maps){
        redissonManager.hMSet(FruitMachineRedisKey.fruit_machine_name_multiple.getKey(),maps);
    }

    public Map<String,String> getFruitMachineNameMultiple(){
       return redissonManager.hGetAll(FruitMachineRedisKey.fruit_machine_name_multiple.getKey());
    }

    public void setResult(String roundId, String result) {
        redissonManager.set(FruitMachineRedisKey.fruit_machine_round_result.getKey(StrUtil.format("{{}}", roundId)), result);
        redissonManager.expire(FruitMachineRedisKey.fruit_machine_round_result.getKey(StrUtil.format("{{}}", roundId)),30 , TimeUnit.MINUTES);
    }

    public String getResult(String roundId) {
        return redissonManager.get(FruitMachineRedisKey.fruit_machine_round_result.getKey(StrUtil.format("{{}}", roundId)));
    }

    public String setTotalBetAmount(String roundId,String key,String totalAmount) {
        return redissonManager.hSet(FruitMachineRedisKey.fruit_machine_round_bet.getKey(StrUtil.format("{{}}", roundId)), key, totalAmount);
    }

    public String getTotalBetAmount(String roundId,String key) {
        return redissonManager.hGet(FruitMachineRedisKey.fruit_machine_round_bet.getKey(StrUtil.format("{{}}", roundId)), key);
    }



}
