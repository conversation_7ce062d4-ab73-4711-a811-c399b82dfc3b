package com.simi.service.cache;

import cn.hutool.core.util.StrUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.redis.GiftRedisKey;
import com.simi.constant.RoomRedisKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class RoomEventCache {

    @Autowired
    private RedissonManager redissonManager;

    private String roomInTimeKey(String roomId){
        return RoomRedisKey.room_user_in_time.getKey(StrUtil.format("{{}}", roomId));
    }

    private String roomMicTimeKey(String roomId){
        return RoomRedisKey.room_mic_time.getKey(StrUtil.format("{{}}", roomId));
    }

    private String giftSendMergeEventKey(String roomId){
        return GiftRedisKey.gift_send_merge_event.getKey(StrUtil.format("{{}}", roomId));
    }

    public void setGiftSendMergeEvent(String roomId, String attach, String uids) {
        redissonManager.hSet(giftSendMergeEventKey(roomId),attach,uids);
    }

    public String getGiftSendMergeEvent(String roomId, String attach) {
        String uids = redissonManager.hGet(giftSendMergeEventKey(roomId), attach);
        redissonManager.hDel(giftSendMergeEventKey(roomId),attach);
        return uids;
    }


    public void setRoomInTime(String roomId,Long uid){
        long time = new Date().getTime();
        redissonManager.hSet(roomInTimeKey(roomId),uid.toString(),time + "");
    }

    public Long getRoomInTime(String roomId,Long uid) {
        String timeStr = redissonManager.hGet(roomInTimeKey(roomId), uid.toString());
        redissonManager.hDel(roomInTimeKey(roomId), uid.toString());
        return Long.valueOf(timeStr);
    }

    public void setRoomMicTime(String roomId,Long uid){
        redissonManager.hIncrement(roomMicTimeKey(roomId),uid.toString(),2 * 60);
    }

    public Long getRoomMicTime(String roomId,Long uid) {
        String timeStr = redissonManager.hGet(roomMicTimeKey(roomId), uid.toString());
        redissonManager.hDel(roomMicTimeKey(roomId), uid.toString());
        return Long.valueOf(timeStr);
    }
}
