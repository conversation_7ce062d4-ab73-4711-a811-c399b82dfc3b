package com.simi.service.cache;

import com.simi.common.util.RedissonManager;
import com.simi.constant.RechargeRecordKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class RechargeRecordCache {

    @Autowired
    private RedissonManager redissonManager;

    private String getRechargeRecordKey(){
        return RechargeRecordKey.recharge_record_purchase_token.getKey();
    }

    public void setRechargeRecord(String purchaseToken,String json){
        redissonManager.hSet(getRechargeRecordKey(),purchaseToken,json);
    }

    public String getRechargeRecord(String purchaseToken){
        String purchase = redissonManager.hGet(getRechargeRecordKey(), purchaseToken);
        redissonManager.expire(getRechargeRecordKey(),1, TimeUnit.DAYS);
        return purchase;
    }
}
