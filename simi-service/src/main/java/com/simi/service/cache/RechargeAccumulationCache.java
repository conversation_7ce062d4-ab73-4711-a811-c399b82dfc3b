package com.simi.service.cache;

import com.simi.common.util.RedissonManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class RechargeAccumulationCache {

    @Autowired
    private RedissonManager redissonManager;

    private String getRechargeAccumulationKey(){
        return "simi:recharge:accumulation";
    }

    public Integer setRechargeAccumulation(Long uid,Long amount){
        return redissonManager.hIncrement(getRechargeAccumulationKey(), uid.toString(), amount.intValue());
    }

    public String getRechargeAccumulation(Long uid){
        return redissonManager.hGet(getRechargeAccumulationKey(),uid.toString());
    }
}
