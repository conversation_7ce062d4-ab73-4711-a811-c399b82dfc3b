package com.simi.service.cache;

import com.simi.common.util.RedissonManager;
import com.simi.constant.Oauth2RedisKey;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class AccountCache {

    @Autowired
    private RedissonManager redissonManager;

    public void setDeviceId(String deviceId, Long uid) {
        redissonManager.hSet(Oauth2RedisKey.device_id.getKey(),uid.toString(), deviceId);
    }

    public String getDeviceId(Long uid) {
        return redissonManager.hGet(Oauth2RedisKey.device_id.getKey(),uid.toString());
    }
}
