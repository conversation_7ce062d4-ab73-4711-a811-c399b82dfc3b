package com.simi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.PayerMaxRefund;
import com.simi.entity.PropInfo;
import com.simi.mapper.PayerMaxRefundMapper;
import com.simi.mapper.PropInfoMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PayerMaxRefundService  extends ServiceImpl<PayerMaxRefundMapper, PayerMaxRefund> {



}
