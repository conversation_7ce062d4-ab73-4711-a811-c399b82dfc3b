package com.simi.service;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.resource.ResourceRedisKey;
import com.simi.entity.Banner;
import com.simi.mapper.BannerMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * banner 服务
 *
 * <AUTHOR>
 * @date 2023/11/11 10:41
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BannerService extends ServiceImpl<BannerMapper, Banner> {

    public static String bannerRedisKey(final int position) {
        return ResourceRedisKey.banner.getKey(StrUtil.format("{{}}", position));
    }

}
