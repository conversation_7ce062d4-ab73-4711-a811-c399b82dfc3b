package com.simi.service;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.dto.PushMicStateInfoDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.room.MicStateInfoDTO;
import com.simi.common.util.TimeUtils;
import com.simi.config.LongLinkConfig;
import com.simi.dto.ComboCountToUids;
import com.simi.dto.push.*;
import com.simi.entity.room.Room;
import com.simi.service.cache.LongLinkCache;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import io.jsonwebtoken.Jwts;

import javax.annotation.PostConstruct;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Date;
import java.security.Key;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@Service
public class LongLinkService {

    private final LongLinkConfig longLinkConfig;
    private final LongLinkCache longLinkCache;
    private final ExecutorService executor = Executors.newFixedThreadPool(4);
    private HttpClient client;

    public LongLinkService(LongLinkConfig longLinkConfig,
                           LongLinkCache longLinkCache) {
        this.longLinkConfig = longLinkConfig;
        this.longLinkCache = longLinkCache;
    }

    @PostConstruct
    public void initHttpClient() {
        this.client = HttpClient.newBuilder()
                .executor(executor)
                .connectTimeout(Duration.ofSeconds(10))
                .version(HttpClient.Version.HTTP_1_1)
                .build();
        log.info("初始Successful..........");
    }

    public void sendMessage(String message) {
        String url = longLinkConfig.baseUrl;
        String apiKey = longLinkConfig.apiKey;
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .header("Content-Type", "application/json")
                .header("X-API-Key", apiKey)
                .POST(HttpRequest.BodyPublishers.ofString(message, StandardCharsets.UTF_8))
                .build();
        client.sendAsync(request, HttpResponse.BodyHandlers.ofString())
                .thenAccept(response -> {
                    log.info("Response Code: {}", response.statusCode());
                    log.info("Response Body: {}", response.body());
                })
                .exceptionally(e -> {
                    log.error("Request failed", e);
                    return null;
                });
    }

    public String genAuthToken(Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        return Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .signWith(key)
                .compact();
    }


    public String genAuthToken1(Long uid) {
        Key key = Keys.hmacShaKeyFor("bbe7d157-a253-4094-9759-06a8236543f9".getBytes());
        return Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .signWith(key)
                .compact();
    }

    public static void main(String[] args) {
        LongLinkService longLinkService = new LongLinkService(new LongLinkConfig(), new LongLinkCache());
        String token = longLinkService.genAuthToken1(1L);
        System.out.println(token);
    }



    public String genInRoomToken(String roomID, Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        String token = Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .claim("channel", "room:" + roomID)
                .signWith(key)
                .compact();
        longLinkCache.setLongLinkToken(uid, "room:" + roomID, token);
        return token;
    }

    public String genGlobalGiftToken(Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        String token = Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .claim("channel", "global_gift")
                .signWith(key)
                .compact();
        longLinkCache.setLongLinkToken(uid, "global_gift", token);
        return token;
    }

    public String generateToken(String userId) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());  // 直接使用密钥字符串

        return Jwts.builder()
                .setSubject(userId)
                .setIssuedAt(new Date())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .signWith(key, SignatureAlgorithm.HS256)
                //.claim("channel", "global")
                .compact();
    }

    public String genBlockUserToken(Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        String token = Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .claim("channel", "black_user:" + uid)
                .signWith(key)
                .compact();
        longLinkCache.setLongLinkToken(uid, "black_user:" + uid, token);
        return token;
    }

    public String genUploadLogToken(Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        String token = Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .claim("channel", "upload_log:" + uid)
                .signWith(key)
                .compact();
        longLinkCache.setLongLinkToken(uid, "upload_log:" + uid, token);
        return token;
    }

    public String genGlobalCommonToken(Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        String token = Jwts.builder()
                .setSubject(uid.toString())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1))
                .claim("channel", "global_common:" + uid)
                .signWith(key)
                .compact();
        longLinkCache.setLongLinkToken(uid, "global_common:" + uid, token);
        return token;
    }

    public String genGlobalToken(String channel, Long uid) {
        Key key = Keys.hmacShaKeyFor(longLinkConfig.secret.getBytes());
        return Jwts.builder()
                .setSubject(uid.toString())
                .setIssuedAt(new Date())
                .setExpiration(TimeUtils.offsetYeah(new Date(), 1)) // 7天过期
                .signWith(key, SignatureAlgorithm.HS256)
                .claim("channel", channel)
                .compact();
    }


    public void pushMicChangedNotice(String roomId, List<MicStateInfoDTO> micListInfo, String event, String toType) {
        PushMicStateInfoDTO dto = new PushMicStateInfoDTO(micListInfo, event);
        PushData date = new PushData();
        date.setEvent(event);
        date.setPayload(dto);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        log.info("pushMicChangedNotice roomId:{} micListInfo:{}", roomId, JSONUtil.toJsonStr(micListInfo));
        var pushData = PushDataDTO.builder()
                .channel("room:" + roomId)
                .data(date)
                .build();
        log.info("pushMicChangedNotice pushData:{} ", JSONUtil.toJsonStr(pushData));
        // 发送POST请求
        sendMessage(JSONUtil.toJsonStr(pushData));
    }

    public void pushRoomMsg(String roomId, UserBaseInfoDTO from, String msg, String event, String toType) {
        PushRoomMsgDTO dto = new PushRoomMsgDTO(msg, from, event, roomId);
        PushData date = new PushData();
        date.setEvent(event);
        date.setPayload(dto);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setIds(Collections.singletonList(from.getUid().toString()));
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .channel("room:" + roomId)
                .type("msg")
                .data(date)
                .build();
        sendMessage(JSONUtil.toJsonStr(pushData));
        log.info("pushRoomMsg pushData:{} ", JSONUtil.toJsonStr(pushData));
    }

    public void pushMicMsg(String roomId, Long uid, String nick, String event, String toType) {
        PushMicMsgDTO dto = new PushMicMsgDTO(uid, event, nick);
        PushData date = new PushData();
        date.setEvent(event);
        date.setPayload(dto);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .channel("room:" + roomId)
                .data(date)
                .build();
        log.info("pushMicChangedNotice pushData:{} ", JSONUtil.toJsonStr(pushData));
        // 发送POST请求
        sendMessage(JSONUtil.toJsonStr(pushData));
    }

    public void pushUpdateRoonMsg(String roomId, Room room, String event, String toType) {
        PushUpdateRoomDTO dto = new PushUpdateRoomDTO(event, room);
        PushData date = new PushData();
        date.setEvent(event);
        date.setPayload(dto);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .channel("room:" + roomId)
                .type("msg")
                .data(date)
                .build();
        log.info("push customer roomMsg pushData[{}] event[{}]", pushData, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
    }

    public void pushComboMsg(String roomId, ComboCountToUids toUids, String event, String toType) {

        PushData date = new PushData();
        date.setEvent(event);
        date.setPayload(toUids);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .channel("room:" + roomId)
                .type("msg")
                .data(date)
                .build();
        log.info("push customer roomMsg pushData[{}] event[{}]", pushData, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
    }


    public void pushCustomerRoomMsg(String roomId, Object data, String event, String toType) {
        pushCustomerRoomMsgByUidList(roomId, data, event, toType, null);
    }

    public void pushCustomerRoomMsgByUidList(String roomId, Object data, String event, String toType, List<String> ids) {
        long begin = System.currentTimeMillis();
        PushData<Object> date = new PushData<>();
        date.setEvent(event);
        date.setPayload(data);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());

        if (CollectionUtils.isNotEmpty(ids)) {
            date.setIds(ids);
        }
        var pushData = PushDataDTO.builder()
                .data(date)
                .type("msg")
                .build();
        if (StrUtil.isBlank(roomId)) {
            pushData.setChannel("global_gift");
        } else {
            pushData.setChannel(StrUtil.format("room:{}", roomId));
        }
        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push customer roomMsg pushData[{}] event[{}]", dataStr, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
        long end = System.currentTimeMillis();
        log.info("success push customer roomMsg requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }

    public void pushCustomerRoomBannerList(String roomId, Object data, String event, String toType) {
        long begin = System.currentTimeMillis();
        PushData<Object> date = new PushData<>();
        date.setEvent(event);
        date.setPayload(data);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());

        var pushData = PushDataDTO.builder()
                .data(date)
                .type("msg")
                .build();
        if (StrUtil.isBlank(roomId)) {
            pushData.setChannel("room");
        } else {
            pushData.setChannel(StrUtil.format("room:{}", roomId));
        }
        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push customer roomMsg BannerList[{}] event[{}]", dataStr, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
        long end = System.currentTimeMillis();
        log.info("success push customer roomMsg requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }

    public void pushBlackMsg(Long uid, Object data, String event, String toType) {
        long begin = System.currentTimeMillis();
        PushData<Object> date = new PushData<>();
        date.setEvent(event);
        date.setPayload(data);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .data(date)
                .type("msg")
                .build();

        pushData.setChannel("black_user:" + uid);

        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push customer black pushData[{}] event[{}]", dataStr, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
        long end = System.currentTimeMillis();
        log.info("success push customer black requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }

    public void pushUploadLogMsg(Long uid, Object data, String event, String toType) {
        long begin = System.currentTimeMillis();
        PushData<Object> date = new PushData<>();
        date.setEvent(event);
        date.setPayload(data);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .data(date)
                .type("msg")
                .build();

        pushData.setChannel("upload_log:" + uid);

        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push upload log pushData[{}] event[{}]", dataStr, event);
        sendMessage(dataStr);
        long end = System.currentTimeMillis();
        log.info("success push upload log requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }


    public void pushCustomerGlobalMsg(Object payload, String uid, String event, String toType) {
        long begin = System.currentTimeMillis();
        PushData<Object> data = PushData.builder()
                .event(event)
                .ids(Collections.singletonList(uid))
                .payload(payload)
                .msgId(UUID.randomUUID().toString(Boolean.TRUE))
                .toType(toType)
                .timestamp(new Date()).build();
        String channel;
        if (StrUtil.isNotBlank(uid)) {
            channel = StrUtil.format("global_common:{}", uid);
        } else {
            channel = "global_common";
        }
        var pushData = PushDataDTO.builder()
                .data(data)
                .type("msg")
                .channel(channel)
                .build();

        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push global customer roomMsg pushData[{}] event[{}]", dataStr, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
        long end = System.currentTimeMillis();
        log.info("success push Customer Global Msg requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }

    public void pushCustomerGlobalNewMsg(Object payload, String uid, String event, String toType) {
        long begin = System.currentTimeMillis();
        PushData<Object> data = PushData.builder()
                .event(event)
                .ids(Collections.singletonList(uid))
                .payload(payload)
                .msgId(UUID.randomUUID().toString(Boolean.TRUE))
                .toType(toType)
                .timestamp(new Date()).build();
        String channel;
        if (StrUtil.isNotBlank(uid)) {
            channel = StrUtil.format("global:{}", uid);
        } else {
            channel = "global";
        }
        var pushData = PushDataDTO.builder()
                .data(data)
                .type("msg")
                .channel(channel)
                .build();

        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push global customer roomMsg pushData[{}] event[{}]", dataStr, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
        long end = System.currentTimeMillis();
        log.info("success push Customer Global Msg  requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }


    public void pushGameMsg(String uid,Object payload, String event, String toType) {
        long begin = System.currentTimeMillis();
        PushData<Object> data = PushData.builder()
                .event(event)
                .payload(payload)
                .msgId(UUID.randomUUID().toString(Boolean.TRUE))
                .toType(toType)
                .timestamp(new Date()).build();
        String channel;
        if (StrUtil.isNotBlank(uid)) {
            channel = StrUtil.format("game:{}", uid);
        } else {
            channel = "game";
        }
        var pushData = PushDataDTO.builder()
                .data(data)
                .type("msg")
                .channel(channel)
                .build();

        String dataStr = JSONUtil.toJsonStr(pushData);
        log.info("push game customer roomMsg pushData[{}] event[{}]", dataStr, event);
        sendMessage(JSONUtil.toJsonStr(pushData));
        long end = System.currentTimeMillis();
        log.info("success push Customer game Msg  requestData[{}]  cost:[{}]ms", dataStr, (end - begin));
    }

    public void pushLobbyStatueMsg(String roomId, Integer status, String event, String toType) {
        PushData date = new PushData();
        date.setEvent(event);
        date.setPayload(status);
        date.setMsgId(UUID.randomUUID().toString(Boolean.TRUE));
        date.setToType(toType);
        date.setTimestamp(new Date());
        var pushData = PushDataDTO.builder()
                .channel("room:" + roomId)
                .data(date)
                .build();
        log.info("pushLobbyStatueMsg pushData:{} ", JSONUtil.toJsonStr(pushData));
        // 发送POST请求
        sendMessage(JSONUtil.toJsonStr(pushData));
    }

}
