package com.simi.service.medal;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.constant.ActivityFlag;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.medal.MedalStageEnum;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.constant.resource.ResourceDurationTypeEnum;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.medal.MedalConfigDTO;
import com.simi.common.dto.medal.UserHaveMedalDTO;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.medal.UserHaveMedalVO;
import com.simi.config.AchievementMedalConfig;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.redisKey.MedalRedisKey;
import com.simi.entity.medal.Medal;
import com.simi.entity.medal.UserMedalRecord;
import com.simi.entity.room.Room;
import com.simi.message.GiftSendMessage;
import com.simi.message.ZegoMicStreamMQMessage;
import com.simi.service.LongLinkService;
import com.simi.service.activity.handle.ActivityBaseService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.room.RoomService;
import com.simi.util.TranslationCopyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/08/21 16:00
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class MedalTaskService implements ActivityBaseService {

    private final MedalServerService medalServerService;
    private final AchievementMedalConfig achievementMedalConfig;
    private final RedissonManager redissonManager;
    private final UserMedalRecordService userMedalRecordService;
    private final TaskExecutor taskExecutor;
    private final LongLinkService longLinkService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final RedissonDistributionLocker distributionLocker;
    private final RoomService roomService;

    public void executeMedalTask(MedalTaskEnum taskEnum, Long uid, Integer incr) {
        List<UserHaveMedalDTO> obtainList = null;
        try {
            List<MedalConfigDTO> medalStageConfig = achievementMedalConfig.getMedalStageConfig();
            Optional<MedalConfigDTO> any = medalStageConfig.stream()
                    .filter(e -> StrUtil.equals(e.getConditionField(), taskEnum.getConditionField())).findAny();
            if (any.isEmpty()) {
                log.info("Check medal task config is empty, taskEnum:[{}] incr:[{}]", taskEnum, incr);
                return;
            }
            MedalConfigDTO config = any.get();

            Medal medal = medalServerService.getMedalByTaskName(config.getConditionField());
            if (Objects.isNull(medal)) {
                log.error("Execute medal task, medal is empty, taskEnum:[{}] uid:[{}] incr:[{}]",
                        taskEnum, uid, incr);
                return;
            }
            String conditionField = StrUtil.isNotBlank(medal.getConditionField()) ? medal.getConditionField() : String.valueOf(medal.getId());
            String taskRateKey = medalServerService.userMedalTaskKey(uid, config.getConditionField());
            Integer afterIncr = redissonManager.hIncrement(taskRateKey, String.valueOf(uid), incr);

            List<MedalConfigDTO.Condition> conditions = config.getConditions();
            List<MedalConfigDTO.Condition> finishStages = conditions.stream().filter(e -> e.getTargetVal() <= afterIncr).toList();
            if (CollUtil.isEmpty(finishStages)) {
                return;
            }
            log.info("User triggers medal task, completes record finishStages:[{}] taskEnum:[{}] uid;[{}] incr:[{}]",
                    finishStages, taskEnum, uid, incr);
            Map<String, List<UserHaveMedalDTO>> userHaveMedalStage = medalServerService.userHaveMedalStage(uid);
            List<UserHaveMedalDTO> havaStageDtos = userHaveMedalStage.getOrDefault(conditionField, CollUtil.newArrayList());
            List<Integer> haveStages = havaStageDtos.stream().map(UserHaveMedalDTO::getStage).toList();
            List<MedalConfigDTO.Condition> notObtainedStages = finishStages.stream()
                    .filter(e -> !haveStages.contains(e.getStage())).toList();
            if (CollUtil.isEmpty(notObtainedStages)) {
                log.info("Medal bypass but Already owned this stage, skip it, taskEnum:[{}] incr:[{}]", taskEnum, incr);
                return;
            }
            Date now = new Date();
            obtainList = notObtainedStages.stream().map(notObtain -> UserHaveMedalDTO.builder()
                    .stage(notObtain.getStage()).obtainTime(now).build()).toList();


            log.info("User obtain medal uid:[{}] list:[{}] hasStageDtos:[{}] taskEnum:[{}] incr:[{}] ",
                    uid, JSONUtil.toJsonStr(obtainList), JSONUtil.toJsonStr(havaStageDtos), taskEnum, incr);
            havaStageDtos.addAll(obtainList);
            String haveMedalKey = medalServerService.userHaveMedalKey(uid);
            redissonManager.hSet(haveMedalKey, String.valueOf(conditionField), JSONUtil.toJsonStr(havaStageDtos));

            obtainList.forEach(userHaveMedalDTO -> {
                try (Locker locker = distributionLocker.lock(notifyLockKey(uid, conditionField, userHaveMedalDTO.getStage()))) {
                    if (Objects.isNull(locker)) {
                        log.warn("User obtain medal unable to obtain lock, taskEnum:[{}] uid:[{}] incr:[{}], userHaveMedalDTO:[{}]",
                                taskEnum, uid, incr, JSONUtil.toJsonStr(userHaveMedalDTO));
                        return;
                    }
                    boolean exists = userMedalRecordService.lambdaQuery()
                            .eq(UserMedalRecord::getUid, uid)
                            .eq(UserMedalRecord::getConditionField, conditionField)
                            .eq(UserMedalRecord::getStage, userHaveMedalDTO.getStage())
                            .exists();
                    if (exists) {
                        log.warn("User obtain medal notify already handled, skip it, taskEnum:[{}] uid:[{}] incr:[{}], userHaveMedalDTO:[{}]",
                                taskEnum, uid, incr, JSONUtil.toJsonStr(userHaveMedalDTO));
                        return;
                    }
                    UserMedalRecord record = UserMedalRecord.builder()
                            .uid(uid)
                            .conditionField(conditionField)
                            .medalId(medal.getId())
                            .stage(userHaveMedalDTO.getStage())
                            .durationType(ResourceDurationTypeEnum.RESOURCE_DURATION_TYPE_PERMANENT.getType())
                            .obtainTime(now)
                            .updateTime(now)
                            .createTime(now)
                            .build();
                    try {
                        boolean saved = userMedalRecordService.save(record);
                        if (!saved) {
                            log.error("Failed to save user medal record, taskEnum:[{}] uid:[{}] incr:[{}] record:[{}]",
                                    taskEnum, uid, incr, JSONUtil.toJsonStr(record));
                        }
                    } catch (Exception e) {
                        log.error("Exception occurred while saving user medal record, taskEnum:[{}] uid:[{}] incr:[{}] record:[{}] errorMsg:[{}]",
                                taskEnum, uid, incr, JSONUtil.toJsonStr(record), ExceptionUtil.formatEx(e));
                    }
                    // 进行通知操作
                    notify(uid, medal, config, userHaveMedalDTO);
                 } catch (Exception e) {
                    log.error("Error during handling user obtain medal notify, taskEnum:[{}] uid:[{}] incr:[{}] userHaveMedalDTO:[{}] errorMsg:[{}]",
                            taskEnum, uid, incr, JSONUtil.toJsonStr(userHaveMedalDTO), ExceptionUtil.formatEx(e));
                }
            });
        } catch (Exception e) {
            log.error("Execute medal task fail, taskEnum:[{}] uid;[{}] incr:[{}] obtainList:[{}] errorMsg:[{}]",
                    taskEnum, uid, incr, JSONUtil.toJsonStr(obtainList), ExceptionUtil.formatEx(e));
        }
    }

    private void notify(Long uid, Medal medal, MedalConfigDTO config, UserHaveMedalDTO userHaveMedalDTO) {
        log.info("Users receive medals and send notify, uid:[{}] medal:[{}] config:[{}] userHaveMedalDTO:[{}]",
                uid, JSONUtil.toJsonStr(medal), JSONUtil.toJsonStr(config), JSONUtil.toJsonStr(userHaveMedalDTO));
        LanguageEnum languageEnum = medalServerService.getUserLang(uid);
        String icon;
        String animation;
        if (Objects.equals(userHaveMedalDTO.getStage(), MedalStageEnum.MIDDLE.getStage())) {
            icon = medal.getIntermediateIcon();
            animation = medal.getIntermediateAnimation();
        } else if (Objects.equals(userHaveMedalDTO.getStage(), MedalStageEnum.SENIOR.getStage())) {
            icon = medal.getAdvancedIcon();
            animation = medal.getAdvancedAnimation();
        } else {
            icon = medal.getBeginnerIcon();
            animation = medal.getBeginnerAnimation();
        }
        Long taskVal = 0L;

        UserHaveMedalVO userHaveMedalVO = medalServerService.getUserHaveMedalVO(config, userHaveMedalDTO, taskVal, medal, languageEnum, icon, animation);
        //冗余
        longLinkService.pushCustomerGlobalMsg(userHaveMedalVO, String.valueOf(uid), PushEvent.global_common_receive_badge_event, PushToType.EXCLUDE_OTHER);
        longLinkService.pushCustomerGlobalNewMsg(userHaveMedalVO, String.valueOf(uid), PushEvent.global_common_receive_badge_event, PushToType.EXCLUDE_OTHER);
        TranslationCopyDTO title = TranslationCopyUtil.translationCopy(CopywritingEnum.ACHIEVE_ACHIEVEMENTS_TITLE.getKey(), null);
        TranslationCopyDTO text = TranslationCopyUtil.translationCopy(CopywritingEnum.ACHIEVE_ACHIEVEMENTS_TEXT.getKey(), null);
        text.setAr(StrUtil.format(text.getAr(), medal.getMedalNameAr()));
        text.setEn(StrUtil.format(text.getEn(), medal.getMedalNameEn()));

        String linkUrl = ClientRouteUtil.toMedal();

        String offlineTitle = Objects.equals(LanguageEnum.ar, languageEnum) ? title.getAr() : title.getEn();
        String offlineText = Objects.equals(LanguageEnum.ar, languageEnum) ? text.getAr() : text.getEn();

        notifyMessageComponent.batchPushSystemComplexIMMsg(Collections.singletonList(String.valueOf(uid)),
                linkUrl, title, text, offlineTitle, offlineText, null);
    }


    @Override
    public void handleMicStreamChange(ZegoMicStreamMQMessage zegoMicStreamMQMessage) {
        log.info("Medal task handle on mic time [{}]", JSONUtil.toJsonStr(zegoMicStreamMQMessage));
        String userId = zegoMicStreamMQMessage.getUserId();
        if (StrUtil.isBlank(userId)) {
            log.error("Medal task handle on mic time uid is empty");
        }
        this.executeMedalTask(MedalTaskEnum.ACCUMULATED_OM_MIC, Long.parseLong(userId), 2);
    }

    @Override
    public void handleGiftData(GiftSendMessage giftSendMessage) {
        taskExecutor.execute(() -> {
            log.info("Medal task handle Send gift time [{}]", JSONUtil.toJsonStr(giftSendMessage));
            this.executeMedalTask(MedalTaskEnum.SEND_GIFTS_GOLD, giftSendMessage.getUid(), giftSendMessage.getTotalCoin());

            String roomId = giftSendMessage.getRoomId();
            Room room = roomService.getRoom(roomId);
            if (Objects.nonNull(room)) {
                // 房主流水, 房主uid
                this.executeMedalTask(MedalTaskEnum.ROOM_RECEIVE_GOLDS, room.getUid(), giftSendMessage.getTotalCoin());
            }

            List<MedalConfigDTO> medalStageConfig = achievementMedalConfig.getMedalStageConfig();
            Optional<MedalConfigDTO> any = medalStageConfig.stream()
                    .filter(e -> Objects.equals(e.getGiftId(), giftSendMessage.getGiftId())).findAny();
            if (any.isEmpty()) {
                return;
            }
            MedalConfigDTO medalConfigDTO = any.get();
            MedalTaskEnum medalTaskEnum = MedalTaskEnum.getByTaskName(medalConfigDTO.getConditionField());

            if (Objects.nonNull(medalTaskEnum)) {
                log.info("Count the number of gifts given deal medal [{}] medalTaskEnum:[{}]", JSONUtil.toJsonStr(giftSendMessage), medalTaskEnum);
                this.executeMedalTask(medalTaskEnum, giftSendMessage.getUid(), giftSendMessage.getTotalGiftNum());
            }
        });
    }

    @Override
    public Pair<Date, Date> timeRange() {
        return Pair.of(BEGIN_TIME, END_TIME);
    }


    @Override
    public ActivityFlag activityFlag() {
        return ActivityFlag.MEDAL_TASK;
    }

    private static final Date BEGIN_TIME = DateUtil.parse("2020-01-01 00:00:00");
    private static final Date END_TIME = DateUtil.parse("2040-01-01 00:00:00");

    private String notifyLockKey(Long uid, String conditionField, Integer stage) {
        return MedalRedisKey.medal_notify_lock.getKey(uid, conditionField, stage);
    }
}
