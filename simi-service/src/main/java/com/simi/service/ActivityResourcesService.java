package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.simi.common.vo.resp.ActivityResourcesResp;
import com.simi.entity.ActivityResources;
import com.simi.mapper.ActivityResourcesMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-07 16:24
 **/
@Slf4j
@Service
public class ActivityResourcesService extends ServiceImpl<ActivityResourcesMapper, ActivityResources> {

    private LoadingCache<String, ActivityResources> activityResourcesLoadingCache;

    @PostConstruct
    public void refreshCache() {
        activityResourcesLoadingCache = CacheBuilder.newBuilder()
                .maximumSize(50).
                expireAfterWrite(1, TimeUnit.MINUTES)
                .refreshAfterWrite(10, TimeUnit.SECONDS)
                .build(new CacheLoader<String, ActivityResources>() {
                    @Override
                    public ActivityResources load(String key) throws Exception {
                        try {
                            return getById(Integer.parseInt(key));
                        } catch (Exception e) {
                            log.error("WeeklyStarConfigCache error", e);
                        }
                        return null;
                    }
                });
    }


    public ActivityResourcesResp getDataById(int id) {
        ActivityResources resources = activityResourcesLoadingCache.getUnchecked(String.valueOf(id));
        return BeanUtil.copyProperties(resources, ActivityResourcesResp.class);
    }

}
