package com.simi.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.ConfigGroup;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.StatusEnum;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.common.dto.aristocracy.AristocracyConfigDTO;
import com.simi.common.dto.gift.GiftInfoDTO;
import com.simi.common.dto.vip.config.VipConfigDTO;
import com.simi.common.redis.GiftRedisKey;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.LangUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.req.BaseCommonCountryGroupBusinessReq;
import com.simi.config.UserLevelConfig;
import com.simi.constant.UserRedisKey;
import com.simi.entity.GiftInfo;
import com.simi.entity.LevelInfo;
import com.simi.entity.group.AppFunctionsGroupConfig;
import com.simi.entity.user.User;
import com.simi.mapper.GiftInfoMapper;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.group.UserFunctionCountryGroupService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.VipConfigService;
import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @description 针对表【gift_info(礼物元数据表)】的数据库操作Service实现
 * @createDate 2024-01-04 10:22:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GiftInfoService extends ServiceImpl<GiftInfoMapper, GiftInfo> {

    private final RedissonManager redissonManager;
    private final AristocracyConfigService aristocracyConfigService;
    private final VipConfigService vipConfigService;
    private final UserFunctionCountryGroupService countryGroupService;

    public List<GiftInfoDTO> getGiftInfo(UserServerService userServerService, Long uId, @NotNull String lang) {
        List<GiftInfoDTO> dtos = new ArrayList<>();
        List<GiftInfo> giftInfos = giftInfosFromCache(uId,userServerService);

        if (CollectionUtils.isNotEmpty(giftInfos)) {
            for (GiftInfo giftInfo : giftInfos) {
                GiftInfoDTO dto = new GiftInfoDTO();
                dto.setIcon(giftInfo.getIcon());
                dto.setName(LangUtil.getContentByLang(giftInfo.getName(), lang));
                dto.setPrice(giftInfo.getPrice());
                dto.setJumpLink(giftInfo.getJumpLink());
                if (lang.equals(LanguageEnum.en.name())) {
                    dto.setBanner(giftInfo.getEnBanner());
                }
                if (lang.equals(LanguageEnum.ar.name())) {
                    dto.setBanner(giftInfo.getArBanner());
                }
                if (!DateUtil.isIn(new Date(), giftInfo.getStartTime(), giftInfo.getEndTime())) {
                    continue;
                }
                dto.setWeight(giftInfo.getWeight());
                dto.setAnimationUrl(giftInfo.getAnimationUrl());
                dto.setCornerMark(giftInfo.getCornerMark());
                dto.setRemark(giftInfo.getRemark());
                dto.setLevelType(giftInfo.getLevelType());
                dto.setId(giftInfo.getId());
                dto.setIsCombo(giftInfo.getIsCombo());
                dto.setDirection(giftInfo.getDirection());
                dto.setTabId(giftInfo.getTabId());
                dto.setGiftType(giftInfo.getGiftType());

                // 处理贵族信息
                try {
                    Integer aristocracyLevel = giftInfo.getAristocracyLevel();
                    if (Objects.nonNull(aristocracyLevel) && aristocracyLevel > 0) {
                        Optional<AristocracyConfigDTO> optional = aristocracyConfigService.getAristocracyConfigById(aristocracyLevel);
                        optional.ifPresent(k -> {
                            GiftInfoDTO.AristocracyInfo aristocracyInfo = new GiftInfoDTO.AristocracyInfo();
                            aristocracyInfo.setAristocracyLevel(k.getId());
                            aristocracyInfo.setIcon(k.getIconUrl2());
                            aristocracyInfo.setEnName(k.getEnName());
                            aristocracyInfo.setArName(k.getArName());
                            dto.setAristocracyInfo(aristocracyInfo);
                        });
                    }
                } catch (Exception e) {
                    log.error("getAristocracyInfo error.ex{}", ExceptionUtil.formatEx(e), e);
                }

                // VIP 信息
                Integer vipLevel = giftInfo.getVipLevel();
                if (Objects.nonNull(vipLevel) && vipLevel > 0) {
                    VipConfigDTO vipConfigDTO = vipConfigService.getVipConfigDTOS().get(vipLevel);
                    if (Objects.nonNull(vipConfigDTO)) {
                        GiftInfoDTO.VipInfo vipInfo = new GiftInfoDTO.VipInfo();
                        vipInfo.setVipLevel(vipLevel);
                        vipInfo.setIcon(vipConfigDTO.getIconUrl());
                        vipInfo.setEnName(vipConfigDTO.getEnName());
                        vipInfo.setArName(vipConfigDTO.getArName());
                        dto.setVipInfo(vipInfo);
                    }
                }

                dtos.add(dto);
            }
        }
        dtos = dtos.stream().sorted(Comparator.comparing(GiftInfoDTO::getWeight).reversed()).toList();
        return dtos;
    }

    public List<GiftInfo> giftInfosFromCache(Long uid, UserServerService userServerService) {
        try {
            AppFunctionsGroupConfig config = countryGroupService.getFunctionsGroup(ConfigGroup.OPERATIONAL_BACKEND_COMMON, uid);
            if (config == null) {
                return Collections.emptyList();
            }
            Map<String, String> giftInfoMap = redissonManager.hGetAll(GiftRedisKey.gift_info.getKey());
            if (MapUtil.isEmpty(giftInfoMap)) {
                LambdaQueryWrapper<GiftInfo> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(GiftInfo::getState, StatusEnum.normal.getStatus())
                        .eq(GiftInfo::getDeleteFlag, StatusEnum.normal.getStatus())
                        .lt(GiftInfo::getStartTime, new Date())
                        .gt(GiftInfo::getEndTime, new Date())
                        .orderByDesc(GiftInfo::getWeight);
                List<GiftInfo> giftInfos = this.list(queryWrapper);
                if (CollectionUtils.isNotEmpty(giftInfos)) {
                    for (GiftInfo giftInfo : giftInfos) {
                        redissonManager.hSet(GiftRedisKey.gift_info.getKey(), giftInfo.getId().toString(), JSONUtil.toJsonStr(giftInfo));
                    }
                }
                giftInfoMap = redissonManager.hGetAll(GiftRedisKey.gift_info.getKey());
            }
            Date now = new Date();
            Gson gson = new Gson();

            String wealthStr = redissonManager.hGet(UserRedisKey.user_wealth_level.getKey(), String.valueOf(uid));
            long wealthAmount = 0;
            if (StringUtils.isNotBlank(wealthStr)) {
                wealthAmount = Long.parseLong(wealthStr);
            }
            LevelInfo currentLevel = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_WEALTH, wealthAmount);

            return giftInfoMap.values()
                    .stream()
                    .map(e -> {
                        try {
                            GiftInfo giftInfo = JSONUtil.toBean(e, GiftInfo.class);
                            if (giftInfo.getGroupIds() != null) {
                                //符合国家组
                                List<String> groupIdsList = Arrays.asList(giftInfo.getGroupIds().split(","));
                                if (groupIdsList.contains(String.valueOf(config.getGroupId()))) {
                                    // 检查礼品是否在有效时间范围内
                                    if (DateUtil.isIn(now, giftInfo.getStartTime(), giftInfo.getEndTime())) {

                                        //在判断投放的范围是否符合
                                        if (giftInfo.getCommonExt() != null) {
                                            BaseCommonCountryGroupBusinessReq baseCommonCountryGroupBusinessReq = gson.fromJson(giftInfo.getCommonExt(), BaseCommonCountryGroupBusinessReq.class);
                                            if (baseCommonCountryGroupBusinessReq.getPackageList().contains("1")) {
                                                //投放类型
                                                Integer publicType = baseCommonCountryGroupBusinessReq.getPublicType();
                                                if (publicType != null) {
                                                    boolean add = false;
                                                    //投放条件：1：全部用户 2：指定用户id  3：财富等级
                                                    if (publicType == 1) {
                                                        // 将 publicValue 解析成列表
                                                        String[] publicValueArray = baseCommonCountryGroupBusinessReq.getPublicValue().split(",");
                                                        if (publicValueArray.length == 2) {
                                                            int id1 = Integer.parseInt(publicValueArray[0]);//新老
                                                            int id2 = Integer.parseInt(publicValueArray[1]);//性别
                                                            User user = userServerService.getUser(uid);
                                                            if (id1 == 0 && id2 == 0) {
                                                                add = true;
                                                            } else {
                                                                //判断老用户
                                                                if (id1 == 1) {
                                                                    add = isType1(user);
                                                                }
                                                                //判断新用户
                                                                if (id1 == 2) {
                                                                    add = !isType1(user);
                                                                }
                                                                //男
                                                                if (id2 == 1) {
                                                                    add = user.getGender() == 1;
                                                                }
                                                                //女
                                                                if (id2 == 2) {
                                                                    add = user.getGender() == 2;
                                                                }
                                                            }
                                                        }
                                                    }
                                                    //设置用户id
                                                    if (publicType == 2) {
                                                        // 将 publicValue 解析成列表
                                                        String[] publicValueArray = baseCommonCountryGroupBusinessReq.getPublicValue().split(",");
                                                        // 判断玩家的 UID 是否在列表中
                                                        boolean isUidInPublicValue = Arrays.asList(publicValueArray).contains(String.valueOf(uid));
                                                        if (isUidInPublicValue) {
                                                            add = true;
                                                        }
                                                    }
                                                    //判断财富等级
                                                    if (publicType == 3 && currentLevel != null) {
                                                        int leve = Integer.parseInt(baseCommonCountryGroupBusinessReq.getPublicValue());
                                                        add = currentLevel.getLevel() >= leve;
                                                    }
                                                    //在判断黑白名单设置
                                                    if (add || publicType == 2) {
                                                        String[] blackListArray = baseCommonCountryGroupBusinessReq.getBlackList() != null ?
                                                                baseCommonCountryGroupBusinessReq.getBlackList().split(",") : new String[]{};
                                                        String[] whiteListArray = baseCommonCountryGroupBusinessReq.getWhiteList() != null ?
                                                                baseCommonCountryGroupBusinessReq.getWhiteList().split(",") : new String[]{};
                                                        // 使用 Set 去重并方便查找
                                                        Set<String> blackSet = new HashSet<>(Arrays.asList(blackListArray));
                                                        Set<String> whiteSet = new HashSet<>(Arrays.asList(whiteListArray));
                                                        // 遍历所有用户，黑名单优先处理
                                                        String uidStr = String.valueOf(uid);
                                                        if (blackSet.contains(uidStr)) {
                                                            add = false;
                                                        }
                                                        if (!blackSet.contains(uidStr) && whiteSet.contains(uidStr)) {
                                                            add = true;
                                                        }
                                                    }
                                                    if (add) {
                                                        return giftInfo;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            return null; // 如果不符合条件，返回 null
                        } catch (Exception ex) {
                            log.warn("get gift info from cache fail giftinfo:[{}]", e);
                            return null;
                        }
                    }).filter(Objects::nonNull) // 过滤掉 null 值
                    .toList(); // 将流转换为 List
        } catch (Exception e) {
            log.error(" uid: {},异常： {}", uid, e.getMessage());
        }
        return Collections.emptyList();
    }

    //是不是今天注册的用户
    private boolean isType1(User user) {
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayStart = calendar.getTime();
        return user.getCreateTime().after(todayStart);
    }

    public List<GiftInfo> giftInfosFromCache() {
        Map<String, String> giftInfoMap = redissonManager.hGetAll(GiftRedisKey.gift_info.getKey());
        if (MapUtil.isEmpty(giftInfoMap)) {
            LambdaQueryWrapper<GiftInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GiftInfo::getState, StatusEnum.normal.getStatus())
                    .eq(GiftInfo::getDeleteFlag, StatusEnum.normal.getStatus())
                    .lt(GiftInfo::getStartTime, new Date())
                    .gt(GiftInfo::getEndTime, new Date())
                    .orderByDesc(GiftInfo::getWeight);
            List<GiftInfo> giftInfos = this.list(queryWrapper);
            if (CollectionUtils.isNotEmpty(giftInfos)) {
                for (GiftInfo giftInfo : giftInfos) {
                    redissonManager.hSet(GiftRedisKey.gift_info.getKey(), giftInfo.getId().toString(), JSONUtil.toJsonStr(giftInfo));
                }
            }
            giftInfoMap = redissonManager.hGetAll(GiftRedisKey.gift_info.getKey());
        }
        Date now = new Date();
        List<String> expireGiftIds = CollUtil.newArrayList();
        List<GiftInfo> result = giftInfoMap.values().stream().map(e -> {
            try {
                GiftInfo bean = JSONUtil.toBean(e, GiftInfo.class);
                if (DateUtil.isIn(now, bean.getStartTime(), bean.getEndTime())) {
                    return bean;
                }
                expireGiftIds.add(String.valueOf(bean.getId()));
                return bean;
            } catch (Exception ex) {
                log.warn("get gift info from cache fail giftinfo:[{}]", e);
                return null;
            }
        }).filter(Objects::nonNull).toList();
        return result;
    }

    public List<GiftInfo> giftInfoByIds(List<Integer> giftIds) {
        List<GiftInfo> giftInfos = giftInfosFromCache();
        return giftInfos.stream().filter(e -> giftIds.contains(e.getId())).toList();
    }

    public List<GiftInfo> getByTabId(Long tagId) {
        LambdaQueryWrapper<GiftInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GiftInfo::getTabId, tagId);
        return list(wrapper);
    }

    public GiftInfo getGiftInfoFromCache(Integer giftId) {
        if (Objects.isNull(giftId)) {
            return null;
        }
        String giftInfoStr = redissonManager.hGet(GiftRedisKey.gift_info.getKey(), String.valueOf(giftId));
        if (StrUtil.isNotBlank(giftInfoStr)) {
            GiftInfo giftInfo = JSONUtil.toBean(giftInfoStr, GiftInfo.class);
            if (DateUtil.isIn(new Date(), giftInfo.getStartTime(), giftInfo.getEndTime())) {
                return giftInfo;
            }
            redissonManager.hDel(GiftRedisKey.gift_info.getKey(), String.valueOf(giftId));
            return null;
        } else {
            LambdaQueryWrapper<GiftInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(GiftInfo::getState, StatusEnum.normal.getStatus())
                    .eq(GiftInfo::getDeleteFlag, StatusEnum.normal.getStatus())
                    .lt(GiftInfo::getStartTime, new Date())
                    .gt(GiftInfo::getEndTime, new Date())
                    .eq(GiftInfo::getId, giftId);
            GiftInfo giftInfo = getOne(queryWrapper);
            if (Objects.nonNull(giftInfo)) {
                redissonManager.hSet(GiftRedisKey.gift_info.getKey(), String.valueOf(giftId), JSONUtil.toJsonStr(giftInfo));
                return giftInfo;
            }
        }
        return null;
    }


}




