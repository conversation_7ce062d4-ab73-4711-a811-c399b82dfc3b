package com.simi.service;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.byteplus.model.request.SmsSendRequest;
import com.byteplus.model.response.SmsSendResponse;
import com.byteplus.service.sms.SmsService;
import com.byteplus.service.sms.SmsServiceInfoConfig;
import com.byteplus.service.sms.impl.SmsServiceImpl;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.InfrastructureRedisKey;
import com.simi.common.constant.SendSmsType;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.http.HttpPostSendTypeEnums;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.HttpUtils;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeUtils;
import com.simi.common.vo.req.HttpGetReq;
import com.simi.common.vo.resp.HttpPostReq;
import com.simi.config.TapsCloudSmsConfig;
import com.simi.constant.PurposeEnum;
import com.simi.constant.SmsChannelEnum;
import com.simi.entity.SmsSendRecord;
import com.simi.service.infrastructure.SmsCache;
import com.simi.util.TranslationCopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class SendSmsService {

    private static final String ACCESS_KEY = "AKAPZjcwMTM3ZGEwNmNkNDgyZGE0OGQxN2YzYTM0ZWIzZjE";
    private static final String SECRET_KEY = "TkRFM01qTXlNelUzT0RKak5ETXdPV0l6TUdVME5USXhZVEUzTm1RelpUaw==";
    private static final String GROUP_ID = "7bcdebf6";
    private static final String TEMPLATE_ID = "ST_7bcd13ab";
    private static final SmsService smsService = SmsServiceImpl.getInstance(new SmsServiceInfoConfig(ACCESS_KEY, SECRET_KEY));

    @Autowired
    private SmsSendRecordService smsSendRecordService;
    @Autowired
    private SmsCache smsCache;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private SystemConfigService systemConfigService;



    public static String testGet() throws Exception {
        HttpGetReq req = new HttpGetReq();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=UTF-8");
        header.put("pub-uid", "91630318");
        header.put("oauth-token", "9617a107af664859bcaaf6019e0866e6");
        req.setHeader(header);
        req.setUrl("https://simi.manhonszeto.com:8443/api/client/api/resource/header-upload-param");
        String get = HttpUtils.sendGet(req);
        return get;
    }

    public static String testPost() throws Exception {
        HttpPostReq req = new HttpPostReq();
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=UTF-8");
        header.put("pub-uid", "91630318");
        header.put("oauth-token", "9617a107af664859bcaaf6019e0866e6");
        req.setHeader(header);
        Map<String, String> param = new HashMap<>();
        param.put("text", "qqqqqqqq");
        req.setParam(param);
        req.setUrl("https://simi.manhonszeto.com:8443/api/client/api/bullet/sendBullet");
        req.setSendType(HttpPostSendTypeEnums.JSON.getType());
        String post = HttpUtils.sendPost(req);
        return post;
    }

    public static String testDoGet() throws IOException {
        return HttpUtils.doPost("https://simi.manhonszeto.com:8443/api/client/api/zego/getNacos");
    }

    public void sendSms(String phoneNo, String areaCode, Integer purpose, String language, Integer type, XAuthToken xAuthToken,String ip) {

        //半小时三条
        final long phoneLimit = redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_phone_limit.getKey(StrUtil.format("{{}}", phoneNo)))
                .incrementAndGet();
        final long deviceLimit = redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_device_limit.getKey(StrUtil.format("{{}}", xAuthToken.getDeviceID())))
                .incrementAndGet();
        final long ipLimit = redissonClient.getAtomicLong(
                InfrastructureRedisKey.sms_ip_limit.getKey(StrUtil.format("{{}}", ip))).incrementAndGet();
        if (phoneLimit > 3 || deviceLimit > 3 || ipLimit > 3) {
            String confValueById = systemConfigService.getSysConfValueById(SystemConfigConstant.SMS_VERIFICATION_SWITCH);
            if (StrUtil.isBlank(confValueById) || StrUtil.equals(confValueById, "1")) {
                throw new ApiException(CodeEnum.SMS_LIMIT);
            }
        }
        redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_phone_limit.getKey(StrUtil.format("{{}}", phoneNo)))
                .expire(30, TimeUnit.MINUTES);
        redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_device_limit.getKey(StrUtil.format("{{}}", xAuthToken.getDeviceID())))
                .expire(30, TimeUnit.MINUTES);
        redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_ip_limit.getKey(StrUtil.format("{{}}", ip)))
                .expire(30, TimeUnit.MINUTES);

        if (type.equals(SendSmsType.BytePlus.getCode())) {
            //sendBytePlus(phoneNo, areaCode, purpose);
            sendTapsCloud(phoneNo, language, areaCode,  purpose);
        }
        if (type.equals(SendSmsType.Whatsapp.getCode())) {
            sendWhatsapp(phoneNo, areaCode, language, purpose);
        }
        if (type.equals(SendSmsType.TapsCloud.getCode())) {
            sendTapsCloud(phoneNo, language, areaCode,  purpose);
        }
    }

    /**
     * 生成接口签名
     * @param time     发送时间
     * @param nonce    随机码
     * @return
     */
    public String genSignMD5(String time, String nonce) {
        String data = "f20b3571c0404cfc8619cdc34c5b39d5" + time + nonce;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] hash = md.digest(data.getBytes());
            return bytesToHex(hash).toLowerCase();
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
            return null;
        }
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02x", b));
        }
        return sb.toString();
    }
    public void sendTapsCloud(String phoneNo,String language,String areaCode, Integer purpose) {
        String time = TimeUtils.toStr(new Date(), TimeUtils.YEAR2SECOND24_NOLINE);
        String nonce = UUID.randomUUID().toString().replace("-", "");
        String sign = genSignMD5(time, nonce);
        Random rand = new Random();
        int code = rand.nextInt((9999 - 1000) + 1) + 1000;

        TranslationCopyDTO textDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.TAPS_CLOUD_CONTENT.getKey(), null);

        HttpPostReq req = new HttpPostReq();
        Map<String, String> param = new HashMap<>();
        param.put("mobile", areaCode + phoneNo);
        param.put("apiKey", TapsCloudSmsConfig.apiKey);
        param.put("time", time);
        param.put("nonce", nonce);
        param.put("sign", sign);
        if (language.equals("ar")) {
            param.put("content", String.format(textDTO.getAr(),code));
        }else {
            param.put("content", String.format(textDTO.getEn(),code));
        }
        req.setParam(param);
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=UTF-8");
        req.setUrl(TapsCloudSmsConfig.url);
        req.setSendType(HttpPostSendTypeEnums.JSON.getType());
        try {
            String post = HttpUtils.sendPost(req);
            SmsSendRecord record = new SmsSendRecord();
            record.setPhone(phoneNo);
            record.setAreaCode(areaCode);
            record.setCode(code + "");
            record.setPurpose(PurposeEnum.getDescByNumber(purpose));
            record.setType(purpose);
            record.setChannel(SmsChannelEnum.TapsCloud.getDesc());
            smsSendRecordService.save(record);
            smsCache.setSms(areaCode + phoneNo, code + "");
            log.info("sendTapsCloud post:{}",post);
        }catch (Exception e) {
            log.error("error................. \n {}", e.getMessage());
        }

    }




    public void sendBytePlus(String phoneNo, String areaCode, Integer purpose) {
        SmsSendRequest req = new SmsSendRequest();
        req.setFrom("12345");
        req.setSmsAccount(GROUP_ID);
        req.setPhoneNumbers(areaCode + phoneNo);
        req.setTemplateId(TEMPLATE_ID);
        Map<String, String> map = new HashMap<>();
        Random rand = new Random();
        int code = rand.nextInt((9999 - 1000) + 1) + 1000;
        map.put("content", code + "");
        req.setTemplateParamByMap(map);
        try {
            SmsSendResponse response = smsService.sendV2(req);
            log.info("sendBytePlus response:{}", JSONUtil.toJsonStr(response));
            SmsSendRecord record = new SmsSendRecord();
            record.setPhone(phoneNo);
            record.setAreaCode(areaCode);
            record.setCode(code + "");
            record.setPurpose(PurposeEnum.getDescByNumber(purpose));
            record.setType(purpose);
            record.setChannel(SmsChannelEnum.SMS.getDesc());
            smsSendRecordService.save(record);
            smsCache.setSms(areaCode + phoneNo, code + "");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void sendWhatsapp(String phoneNo, String areaCode, String language, Integer purpose) {
        Random rand = new Random();
        int code = rand.nextInt((9999 - 1000) + 1) + 1000;

        HttpPostReq req = new HttpPostReq();
        Map<String, String> param = new HashMap<>();
        param.put("phone", areaCode + phoneNo);
        param.put("language", language);
        param.put("code", code + "");
        req.setParam(param);
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json; charset=UTF-8");
        req.setHeader(header);
        req.setUrl("http://simi-internal-services:8089/whatsapp/send-verify-code");
        req.setSendType(HttpPostSendTypeEnums.JSON.getType());
        try {
            String post = HttpUtils.sendPost(req);
            SmsSendRecord record = new SmsSendRecord();
            record.setPhone(phoneNo);
            record.setAreaCode(areaCode);
            record.setCode(code + "");
            record.setPurpose(PurposeEnum.getDescByNumber(purpose));
            record.setType(purpose);
            record.setChannel(SmsChannelEnum.Whatsapp.getDesc());
            smsSendRecordService.save(record);
            smsCache.setSms(areaCode + phoneNo, code + "");
        } catch (Exception e) {
            log.error("error..............", e);
        }
    }
}

