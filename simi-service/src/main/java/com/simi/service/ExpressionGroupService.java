package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.simi.common.dto.ExpressionGroupDTO;
import com.simi.common.dto.expression.ExpressionInfoDTO;
import com.simi.common.vo.req.ExpressionGroupReq;
import com.simi.entity.ExpressionGroup;
import com.simi.mapper.ExpressionGroupMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-01 11:07
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class ExpressionGroupService extends ServiceImpl<ExpressionGroupMapper, ExpressionGroup> {
    private LoadingCache<String, List<ExpressionGroupDTO>> expressionGroupCache;
    private final ExpressionInfoService expressionInfoService;

    @PostConstruct
    public void refreshCache() {
        expressionGroupCache = CacheBuilder.newBuilder()
                .maximumSize(50).
                expireAfterWrite(1, TimeUnit.MINUTES)
                .refreshAfterWrite(10, TimeUnit.SECONDS)
                .build(new CacheLoader<String, List<ExpressionGroupDTO>>() {
                    @Override
                    public List<ExpressionGroupDTO> load(String key) throws Exception {
                        try {
                            return getGroupDTOList();
                        } catch (Exception e) {
                            log.error("expressionGroupCache error", e);
                        }
                        return new ArrayList<>();
                    }
                });
    }


    /**
     * 新增或者更新
     */
    public void saveOrUpdateData(ExpressionGroupReq req) {
        ExpressionGroup expressionGroup = BeanUtil.copyProperties(req, ExpressionGroup.class);
        saveOrUpdate(expressionGroup);
    }


    /**
     * 删除
     */
    public void deleteById(Integer id) {
        if (id == 1) {
            // 不能删除默认组
            throw new IllegalArgumentException("不能删除默认组");
        }
        // 将该表情的所有在组设置为默认组 1

        List<ExpressionInfoDTO> expressionInfoDTOS = expressionInfoService.queryListByClassify(id);
        expressionInfoDTOS.forEach(k -> k.setClassify(1));
        expressionInfoService.updateBatch(expressionInfoDTOS);
        removeById(id);
    }

    /**
     * 获取所有组信息
     */
    public List<ExpressionGroupDTO> groupDTOListCache() {
        return expressionGroupCache.getUnchecked("");
    }

    private List<ExpressionGroupDTO> getGroupDTOList(){
        List<ExpressionGroup> list = this.list();
        return BeanUtil.copyToList(list, ExpressionGroupDTO.class);
    }
}
