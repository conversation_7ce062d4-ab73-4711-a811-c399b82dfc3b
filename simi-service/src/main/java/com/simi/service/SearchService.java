//package com.simi.service;
//
//import cn.hutool.core.bean.BeanUtil;
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.simi.common.dto.ListWithTotal;
//import com.simi.common.dto.user.UserBaseInfoDTO;
//import com.simi.common.dto.user.search.SearchRoomDTO;
//import com.simi.common.dto.user.search.SearchUserDTO;
//import com.simi.common.entity.room.RoomInfoDTO;
//import com.simi.common.vo.SearchResult;
//import com.simi.common.vo.search.SearchRoomVO;
//import com.simi.common.vo.search.SearchUserVO;
//import com.simi.mapper.search.MeiliSearchRoomMapper;
//import com.simi.mapper.search.MeiliSearchUserMapper;
//import com.simi.service.room.RoomHighService;
//import com.simi.service.user.UserServerService;
//import com.meilisearch.sdk.SearchRequest;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//import java.util.Map;
//import java.util.Objects;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * 搜索相关业务service
// *
// * <AUTHOR>
// * @date 2024/04/10 15:10
// **/
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class SearchService {
//
//    private final RoomHighService roomHighService;
//    private final UserServerService userServerService;
//    private final MeiliSearchUserMapper meiliSearchUserMapper;
//    private final MeiliSearchRoomMapper meiliSearchRoomMapper;
//
//    public ListWithTotal<SearchUserVO> searchUserInfo(String keyword, Integer pageNum, Integer pageSize) {
//        int offset = (pageNum - 1) * pageSize;
//        SearchRequest searchRequest = SearchRequest.builder()
//                .offset(offset)
//                .limit(pageSize)
//                .q(keyword)
//                .sort(new String[] {"userNo:asc", "wealthLevel:desc", "charmLevel:desc", "createTime:asc"})
//                .build();
//        SearchResult<SearchUserDTO> search = meiliSearchUserMapper.search(searchRequest);
//        List<SearchUserVO> result = CollUtil.newArrayList();
//        if (CollUtil.isNotEmpty(search.getHits())) {
//            result = BeanUtil.copyToList(search.getHits(), SearchUserVO.class);
//            result.forEach(searchUserVO -> {
//                UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(searchUserVO.getUid());
//                if (Objects.nonNull(userBaseInfoDTO)) {
//                    searchUserVO.setUserLevel(userBaseInfoDTO.getUserLevel());
//                    searchUserVO.setAvatar(userBaseInfoDTO.getAvatar());
//                    searchUserVO.setHasPrettyNo(userBaseInfoDTO.getHasPrettyNo());
//                }
//            });
//        }
//        return ListWithTotal.<SearchUserVO>builder().total(search.getEstimatedTotalHits()).list(result).build();
//    }
//
//    public ListWithTotal<SearchRoomVO> searchRoomInfo(String keyword, Integer pageNum, Integer pageSize) {
//        int offset = (pageNum - 1) * pageSize;
//        if (StrUtil.isBlank(keyword)) {
//            return ListWithTotal.empty();
//        }
//        SearchRequest searchRequest = SearchRequest.builder()
//                .offset(offset)
//                .limit(pageSize)
//                .q(keyword)
//                .sort(new String[] {"roomNo:asc", "wealthLevel:desc", "charmLevel:desc", "createTime:asc"})
//                .build();
//        SearchResult<SearchRoomDTO> search = meiliSearchRoomMapper.search(searchRequest);
//        List<SearchRoomVO> result = CollUtil.newArrayList();
//        if (CollUtil.isNotEmpty(search.getHits())) {
//            result = BeanUtil.copyToList(search.getHits(), SearchRoomVO.class);
//
//            List<Long> uids = result.stream().map(SearchRoomVO::getRoomUid).collect(Collectors.toList());
//            Map<Long, UserBaseInfoDTO> longUserMap = userServerService.batchUserSummary(uids);
//
//            List<String> roomIds = result.stream().map(SearchRoomVO::getRoomId).collect(Collectors.toList());
//            Map<String, RoomInfoDTO> roomInfoDTOMap = roomHighService.batchGetRoom(roomIds).stream().collect(Collectors.toMap(RoomInfoDTO::getRoomId, Function.identity()));
//
//            result.forEach(searchRoomVO -> {
//                UserBaseInfoDTO user = longUserMap.get(searchRoomVO.getRoomUid());
//                if (Objects.nonNull(user)) {
//                    searchRoomVO.setCountryCode(user.getCountryCode());
//                    searchRoomVO.setHasPrettyNo(user.getHasPrettyNo());
//                }
//
//                RoomInfoDTO roomInfoDTO = roomInfoDTOMap.get(searchRoomVO.getRoomId());
//                if (Objects.nonNull(roomInfoDTO)) {
//                    searchRoomVO.setAvatar(roomInfoDTO.getAvatar());
//                    searchRoomVO.setRoomTypeValue(roomInfoDTO.getRoomTypeValue());
//                }
//            });
//        }
//        return ListWithTotal.<SearchRoomVO>builder().list(result).total(search.getEstimatedTotalHits()).build();
//    }
//
//
//
//
//}
