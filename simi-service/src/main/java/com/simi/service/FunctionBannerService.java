package com.simi.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.nacos.shaded.com.google.gson.Gson;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.ConfigGroup;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.req.BaseCommonCountryGroupBusinessReq;
import com.simi.common.vo.resp.BannerFunctionResp;
import com.simi.config.UserLevelConfig;
import com.simi.constant.UserRedisKey;
import com.simi.entity.FunctionBanner;
import com.simi.entity.LevelInfo;
import com.simi.entity.group.AppFunctionsGroupConfig;
import com.simi.entity.user.User;
import com.simi.mapper.FunctionBannerMapper;
import com.simi.service.group.UserFunctionCountryGroupService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class FunctionBannerService extends ServiceImpl<FunctionBannerMapper, FunctionBanner> {
    private final UserFunctionCountryGroupService countryGroupService;
    private final RedissonManager redissonManager;
    private final UserServerService userServerService;

    public List<BannerFunctionResp> normalBanners(Long uid) {
        //通过国家组获取符合的BannerDTO
        AppFunctionsGroupConfig config = countryGroupService.getFunctionsGroup(ConfigGroup.OPERATIONAL_BACKEND_COMMON, uid);
        if (config == null) {
            return Collections.emptyList();
        }
        List<BannerFunctionResp> respList = new ArrayList<>();
        List<FunctionBanner> bannerDTOList = getBannerList(config.getGroupId());
        final Gson gson = new Gson();
        for (FunctionBanner bannerDTO : bannerDTOList) {
            if (bannerDTO.getCommonExt() != null) {

                BaseCommonCountryGroupBusinessReq baseCommonCountryGroupBusinessReq = gson.fromJson(bannerDTO.getCommonExt(), BaseCommonCountryGroupBusinessReq.class);
                if (baseCommonCountryGroupBusinessReq.getPackageList().contains("1")) {

                    String wealthStr = redissonManager.hGet(UserRedisKey.user_wealth_level.getKey(), String.valueOf(uid));
                    long wealthAmount = 0;
                    if (StringUtils.isNotBlank(wealthStr)) {
                        wealthAmount = Long.parseLong(wealthStr);
                    }
                    LevelInfo currentLevel = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_WEALTH, wealthAmount);

                    //投放类型
                    //{"packageList":null,"publicType":null,"publicValue":null,"blackList":null,"whiteList":null}
                    Integer publicType = baseCommonCountryGroupBusinessReq.getPublicType();
                    if (publicType != null) {
                        boolean add = false;
                        //投放条件：1：全部用户 2：指定用户id  3：财富等级
                        if (publicType == 1) {
                            // 将 publicValue 解析成列表
                            //{"packageList":"1,2","publicType":2,"publicValue":"73239902,35841156","blackList":"24003920","whiteList":"30519622"}
                            String[] publicValueArray = baseCommonCountryGroupBusinessReq.getPublicValue().split(",");
                            if (publicValueArray.length == 2) {
                                int id1 = Integer.parseInt(publicValueArray[0]);//新老
                                int id2 = Integer.parseInt(publicValueArray[1]);//性别
                                User user = userServerService.getUser(uid);
                                if (id1 == 0 && id2 == 0) {
                                    add = true;
                                } else {
                                    //判断老用户
                                    if (id1 == 1) {
                                        add = isType1(user);
                                    }
                                    //判断新用户
                                    if (id1 == 2) {
                                        add = !isType1(user);
                                    }
                                    //男
                                    if (id2 == 1) {
                                        add = user.getGender() == 1;
                                    }
                                    //女
                                    if (id2 == 2) {
                                        add = user.getGender() == 2;
                                    }
                                }
                            }
                        }
                        //设置用户id
                        if (publicType == 2) {
                            // 将 publicValue 解析成列表
                            String[] publicValueArray = baseCommonCountryGroupBusinessReq.getPublicValue().split(",");
                            // 判断玩家的 UID 是否在列表中
                            boolean isUidInPublicValue = Arrays.asList(publicValueArray).contains(String.valueOf(uid));
                            if (isUidInPublicValue) {
                                add = true;
                            }
                        }
                        //判断财富等级
                        if (publicType == 3 && currentLevel != null) {
                            int leve = Integer.parseInt(baseCommonCountryGroupBusinessReq.getPublicValue());
                            add = currentLevel.getLevel() >= leve;
                        }

                        //在判断黑白名单设置
                        if (add || publicType == 2) {
                            String[] blackListArray = baseCommonCountryGroupBusinessReq.getBlackList() != null ?
                                    baseCommonCountryGroupBusinessReq.getBlackList().split(",") : new String[]{};
                            String[] whiteListArray = baseCommonCountryGroupBusinessReq.getWhiteList() != null ?
                                    baseCommonCountryGroupBusinessReq.getWhiteList().split(",") : new String[]{};
                            // 使用 Set 去重并方便查找
                            Set<String> blackSet = new HashSet<>(Arrays.asList(blackListArray));
                            Set<String> whiteSet = new HashSet<>(Arrays.asList(whiteListArray));
                            // 遍历所有用户，黑名单优先处理
                            String uidStr = String.valueOf(uid);
                            if (blackSet.contains(uidStr)) {
                                add = false;
                            }
                            if (!blackSet.contains(uidStr) && whiteSet.contains(uidStr)) {
                                add = true;
                            }
                        }
                        if (add) {
                            BannerFunctionResp bannerFunctionResp = new BannerFunctionResp();
                            bannerFunctionResp.setId(bannerDTO.getId());
                            bannerFunctionResp.setName(bannerDTO.getName());
                            bannerFunctionResp.setPosition(bannerDTO.getPosition());
                            respList.add(bannerFunctionResp);
                        }
                    }
                }
            }
        }
        return respList;
    }

    private List<FunctionBanner> getBannerList(int groupId) {
        try {

            Date now = new Date();

            LambdaQueryWrapper<FunctionBanner> queryWrapper = new LambdaQueryWrapper<>();
            String groupIdStr = String.valueOf(groupId);

            // 使用 FIND_IN_SET 函数代替 like 查询
            queryWrapper.isNotNull(FunctionBanner::getGroupIds)//isNotNull(Banner::getGroupIds)：确保 group_ids 字段非 NULL
                    .apply("FIND_IN_SET({0}, group_ids) > 0", groupIdStr)  // 精确匹配 groupId 在 groupIds 中
                    .lt(FunctionBanner::getStartTime, now)
                    .gt(FunctionBanner::getEndTime, now);

            List<FunctionBanner> list = this.list(queryWrapper);
            if (CollectionUtils.isEmpty(list)) {
                return CollUtil.newArrayList();
            }
            return list;

        } catch (Exception e) {
            log.error("异常", e);
        }
        return Collections.emptyList();
    }

    //是不是今天注册的用户
    private boolean isType1(User user) {
        Date currentDate = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayStart = calendar.getTime();
        return user.getCreateTime().after(todayStart);
    }
}
