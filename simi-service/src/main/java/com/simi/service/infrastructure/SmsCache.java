package com.simi.service.infrastructure;

import com.simi.common.util.RedissonManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

@Service
public class SmsCache {

    @Autowired
    private RedissonManager redissonManager;

    private String getSendSmsKey(String phone) {
        return "sms:" + phone;
    }

    public void setSms(String phone, String smsCode) {
        redissonManager.set(getSendSmsKey(phone), smsCode);
        redissonManager.expire(getSendSmsKey(phone), 2, TimeUnit.MINUTES);
    }

    public void removeSmsCode(String phone) {
        if (redissonManager.exists(phone)) {
            redissonManager.del(getSendSmsKey(phone));
        }
    }

    public String getSmsCode(String phone) {
        return redissonManager.get(getSendSmsKey(phone));
    }
}
