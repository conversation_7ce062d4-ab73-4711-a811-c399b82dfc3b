package com.simi.service.infrastructure;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.InfrastructureRedisKey;
import com.simi.common.constant.SmsSendType;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.RandomUtil;
import com.simi.config.WhiteListConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 短信
 *
 * @Author: Andy
 * @Date: 2023-12-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SmsService {
    /**
     * 默认短信运营商
     */
    private static final String DEFAULT_SMS_CARRIER = "chuanglan";
    @Resource
    private final RedissonClient redissonClient;
    @Resource
    private final SmsFactory smsFactory;
    @Resource
    private final SystemConfigService systemConfigService;

    private final SmsCache smsCache;

    private final WhiteListConfig whiteListConfig;

    /**
     * 发送短信验证码
     *
     * @param phone
     * @param sendType
     */
    public void sendSms(String phone, SmsSendType sendType, String deviceId, String ip,
                        String lang) {
        log.info("SmsService sendSms, phone:{}, sendType:{}, deviceId:{}, ip:{}", phone, sendType,
                deviceId, ip);
        checkSend(phone, sendType, deviceId, ip);

        final String smsCode = Convert.toStr(RandomUtil.getFourRandomNumber());
        String smsCarrier = getSmsCarrier();
        log.info("send code message by {}, code:{}", smsCarrier, smsCode);
        ISmsService smsService = smsFactory.getSmsService(smsCarrier);
        boolean res = smsService.sendSms(phone, smsCode, lang);
        if (!res) {
            throw new ApiException(CodeEnum.SMS_ERROR);
        }
        redissonClient.getBucket(
                        InfrastructureRedisKey.sms_send_record.getKey(sendType.getCode(), phone))
                .set(smsCode, 302, TimeUnit.SECONDS);
    }


    /**
     * 验证短信
     *
     * @param phone
     * @param smsCode
     * @return
     */
    public boolean verifySms(String areaCode, String phone, String smsCode) {
        //后门登录手机号

        for (var phoneInfo : whiteListConfig.getPhoneList()) {
            if (StrUtil.equalsIgnoreCase(phoneInfo.getAreaCode(), areaCode) && StrUtil.equalsIgnoreCase(phoneInfo.getPhone(), phone)) {
                return true;
            }
        }

        if (Objects.equals(whiteListConfig.getSmsCode(), smsCode)) {
            return true;
        }
       

        String code = StringUtils.getDigits(areaCode);
        String codePhone = code + phone;
        String cacheSmsCode = smsCache.getSmsCode(codePhone);
        return StrUtil.equalsIgnoreCase(smsCode, cacheSmsCode);
    }

    private void checkSend(String phone, SmsSendType sendType, String deviceId, String ip) {
        if (StrUtil.isBlank(phone) || StrUtil.isBlank(deviceId) || StrUtil.isBlank(ip)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        final RBucket<String> bucket = redissonClient.getBucket(
                InfrastructureRedisKey.sms_send_record.getKey(sendType.getCode(), phone));
        if (bucket.isExists()) {
            throw new ApiException(CodeEnum.SMS_LIMIT);
        }

        //半小时三条
        final long phoneLimit = redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_phone_limit.getKey(StrUtil.format("{{}}", phone)))
                .incrementAndGet();
        final long deviceLimit = redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_device_limit.getKey(StrUtil.format("{{}}", deviceId)))
                .incrementAndGet();
        final long ipLimit = redissonClient.getAtomicLong(
                InfrastructureRedisKey.sms_ip_limit.getKey(StrUtil.format("{{}}", ip))).incrementAndGet();
        if (phoneLimit > 3 || deviceLimit > 3 || ipLimit > 3) {
            throw new ApiException(CodeEnum.SMS_LIMIT);
        }
        redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_phone_limit.getKey(StrUtil.format("{{}}", phone)))
                .expire(30, TimeUnit.MINUTES);
        redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_device_limit.getKey(StrUtil.format("{{}}", deviceId)))
                .expire(30, TimeUnit.MINUTES);
        redissonClient.getAtomicLong(
                        InfrastructureRedisKey.sms_ip_limit.getKey(StrUtil.format("{{}}", ip)))
                .expire(30, TimeUnit.MINUTES);
    }

    /**
     * 获取短信运营商
     *
     * @return
     */
    private String getSmsCarrier() {
        String smsCarrier = systemConfigService.getSysConfValueById(SystemConfigConstant.SMS_CARRIER);
        return StringUtils.isBlank(smsCarrier) ? DEFAULT_SMS_CARRIER : smsCarrier;
    }
}
