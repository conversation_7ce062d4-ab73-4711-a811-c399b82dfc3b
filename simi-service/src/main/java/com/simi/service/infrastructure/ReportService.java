package com.simi.service.infrastructure;

import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.vo.req.ReportReq;
import com.simi.constant.ProcessStatusEnum;
import com.simi.constant.ReportTypeEnum;
import com.simi.entity.ReportRecord;
import com.simi.entity.room.Room;
import com.simi.service.room.RoomService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 举报服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportService {

    private final ReportRecordService reportRecordService;
    private final UserServerService userServeService;
    private final RoomService roomService;

    /**
     * 举报
     *
     * @param param
     * @param uid
     */
    public void report(ReportReq param, final long uid) {
        ReportTypeEnum typeEnum = ReportTypeEnum.getByType(param.getReportType());
        if (Objects.isNull(param.getReportId())) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        if (CollectionUtils.isEmpty(param.getCategoriesList())) {
            log.warn("categories cannot be empty");
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        String showId;
        switch (typeEnum) {
            case REPORT_TYPE_USER:
                UserBaseInfoDTO userDTO = userServeService.getFromCache(Long.valueOf(param.getReportId()));
                if (Objects.isNull(userDTO) || !Objects.equals(userDTO.getUid(), Long.parseLong(param.getReportId()))) {
                    throw new ApiException(CodeEnum.USER_NO_EXIST);
                }
                showId = String.valueOf(userDTO.getUserNo());
                break;
            case REPORT_TYPE_ROOM:
                Room room = roomService.getRoom(String.valueOf(param.getReportId()));
                if (Objects.isNull(room) || !Objects.equals(room.getId(), param.getReportId())) {
                    throw new ApiException(CodeEnum.ROOM_MATCH_NOT_FOUND);
                }
                showId = String.valueOf(room.getRoomNo());
                break;
            default:
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        ReportRecord record = ReportRecord.builder()
                .reportType(typeEnum.getType())
                .reportId(String.valueOf(param.getReportId()))
                .categories(param.getCategoriesList())
                .description(param.getDescription())
                .photos(param.getPhotosList())
                .uid(uid)
                .showId(showId)
                .createTime(new Date())
                .status(ProcessStatusEnum.UNPROCESSED.getNumber())
                .build();
        reportRecordService.save(record);
    }
}
