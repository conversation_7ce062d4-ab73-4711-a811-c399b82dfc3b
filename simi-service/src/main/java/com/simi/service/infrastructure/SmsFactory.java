package com.simi.service.infrastructure;

import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @date 2024/2/20 14:05
 */
@Component
public class SmsFactory {
    private final Map<String, ISmsService> smsServiceMap = new ConcurrentHashMap<>();

    public SmsFactory(Map<String, ISmsService> smsServiceMap) {
        this.smsServiceMap.clear();
        smsServiceMap.forEach(this.smsServiceMap::put);
    }


    public ISmsService getSmsService(String serviceName) {
        return this.smsServiceMap.get(serviceName);
    }
}
