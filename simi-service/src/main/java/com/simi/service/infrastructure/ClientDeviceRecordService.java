package com.simi.service.infrastructure;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.InfrastructureRedisKey;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.vo.CommonReq;
import com.simi.entity.ClientDeviceRecord;
import com.simi.mapper.infrastructure.ClientDeviceRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class ClientDeviceRecordService extends ServiceImpl<ClientDeviceRecordMapper, ClientDeviceRecord> {
    @Autowired
    private final RedissonDistributionLocker distributionLocker;

    public boolean isNewDevice(CommonReq param){
        try(Locker lock = distributionLocker.lock(InfrastructureRedisKey.client_device_lock.getKey(param.getDeviceId()))){
            if(Objects.isNull(lock)){
                return false;
            }
            ClientDeviceRecord record = getById(param.getDeviceId());
            Date now = new Date();
            if(Objects.isNull(record)){
                record = ClientDeviceRecord.builder().id(param.getDeviceId())
                        .os(param.getOs())
                        .osVersion(param.getOsVersion())
                        .app(param.getApp())
                        .appVersion(param.getAppVersion())
                        .appVersionCode(param.getAppVersionCode())
                        .channel(param.getChannel())
                        .bundleId(param.getBundleId())
                        .isPhysicalDevice(param.getIsPhysicalDevice())
                        .build();
                save(record);
                return true;
            }
            Date beginTime = DateUtil.beginOfDay(now);
            Date endTime = DateUtil.endOfDay(now);
            return DateUtil.isIn(record.getFirstTime(), beginTime, endTime);
        } catch (Exception e) {
            log.warn("Get client device locker error:{}", ExceptionUtil.formatEx(e));
            return false;
        }
    }

    public boolean isNewDevice(String deviceId){
        try(Locker lock = distributionLocker.lock(InfrastructureRedisKey.client_device_lock.getKey(deviceId))){
            if(Objects.isNull(lock)){
                return false;
            }
            ClientDeviceRecord record = getById(deviceId);
            if(Objects.isNull(record)){
                return true;
            }
            Date now = new Date();
            Date beginTime = DateUtil.beginOfDay(now);
            Date endTime = DateUtil.endOfDay(now);
            return DateUtil.isIn(record.getFirstTime(), beginTime, endTime);
        } catch (Exception e) {
            log.warn("Get client device locker error:{}", ExceptionUtil.formatEx(e));
            return false;
        }
    }
}
