package com.simi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.UserUploadLogRecord;
import com.simi.mapper.UserUploadLogRecordMapper;
import org.springframework.stereotype.Service;

import java.util.Date;

@Service
public class UserUploadLogRecordService  extends ServiceImpl<UserUploadLogRecordMapper, UserUploadLogRecord> {

    public void addRecord(Long uid, String logUrl){
        UserUploadLogRecord record = new UserUploadLogRecord();
        record.setUid(uid);
        record.setLogUrl(logUrl);
        record.setCreateTime(new Date());
        save(record);
    }
}
