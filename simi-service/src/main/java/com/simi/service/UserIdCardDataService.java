package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.StatusEnum;
import com.simi.common.dto.UserIdCardDataDTO;
import com.simi.common.dto.UserIdCardDataReq;
import com.simi.common.exception.ApiException;
import com.simi.entity.user.UserIdCardData;
import com.simi.mapper.UserIdCardDataMapper;
import com.simi.common.util.OssUrlUtil;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Service
public class UserIdCardDataService extends ServiceImpl<UserIdCardDataMapper, UserIdCardData> {

    public void saveUserIdCardData(Long uid,UserIdCardDataReq req){
        if (req.getId() == null) {
            LambdaQueryWrapper<UserIdCardData> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserIdCardData::getUid,uid);
            wrapper.eq(UserIdCardData::getChannel,req.getChannel());
            wrapper.eq(UserIdCardData::getStatus, StatusEnum.normal.getStatus());
            long count = count(wrapper);
            if (count >= 5) {
                throw new ApiException(CodeEnum.USER_WITHDRAW_CHANNEL);
            }
        }
        UserIdCardData userIdCardData = BeanUtil.copyProperties(req, UserIdCardData.class);
        userIdCardData.setUid(uid);
        if (req.getId() != null) {
            userIdCardData.setCreatedAt(new Date());
        }else {
            userIdCardData.setUpdatedAt(new Date());
        }
        saveOrUpdate(userIdCardData);

    }

    public List<UserIdCardDataDTO> getRealName(Long uid,Integer channel){
        List<UserIdCardDataDTO> userIdCardDataDTOS = new ArrayList<>();
        LambdaQueryWrapper<UserIdCardData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserIdCardData::getUid,uid);
        wrapper.eq(UserIdCardData::getChannel,channel);
        wrapper.eq(UserIdCardData::getStatus, StatusEnum.normal.getStatus());
        List<UserIdCardData> userIdCardData = list(wrapper);
        for (UserIdCardData userIdCardDatum : userIdCardData) {
            UserIdCardDataDTO userIdCardDataDTO = BeanUtil.copyProperties(userIdCardDatum, UserIdCardDataDTO.class);
            userIdCardDataDTO.setCardPhoto(OssUrlUtil.jointUrl(userIdCardDataDTO.getCardPhoto()));
            userIdCardDataDTO.setIdPhoto(OssUrlUtil.jointUrl(userIdCardDataDTO.getIdPhoto()));
            userIdCardDataDTOS.add(userIdCardDataDTO);
        }
        return userIdCardDataDTOS;
    }

    public void delete(Long uid,Long id){
        LambdaUpdateWrapper<UserIdCardData> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(UserIdCardData::getStatus,StatusEnum.invalid.getStatus());
        wrapper.eq(UserIdCardData::getId,id);
        wrapper.eq(UserIdCardData::getUid,uid);
        update(wrapper);
    }

    public Long countByChannel(Integer channel,Long uid){
        LambdaQueryWrapper<UserIdCardData> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserIdCardData::getChannel,channel);
        wrapper.eq(UserIdCardData::getUid,uid);
        wrapper.eq(UserIdCardData::getStatus, StatusEnum.normal.getStatus());
        return count(wrapper);
    }

    public List<UserIdCardData> getByUidIn(List<Long> id){
        LambdaQueryWrapper<UserIdCardData> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserIdCardData::getId,id);
        // wrapper.eq(UserIdCardData::getStatus, StatusEnum.normal.getStatus());
        return list(wrapper);
    }

}
