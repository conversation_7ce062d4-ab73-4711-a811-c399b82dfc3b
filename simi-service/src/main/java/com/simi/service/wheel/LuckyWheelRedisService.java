package com.simi.service.wheel;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.wheel.LuckyWheelDetailVO;
import com.simi.constant.redisKey.WheelRedisKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;

@RequiredArgsConstructor
@Slf4j
@Service
public class LuckyWheelRedisService {
    private final RedissonManager redissonManager;
    private final static int ttl = 15;
    private final static  String LOCK_FLAG = "1";
    private final static Integer LOCK_TTL = 5;

    public void refreshLuckyWheelInfo(String roomId, LuckyWheelDetailVO luckyWheelDetailVO){
        String key = WheelRedisKey.lucky_wheel_detail.getKey(roomId);
        String value = Objects.isNull(luckyWheelDetailVO) ? "" : JSON.toJSONString(luckyWheelDetailVO);
        redissonManager.set(key, value,ttl, TimeUnit.MINUTES);
    }

    public LuckyWheelDetailVO getLuckyWheelInfo(String roomId){
        String key = WheelRedisKey.lucky_wheel_detail.getKey(roomId);
        String value = redissonManager.get(key);
        if (StringUtils.isBlank(value)){
            return null;
        }
        return JSON.parseObject(value,LuckyWheelDetailVO.class);
    }

    public boolean setWheelLock(Long wheelId){
        String key = WheelRedisKey.handle_lucky_wheel_status.getKey(wheelId);
        return redissonManager.setnx(key,LOCK_FLAG,LOCK_TTL, TimeUnit.SECONDS);
    }

    public boolean setJoinLock(Long wheelId,Long uid){
        String key = WheelRedisKey.join_lucky_wheel.getKey(wheelId,uid);
        return redissonManager.setnx(key,LOCK_FLAG,LOCK_TTL, TimeUnit.SECONDS);
    }
}
