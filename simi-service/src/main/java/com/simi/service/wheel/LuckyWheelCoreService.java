package com.simi.service.wheel;

import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.constant.wheel.LuckyWheelStatusEnums;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.wheel.LuckyWheelCancelContentDTO;
import com.simi.common.dto.wheel.LuckyWheelDataDTO;
import com.simi.common.dto.wheel.LuckyWheelDelayMsgDTO;
import com.simi.common.dto.wheel.LuckyWheelStartContentDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.CommonUtil;
import com.simi.common.vo.req.wheel.CreateLuckyWheelReq;
import com.simi.common.vo.wheel.LuckyWheelDetailVO;
import com.simi.common.vo.wheel.ParticipantsInfo;
import com.simi.constant.BillEnum;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.entity.wheel.LuckyWheelInfo;
import com.simi.entity.wheel.LuckyWheelParticipantsInfo;
import com.simi.service.LongLinkService;
import com.simi.service.RocketMqSender;
import com.simi.service.purse.PurseManageService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

@RequiredArgsConstructor
@Slf4j
@Service
public class LuckyWheelCoreService {
    private final LuckyWheelManagerService luckyWheelManagerService;
    private final LuckyWheelParticipantsInfoManagerService luckyWheelParticipantsInfoManagerService;
    private final SystemConfigService systemConfigService;
    private final PurseManageService purseManageService;
    private final RocketMqSender rocketMqSender;
    private final LongLinkService longLinkService;
    private final UserServerService userServerService;
    private final LuckyWheelRedisService luckyWheelRedisService;
    private final static String CANCEL_CONTENT_EN = "Due to the Lucky Wheel hasn't started for a long time, the event is closed.";
    private final static String CANCEL_CONTENT_AR = "نظرًا لأن عجلة الحظ لم تبدأ منذ فترة طويلة، فقد تم إغلاق الحدث.";

    private final static String START_CONTENT_EN = "I started the Lucky Wheel. Welcome to join~";
    private final static String START_CONTENT_AR = "لقد بدأت عجلة الحظ. مرحبًا بك للانضمام إليّ~";
    /**
     * 转盘超时时间，10分钟
     */
    private final static Long WHEEL_TIMEOUT = 600000L;

    @Transactional(rollbackFor = Exception.class)
    public long doCreateLuckWheel(Long uid, CreateLuckyWheelReq createLuckyWheelReq) {
        //创建转盘
        LuckyWheelInfo luckWheelInfo = createLuckWheelInfo(uid, createLuckyWheelReq);
        LuckyWheelDelayMsgDTO luckyWheelDelayMsgDTO = getLuckyWheelDelayMsgDTO(luckWheelInfo.getId(), 1,uid);
        String timeOut = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_DELAY_CANCEL_TIME,WHEEL_TIMEOUT.toString());
        //将转盘数据放到延迟队列中
        rocketMqSender.sendDeliverMessage(RocketMQTopic.LUCKY_WHEEL_DELAY_TOPIC, JSON.toJSONString(luckyWheelDelayMsgDTO),Long.valueOf(timeOut));
        //创建者是否参与
        if (createLuckyWheelReq.isJoin()){
            joinLuckyWheel(uid,createLuckyWheelReq.getEntryFee(),luckWheelInfo.getId());
        }

        Long id = luckWheelInfo.getId();
        //将数据转盘推送到客户端
        pushLuckWheel(id);

        //将发起转盘文案推送至客户端
        pushCreateContent(createLuckyWheelReq.getRoomId(),id,uid);
        return id;
    }

    private void pushCreateContent(String roomId, Long id, Long uid) {
        LuckyWheelStartContentDTO luckyWheelStartContentDTO = LuckyWheelStartContentDTO.builder()
                .wheelId(id)
                .roomId(roomId)
                .startContentEn(START_CONTENT_EN)
                .startContentAr(START_CONTENT_AR)
                .build();

        UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);
        longLinkService.pushRoomMsg(roomId, userPB, JSON.toJSONString(luckyWheelStartContentDTO), PushEvent.LUCKY_WHEEL_START_CONTENT, PushToType.MESSAGE_TO_ALL);
    }

    public LuckyWheelDetailVO pushLuckWheel(Long wheelId) {
        LuckyWheelDetailVO luckyWheelDetail = getLuckyWheelDetail(wheelId);
        if (Objects.isNull(luckyWheelDetail)){
            return null;
        }

        String roomId = luckyWheelDetail.getRoomId();
        String msg = JSON.toJSONString(luckyWheelDetail);
        log.info("pushLuckWheel start,roomId:{},luckyWheelDetail:{}",roomId,msg);

        longLinkService.pushCustomerRoomMsg(roomId, msg, PushEvent.lucky_wheel_info, PushToType.MESSAGE_TO_ALL);
        return luckyWheelDetail;
    }

    public LuckyWheelDetailVO getLuckyWheelDetail(Long wheelId) {
        LuckyWheelInfo luckyWheelInfo = luckyWheelManagerService.getById(wheelId);
        if (Objects.isNull(luckyWheelInfo)){
            return null;
        }


        List<LuckyWheelParticipantsInfo> luckyWheelParticipantsInfos = luckyWheelParticipantsInfoManagerService.selectByWheelId(wheelId);
        LuckyWheelDetailVO luckyWheelDetailVO = buildLuckyWheelDetailVO(luckyWheelInfo, luckyWheelParticipantsInfos);
        luckyWheelRedisService.refreshLuckyWheelInfo(luckyWheelDetailVO.getRoomId(),luckyWheelDetailVO);

        return luckyWheelDetailVO;
    }

    private LuckyWheelDetailVO buildLuckyWheelDetailVO(LuckyWheelInfo luckyWheelInfo, List<LuckyWheelParticipantsInfo> luckyWheelParticipantsInfos) {
        LuckyWheelDetailVO luckyWheelDetailVO = new LuckyWheelDetailVO();
        luckyWheelDetailVO.setId(luckyWheelInfo.getId());
        luckyWheelDetailVO.setWheelStatus(luckyWheelInfo.getWheelStatus());
        luckyWheelDetailVO.setCreateTime(luckyWheelInfo.getCreateTime());
        luckyWheelDetailVO.setCreateTimestamp(luckyWheelInfo.getCreateTime().getTime());
        luckyWheelDetailVO.setRoomId(luckyWheelInfo.getRoomId());
        luckyWheelDetailVO.setUid(luckyWheelInfo.getUid());
        luckyWheelDetailVO.setEntryFee(luckyWheelInfo.getEntryFee());

        String maxParticipants = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_MAX_PARTICIPANTS_SIZE,"10");
        luckyWheelDetailVO.setCanParticipateNumber(Integer.parseInt(maxParticipants));
        if (CollectionUtils.isEmpty(luckyWheelParticipantsInfos)){
            return luckyWheelDetailVO;
        }

        int participatingNumber = 0;
        int totalParticipateNumber = 0;
        BigDecimal totalFee = BigDecimal.ZERO;
        List<Long> userIds = new ArrayList<>();
        Map<Long,Long> disuseTimestampMap = new HashMap<>();
        long currentTimeMillis = System.currentTimeMillis();
        for (LuckyWheelParticipantsInfo luckyWheelParticipantsInfo : luckyWheelParticipantsInfos) {
            Long disuseTimestamp = luckyWheelParticipantsInfo.getDisuseTimestamp();
            if (currentTimeMillis < disuseTimestamp || disuseTimestamp == 0){
                participatingNumber++;
                userIds.add(luckyWheelParticipantsInfo.getUid());
            }
            totalParticipateNumber++;
            BigDecimal fee = luckyWheelParticipantsInfo.getFee();
            totalFee = totalFee.add(fee);
            disuseTimestampMap.put(luckyWheelParticipantsInfo.getUid(),luckyWheelParticipantsInfo.getDisuseTimestamp());
        }

        luckyWheelDetailVO.setParticipatingNumber(participatingNumber);
        luckyWheelDetailVO.setTotalParticipateNumber(totalParticipateNumber);

        luckyWheelDetailVO.setTotalFee(totalFee);
        BigDecimal commissionFee = calcCommissionFee(totalFee,SystemConfigConstant.LUCKY_WHEEL_COMMISSION_RATIO,"0.9");
        luckyWheelDetailVO.setCommissionFee(commissionFee);

        Map<Long, UserBaseInfoDTO> longUserBaseInfoDTOMap = userServerService.batchUserSummary(userIds);
        List<ParticipantsInfo> participantsInfos = new ArrayList<>();
        int index = 0;
        for (Long userId : userIds) {
            ParticipantsInfo participantsInfo = new ParticipantsInfo();
            participantsInfo.setUid(userId);
            UserBaseInfoDTO userBaseInfoDTO = longUserBaseInfoDTOMap.get(userId);
            if (Objects.nonNull(userBaseInfoDTO)){
                participantsInfo.setNick(userBaseInfoDTO.getNick());
                participantsInfo.setAvatar(userBaseInfoDTO.getAvatar());
            }
            Long disuseTimestamp = disuseTimestampMap.get(userId);
            if (Objects.isNull(disuseTimestamp) || disuseTimestamp == 0){
                participantsInfo.setDisuseTimestamp(null);
            }else {
                participantsInfo.setDisuseTimestamp(disuseTimestamp);
            }
            participantsInfo.setIndex(index);
            index++;
            participantsInfos.add(participantsInfo);
        }
        luckyWheelDetailVO.setParticipantsInfos(participantsInfos);
        return luckyWheelDetailVO;

    }

    private BigDecimal calcCommissionFee(BigDecimal totalFee,String config,String defaultValue) {
        String ratio = systemConfigService.getOrDefaultConfValueById(config,defaultValue);
        BigDecimal commissionFee = totalFee.multiply(new BigDecimal(ratio)).setScale(0, RoundingMode.DOWN);
        return commissionFee;
    }

    /**
     * 加入转盘
     * @param uid
     * @param entryFee
     * @param wheelId
     */
    public void joinLuckyWheel(Long uid, BigDecimal entryFee, Long wheelId) {
        //加入转盘防止重复
        if (luckyWheelParticipantsInfoManagerService.hasJoin(wheelId,uid)) {
            throw new ApiException(CodeEnum.CANNOT_JOIN_REPEATEDLY);
        }

        long finalCoin = entryFee.longValue();
        String bizOrderId = CommonUtil.genId();
        BillEnum billEnum = BillEnum.LUCKY_WHEEL_DEDUCT_COIN;
        log.info("joinLuckyWheel wheelId:{},uid:{}，coin:{},billEnum:{},bizOrderId:{}",
                wheelId,uid,finalCoin,billEnum,bizOrderId);
        //扣减金币，生成账单
        purseManageService.deductCoin(uid, finalCoin,billEnum, bizOrderId, "", new HashMap<>(), null, PurseRoleTypeEnum.USER.getType());

        Date date = new Date();
        LuckyWheelParticipantsInfo info = LuckyWheelParticipantsInfo.builder()
                .fee(entryFee)
                .wheelId(wheelId)
                .uid(uid)
                .createTime(date)
                .updateTime(date)
                .build();
        luckyWheelParticipantsInfoManagerService.save(info);
    }

    private LuckyWheelInfo createLuckWheelInfo(Long uid, CreateLuckyWheelReq createLuckyWheelReq) {
        String ratio = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_COMMISSION_RATIO,"0.9");
        String startRatio = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_GAME_STARTER_RATIO,"0.05");
        String maxParticipants = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_MAX_PARTICIPANTS_SIZE,"10");
        Date date = new Date();
        LuckyWheelInfo luckyWheelInfo = LuckyWheelInfo.builder()
                .commissionRatio(new BigDecimal(ratio))
                .starterRatio(new BigDecimal(startRatio))
                .maxParticipantsSize(Integer.parseInt(maxParticipants))
                .wheelStatus(LuckyWheelStatusEnums.NOT_START.getStatus())
                .entryFee(createLuckyWheelReq.getEntryFee())
                .roomId(createLuckyWheelReq.getRoomId())
                .uid(uid)
                .createTime(date)
                .build();
        luckyWheelManagerService.save(luckyWheelInfo);
        return luckyWheelInfo;
    }

    @Transactional(rollbackFor = Exception.class)
    public void doStartLuckyWheel(Long wheelId, Long uid) {
        Date date = new Date();
        //更新状态
        luckyWheelManagerService.updateStatusById(wheelId,LuckyWheelStatusEnums.RUNNING.getStatus(),date);

        //计算用户的淘汰时间
        handleUserDisuseTimestamp(wheelId,date,uid);
        //推送数据
        pushLuckWheel(wheelId);
    }



    private void handleUserDisuseTimestamp(Long wheelId, Date date, Long uid) {
        List<LuckyWheelParticipantsInfo> luckyWheelParticipantsInfos = luckyWheelParticipantsInfoManagerService.selectByWheelId(wheelId);
        Collections.shuffle(luckyWheelParticipantsInfos);
        /**
         * 转动时间
         */
        String turnTimeStr = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_TURN_TIME,"4000");
        /**
         * 展示时间
         */
        String showTimeStr = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_RESULT_SHOW_TIME,"4000");
        String beforeTimeStr = systemConfigService.getOrDefaultConfValueById(SystemConfigConstant.LUCKY_WHEEL_BEFORE_STARTING_TIME,"3000");
        Long showTime = Long.valueOf(showTimeStr);
        Long beforeTime = Long.valueOf(beforeTimeStr);
        Long turnTime = Long.valueOf(turnTimeStr);
        long currentTimestamp = date.getTime();
        int size = luckyWheelParticipantsInfos.size();
        long diff = 0l;
        for (int i = 0; i < size - 1; i++) {
            LuckyWheelParticipantsInfo info = luckyWheelParticipantsInfos.get(i);
            //首位是开始倒计时时间 + 转动时间
            if (i == 0){
                diff = beforeTime + turnTime;
            }else {
                //其它是在原来基础上加上展示时间 + 转动时间
                diff += showTime + turnTime;
            }
            long disuseTimestamp = currentTimestamp + diff;
            info.setDisuseTimestamp(disuseTimestamp);
            //最后一次不需要推送，后面已经推送了
            if (i < size - 2){
                LuckyWheelDelayMsgDTO disuseDelayMsg = getLuckyWheelDelayMsgDTO(wheelId, 2, uid);
                //将淘汰数据放入到延迟队列中，以便更新，实际延迟500ms，以免数据库未更新
                rocketMqSender.sendDeliverMessage(RocketMQTopic.LUCKY_WHEEL_DELAY_TOPIC, JSON.toJSONString(disuseDelayMsg),diff + 500L);
            }
        }
        luckyWheelParticipantsInfoManagerService.saveOrUpdateBatch(luckyWheelParticipantsInfos);

        LuckyWheelDelayMsgDTO luckyWheelDelayMsgDTO = getLuckyWheelDelayMsgDTO(wheelId, 0, uid);
        //将转盘数据放到延迟队列中
        rocketMqSender.sendDeliverMessage(RocketMQTopic.LUCKY_WHEEL_DELAY_TOPIC, JSON.toJSONString(luckyWheelDelayMsgDTO),diff);
    }

    /**
     * @param wheelId 转盘id
     * @param type    类型 0-正常结束幸运转盘游戏，1-超时未开始结束游戏,2-淘汰推送
     * @param uid
     * @return
     */
    private LuckyWheelDelayMsgDTO getLuckyWheelDelayMsgDTO(Long wheelId, int type, Long uid) {
        LuckyWheelDelayMsgDTO luckyWheelDelayMsgDTO = LuckyWheelDelayMsgDTO.builder()
                .type(type)
                .id(wheelId)
                .uid(uid)
                .build();
        return luckyWheelDelayMsgDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void doCancelLuckyWheel(Long wheelId, Integer wheelStatus, boolean pushContent, Long uid) {
        //更新状态
        luckyWheelManagerService.updateStatusById(wheelId,wheelStatus,new Date());

        List<LuckyWheelParticipantsInfo> luckyWheelParticipantsInfos = luckyWheelParticipantsInfoManagerService.selectByWheelId(wheelId);
        if (CollectionUtils.isNotEmpty(luckyWheelParticipantsInfos)){
            for (LuckyWheelParticipantsInfo luckyWheelParticipantsInfo : luckyWheelParticipantsInfos) {
                Long participantsInfoUid = luckyWheelParticipantsInfo.getUid();
                BigDecimal fee = luckyWheelParticipantsInfo.getFee();
                BillEnum billEnum = BillEnum.LUCKY_WHEEL_REFUND_COIN;
                String bizOrderId = CommonUtil.genId();
                purseManageService.addCoin(participantsInfoUid,fee.longValue(),billEnum,bizOrderId,"",new HashMap<>(),null,PurseRoleTypeEnum.USER.getType());
                log.info("doCancelLuckyWheel refund coin,wheelId:{},uid:{},fee:{},bizOrderId:{}",wheelId,participantsInfoUid,fee,bizOrderId);
            }
        }

        //推送转盘游戏消息
        LuckyWheelDetailVO luckyWheelDetailVO = pushLuckWheel(wheelId);

        //推送横幅
        if (pushContent){
            pushLuckWheelCancelContent(wheelId,luckyWheelDetailVO.getRoomId(),uid);
        }
    }

    private void pushLuckWheelCancelContent(Long wheelId, String roomId, Long uid) {
        LuckyWheelCancelContentDTO luckyWheelCancelContentDTO = LuckyWheelCancelContentDTO.builder()
                .wheelId(wheelId)
                .roomId(roomId)
                .cancelContentEn(CANCEL_CONTENT_EN)
                .cancelContentAr(CANCEL_CONTENT_AR)
                .build();

        UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);
        longLinkService.pushRoomMsg(roomId, userPB, JSON.toJSONString(luckyWheelCancelContentDTO), PushEvent.LUCKY_WHEEL_CANCEL_CONTENT, PushToType.MESSAGE_TO_ALL);

    }

    @Transactional(rollbackFor = Exception.class)
    public void finishLuckyWheel(Long wheelId) {
        LuckyWheelInfo luckyWheelInfo = checkRunningWheel(wheelId);
        if (Objects.isNull(luckyWheelInfo)){
            log.warn("wheelId:{},finishLuckyWheel end,luckyWheelInfo is null",wheelId);
            return;
        }

        List<LuckyWheelParticipantsInfo> luckyWheelParticipantsInfos = luckyWheelParticipantsInfoManagerService.selectByWheelId(wheelId);
        if (CollectionUtils.isEmpty(luckyWheelParticipantsInfos)){
            return;
        }

        Long winnerId = null;
        for (LuckyWheelParticipantsInfo luckyWheelParticipantsInfo : luckyWheelParticipantsInfos) {
            if (luckyWheelParticipantsInfo.getDisuseTimestamp().equals(0L)) {
                winnerId = luckyWheelParticipantsInfo.getUid();
                break;
            }
        }

        Long startUid = luckyWheelInfo.getUid();
        doEndLuckyWheel(winnerId,wheelId,startUid);
    }

    private LuckyWheelInfo checkRunningWheel(Long wheelId) {
        List<Integer> statusList = Arrays.asList(LuckyWheelStatusEnums.RUNNING.getStatus());
        LuckyWheelInfo luckyWheelInfo = luckyWheelManagerService.selectById(wheelId, null, statusList);
        if (Objects.isNull(luckyWheelInfo)){
            return null;
        }
        return luckyWheelInfo;
    }

    public void doEndLuckyWheel(Long winnerId, Long wheelId, Long startUid) {
        LuckyWheelDataDTO luckyWheelDataDTO = luckyWheelParticipantsInfoManagerService.sumWheelData(wheelId);
        if (Objects.isNull(luckyWheelDataDTO)){
            return;
        }

        BigDecimal totalFee = luckyWheelDataDTO.getTotalFee();
        if (Objects.isNull(totalFee)){
            return;
        }

        //赢的人收入
        BigDecimal commissionFee = calcCommissionFee(totalFee,SystemConfigConstant.LUCKY_WHEEL_COMMISSION_RATIO,"0.9");
        BillEnum billEnum = BillEnum.LUCKY_WHEEL_WIN_COIN;
        String bizOrderId = CommonUtil.genId();
        purseManageService.addCoin(winnerId,commissionFee.longValue(),billEnum,bizOrderId,"",new HashMap<>(),null,PurseRoleTypeEnum.USER.getType());
        log.info("doEndLuckyWheel winner income coin,wheelId:{},uid:{},fee:{},bizOrderId:{}",wheelId,winnerId,commissionFee,bizOrderId);

        //发起者收益
        BigDecimal startCommissionFee = calcCommissionFee(totalFee,SystemConfigConstant.LUCKY_WHEEL_GAME_STARTER_RATIO,"0.05");
        String startBizOrderId = CommonUtil.genId();
        purseManageService.addCoin(startUid,startCommissionFee.longValue(),billEnum,startBizOrderId,"",new HashMap<>(),null,PurseRoleTypeEnum.USER.getType());
        log.info("doEndLuckyWheel starter income coin,wheelId:{},uid:{},fee:{},bizOrderId:{}",wheelId,startUid,startCommissionFee,startBizOrderId);

        //更新状态
        luckyWheelManagerService.updateStatusById(wheelId,LuckyWheelStatusEnums.END.getStatus(),new Date());

        pushLuckWheel(wheelId);
    }


}
