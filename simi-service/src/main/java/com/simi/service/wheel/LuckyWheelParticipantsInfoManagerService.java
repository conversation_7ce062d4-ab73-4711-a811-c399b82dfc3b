package com.simi.service.wheel;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.dto.wheel.LuckyWheelDataDTO;
import com.simi.entity.wheel.LuckyWheelParticipantsInfo;
import com.simi.mapper.wheel.LuckyWheelParticipantsInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@RequiredArgsConstructor
@Slf4j
@Service
public class LuckyWheelParticipantsInfoManagerService extends ServiceImpl<LuckyWheelParticipantsInfoMapper, LuckyWheelParticipantsInfo> {
    private final LuckyWheelParticipantsInfoMapper luckyWheelParticipantsInfoMapper;
    public boolean hasJoin(long wheelId, long userId) {
        return this.lambdaQuery()
                .eq(LuckyWheelParticipantsInfo::getWheelId, wheelId)
                .eq(LuckyWheelParticipantsInfo::getUid, userId)
                .exists();
    }

    public LuckyWheelDataDTO sumWheelData(Long wheelId) {
        long timestamp = new Date().getTime();
        return luckyWheelParticipantsInfoMapper.sumWheelData(wheelId,timestamp);
    }

    public List<LuckyWheelParticipantsInfo> selectByWheelId(Long wheelId) {
        return this.lambdaQuery()
                .eq(LuckyWheelParticipantsInfo::getWheelId, wheelId)
                .select(LuckyWheelParticipantsInfo::getWheelId,LuckyWheelParticipantsInfo::getUid,
                        LuckyWheelParticipantsInfo::getDisuseTimestamp,LuckyWheelParticipantsInfo::getFee,LuckyWheelParticipantsInfo::getId)
                .orderByAsc(LuckyWheelParticipantsInfo::getId)
                .list();
    }
}
