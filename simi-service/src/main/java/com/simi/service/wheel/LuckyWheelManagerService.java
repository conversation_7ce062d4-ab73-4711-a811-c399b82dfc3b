package com.simi.service.wheel;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.wheel.LuckyWheelInfo;
import com.simi.mapper.wheel.LuckyWheelInfoMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@RequiredArgsConstructor
@Slf4j
@Service
public class LuckyWheelManagerService extends ServiceImpl<LuckyWheelInfoMapper, LuckyWheelInfo> {
    private final LuckyWheelInfoMapper luckyWheelInfoMapper;

    public boolean existWheel(String roomId, List<Integer> wheelStatusList) {
        return this.lambdaQuery()
                .eq(LuckyWheelInfo::getRoomId, roomId)
                .in(LuckyWheelInfo::getWheelStatus, wheelStatusList)
                .exists();
    }

    public Long getIdByRoomId(String roomId, List<Integer> statusList) {
        List<LuckyWheelInfo> list = this.lambdaQuery()
                .eq(LuckyWheelInfo::getRoomId, roomId)
                .in(LuckyWheelInfo::getWheelStatus, statusList)
                .select(LuckyWheelInfo::getId).list();
        if (CollectionUtils.isEmpty(list)){
            return null;
        }

        return list.get(0).getId();
    }

    public LuckyWheelInfo selectById(Long wheelId, Long uid, List<Integer> statusList) {
        List<LuckyWheelInfo> list = this.lambdaQuery()
                .eq(LuckyWheelInfo::getId, wheelId)
                .eq(Objects.nonNull(uid),LuckyWheelInfo::getUid,uid)
                .in(LuckyWheelInfo::getWheelStatus, statusList)
                .list();
        if (CollectionUtils.isEmpty(list)){
            return null;
        }

        return list.get(0);
    }

    public void updateStatusById(Long wheelId, Integer status,Date date) {
        this.lambdaUpdate()
                .set(LuckyWheelInfo::getWheelStatus,status)
                .set(LuckyWheelInfo::getUpdateTime,date)
                .eq(LuckyWheelInfo::getId,wheelId)
                .update();
    }
}
