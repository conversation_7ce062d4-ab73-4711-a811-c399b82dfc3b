package com.simi.service.resource;

import cn.hutool.json.JSONUtil;
import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.sts20150401.AsyncClient;
import com.aliyun.sdk.service.sts20150401.models.AssumeRoleRequest;
import com.aliyun.sdk.service.sts20150401.models.AssumeRoleResponse;
import com.simi.common.config.ResourceConfig;
import com.simi.common.dto.room.ResourceUploadParamDTO;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Locale;
import java.util.concurrent.CompletableFuture;

@Slf4j
@Service
@RequiredArgsConstructor
public class OSSResourceService {

    public ResourceUploadParamDTO getUploadParam(String path) throws ParseException {
        StaticCredentialProvider provider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(ResourceConfig.accessKey)
                .accessKeySecret(ResourceConfig.accessKeySecret)
                .build());

        AsyncClient client = AsyncClient.builder()
                .region(ResourceConfig.asyncRegion)
                .credentialsProvider(provider)
                .overrideConfiguration(
                        ClientOverrideConfiguration.create().setEndpointOverride(ResourceConfig.endpointOverride))
                .build();

        AssumeRoleRequest assumeRoleRequest = AssumeRoleRequest.builder()
                .roleArn(ResourceConfig.roleArn)
                .roleSessionName(ResourceConfig.roleSessionName)
                .build();

        CompletableFuture<AssumeRoleResponse> response = client.assumeRole(assumeRoleRequest);
        AssumeRoleResponse resp = null;
        try {
            resp = response.get();
            log.info("aliyun upload param resp:[{}]", JSONUtil.toJsonStr(resp));
        } catch (Exception e) {
            log.error("get aliyun assumeRole is error[{}]", e.getMessage(), e);
        }
        client.close();

        assert resp != null;
        var data = resp.getBody().getCredentials();

        var expiration = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssX").parse(data.getExpiration());

        return ResourceUploadParamDTO.builder()
                .endpoint(ResourceConfig.endpoint)
                .accessKeyId(data.getAccessKeyId())
                .accessKeySecret(data.getAccessKeySecret())
                .securityToken(data.getSecurityToken())
                .expiration(
                        expiration.toInstant()
                                .atZone(ZoneId.systemDefault())
                                .toLocalDateTime()
                                .format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")
                                        .withLocale(Locale.CHINA)))
                .bucket(ResourceConfig.bucket)
                .region(ResourceConfig.region)
                .domain(ResourceConfig.domain)
                .path(path)
                .build();
    }
}
