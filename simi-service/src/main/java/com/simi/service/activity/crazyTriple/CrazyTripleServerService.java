package com.simi.service.activity.crazyTriple;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.google.common.base.Splitter;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.crazyTriple.CrazyTripleGameJoinStatusEnum;
import com.simi.common.constant.crazyTriple.CrazyTripleGameStatusEnum;
import com.simi.common.constant.crazyTriple.CrazyTripleProductTypeEnum;
import com.simi.common.constant.rank.RankFrequencyEnum;
import com.simi.common.constant.rank.RankFrequencyTypeEnum;
import com.simi.common.dto.activity.crazyTriple.CurGameInfoDTO;
import com.simi.common.dto.rank.RankKeyParam;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.DelayQueueKey;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedisDelayedQueue;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.redis.message.CrazyTripleToLotteryMsg;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.GsonUtil;
import com.simi.common.vo.activity.crazyTriple.GameInfoVO;
import com.simi.common.vo.activity.crazyTriple.JoinGameVO;
import com.simi.common.vo.req.activity.crazyTriple.GameRequest;
import com.simi.common.vo.req.activity.crazyTriple.WinRankReqReq;
import com.simi.config.CrazyTripleConfig;
import com.simi.constant.ActivityRedisKey;
import com.simi.constant.BillEnum;
import com.simi.constant.crazyTriple.CrazyTripleGameStageEnum;
import com.simi.dto.crazyTriple.CrazyTriplePriceConf;
import com.simi.dto.crazyTriple.CrazyTripleProductConf;
import com.simi.dto.crazyTriple.RankInfoDTO;
import com.simi.dto.crazyTriple.WinRankDTO;
import com.simi.entity.crazyTriple.ActivityCrazyTripleGame;
import com.simi.entity.crazyTriple.ActivityCrazyTripleJoinRecord;
import com.simi.manager.crazyTriple.CrazyTripleCacheManager;
import com.simi.service.activity.rank.RankComponent;
import com.simi.service.purse.PurseManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;


/**
 * @Description: 疯狂Triple
 * @Author: Yibo Liu
 * @Date: 2024-05-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrazyTripleServerService {

    private final CrazyTripleConfService crazyTripleConfService;
    private final PurseManageService purseManageService;
    private final CrazyTripleCacheManager crazyTripleCacheManager;
    private final ActivityCrazyTripleGameService crazyTripleGameService;
    private final CrazyTripleMsgService crazyTripleMsgService;
    private final RedissonDistributionLocker distributionLocker;
    private final ActivityCrazyTripleJoinRecordService crazyTripleJoinRecordService;
    private final RankComponent rankComponent;
    private final RedisDelayedQueue redisDelayedQueue;

    public boolean isGameOpen() {
        return DateTimeUtil.isBetweenTime(NORM_DATETIME_PATTERN, CrazyTripleConfig.startTime, CrazyTripleConfig.endTime);
    }

    public GameInfoVO getGameInfo(long uid, String lang) {
        GameInfoVO gameInfoVO = new GameInfoVO();
        // 价格档位配置
        List<GameInfoVO.PriceInfo> priceInfos = buildPriceInfoList();
        // 商品配置
        List<GameInfoVO.Product> products = buildProductList(lang);
        gameInfoVO.setPriceInfo(priceInfos);
        gameInfoVO.setProduct(products);
        PurseDTO purse = purseManageService.getPurse(uid);
        long currencyBalance = purse.getCoin();
        gameInfoVO.setCurrencyBalance(currencyBalance);
        return gameInfoVO;
    }

    public CurGameInfoDTO getCurGameInfo(long uid, String lang) {
        ActivityCrazyTripleGame game = getCurGame();
        if (game != null) {
            long gameId = game.getId();
            CurGameInfoDTO curGame = crazyTripleMsgService.buildCurGame(game);

            // 游戏状态如果是投注阶段或者停止投注阶段，获取用户的投注记录
            Collection<String> betList = (curGame.getGameStage() ==
                    CrazyTripleGameStageEnum.CRAZY_TRIPLE_GAME_STAGE_NONE.getValue()
                    || curGame.getGameStage() == CrazyTripleGameStageEnum.CRAZY_TRIPLE_GAME_STAGE_BREAK_TIME.getValue())
                    ? null : crazyTripleCacheManager.getBetList(gameId);
            Map<Integer, List<CurGameInfoDTO.BetItem>> betMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(betList)) {
                for (String str : betList) {
                    List<String> stringList = Splitter.on(CrazyTripleCacheManager.SPLITTER).splitToList(str);
                    long userId = Long.parseLong(stringList.get(0));
                    int productType = Integer.parseInt(stringList.get(1));
                    int priceId = Integer.parseInt(stringList.get(2));
                    int joinAmount = Integer.parseInt(stringList.get(3));
                    CurGameInfoDTO.BetItem item = new CurGameInfoDTO.BetItem();
                    item.setUid(userId);
                    item.setProductType(productType);
                    item.setPriceId(priceId);
                    item.setPrice(joinAmount);

                    List<CurGameInfoDTO.BetItem> betPbList = betMap.get(productType);
                    if (betPbList == null) {
                        betPbList = new ArrayList<>();
                    }
                    betPbList.add(item);
                    betMap.put(productType, betPbList);
                }
            }
            curGame.setBetSummary(new ArrayList<>());
            curGame.setBetItem(new ArrayList<>());
            for (CrazyTripleProductTypeEnum productTypeEnum : CrazyTripleProductTypeEnum.values()) {
                int productType = productTypeEnum.getProductType().getValue();
                // 总投注额
                long totalAmount = crazyTripleCacheManager.getJoinAmountTotal(gameId, productType);
                // 用户投注额
                long userAmount = crazyTripleCacheManager.getJoinAmount(gameId, productType, uid);
                // 投注概要
                CurGameInfoDTO.BetSummary betSummary = new CurGameInfoDTO.BetSummary();
                betSummary.setProductType(productTypeEnum.getProductType().getValue());
                betSummary.setAllCurrencyAmount(totalAmount);
                betSummary.setCurrencyAmount(userAmount);
                List<CurGameInfoDTO.BetSummary> betSummaryList = curGame.getBetSummary();

                betSummaryList.add(betSummary);
                // 投注明细
                List<CurGameInfoDTO.BetItem> betItems = betMap.get(productType);
                curGame.setBetItem(Objects.requireNonNullElseGet(betItems, ArrayList::new));
            }
            return curGame;
        }
        log.error("getCurGameInfo failed, game is null");
        return new CurGameInfoDTO();
    }

    /**
     * 获取当前游戏信息，优先查缓存
     *
     * @return ActivityCrazyTripleGame
     */
    public ActivityCrazyTripleGame getCurGame() {
        ActivityCrazyTripleGame game = crazyTripleCacheManager.getCurGameInfo();
        if (game == null) {
            game = crazyTripleGameService.getLastGame();
        }
        return game;
    }

    public JoinGameVO joinGame(GameRequest req, long uid, String lang) {
        long gameId = req.getGameId();
        int productType = req.getProductType();
        int priceId = req.getPriceId();
        if (!isGameOpen()) {
            log.warn("[crazyTriple] joinGame fail gameClose uid:{},gameId:{},productType:{},priceId:{}", uid, gameId, productType, priceId);
            throw new ApiException(CodeEnum.ACTIVITY_IS_OUTDATED);
        }

        // 获取商品信息
        CrazyTripleProductConf productConf = crazyTripleConfService.getCrazyTripleProductConf(productType);
        if (productConf == null) {
            log.warn("[crazyTriple] joinGame fail productConf null uid:{},gameId:{},productType:{},priceId:{}", uid, gameId, productType, priceId);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        // 获取价格档位信息
        CrazyTriplePriceConf priceConf = crazyTripleConfService.getCrazyTriplePriceConf(priceId);
        if (priceConf == null) {
            log.warn("[crazyTriple] joinGame fail priceConf null uid:{},gameId:{},productType:{},priceId:{}", uid, gameId, productType, priceId);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        int price = priceConf.getPrice();
        if (price <= 0) {
            log.warn("[crazyTriple] joinGame fail price illegal uid:{},gameId:{},productType:{},priceId:{},price:{}", uid, gameId, productType, priceId, price);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        ActivityCrazyTripleGame game = getCurGame();
        if (game == null || game.getStatus() != CrazyTripleGameJoinStatusEnum.CRAZY_TRIPLE_GAME_JOIN_STATUS_INIT.getValue()) {
            log.warn("[crazyTriple] joinGame fail status illegal uid:{},gameId:{},productType:{},priceId:{},price:{},status:{}", uid, game == null ? gameId : game.getId(), productType, priceId, price, game == null ? -1 : game.getStatus());
            throw new ApiException(CodeEnum.ACTIVITY_GAME_BET_NOT_START);
        }
        gameId = game.getId();

        long timeToJoin = getTimeToJoin(game);
        if (timeToJoin <= 0) {
            log.warn("[crazyTriple] joinGame time limit first uid:{},gameId:{},productType:{},priceId:{},price:{},timeToJoin:{}", uid, gameId, productType, priceId, price, timeToJoin);
            throw new ApiException(CodeEnum.ACTIVITY_GAME_BET_END);
        }
        Date nowTime = new Date();
        String traceId = IdUtil.getSnowflakeNextIdStr();
        ActivityCrazyTripleJoinRecord record;
        log.info("[crazyTriple] joinGame tryLock traceId:{},uid:{},gameId:{},productType:{},priceId:{},price:{},waitTime:{}", traceId, uid, gameId, productType, priceId, price, CrazyTripleConfig.joinGameLockWaitMillis);
        try {
            try (Locker locker = distributionLocker.tryLock(ActivityRedisKey.crazyTripleJoinLock(uid), CrazyTripleConfig.joinGameLockWaitMillis)) {
                if (Objects.isNull(locker)) {
                    log.warn("[crazyTriple] joinGame get lock null traceId:{},uid:{},gameId:{},productType:{},priceId:{},price:{}", traceId, uid, gameId, productType, priceId, price);
                    throw new ApiException(CodeEnum.SERVER_BUSY);
                }
                PurseDTO purse = purseManageService.getPurse(uid);
                long currencyBalance = purse.getCoin();
                if (currencyBalance < price) {
                    log.warn("[crazyTriple] joinGame fail balance not enough uid:{},gameId:{},productType:{},priceId:{},price:{},balance:{}", uid, gameId, productType, priceId, price, currencyBalance);
                    throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
                }
                timeToJoin = getTimeToJoin(game);
                if (timeToJoin <= 0) {
                    log.warn("[crazyTriple] joinGame time limit getLock uid:{},gameId:{},productType:{},priceId:{},price:{},timeToJoin:{}", uid, gameId, productType, priceId, price, timeToJoin);
                    throw new ApiException(CodeEnum.ACTIVITY_GAME_BET_END);
                }
                long joinAmount = crazyTripleCacheManager.getJoinAmount(gameId, productType, uid);
                log.info("[crazyTriple] start traceId:{},uid:{},gameId:{},productType:{},priceId:{},price:{},joinAmount:{}", traceId, uid, gameId, productType, priceId, price, joinAmount);
                record = ActivityCrazyTripleJoinRecord.builder()
                        .gameId(gameId)
                        .userId(uid)
                        .productType(productType)
                        .productCount(1)
                        .priceId(priceId)
                        .currencyAmount((long) price)
                        .status(CrazyTripleGameJoinStatusEnum.CRAZY_TRIPLE_GAME_JOIN_STATUS_INIT.getValue())
                        .createTime(nowTime)
                        .modifyTime(nowTime)
                        .build();
                crazyTripleJoinRecordService.save(record);
                // 扣减余额
                boolean paySuc = deductCoin(record, traceId);
                if (!paySuc) {
                    log.error("[crazyTriple] joinGame deductCoin fail traceId:{},uid:{},gameId:{},productType:{},priceId:{},price:{}", traceId, uid, gameId, productType, priceId, price);
                    throw new ApiException(CodeEnum.SERVER_ERROR);
                }
            }
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.warn("[crazyTriple] joinGame get lock error traceId:{},uid:{},gameId:{},productType:{},priceId:{},price:{}", traceId, uid, gameId, productType, priceId, price, e);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        if (record != null) {
            // 刷新缓存
            // 个人投注额度
            crazyTripleCacheManager.addJoinAmount(gameId, productType, uid, price);
            // 总额
            crazyTripleCacheManager.addJoinAmountTotal(gameId, productType, price);
            // 周榜
            crazyTripleCacheManager.addRankJoin(uid, price, nowTime.getTime());
            boolean isAddTop3 = false;
            if (crazyTripleCacheManager.isDayTop3User(uid)) {
                boolean existLock = crazyTripleCacheManager.existLockPushTop3UserBet();
                if (existLock) {
                    isAddTop3 = true;
                } else {
                    // 更新 前 3 名数据
                    crazyTripleMsgService.pushTop3JoinInfo(record);
                }
            }

            crazyTripleCacheManager.addBetInfo(gameId, record, isAddTop3);
        }

        JoinGameVO joinGameVO = new JoinGameVO();
        PurseDTO purse = purseManageService.getPurse(uid);
        long currencyBalance = purse.getCoin();
        joinGameVO.setCurrencyBalance(currencyBalance);
        return joinGameVO;
    }

    public WinRankDTO winRank(WinRankReqReq req, long uid, String lang) {
        int frequencyType = req.getFrequencyType();
        // 安全地转成long  long targetUid = req.getTargetUid();
        long targetUid = req.getTargetUid();

        RankFrequencyEnum rankFrequencyEnum = RankFrequencyEnum.getTargetEnum(RankFrequencyTypeEnum.getByType(frequencyType));
        if (rankFrequencyEnum == null) {
            log.error("[crazyTriple] winRank rankFrequency param illegal uid:{},targetUid:{},frequencyType:{}", uid, targetUid, frequencyType);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        targetUid = targetUid <= 0 ? uid : targetUid;
        WinRankDTO winRankDTO = new WinRankDTO();
        winRankDTO.setRanks(new ArrayList<>());
        RankKeyParam rankKeyParam = RankKeyParam.builder()
                .keyPrefix(ActivityRedisKey.crazyTripleRankReward())
                .keyParam("")
                .timeMillis(System.currentTimeMillis())
                .rankFrequencyEnum(rankFrequencyEnum)
                .build();
        String rankKey = rankComponent.getRankKey(rankKeyParam);
        RankInfoDTO targetRank = null;
        List<RankInfoDTO> rankList = crazyTripleCacheManager.getRankRewardCache(rankKey, 0, 99);
        if (!CollectionUtils.isEmpty(rankList)) {
            winRankDTO.setRanks(rankList);
            final Map<Long, RankInfoDTO> map = rankList.stream().filter(x -> x.getUser().getUid() > 0)
                    .collect(Collectors.toMap(x -> x.getUser().getUid(), x -> x));
            targetRank = map.get(targetUid);
        }
        if (targetRank == null) {
            targetRank = rankComponent.getUserRankInfoPB(rankKey, String.valueOf(targetUid));
        }
        if (targetRank != null) {
            winRankDTO.setTargetUserRank(targetRank);
        }
        return winRankDTO;
    }

    /**
     * 获取当前游戏，优先查缓存
     *
     * @return long
     */
    public long startGameByTask() {
        boolean existLockStartGame = crazyTripleCacheManager.existLockStartNextGame();
        if (existLockStartGame) {
            log.warn("[crazyTriple] startGame byTask get lock fail");
            return 0L;
        }
        return startGame(System.currentTimeMillis());
    }

    /**
     * 获取当前游戏，优先查缓存
     *
     * @return
     */
    public long startGame(long startTimeMillis) {

        // 创建一个游戏局，并不是结算逻辑
        if (!isGameOpen()) {
            log.warn("[crazyTriple] startGame fail close");
            return 0L;
        }

        try (Locker locker = distributionLocker.lock(ActivityRedisKey.crazyTripleStartGameLock())) {
            if (Objects.isNull(locker)) {
                log.warn("[crazyTriple] joinGame get lock null startTimeMillis:{}", startTimeMillis);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            ActivityCrazyTripleGame game = crazyTripleGameService.getLastGame();
            boolean isExistGameNotEnd = true;
            if (game == null || (game.getStatus() == CrazyTripleGameStatusEnum.CRAZY_TRIPLE_GAME_STATUS_END.getValue())
                    || game.getRetryNum() > CrazyTripleConfig.endFailRetryNum) {
                isExistGameNotEnd = false;
            }

            if (isExistGameNotEnd) {
                log.warn("crazyTriple startGame fail nowGame:{}", GsonUtil.getGson().toJson(game));
                return 0L;
            }
            Date nowTime = new Date();
            ActivityCrazyTripleGame newGame = ActivityCrazyTripleGame.builder()
                    .status(CrazyTripleGameStatusEnum.CRAZY_TRIPLE_GAME_STATUS_IN_PROGRESS.getValue())
                    .retryNum(0)
                    .duration((int) (CrazyTripleConfig.betTimeMillis / 1000))
                    .endTime(DateUtil.offsetMillisecond(nowTime, (int) CrazyTripleConfig.betTimeMillis).toJdkDate())
                    .createTime(nowTime)
                    .modifyTime(nowTime)
                    .build();
            crazyTripleGameService.save(newGame);
            crazyTripleCacheManager.setCurGameId(newGame.getId());
            crazyTripleCacheManager.setGameInfo(newGame);
            crazyTripleCacheManager.lockStartNextGame(newGame.getId());
            log.info("crazyTriple startGame suc newGame:{}", GsonUtil.getGson().toJson(newGame));

            // 推送开始游戏的消息
            crazyTripleMsgService.pushStartGame(newGame);

            long delayTimeMillis = CrazyTripleConfig.betTimeMillis + CrazyTripleConfig.startReserveTimeMillis + CrazyTripleConfig.joinReserveTimeMillis + CrazyTripleConfig.lotteryReserveTimeMillis;
            CrazyTripleToLotteryMsg delayMsg = CrazyTripleToLotteryMsg.builder()
                    .id(newGame.getId())
                    .duration(newGame.getDuration())
                    .status(newGame.getStatus())
                    .endTimeMillis(newGame.getEndTime().getTime())
                    .createTimeMillis(newGame.getCreateTime().getTime())
                    .build();
            redisDelayedQueue.add(delayMsg, delayTimeMillis, TimeUnit.MILLISECONDS,
                    DelayQueueKey.crazy_triple_to_lottery.getKey());
            return newGame.getId();
        } catch (Exception e) {
            log.error("crazyTriple startGame error error:{}", ExceptionUtil.formatEx(e));
        }
        return 0L;
    }

    private long getTimeToJoin(ActivityCrazyTripleGame game) {
        return (game.getEndTime().getTime() + CrazyTripleConfig.startReserveTimeMillis + CrazyTripleConfig.joinReserveTimeMillis) - System.currentTimeMillis();
    }

    /**
     * 构建价格档位配置
     * @return 价格档位配置
     */
    private List<GameInfoVO.PriceInfo> buildPriceInfoList() {
        List<CrazyTriplePriceConf> list = crazyTripleConfService.getCrazyTriplePriceConfList();
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<GameInfoVO.PriceInfo> priceInfoList = new ArrayList<>();
        for (CrazyTriplePriceConf item : list) {
            if (item.getPrice() <= 0) {
                continue;
            }
            GameInfoVO.PriceInfo priceInfo = new GameInfoVO.PriceInfo();
            priceInfo.setPriceId(item.getPriceId());
            priceInfo.setPrice(item.getPrice());
            priceInfo.setImgUrl(item.getImgUrl());
            priceInfoList.add(priceInfo);
        }
        return priceInfoList;
    }

    private List<GameInfoVO.Product> buildProductList(String lang) {
        List<CrazyTripleProductConf> list = crazyTripleConfService.getCrazyTripleProductConfList();
        if (CollectionUtil.isEmpty(list)) {
            return new ArrayList<>();
        }
        List<GameInfoVO.Product> productList = new ArrayList<>();
        for (CrazyTripleProductConf item : list) {
            GameInfoVO.Product product = new GameInfoVO.Product();
            product.setProductType(item.getProductType());
            product.setMultiple(item.getMultiple());
            product.setImgUrl(item.getImgUrl());
            product.setName(item.getName() == null ? ""
                    : StringUtils.defaultIfEmpty(item.getName().get(lang), item.getName().get("en")));
           productList.add(product);
        }
        return productList;
    }

    private boolean deductCoin(ActivityCrazyTripleJoinRecord item, String traceId) {
        try {
            // 扣除金币
            purseManageService.deductCoin(item.getUserId(), item.getCurrencyAmount(),
                    BillEnum.CRAZY_TRIPLE_DEDUCT, CommonUtil.genId(), "", Collections.emptyMap(), 0L, PurseRoleTypeEnum.USER.getType());
            log.info("crazyTriple deductCoin end traceId:{},item:{}", traceId, GsonUtil.getGson().toJson(item));
            item.setStatus(CrazyTripleGameJoinStatusEnum.CRAZY_TRIPLE_GAME_JOIN_STATUS_SUCCESS.getValue());
            item.setModifyTime(new Date());
            crazyTripleJoinRecordService.updateById(item);
            return true;
        } catch (ApiException ae) {
            log.error("crazyTriple deductCoin ae traceId:{},code:{},item:{}", traceId, ae.getResponseCode(), GsonUtil.getGson().toJson(item), ae);
            throw ae;
        } catch (Exception e) {
            log.error("crazyTriple deductCoin error traceId:{},item:{}", traceId, GsonUtil.getGson().toJson(item), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }
}




