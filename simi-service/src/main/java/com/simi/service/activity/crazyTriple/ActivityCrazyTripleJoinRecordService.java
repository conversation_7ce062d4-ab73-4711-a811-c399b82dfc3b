package com.simi.service.activity.crazyTriple;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.crazyTriple.CrazyTripleGameJoinStatusEnum;
import com.simi.entity.crazyTriple.ActivityCrazyTripleJoinRecord;
import com.simi.mapper.crazyTriple.ActivityCrazyTripleJoinRecordMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【activity_crazy_triple_join_record(疯狂triple参与记录表)】的数据库操作Service实现
* @createDate 2024-05-28 10:55:29
*/
@Service
public class ActivityCrazyTripleJoinRecordService extends ServiceImpl<ActivityCrazyTripleJoinRecordMapper, ActivityCrazyTripleJoinRecord> {
    public List<ActivityCrazyTripleJoinRecord> getJoinRecordList(long gameId) {
        LambdaQueryWrapper<ActivityCrazyTripleJoinRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityCrazyTripleJoinRecord::getGameId, gameId);
        queryWrapper.eq(ActivityCrazyTripleJoinRecord::getStatus, CrazyTripleGameJoinStatusEnum.CRAZY_TRIPLE_GAME_JOIN_STATUS_SUCCESS.getValue());
        return list(queryWrapper);
    }
    

    public boolean updateJoinRecordSettle(List<Long> joinRecordIdList) {
        LambdaUpdateWrapper<ActivityCrazyTripleJoinRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(ActivityCrazyTripleJoinRecord::getStatus, CrazyTripleGameJoinStatusEnum.CRAZY_TRIPLE_GAME_JOIN_STATUS_SETTLE.getValue());
        updateWrapper.in(ActivityCrazyTripleJoinRecord::getId, joinRecordIdList);
        updateWrapper.eq(ActivityCrazyTripleJoinRecord::getStatus, CrazyTripleGameJoinStatusEnum.CRAZY_TRIPLE_GAME_JOIN_STATUS_SUCCESS.getValue());
        return update(updateWrapper);
    }
}




