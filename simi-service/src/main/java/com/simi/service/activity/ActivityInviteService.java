package com.simi.service.activity;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.constant.*;
import com.simi.common.constant.rank.RankFrequencyEnum;
import com.simi.common.constant.resource.ResourceTypeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.activity.invite.InviteLevelConfig;
import com.simi.common.dto.activity.prize.PrizeDTO;
import com.simi.common.dto.rewardPack.RewardPackResourceDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.DateTimeRange;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.common.vo.activity.common.BaseRankUserVO;
import com.simi.common.vo.activity.invite.ActivityInviteVO;
import com.simi.common.vo.activity.invite.InviteClaimVO;
import com.simi.constant.ActivityRedisKey;
import com.simi.constant.InviteRedisKey;
import com.simi.message.GiftSendMessage;
import com.simi.service.activity.handle.AbstractActivityWrapper;
import com.simi.service.activity.handle.ActivityBaseService;
import com.simi.service.invite.InviteServerService;
import com.simi.service.rewardpack.RewardPackServerService;
import com.simi.service.user.UserServerService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 邀请代理达标活动service
 *
 * <AUTHOR>
 * @date 2024/05/15 16:35
 **/
@Slf4j
@Service
public class ActivityInviteService extends AbstractActivityWrapper implements ActivityBaseService {

    RedissonClient redissonClient;
    RedissonManager redissonManager;
    UserServerService userServerService;
    SystemConfigService systemConfigService;
    @Resource
    private InviteServerService inviteServerService;
    @Resource
    private RewardPackServerService rewardPackServerService;
    @Resource
    private TaskExecutor taskExecutor;

    private static final Integer ROLE_ANCHOR = 1;
    private static final Integer ROLE_AGENT = 2;


    public ActivityInviteService(RedissonClient redissonClient, RedissonManager redissonManager,
                                 UserServerService userServerService, SystemConfigService systemConfigService) {
        this.redissonClient = redissonClient;
        this.redissonManager = redissonManager;
        this.userServerService = userServerService;
        this.systemConfigService = systemConfigService;
    }

    @Override
    public Pair<Date, Date> timeRange() {
        DateTimeRange dateTimeRange = actTime();
        if (dateTimeRange == null) return null;
        return Pair.of(DateUtil.offsetHour(dateTimeRange.getBeginTime(), -3),
                DateUtil.offsetHour(dateTimeRange.getEndTime(), -3));
    }

    /**
     * 时间###### 以东三的维度定为开始结束时间
     *
     * @return
     */
    @Nullable
    private DateTimeRange actTime() {
        String actTimeStr = systemConfigService.getSysConfValueById(SystemConfigConstant.ACT_ANCHOR_INVITE_TIME);
        DateTimeRange dateTimeRange = dateTimeRange(actTimeStr);
        if (Objects.isNull(dateTimeRange)) {
            log.error("activity invite time config is empty");
            return null;
        }
        return dateTimeRange;
    }

    @Override
    public ActivityFlag activityFlag() {
        return ActivityFlag.ACT_ANCHOR_INVITE;
    }

    @Override
    public void handleGiftData(GiftSendMessage giftSendMessage) {
        Long targeUid = giftSendMessage.getTargeUid();
        DateTime dateTime = DateUtil.offsetHour(giftSendMessage.getSendTime(), 3);
        Integer totalDiamond = giftSendMessage.getTotalDiamond();
        if (totalDiamond <= 0) {
            log.info("activity anchor invite service totalDiamond zero.");
            return;
        }
        TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3);
        String dataStr = RankFrequencyEnum.beginDataOfType(timeZoneDateDTO.getRecordTime(), RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK);
        String anchorKey = ActivityRedisKey.invite_diamond_anchor.getKey(dataStr);
        log.info("activity anchor invite targetUid[{}] totalDiamond[{}] giftId[{}]", targeUid, totalDiamond, giftSendMessage.getGiftId());
        // 主播
        List<InviteLevelConfig> anchorConfigs = getConfig(ROLE_ANCHOR);
        Double anchorDiamondAft = redissonManager.zIncrement(anchorKey, Double.valueOf(totalDiamond), String.valueOf(targeUid));
        int levelAfter = 0;
        Optional<InviteLevelConfig> levelConfigAft = levelByDiamond(anchorDiamondAft.longValue(), anchorConfigs);
        if (levelConfigAft.isPresent()) {
            levelAfter = levelConfigAft.get().getLevel();
        }
        int levelBefore = 0;
        Optional<InviteLevelConfig> levelConfigBefore = levelByDiamond(anchorDiamondAft.longValue() - totalDiamond, anchorConfigs);
        if (levelConfigBefore.isPresent()) {
            levelBefore = levelConfigBefore.get().getLevel();
        }
        if (!Objects.equals(levelAfter, levelBefore)) {
            // 跨级记录时间, 时间为E3
            updateUpperRecord(dataStr, ROLE_ANCHOR, levelAfter, targeUid, dateTime.getTime(), anchorConfigs);
        }

        // 代理
        Long superiorUid = inviteServerService.superiorInvite(targeUid);
        if (Objects.nonNull(superiorUid)) {
            log.info("activity anchor invite agent targetUid[{}] totalDiamond[{}] superiorUid[{}]", targeUid, totalDiamond, superiorUid);
            String agentKey = ActivityRedisKey.invite_diamond_agent.getKey(dataStr);
            Double agentDiamondAft = redissonManager.zIncrement(agentKey, Double.valueOf(totalDiamond), String.valueOf(superiorUid));
            List<InviteLevelConfig> agentConfigs = getConfig(ROLE_AGENT);
            Integer agentLevelAfter = 0;
            Optional<InviteLevelConfig> levelConfigAgentAft = levelByDiamond(agentDiamondAft.longValue(), agentConfigs);
            if (levelConfigAgentAft.isPresent()) {
                agentLevelAfter = levelConfigAgentAft.get().getLevel();
            }
            Integer agentLevelBefore = 0;
            Optional<InviteLevelConfig> levelConfigAgentBefore = levelByDiamond(agentDiamondAft.longValue() - totalDiamond, anchorConfigs);
            if (levelConfigAgentBefore.isPresent()) {
                agentLevelBefore = levelConfigAgentBefore.get().getLevel();
            }
            if (!Objects.equals(agentLevelAfter, agentLevelBefore)) {
                updateUpperRecord(dataStr, ROLE_AGENT, agentLevelAfter, superiorUid, dateTime.getTime(), agentConfigs);
            }
        }
    }


    public ActivityInviteVO info(Long uid, Integer type) {
        ActivityInviteVO result = ActivityInviteVO.builder().build();

        String redisKey;
        TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3);
        String timeAttach = RankFrequencyEnum.beginDataOfType(timeZoneDateDTO.getRecordTime(), RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK);

        if (Objects.equals(type, ROLE_ANCHOR)) {
            redisKey = ActivityRedisKey.invite_diamond_anchor.getKey(timeAttach);
        } else if (Objects.equals(type, ROLE_AGENT)) {
            redisKey = ActivityRedisKey.invite_diamond_agent.getKey(timeAttach);
        } else {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Double diamondVal = redissonManager.zScore(redisKey, String.valueOf(uid));
        Long myDiamond = Objects.isNull(diamondVal) ? 0L : diamondVal.longValue();
        result.setDiamondVal(myDiamond);

        InviteLevelConfig nextLevel = nextLevel(myDiamond, type);
        result.setNextLevelName(nextLevel.getLevelName());
        result.setGapNextLevel(nextLevel.getDiamondVal() - myDiamond);

        Long countdown = RankFrequencyEnum.countdown(timeZoneDateDTO.getRecordTime(), RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK);
        result.setCountdown(countdown / 1000);
        return result;
    }

    public void claimByType(Long uid, String dateKey, Integer roleType) {
        DateTime parse;
        try {
            parse = DateUtil.parse(dateKey, DatePattern.PURE_DATE_PATTERN);
        } catch (Exception e) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3);
        if (parse.getTime() > timeZoneDateDTO.getRecordTime().getTime()) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        String timeAttach = RankFrequencyEnum.beginDataOfType(parse, RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK);
        String redisKey = null;
        String recordKey = null;
        if (Objects.equals(roleType, ROLE_ANCHOR)) {
            redisKey = ActivityRedisKey.invite_diamond_anchor_level_record.getKey(timeAttach);
            recordKey = ActivityRedisKey.invite_diamond_anchor_claim_record.getKey(timeAttach);
        } else if (Objects.equals(roleType, ROLE_AGENT)) {
            redisKey = ActivityRedisKey.invite_diamond_agent_level_record.getKey(timeAttach);
            recordKey = ActivityRedisKey.invite_diamond_agent_claim_record.getKey(timeAttach);
        }
        claimPrize(uid, dateKey, redisKey, recordKey, roleType);
    }


    public ListWithTotal<InviteClaimVO> claimRecord(Long uid, Integer roleType, Integer pageNum, Integer pageSize) {
        DateTimeRange dateTimeRange = actTime();
        if (Objects.isNull(dateTimeRange)) {
            return ListWithTotal.empty();
        }

        // 活动开始的东三时间
        Date beginTime = dateTimeRange.getBeginTime();
        Date endTime = dateTimeRange.getEndTime();

        // 当前东三时间
        Date recordTime = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3).getRecordTime();
        recordTime = DateUtil.offsetWeek(recordTime, -1);
        // 已经结束
        if (endTime.before(recordTime)) {
            recordTime = endTime;
        }
        boolean sameWeek = DateUtil.isSameWeek(beginTime, recordTime, false);
        // 同一周, 刚上无领取数据
        if (sameWeek) {
            return ListWithTotal.empty();
        }

        List<DateTime> dateTimeList = DateUtil.rangeFunc(beginTime, recordTime, DateField.WEEK_OF_MONTH, e -> DateUtil.beginOfWeek(e, false));
        dateTimeList.add(DateUtil.beginOfWeek(recordTime, false));
        Date now = new Date();
        List<String> timeRecords = dateTimeList
                .stream().map(e -> DateUtil.format(e, DatePattern.PURE_DATE_PATTERN))
                .filter(e -> !StrUtil.equals(e, DateUtil.format(DateUtil.beginOfWeek(now, false), DatePattern.PURE_DATE_PATTERN)))
                .distinct()
                .sorted(Comparator.reverseOrder()).toList();

        ActivityRedisKey levelKey;
        ActivityRedisKey recordKey;
        ActivityRedisKey diamondKey;
        List<InviteLevelConfig> configs;
        if (Objects.equals(roleType, ROLE_ANCHOR)) {
            diamondKey = ActivityRedisKey.invite_diamond_anchor;
            levelKey = ActivityRedisKey.invite_diamond_anchor_level_record;
            recordKey = ActivityRedisKey.invite_diamond_anchor_claim_record;
            configs = getConfig(ROLE_ANCHOR);
        } else if (Objects.equals(roleType, ROLE_AGENT)) {
            diamondKey = ActivityRedisKey.invite_diamond_agent;
            levelKey = ActivityRedisKey.invite_diamond_agent_level_record;
            recordKey = ActivityRedisKey.invite_diamond_agent_claim_record;
            configs = getConfig(ROLE_AGENT);
        } else {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        String uidStr = String.valueOf(uid);

        List<InviteClaimVO> vos = timeRecords.stream().map(time -> {
            String recordStr = redissonManager.hGet(levelKey.getKey(time), String.valueOf(uid));
            if (StrUtil.isBlank(recordStr)) {
                return null;
            }

            Map<String, String> recordMap = JSONUtil.toBean(recordStr, Map.class);
            Optional<Map.Entry<String, String>> max = recordMap.entrySet()
                    .stream().max(Map.Entry.comparingByValue());
            if (max.isEmpty()) {
                return null;
            }
            Map.Entry<String, String> maxLevel = max.get();
            List<String> split = StrUtil.split(maxLevel.getKey(), StrUtil.C_COLON);
            String levelStr = split.get(0);
            String packIdStr = split.get(1);
            Optional<InviteLevelConfig> levelConfig = levelByLevel(Integer.valueOf(levelStr), configs);
            if (levelConfig.isEmpty()) {
                return null;
            }
            DateTime parse = DateUtil.parse(time, DatePattern.PURE_DATE_PATTERN);
            //Integer packId = levelConfig.map(InviteLevelConfig::getPrizePackId).orElse(null);
            Integer packId = Integer.valueOf(packIdStr);
            String claimRecord = redissonManager.hGet(recordKey.getKey(time), uidStr);
            // 钻石值取当时用户的钻石值, 不根据配置取, 因为活动值可能改变
            Double diamond = redissonManager.zScore(diamondKey.getKey(time), uidStr);
            return InviteClaimVO.builder()
                    .levelName(levelConfig.map(InviteLevelConfig::getLevelName).orElse(StrUtil.EMPTY))
                    .diamond(Optional.ofNullable(diamond).map(Double::longValue).orElse(0L))
                    .dateKey(time)
                    .packId(packId)
                    .status(StrUtil.isNotBlank(claimRecord))
                    .beginTime(DateUtil.format(DateUtil.beginOfWeek(parse, false), DatePattern.NORM_DATETIME_PATTERN))
                    .endTime(DateUtil.format(DateUtil.endOfWeek(parse, false), DatePattern.NORM_DATETIME_PATTERN))
                    .build();
        }).filter(Objects::nonNull).toList();

        Set<Integer> packIds = vos.stream().map(InviteClaimVO::getPackId).collect(Collectors.toSet());
        Map<Integer, List<RewardPackResourceDTO>> rewaredPackMap = rewardPackServerService.batchRewardPackResourceCache(packIds);
        Map<Integer, Long> packIdToAmountMap = rewaredPackMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream()
                                .filter(dto -> Objects.equals(dto.getResourceType(), ResourceTypeEnum.RESOURCE_USD.getNumber()))
                                .mapToLong(RewardPackResourceDTO::getAmount)
                                .sum()
                ));
        vos.forEach(vo -> {
            Long usd = packIdToAmountMap.get(vo.getPackId());
            BigDecimal divide = new BigDecimal(Optional.ofNullable(usd).orElse(0L)).divide(new BigDecimal("100"), 2, RoundingMode.DOWN);
            vo.setUsd(divide);
        });

        int total = vos.size();
        List<List<InviteClaimVO>> partition = CollUtil.split(vos, pageSize);

        vos = pageNum > partition.size() ? CollUtil.newArrayList() : partition.get(pageNum - 1);
        return ListWithTotal.<InviteClaimVO>builder()
                .list(vos)
                .total(total)
                .build();
    }

    public ListWithTotal<BaseRankUserVO> invitedList(Long uid, Integer pageNum, Integer pageSize) {
        Collection<String> invitedUids = redissonManager.zrange(InviteRedisKey.user_branch_invited.getKey(String.valueOf(uid)), 0, -1);
        if (CollUtil.isEmpty(invitedUids)) {
            return ListWithTotal.empty();
        }
        TimeZoneDateDTO zoneDateDTO = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3);
        DateTime dateTime = DateUtil.beginOfWeek(zoneDateDTO.getRecordTime(), false);
        String dataStr = DateUtil.format(dateTime, DatePattern.PURE_DATE_PATTERN);
        Set<String> invitedUidsSet = new HashSet<>(invitedUids);
        Map<String, Double> scoresMap = redissonManager.getScores(ActivityRedisKey.invite_diamond_anchor.getKey(dataStr), invitedUidsSet);

        List<BaseRankUserVO> rankUserVOS = invitedUids.stream().map(e -> {
                    Double score = scoresMap.get(e);
                    if (Objects.isNull(score) || score == 0) {
                        return null;
                    }
                    return BaseRankUserVO.builder()
                            .uid(Long.parseLong(e))
                            .rankValFlag(score).build();
                })
                .filter(Objects::nonNull)
                .sorted(Comparator.comparing(BaseRankUserVO::getRankValFlag).reversed()).toList();

        List<List<BaseRankUserVO>> partition = CollUtil.split(rankUserVOS, pageSize);

        rankUserVOS = pageNum > partition.size() ? CollUtil.newArrayList() : partition.get(pageNum - 1);

        List<Long> uids = rankUserVOS.stream().map(BaseRankUserVO::getUid).toList();
        Map<Long, UserBaseInfoDTO> userMap = userServerService.batchUserSummary(uids);

        rankUserVOS.forEach(vo -> {
            UserBaseInfoDTO userInfo = userMap.get(vo.getUid());
            if (Objects.nonNull(userInfo)) {
                vo.setAvatar(userInfo.getAvatar());
                vo.setNick(userInfo.getNick());
                vo.setCountry(userInfo.getCountryCode());
                vo.setRankVal(vo.getRankValFlag().longValue());
                vo.setUserNo(userInfo.getUserNo());
                vo.setGender(userInfo.getGender());
            }
        });
        return ListWithTotal.<BaseRankUserVO>builder()
                .list(rankUserVOS)
                .total(invitedUids.size())
                .build();
    }

    private void claimPrize(Long uid, String dateKey, String redisKey, String recordKey, Integer roleAnchor) {
        RLock rLock = redissonManager.fairLock(ActivityRedisKey.invite_diamond_claim_lock.getKey(uid), 5, 10);
        if (Objects.nonNull(rLock) && rLock.isLocked()) {
            log.info("user act invite claim prize uid:[{}] dateKey:[{}] roleAnchor:[{}]", uid, dateKey, roleAnchor);
            try {
                String claimTime = redissonManager.hGet(recordKey, String.valueOf(uid));
                if (StrUtil.isNotBlank(claimTime)) {
                    throw new ApiException(CodeEnum.MISSION_CLAIMED);
                }

                String levelRecord = redissonManager.hGet(redisKey, String.valueOf(uid));
                if (StrUtil.isBlank(levelRecord)) {
                    log.info("user actInvite claim prize fail uid:[{}] dateKey:[{}] roleAnchor:[{}]", uid, dateKey, roleAnchor);
                    throw new ApiException(CodeEnum.BACKPACK_NOT_FOUND_GOODS);
                }
                Map<String, String> recordMap = JSONUtil.toBean(levelRecord, Map.class);
                Optional<Map.Entry<String, String>> max = recordMap.entrySet()
                        .stream().max(Map.Entry.comparingByValue());
                if (max.isEmpty()) {
                    log.info("user actInvite claim prize fail uid:[{}] dateKey:[{}] roleAnchor:[{}]", uid, dateKey, roleAnchor);
                    throw new ApiException(CodeEnum.BACKPACK_NOT_FOUND_GOODS);
                }

                Map.Entry<String, String> level = max.get();
                List<String> split = StrUtil.split(level.getKey(), StrUtil.C_COLON);
                String levelStr = split.get(0);
                String packIdStr = split.get(1);
                Integer prizePackId = Integer.valueOf(packIdStr);
                String bizId = StrUtil.format("{}{}{}", dateKey, levelStr, Math.abs(RandomUtil.randomInt(99999999)));
                PrizeDTO prizeDTO = buildPrizeDTO(uid, prizePackId, Long.parseLong(bizId), RewardPackCopywritingEnum.SEND_ACT_REWARD_SUCCESS, null);
                taskExecutor.execute(() -> {
                    try {
                        rewardPackServerService.sendRewardPack(prizeDTO);
                    } catch (Exception e) {
                        log.error("act invite claim fail uid:[{}] dateKey:[{}] level:[{}] msg:[{}] roleAnchor:[{}]",
                                uid, dateKey, levelStr, e.getMessage(), roleAnchor, e);
                    }
                });
                redissonManager.hSet(recordKey,
                        String.valueOf(uid), StrUtil.format("{}:{}", levelStr, System.currentTimeMillis()));
            } finally {
                if (rLock.isHeldByCurrentThread()) {
                    rLock.unlock();
                }
            }
        }
    }


    private List<InviteLevelConfig> getConfig(Integer type) {
        String configKey;
        if (Objects.equals(type, ROLE_ANCHOR)) {
            configKey = SystemConfigConstant.ACT_ANCHOR_INVITE_ANCHOR_CONFIG;
        } else if (Objects.equals(type, ROLE_AGENT)) {
            configKey = SystemConfigConstant.ACT_ANCHOR_INVITE_AGENT_CONFIG;
        } else {
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        String configStr = systemConfigService.getSysConfValueById(configKey);
        if (StrUtil.isBlank(configStr)) {
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        return JSONUtil.toList(configStr, InviteLevelConfig.class);
    }

    private Optional<InviteLevelConfig> levelByDiamond(Long diamond, List<InviteLevelConfig> configs) {
        diamond = Optional.ofNullable(diamond).orElse(0L);
        Long finalDiamond = diamond;
        return configs.stream().filter(e -> e.getDiamondVal() <= finalDiamond)
                .max(Comparator.comparing(InviteLevelConfig::getDiamondVal));
    }

    private Optional<InviteLevelConfig> levelByLevel(Integer level, List<InviteLevelConfig> configs) {
        return configs.stream().filter(e -> Objects.equals(level, e.getLevel()))
                .findFirst();
    }

    private InviteLevelConfig nextLevel(Long diamond, Integer type) {
        diamond = Optional.ofNullable(diamond).orElse(0L);
        List<InviteLevelConfig> config = getConfig(type);
        Long finalDiamond = diamond;
        return config.stream().filter(e -> e.getDiamondVal() > finalDiamond)
                .min(Comparator.comparing(InviteLevelConfig::getDiamondVal))
                .orElse(InviteLevelConfig.builder().diamondVal(9999999999999L).levelName("max").build());
    }

    private void updateUpperRecord(String timeAttach, Integer type, Integer level, Long uid, Long time, List<InviteLevelConfig> configs) {
        String redisKey;
        if (Objects.equals(type, ROLE_ANCHOR)) {
            redisKey = ActivityRedisKey.invite_diamond_anchor_level_record.getKey(timeAttach);
        } else if (Objects.equals(type, ROLE_AGENT)) {
            redisKey = ActivityRedisKey.invite_diamond_agent_level_record.getKey(timeAttach);
        } else {
            return;
        }
        Optional<InviteLevelConfig> levelConfig = levelByLevel(level, configs);
        if (levelConfig.isEmpty()) {
            log.warn("act invite updateUpper Record config is empty, level:[{}] timeAttach:[{}] type:[{}] uid:[{}]",
                    level, timeAttach, type, uid);
            return;
        }
        InviteLevelConfig config = levelConfig.get();
        Map<String, String> recordMap = MapUtil.newHashMap();
        String recordStr = redissonManager.hGet(redisKey, String.valueOf(uid));
        if (StrUtil.isNotBlank(recordStr)) {
            recordMap = JSONUtil.toBean(recordStr, Map.class);
        }
        recordMap.put(StrUtil.format("{}:{}", String.valueOf(level), config.getPrizePackId()), String.valueOf(time));
        log.info("activity invite update upper record uid:[{}] type:[{}] level:[{}] time:[{}]", uid, type, level, time);
        redissonManager.hSet(redisKey, String.valueOf(uid), JSONUtil.toJsonStr(recordMap));
    }

}
