package com.simi.service.activity.rank;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Pair;
import com.google.common.collect.Lists;
import com.simi.common.config.PkRankConfig;
import com.simi.common.constant.ActivityFlag;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.TimeZoneEnum;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.constant.rank.RankFrequencyEnum;
import com.simi.common.constant.rank.RankRedisKey;
import com.simi.common.dto.aristocracy.FruitPerRoundMsg;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.common.vo.rank.GenericRankVO;
import com.simi.entity.RankScoreDetail;
import com.simi.service.activity.handle.AbstractActivityWrapper;
import com.simi.service.activity.handle.ActivityBaseService;
import com.simi.service.medal.MedalTaskService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-28 17:43
 **/
@Slf4j
@Service
public class FruitRankService extends AbstractActivityWrapper implements ActivityBaseService {
    private static final Date BEGIN_TIME = DateUtil.parse("2020-01-01 00:00:00");
    private static final Date END_TIME = DateUtil.parse("2040-01-01 00:00:00");
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private PkRankConfig pkRankConfig;
    @Autowired
    private MedalTaskService medalTaskService;
    @Autowired
    private RedissonManager redissonManager;

    private final static List<String> fruits = Lists.newArrayList("cherry", "grape",
            "watermelon", "apple", "lemon", "orange", "mango", "strawberry");

    @Override
    public Pair<Date, Date> timeRange() {
        return Pair.of(BEGIN_TIME, END_TIME);
    }

    @Override
    public ActivityFlag activityFlag() {
        return ActivityFlag.FRUIT_RANK;
    }

    @Override
    public void handleFruitPerRoundMsg(FruitPerRoundMsg msg) {
        // 判断活动是否开启
        String fruitRankDate = systemConfigService.getSysConfValueById(SystemConfigConstant.FRUIT_RANK_START);
        boolean fruitTest = pkRankConfig.isFruitTest();
        // 转成日期
        if (StringUtils.isBlank(fruitRankDate)) {
            log.info("handleFruitPerRoundMsg fruitRankDate is null.msg:{}", msg);
            return;
        }
        // yyyy-MM-dd hh:mm:ss 转成 Date 这个是0时区的时间
        Date date = DateUtil.parse(fruitRankDate);
        // -3个小时 转成 UTC+3 时区的时间
        Date utcTime = TimeZoneUtils.getTime(date, TimeZoneEnum.GMT_WEST3).getRecordTime();
        // 获取日期
        String dayFormat = DateUtil.format(DateUtil.beginOfDay(date), DateTimeUtil.DAY_PATTERN);
        long startTime = utcTime.getTime();

        log.info("handleFruitPerRoundMsg 开始日期 dayFormat {} {},msg:{}", dayFormat, startTime, msg);
        // 判断是否在8天内
        // 8天时间
        long endTime = startTime + 8 * 24 * 60 * 60 * 1000;
        // 当前时间
        long nowTime = System.currentTimeMillis();
        if (nowTime < startTime) {
            log.info("handleFruitPerRoundMsg 活动未开启 msg:{}", msg);
            return;
        }
        if (nowTime > endTime) {
            log.info("handleFruitPerRoundMsg 活动已经结束 msg:{}", msg);
            return;
        }
        // 计算目前是活动开始的第 X 天 注意：从0天开始
        int dayIndex = (int) ((nowTime - startTime) / (1000 * DateTimeUtil.SECONDS_PER_DAY));
        // 获取当天的水果
        String fruit = fruits.get(dayIndex);
        log.info("handleFruitPerRoundMsg dayIndex {},fruit {},msg:{}", dayIndex, fruit, msg);

        // 水果榜
        RankFrequencyEnum weekEnum = RankFrequencyEnum.RANK_FREQUENCY_TYPE_ALL;
        String timeDiff = getTimeDiff(nowTime);
        String roundFruit = msg.getFruit();
        // 如果测试，则每一个水果都加
        if (fruitTest) {
            log.info("handleFruitPerRoundMsg 测试，替换中奖水谷");
            fruit = roundFruit;
        }
        List<FruitPerRoundMsg.Winner> winners = msg.getWinner();
        if (fruit.equals(roundFruit)&& CollectionUtil.isNotEmpty(winners)) {
            String fruitRankDayKey = RankRedisKey.fruit_rank_day.getKey(dayFormat, fruit);
            winners.forEach(k->{
                long userId = k.getUserId();
                int coin = k.getCoin();
                try {
                    RankScoreDetail sendGift = RankScoreDetail.builder()
                            .score(coin).userId(userId).rankKey(fruitRankDayKey).build();
                    super.record(sendGift);
                    super.addRankScore(fruitRankDayKey, userId + "", weekEnum.getTtl(), (long) coin, timeDiff);
                } catch (Exception e) {
                    log.error("handleFruitPerRoundMsg 水果榜失败 msg:{},uid:{},coin:{},e {}", msg, userId, coin, ExceptionUtil.formatEx(e), e);
                }
            });
        }

        // 下注榜单
        List<Long> plays = msg.getPlays();
        if (CollectionUtil.isNotEmpty(plays)) {
            String fruitRankWeekKey = RankRedisKey.fruit_rank_bets_count.getKey(dayFormat);
            plays.forEach(k->{
                try {
                    RankScoreDetail sendGift = RankScoreDetail.builder()
                            .score(1).userId(k).rankKey(fruitRankWeekKey).build();
                    super.record(sendGift);
                    super.addRankScore(fruitRankWeekKey, k + "", weekEnum.getTtl(), 1L, timeDiff);
                } catch (Exception e) {
                    log.error("handleFruitPerRoundMsg 下注榜失败 msg:{}, uid:{} e {}", msg, k, ExceptionUtil.formatEx(e), e);
                }
            });
        }

        // 赢钱总榜
        if (CollectionUtil.isNotEmpty(winners)) {
            String fruitRankWeekKey = RankRedisKey.fruit_rank_win_count.getKey(dayFormat);
            winners.forEach(k->{
                long userId = k.getUserId();
                int coin = k.getCoin();
                try {
                    RankScoreDetail sendGift = RankScoreDetail.builder()
                            .score(coin).userId(userId).rankKey(fruitRankWeekKey).build();
                    super.record(sendGift);
                    super.addRankScore(fruitRankWeekKey, userId + "", weekEnum.getTtl(), (long) coin, timeDiff);
                } catch (Exception e) {
                    log.error("handleFruitPerRoundMsg 赢钱总榜失败 msg:{},uid:{},coin:{},e {}", msg, userId, coin, ExceptionUtil.formatEx(e), e);
                }
                medalTaskService.executeMedalTask(MedalTaskEnum.WIN_IN_FRUIT, userId, coin);
            });
        }

        // 记录当天活动数值
        /**
         *     // fruit 参与人数
         *     fruit_rank_participant_count,
         *     // fruit 总参与人数
         *     fruit_rank_total_participant_count,
         *     // fruit 下注金额
         *     fruit_rank_bets_amount,
         *     // fruit 总奖金额
         *     fruit_rank_total_amount
         */
        if (CollectionUtil.isNotEmpty(plays)) {
            plays.forEach(uid->{
                // fruit 参与人数
                String countKey = RankRedisKey.fruit_rank_participant_count.getKey(dayFormat, dayIndex);
                redissonManager.sAdd(countKey, uid + "");

                // fruit 总参与人数
                String totalKey = RankRedisKey.fruit_rank_total_participant_count.getKey(dayFormat);
                redissonManager.sAdd(totalKey, uid + "");
            });
        }
        Long total = msg.getTotal();
        Long winTotal = msg.getWinTotal();
        if (Objects.nonNull(total)) {
            // fruit 下注金额
            String betKey = RankRedisKey.fruit_rank_bets_amount.getKey(dayFormat, dayIndex);
            redissonManager.increment(betKey, total);
            log.info("handleFruitPerRoundMsg total:{} winTotal:{}", total, winTotal);
        }
        if (Objects.nonNull(winTotal)) {
            // fruit 总奖金额
            String amountKey = RankRedisKey.fruit_rank_total_amount.getKey(dayFormat, dayIndex);
            redissonManager.increment(amountKey, winTotal);
            log.info("handleFruitPerRoundMsg total:{} winTotal:{}", total, winTotal);
        }
    }


    /**
     * @param uid
     * @param size
     * @param type  0 送礼 1 收礼
     * @param fruitIndex key 的周期时间
     * @return
     */
    public GenericRankVO commonlyRankInfo(Long uid,
                                          Integer size, int type, String startDay, int fruitIndex) {

        String key = null;
        if (type == 1) {
            String fruit = fruits.get(fruitIndex);
            key = RankRedisKey.fruit_rank_day.getKey(startDay, fruit);
        }

        if (type == 2) {
            key = RankRedisKey.fruit_rank_win_count.getKey(startDay);
        }

        if (type == 3) {
            key = RankRedisKey.fruit_rank_bets_count.getKey(startDay);
        }

        GenericRankVO genericRankVO = super.genericRankVO(key, uid, size);
        genericRankVO.setCountdown(0L);
        return genericRankVO;
    }
}
