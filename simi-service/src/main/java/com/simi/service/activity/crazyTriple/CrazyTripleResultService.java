package com.simi.service.activity.crazyTriple;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.reflect.TypeToken;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.crazyTriple.CrazyTripleGameRewardStatusEnum;
import com.simi.common.constant.crazyTriple.CrazyTripleGameStatusEnum;
import com.simi.common.constant.crazyTriple.CrazyTripleProductTypeEnum;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.GsonUtil;
import com.simi.common.vo.req.activity.crazyTriple.ResultUserRecordReq;
import com.simi.config.CrazyTripleConfig;
import com.simi.constant.BillEnum;
import com.simi.dto.crazyTriple.*;
import com.simi.entity.crazyTriple.*;
import com.simi.manager.crazyTriple.CrazyTripleCacheManager;
import com.simi.service.purse.PurseManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StopWatch;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Description: 疯狂Triple
 * @Author: Yibo Liu
 * @Date: 2024-05-18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CrazyTripleResultService {

    private  final CrazyTripleCacheManager crazyTripleCacheManager;
    private final ActivityCrazyTripleResultService crazyTripleResultService;
    private final ActivityCrazyTripleResultUserService crazyTripleResultUserService;
    private final CrazyTripleConfService crazyTripleConfService;
    private final ActivityCrazyTripleGameService crazyTripleGameService;
    private final ActivityCrazyTripleRewardRecordService crazyTripleRewardRecordService;
    private final ActivityCrazyTripleJoinRecordService crazyTripleJoinRecordService;
    private final PurseManageService purseManageService;

    public GetGameHistoryDataDTO getHistoryData() {
        GetGameHistoryDataDTO getGameHistoryDataDTO = new GetGameHistoryDataDTO();
        String lang = LanguageEnum.ar.name();
        CrazyTripleGetGameHistoryDataDTO historyData = crazyTripleCacheManager.getHistoryData(lang);
        if (historyData == null) {
            historyData = buildGameHistoryData(lang);
            crazyTripleCacheManager.setHistoryData(lang, historyData);
        }
        getGameHistoryDataDTO.setHistoryData(historyData);
        return getGameHistoryDataDTO;
    }

    @Transactional(rollbackFor = Exception.class)
    public void saveGameResult(ActivityCrazyTripleGame game, List<ActivityCrazyTripleJoinRecord> joinRecordList,
                               ActivityCrazyTripleResult result, List<ActivityCrazyTripleResultUser> resultUserList,
                               List<ActivityCrazyTripleRewardRecord> rewardList) {
        game.setStatus(CrazyTripleGameStatusEnum.CRAZY_TRIPLE_GAME_STATUS_END.getValue());
        game.setModifyTime(new Date());
        crazyTripleGameService.updateById(game);

        crazyTripleResultService.save(result);
        if (CollectionUtil.isNotEmpty(resultUserList)) {
            crazyTripleResultUserService.saveBatch(resultUserList);
        }
        if (CollectionUtil.isNotEmpty(rewardList)) {
            crazyTripleRewardRecordService.saveBatch(rewardList);
        }
        if (CollectionUtil.isNotEmpty(joinRecordList)) {
            List<Long> joinRecordIdList = joinRecordList.stream()
                    .map(ActivityCrazyTripleJoinRecord::getId)
                    .collect(Collectors.toList());
            crazyTripleJoinRecordService.updateJoinRecordSettle(joinRecordIdList);
        }
    }

    public void refreshResultHistory() {
        String lang = LanguageEnum.ar.name();
        CrazyTripleGetGameHistoryDataDTO crazyTripleGetGameHistoryDataDTO = buildGameHistoryDataPB(lang);
        crazyTripleCacheManager.setHistoryDataPB(lang, crazyTripleGetGameHistoryDataDTO);
    }

    /**
     * 给用户发送奖励
     * @param rewardList
     * @param isRetry
     */
    public void sendReward(List<ActivityCrazyTripleRewardRecord> rewardList, boolean isRetry) {
        if (CollectionUtil.isEmpty(rewardList)) {
            return;
        }
        for (ActivityCrazyTripleRewardRecord item : rewardList) {
            if (item.getStatus() != CrazyTripleGameRewardStatusEnum.CRAZY_TRIPLE_GAME_REWARD_STATUS_INIT.getValue()) {
                continue;
            }
            try {
                purseManageService.addCoin(item.getUserId(), item.getAmount(), BillEnum.CRAZY_TRIPLE_REWARD,
                        CommonUtil.genId(), "", Collections.emptyMap(), 0l, PurseRoleTypeEnum.USER.getType());

                item.setStatus(CrazyTripleGameRewardStatusEnum.CRAZY_TRIPLE_GAME_REWARD_STATUS_SUC.getValue());
                item.setModifyTime(new Date());
                crazyTripleRewardRecordService.updateById(item);
                crazyTripleCacheManager.addRankReward(item.getUserId(), item.getAmount(), item.getCreateTime().getTime());
                log.info("[crazyTriple] sendReward suc data:{}", GsonUtil.getGson().toJson(item));

            } catch (Exception e) {
                log.error("[crazyTriple] sendReward error data:{}", GsonUtil.getGson().toJson(item), e);
            }
            if (isRetry) {
                crazyTripleRewardRecordService.incrRetryNum(item);
            }
        }
    }

    private CrazyTripleGetGameHistoryDataDTO buildGameHistoryDataPB(String lang) {
        CrazyTripleGetGameHistoryDataDTO crazyTripleGetGameHistoryDataDTO = new CrazyTripleGetGameHistoryDataDTO();

        int rankTopCount = 3;
        // 日榜top3
        long timeMillis = System.currentTimeMillis();
        String rankKey = crazyTripleCacheManager.getRankJoinKey(timeMillis);
        crazyTripleCacheManager.clearRankJoinCache(rankKey, 0, rankTopCount);
        List<RankInfoDTO> rankList = crazyTripleCacheManager.getRankJoinCache(rankKey, 0, rankTopCount);
        crazyTripleGetGameHistoryDataDTO.setDayRankInfo(rankList);

        // 周榜top3
        rankKey = crazyTripleCacheManager.getRankRewardKey(timeMillis);
        crazyTripleCacheManager.clearRankRewardCache(rankKey, 0, rankTopCount);
        rankList = crazyTripleCacheManager.getRankRewardCache(rankKey, 0, rankTopCount);
        crazyTripleGetGameHistoryDataDTO.setWeekRankInfo(rankList);

        List<ActivityCrazyTripleResult> recentResultList = crazyTripleResultService.getRecentResultList(CrazyTripleConfig.resultGameCount);
        log.info("crazyTriple build HistoryData recentResultList:{}", recentResultList == null ? null : recentResultList.size());
        List<CrazyTripleGameResultDTO> crazyTripleGameResultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recentResultList)) {
            for (ActivityCrazyTripleResult item : recentResultList) {
                CrazyTripleGameResultDTO crazyTripleGameResultDTO = new CrazyTripleGameResultDTO();
                crazyTripleGameResultDTO.setGameId(item.getGameId());
                crazyTripleGameResultDTO.setProductType(item.getProductType());
                List<Integer> dicePointList = GsonUtil.getGson().fromJson(item.getDiceResult(), new TypeToken<List<Integer>>() {
                }.getType());
                crazyTripleGameResultDTO.setDicePoint(dicePointList);
                crazyTripleGameResultDTO.setAllRewardList(new ArrayList<>());
                crazyTripleGameResultDTO.setTop3RewardList(new ArrayList<>());
                crazyTripleGameResultList.add(crazyTripleGameResultDTO);
            }
        }
        crazyTripleGetGameHistoryDataDTO.setResultList(crazyTripleGameResultList);

        return crazyTripleGetGameHistoryDataDTO;
    }


    private CrazyTripleGetGameHistoryDataDTO buildGameHistoryData(String lang) {
        CrazyTripleGetGameHistoryDataDTO crazyTripleGetGameHistoryDataDTO = new CrazyTripleGetGameHistoryDataDTO();
        int rankTopCount = 3;
        // 日榜top3
        long timeMillis = System.currentTimeMillis();
        String rankKey = crazyTripleCacheManager.getRankJoinKey(timeMillis);
        crazyTripleCacheManager.clearRankJoinCache(rankKey, 0, rankTopCount);
        List<RankInfoDTO> rankList = crazyTripleCacheManager.getRankJoinCache(rankKey, 0, rankTopCount);
        crazyTripleGetGameHistoryDataDTO.setDayRankInfo(rankList == null ? new ArrayList<>() : rankList);

        // 周榜top3
        rankKey = crazyTripleCacheManager.getRankRewardKey(timeMillis);
        crazyTripleCacheManager.clearRankRewardCache(rankKey, 0, rankTopCount);
        rankList = crazyTripleCacheManager.getRankRewardCache(rankKey, 0, rankTopCount);
        crazyTripleGetGameHistoryDataDTO.setWeekRankInfo(rankList == null ? new ArrayList<>() : rankList);

        List<ActivityCrazyTripleResult> recentResultList = crazyTripleResultService.getRecentResultList(CrazyTripleConfig.resultGameCount);
        log.debug("crazyTriple build HistoryData recentResultList:{}", recentResultList == null ? null : recentResultList.size());
        List<CrazyTripleGameResultDTO> crazyTripleGameResultList = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(recentResultList)) {
            for (ActivityCrazyTripleResult item : recentResultList) {
                CrazyTripleGameResultDTO crazyTripleGameResultDTO = new CrazyTripleGameResultDTO();
                crazyTripleGameResultDTO.setGameId(item.getGameId());
                crazyTripleGameResultDTO.setProductType(item.getProductType());
                List<Integer> dicePointList = GsonUtil.getGson().fromJson(item.getDiceResult(), new TypeToken<List<Integer>>() {
                }.getType());
                crazyTripleGameResultDTO.setDicePoint(dicePointList);
                crazyTripleGameResultList.add(crazyTripleGameResultDTO);
            }
        }
        crazyTripleGetGameHistoryDataDTO.setResultList(crazyTripleGameResultList);
        return crazyTripleGetGameHistoryDataDTO;
    }

    public CrazyTripleGetGameResultUserRecordDTO getResultUserRecord(ResultUserRecordReq req, long uid, String lang) {
        CrazyTripleGetGameResultUserRecordDTO resultUserRecordDTO
                = new CrazyTripleGetGameResultUserRecordDTO();
        StopWatch sw = new StopWatch();
        sw.start();
        String nextPageParam = req.getNextPageParam();
        long preDataId = 0;
        if (StringUtils.isNotBlank(nextPageParam)) {
            preDataId = Long.parseLong(nextPageParam);
        }
        int pageSize = 10;
        List<ActivityCrazyTripleResultUser> list = crazyTripleResultUserService.getCrazyTripleResultUserList(uid, preDataId, pageSize);
        boolean hasMore = false;
        if (!CollectionUtils.isEmpty(list)) {
            Map<Integer, CrazyTripleProductConf> productConfMap = crazyTripleConfService.getCrazyTripleProductConfMap();
            List<GameResultUserDTO> gameResultUserDTOList = new ArrayList<>();
            for (ActivityCrazyTripleResultUser item : list) {
                GameResultUserDTO gameResultUserDTO = buildGameResultUserPB(lang, item, productConfMap);
                if (gameResultUserDTO != null) {
                    gameResultUserDTOList.add(gameResultUserDTO);
                }
            }
            resultUserRecordDTO.setUserList(gameResultUserDTOList);
            long newPreDataId = list.get(list.size() - 1).getId();
            nextPageParam = String.valueOf(newPreDataId);
            resultUserRecordDTO.setNextPageParam(nextPageParam);
            if (list.size() >= pageSize) {
                hasMore = crazyTripleResultUserService.existMoreCrazyTripleResultUser(uid, preDataId);
            }
        }
        resultUserRecordDTO.setHasMore(hasMore);
        sw.stop();
        log.info("crazyTriple getResultUserRecord uid:{},param:{},nextPageParam:{},hasMore:{},timeCost:{}",
                uid, req.getNextPageParam(), resultUserRecordDTO.getNextPageParam(), resultUserRecordDTO.isHasMore(), sw.getTotalTimeMillis());
        return resultUserRecordDTO;
    }

    public GameResultUserDTO buildGameResultUserPB(String lang, ActivityCrazyTripleResultUser item, Map<Integer, CrazyTripleProductConf> productConfMap) {

        GameResultUserDTO gameResultUserDTO = new GameResultUserDTO();

        gameResultUserDTO.setGameId(item.getGameId());
        gameResultUserDTO.setProductType(item.getProductType());
        List<Integer> dicePointList = GsonUtil.getGson().fromJson(item.getDiceResult(), new TypeToken<List<Integer>>() {
        }.getType());
        gameResultUserDTO.setDicePoint(dicePointList);

        gameResultUserDTO.setGameId(item.getGameId());
        gameResultUserDTO.setCreateTime(item.getCreateTime().getTime());
        gameResultUserDTO.setRewardCoinAmount(item.getActualRewardCurrencyAmount());
        gameResultUserDTO.setCreateTime(item.getCreateTime().getTime());
        List<CrazyTripleJoinInfoDTO> joinInfoList = parseJoinInfo(item.getJoinInfo());
        Map<Integer, CrazyTripleJoinInfoDTO> joinInfoMap = joinInfoList == null ? null : joinInfoList.stream()
                .collect(Collectors.toMap(
                        CrazyTripleJoinInfoDTO::getProductType,
                        Function.identity()
                ));
        List<BetItemDTO> betItemDTOList = new ArrayList<>();
        for (CrazyTripleProductTypeEnum productTypeEnum : CrazyTripleProductTypeEnum.values()) {
            int productType = productTypeEnum.getProductType().getValue();
            CrazyTripleJoinInfoDTO joinInfoDTO = joinInfoMap == null ? null : joinInfoMap.get(productType);
            BetItemDTO betItemDTO = new BetItemDTO();
            betItemDTO.setProductType(productType);
            betItemDTO.setJoinNum(joinInfoDTO == null ? 0 : (int) joinInfoDTO.getJoinAmount());
            CrazyTripleProductConf productConf = productConfMap == null ? null : productConfMap.get(productType);
            if (productConf != null && productConf.getName() != null) {
                String name = productConf.getName().get(lang);
                betItemDTO.setProductName(StringUtils.defaultIfEmpty(name, ""));
            }
            betItemDTOList.add(betItemDTO);
        }
        gameResultUserDTO.setBetList(betItemDTOList);
        return gameResultUserDTO;
    }

    public List<CrazyTripleJoinInfoDTO> parseJoinInfo(String joinInfoJson) {
        if (StringUtils.isBlank(joinInfoJson)) {
            return null;
        }
        return GsonUtil.getGson().fromJson(joinInfoJson, new TypeToken<List<CrazyTripleJoinInfoDTO>>() {
        }.getType());
    }

}
