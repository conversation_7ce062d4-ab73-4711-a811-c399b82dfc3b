package com.simi.service.activity.crazyTriple;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.simi.entity.crazyTriple.ActivityCrazyTripleResultUser;
import com.simi.mapper.crazyTriple.ActivityCrazyTripleResultUserMapper;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【activity_crazy_triple_result_user(疯狂triple开奖结果表-分用户)】的数据库操作Service实现
 * @createDate 2024-05-28 10:55:29
 */
@Service
public class ActivityCrazyTripleResultUserService extends ServiceImpl<ActivityCrazyTripleResultUserMapper, ActivityCrazyTripleResultUser> {
    public List<ActivityCrazyTripleResultUser> getCrazyTripleResultUserList(long uid, long preDataId, int pageSize) {
        LambdaQueryWrapper<ActivityCrazyTripleResultUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(preDataId > 0, ActivityCrazyTripleResultUser::getId, preDataId);
        queryWrapper.eq(ActivityCrazyTripleResultUser::getUserId, uid);
        queryWrapper.gt(ActivityCrazyTripleResultUser::getCreateTime, DateUtil.offsetDay(new Date(), -90));
        queryWrapper.orderByDesc(ActivityCrazyTripleResultUser::getId);
        PageHelper.startPage(1, pageSize);
        return list(queryWrapper);
    }

    public boolean existMoreCrazyTripleResultUser(long uid, long preDataId) {
        LambdaQueryWrapper<ActivityCrazyTripleResultUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.lt(preDataId > 0, ActivityCrazyTripleResultUser::getId, preDataId);
        queryWrapper.gt(ActivityCrazyTripleResultUser::getCreateTime, DateUtil.offsetDay(new Date(), -90));
        queryWrapper.eq(ActivityCrazyTripleResultUser::getUserId, uid);
        return exists(queryWrapper);
    }

    public ActivityCrazyTripleResultUser getResultUser(long uid, long gameId) {
        LambdaQueryWrapper<ActivityCrazyTripleResultUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityCrazyTripleResultUser::getUserId, uid);
        queryWrapper.eq(ActivityCrazyTripleResultUser::getGameId, gameId);
        return getOne(queryWrapper);
    }

    public List<ActivityCrazyTripleResultUser> getRewardList(long gameId) {
        LambdaQueryWrapper<ActivityCrazyTripleResultUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ActivityCrazyTripleResultUser::getGameId, gameId);
        queryWrapper.gt(ActivityCrazyTripleResultUser::getActualRewardCurrencyAmount, 0);
        return list(queryWrapper);
    }

    public List<ActivityCrazyTripleResultUser> getResultGtId(long idGt,int limitSize) {
        PageHelper.startPage(1,limitSize);
        LambdaQueryWrapper<ActivityCrazyTripleResultUser> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.gt( ActivityCrazyTripleResultUser::getId, idGt);
        queryWrapper.orderByAsc(ActivityCrazyTripleResultUser::getId);
        return list(queryWrapper);
    }
}




