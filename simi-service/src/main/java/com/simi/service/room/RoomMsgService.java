package com.simi.service.room;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.TimeZoneEnum;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.expression.ExpressionInfoDTO;
import com.simi.common.dto.room.RoomAudienceDTO;
import com.simi.common.dto.shumei.ShumeiAuditImgResp;
import com.simi.common.dto.shumei.ShumeiConstant;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.OssUrlUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.common.vo.req.RoomWelcomeContentPushReq;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.RoomRedisKey;
import com.simi.dto.game.UserWinnerDTO;
import com.simi.service.LongLinkService;
import com.simi.service.SensitiveVocabularyService;
import com.simi.service.cache.RoomMsgCache;
import com.simi.service.cache.SpeechCensusCache;
import com.simi.service.room.cache.RoomWelcomeContentRedisService;
import com.simi.service.thirdparty.ShumeiAuditImgApiComponent;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.security.SecureRandom;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class RoomMsgService {

    @Autowired
    private SensitiveVocabularyService sensitiveVocabularyService;
    @Autowired
    private UserServerService userServerService;
    @Autowired
    private LongLinkService longLinkService;
    @Autowired
    private RoomMsgCache roomMsgCache;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private SpeechCensusCache speechCensusCache;
    @Autowired
    private RoomLowService roomLowService;
    @Autowired
    private RedissonManager redissonManager;
    @Autowired
    private RoomHighService roomHighService;
    @Autowired
    private UserVipService userVipService;
    @Autowired
    private RoomWelcomeContentRedisService roomWelcomeContentRedisService;
    @Autowired
    private ShumeiAuditImgApiComponent shumeiAuditImgApiComponent;


    public void pushMsg(long uid, String event, String roomId, String msg) {
        log.info("RoomMsg pushMsg uid:{} event:{} roomId:{} msg:{}", uid, event, roomId, msg);
        String msgfilter = msg;
        String messageTo = PushToType.MESSAGE_TO_ALL;
        String interval = systemConfigService.getSysConfValueById(SystemConfigConstant.ROOM_SEND_INTERVAL);

        if (roomMsgCache.isInterval(uid, Integer.parseInt(interval))) {
            throw new ApiException(CodeEnum.ROOM_SEND_MSG_INTERVAL);
        }

        Long silenceEndTime = roomLowService.isSilence(roomId, uid);
        if (Objects.nonNull(silenceEndTime)) {
            TimeZoneEnum timeZoneEnum = userServerService.userTimeZone(uid);
            TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(new Date(silenceEndTime), timeZoneEnum);
            String endTimeStr = DateUtil.format(timeZoneDateDTO.getRecordTime(), DatePattern.NORM_DATETIME_PATTERN);
            throw new ApiException(CodeEnum.ROOM_SILENCE, endTimeStr);
        }

        try {
            if (PushEvent.room_screen_message_event.equals(event)) {
                msgfilter = sensitiveVocabularyService.filter(msg);
                Long roomMsg = roomMsgCache.incrRoomMsg(uid, msg);
                String sendMsgTime = systemConfigService.getSysConfValueById(SystemConfigConstant.ROOM_SEND_MSG_TIME);
                if (roomMsg.equals(Long.parseLong(sendMsgTime))) {
                    messageTo = PushToType.EXCLUDE_OTHER;
                }
            }
            if (PushEvent.room_screen_expression_event.equals(event)) {
                ExpressionInfoDTO bean = JSONUtil.toBean(msg, ExpressionInfoDTO.class);

                var random = new SecureRandom();

                if (bean.getType() == 2) {
                    String rsp = "rsp";
                    int index = random.nextInt(rsp.length());
                    bean.setExt(String.valueOf(rsp.charAt(index)));
                    msgfilter = JSONUtil.toJsonStr(bean);
                } else if (bean.getType() == 3) {
                    String ext = "123456";
                    int index = random.nextInt(ext.length());
                    bean.setExt(String.valueOf(ext.charAt(index)));
                    msgfilter = JSONUtil.toJsonStr(bean);
                } else if (bean.getType() == 4) {
                    var number = random.nextInt(1000);
                    bean.setExt(String.format("%03d", number));
                    msgfilter = JSONUtil.toJsonStr(bean);
                }
            }
            if (PushEvent.room_screen_image_event.equals(event)) {
//                ShumeiAuditImgResp auditResult = shumeiAuditImgApiComponent.auditShowImg(uid, msg);
//                if (StrUtil.equalsIgnoreCase(ShumeiConstant.RiskLevel.REJECT, auditResult.getRiskLevel())) {
//                    throw new ApiException(CodeEnum.IMAGE_VIOLATION);
//                }
                msgfilter = OssUrlUtil.jointUrl(msg);

            }
            UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);
            // 记录用户行为
            redissonManager.setnx(RoomRedisKey.room_action.getKey(roomId, uid), "1", 1, TimeUnit.DAYS);
            log.info("RoomMsg pushMsg userPB:{} roomId:{} filter:{}", userPB, roomId, msgfilter);
            longLinkService.pushRoomMsg(roomId, userPB, msgfilter, event, messageTo);
            speechCensusCache.speechCensus(roomId, System.currentTimeMillis());
            //统计公屏的数量
            speechCensusCache.recordMsgCount(roomId);
            RoomAudienceDTO roomAudienceDTO = roomHighService.audienceTop(roomId, 3);
            if (userVipService.isInvisibleEnter(uid) && roomAudienceDTO != null) {
                long now = System.currentTimeMillis();
                log.info("公屏消息 观众刷新列表{}", now);
                longLinkService.pushCustomerRoomMsg(roomId, roomAudienceDTO, PushEvent.room_audience_update_event, PushToType.MESSAGE_TO_ALL);
                log.info("公屏消息 观众刷新列表{}", System.currentTimeMillis() - now);
            }
        } catch (Exception e) {
            log.error("error.........................", e);
        }

    }

    public void pushWelcomeContent(RoomWelcomeContentPushReq req, Long uid) {
        String content = roomWelcomeContentRedisService.getRandomContentByLanguage(req.getLanguage());
        String roomId = req.getRoomId();
        pushMsg(uid, PushEvent.room_screen_message_event, roomId, content);
        log.info("pushWelcomeContent successful,req:{} uid:{} content:{}", JSON.toJSONString(req), uid,content);
    }

    public void gameWinner(Long uid,String roomId) {
        UserWinnerDTO userWinnerDTO = new UserWinnerDTO();
        userWinnerDTO.setWinAmount(10000L);
        userWinnerDTO.setMultiple(10);
        userWinnerDTO.setUserBaseInfo(userServerService.getUserBaseInfo(uid));
        longLinkService.pushCustomerRoomBannerList(roomId,userWinnerDTO, PushEvent.fruit_machine_event, PushToType.EXCLUDE_OTHER);
    }
}
