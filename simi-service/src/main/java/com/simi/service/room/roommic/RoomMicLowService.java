package com.simi.service.room.roommic;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Maps;

import com.simi.common.entity.MicStateInfo;
import com.simi.common.util.RedissonManager;
import com.simi.constant.RoomConstant;
import com.simi.constant.RoomRedisKey;
import com.simi.service.LongLinkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * 房间麦位低层
 * @Author: Andy
 * @Date: 2024-01-26
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomMicLowService {

    private final RedissonClient redissonClient;
    private final RoomMicService roomMicService;
    private final RedissonManager redissonManager;
    private final LongLinkService longLinkService;

    public List<MicStateInfo> listMicInfo(String roomId) {
        return roomMicService.listMicInfo(roomId);
    }

    /**
     * 初始化房间麦位
     * 十个麦位、禁言
     * @param roomId
     */
    public void initRoomMic(String roomId) {
        Map<Integer, String> map = Maps.newHashMapWithExpectedSize(RoomConstant.NORMAL_ROOM_MIC_COUNT);
        final long currentTimeMillis = System.currentTimeMillis();
        for (int i = 0; i < RoomConstant.NORMAL_ROOM_MIC_COUNT; i++){
            MicStateInfo micStateInfo = new MicStateInfo();
            micStateInfo.setPosition(i);
            micStateInfo.setIsMute(false);
            micStateInfo.setIsLock(false);
            micStateInfo.setUserInfo(null);
            micStateInfo.setTimestamp(currentTimeMillis);
            map.put(i, JSONUtil.toJsonStr(micStateInfo));
        }
        redissonClient.getMap(RoomRedisKey.room_mic_list.getKey(StrUtil.format("{{}}", roomId))).putAll(map);
    }


}
