package com.simi.service.room;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson2.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.fasterxml.jackson.databind.json.JsonMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.simi.builder.NextEventDataPBBuilder;
import com.simi.common.annotation.MissionComplete;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.RoomCopywritingEnum;
import com.simi.common.constant.*;
import com.simi.common.constant.longLink.CommonMessageEvent;
import com.simi.common.constant.room.RoomIdentity;
import com.simi.common.constant.room.RoomRecEnum;
import com.simi.common.dto.CountryCodeSkipRooms;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.aristocracy.UserCurAristocracyInfo;
import com.simi.common.dto.room.*;
import com.simi.common.dto.shumei.ShumeiAuditImgResp;
import com.simi.common.dto.shumei.ShumeiConstant;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.entity.MicStateInfo;
import com.simi.common.entity.RoomUserConf;
import com.simi.common.entity.room.*;
import com.simi.common.entity.user.NextEventData;
import com.simi.common.exception.ApiException;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.*;
import com.simi.common.vo.RoomAudienceVo;
import com.simi.common.vo.req.RoomIdReq;
import com.simi.common.vo.req.RoomKickOutReq;
import com.simi.common.vo.req.RoomUpdateReq;
import com.simi.common.vo.req.roomParty.RoomPartyFilterReq;
import com.simi.common.vo.resp.RoomManagementInfoGetResp;
import com.simi.common.vo.resp.UserLevelBaseVO;
import com.simi.common.vo.room.SingleRoomPartyVO;
import com.simi.common.vo.search.SearchRoomVO;
import com.simi.constant.*;
import com.simi.dto.Audience;
import com.simi.dto.event.KickOutEventDTO;
import com.simi.dto.event.RoomInTimeEventDTO;
import com.simi.entity.room.Room;
import com.simi.entity.room.RoomCommonCopywriting;
import com.simi.entity.room.RoomKickOutEventModel;
import com.simi.entity.room.RoomUserInRecord;
import com.simi.event.UserInRoomEvent;
import com.simi.message.UserInRoomMessage;
import com.simi.service.EventTrackingService;
import com.simi.service.LongLinkService;
import com.simi.service.SensitiveVocabularyService;
import com.simi.service.agora.AgoraService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.cache.AccountCache;
import com.simi.service.cache.RoomEventCache;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.privatephoto.PrivatePhotoHighService;
import com.simi.service.room.cache.RoomMsgRecordRedisService;
import com.simi.service.room.party.RoomPartyServerService;
import com.simi.service.room.roommic.RoomMicLowService;
import com.simi.service.room.roommic.RoomMicMiddleService;
import com.simi.service.user.BlockRecordService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import com.simi.util.TranslationCopyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 房间高层
 *
 * @Author: Andy
 * @Date: 2023/11/10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomHighService {

    private final RedissonClient redissonClient;
    private final RedissonDistributionLocker distributionLocker;
    private final RoomService roomService;
    private final RoomUserInRecordService roomUserInRecordService;
    private final UserServerService userServerService;
    private final RoomManagerService roomManagerService;
    private final RoomLowService roomLowService;
    private final RoomLiveLowService roomLiveLowService;
    private final RoomMicLowService roomMicLowService;
    private final RoomMicMiddleService roomMicMiddleService;
    private final AgoraService agoraService;
    private final SensitiveVocabularyService sensitiveVocabularyService;
    private final LongLinkService longLinkService;
    private final SystemConfigService systemConfigService;
    private final PrivatePhotoHighService privatePhotoHighService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final ApplicationContext applicationContext;
    private final RedissonManager redissonManager;
    private final RoomManagerHighService roomManagerHighService;
    private final BlockRecordService blockRecordService;
    private final RoomEventCache roomEventCache;
    private final EventTrackingService eventTrackingService;
    private final UserAristocracyRecordsService userAristocracyRecordsService;
    private final RoomPartyServerService partyServerService;
    private final AccountCache accountCache;
    private final UserVipService userVipService;
    private final RoomMsgRecordRedisService roomMsgRecordRedisService;
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * 用户踩房redis健
     *
     * @param uid
     * @return
     */
    private static String roomHistoryKey(Long uid) {
        return RoomRedisKey.room_history.getKey(StrUtil.format("{{}}", uid));
    }

    private static String speechCensusKey(String roomId) {
        return RoomRedisKey.speech_census.getKey(StrUtil.format("{{}}", roomId));
    }

    public Integer getRoomMode(String roomId) {
        var v = redissonManager.get("room_mode:" + roomId);
        try {
            return Integer.parseInt(v);
        } catch (Exception e) {
            return 0;
        }
    }


    public String skipRoom(Long uid, Integer mode) {
        Map<String, String> roomMap = redissonManager.hGetAll("simi:game_room:" + mode);
        try {
            List<String> roomIds = roomMap.keySet().stream().toList();
            Map<String, Room> stringRoomMap = roomService.batchGet(roomIds);
            for (String roomId : roomIds) {
                Room room = stringRoomMap.get(roomId);
                if (room != null) {
                    return room.getId();
                }
            }
            Room room = roomService.getRoomByUid(uid);
            if (room != null) {
                return room.getId();
            }
        } catch (Exception e) {
            log.error("skip error............. e:{}", e.getMessage());
        }
        return "";
    }

    public Map<Long, RoomUserBaseDTO> getRoomAudience(String roomId) {
        RScoredSortedSet<String> audienceRScore = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)));
        Collection<ScoredEntry<String>> audienceEntries = audienceRScore.entryRange(0, -1);
        if (CollectionUtils.isEmpty(audienceEntries)) {
            return new HashMap<>();
        }
        List<Long> uids = audienceEntries.stream().map(e -> Long.parseLong(e.getValue())).toList();
        // vip 过滤
        uids = uids.stream().filter(k -> {
            if (userVipService.isInvisibleEnter(k)) {
                // 获取该用户在房间是否有送礼、发公屏、上麦
                String value = redissonManager.get(RoomRedisKey.room_action.getKey(roomId, k));
                // 不为空则需要现身
                return StringUtils.isNotBlank(value);
            }
            return true;
        }).collect(Collectors.toList());
        return roomLowService.batchRoomUserInfo(roomId, uids);
    }

    public void setRoomMode(String roomId, Integer mode) {
        var oldMode = redissonManager.get("room_mode:" + roomId);
        if (StrUtil.isNotBlank(oldMode)) {
            if (oldMode.equals(mode.toString())) {
                return;
            } else {
                redissonManager.set("room_mode:" + roomId, mode.toString());

                redissonManager.hDel("simi:game_room:" + oldMode, roomId);
                redissonManager.hSet("simi:game_room:" + mode, roomId, "1");

                return;
            }
        }

        redissonManager.hSet("simi:game_room:" + mode, roomId, "1");
        redissonManager.set("room_mode:" + roomId, mode.toString());
    }

    public Map<String, Room> batchGetByUids(List<Long> uids) {
        return roomService.batchGetByUids(uids);
    }


    /**
     * 获取房间信息
     *
     * @param roomId
     * @return
     */
    public RoomGetResp getRoomInfoResp(Long uid, String roomId) {
        RoomInfoDTO roomInfoPB = roomLowService.getRoomInfoPB(roomId);
        RoomConf roomConf = roomLowService.getRoomConf(roomId);
        if (roomInfoPB != null) {
            UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(roomInfoPB.getRoomUid());
            if (userBaseInfo != null) {
                roomInfoPB.setCountry(userBaseInfo.getCountryCode());
                roomInfoPB.setHasPrettyNo(userBaseInfo.getHasPrettyNo());
            }
        }
        return new RoomGetResp(roomInfoPB, roomConf.getMicUpType());
    }

    public List<RoomInfoDTO> batchGetRoom(List<String> roomIdList) {
        return roomLowService.batchGetRoom(roomIdList);
    }

    /**
     * 获取房间管理信息
     *
     * @param roomId
     * @return
     */
    public RoomManagementInfoGetResp getManagementInfoResp(String roomId) {
        final RoomInfoDTO roomInfoPB = roomLowService.getRoomInfoPB(roomId);
        final RoomConf roomConf = roomLowService.getRoomConf(roomId);

        final RoomManagementInfoGetResp resp = new RoomManagementInfoGetResp();
        resp.setRoomInfoDTO(roomInfoPB);
        resp.setMicUpType(roomConf.getMicUpType());
        return resp;
    }

    /**
     * 更新房间信息
     *
     * @param uid
     * @param req
     */
    public Room updateRoomInfo(Long uid, RoomUpdateReq req) {
        Date date = new Date();
        log.info("roomHighService updateRoomInfo, roomId:{}, uid:{}", req.getRoomId(), uid);

        final String roomId = req.getRoomId();
        RLock rLock = redissonManager.fairLock(RoomRedisKey.room_update_info_lock.getKey(StrUtil.format("{{}}", roomId)), 5, 10);
        if (Objects.isNull(rLock) || !rLock.isLocked()) {
            log.error("fail to get room_update_lock, uid:{}", uid);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        try {
            Room room = roomService.getById(roomId);
            Set<String> uidsSet = redissonClient.<String>getSet(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId))).readAll();
            List<Long> uids = new ArrayList<>();
            if (!CollectionUtils.isEmpty(uidsSet)) {
                uids = uidsSet.stream().map(Long::parseLong).toList();
            }
            if (Objects.isNull(room) || (!room.getUid().equals(uid) && !uids.contains(uid))) {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }


            if (StrUtil.isNotBlank(req.getTitle())) {
                if (sensitiveVocabularyService.recognize(req.getTitle())) {
                    throw new ApiException(CodeEnum.ROOM_TITLE_ILLEGAL);
                }
            }
            if (StrUtil.isNotBlank(req.getRoomDesc())) {
                if (sensitiveVocabularyService.recognize(req.getRoomDesc())) {
                    throw new ApiException(CodeEnum.ROOM_DESC_ILLEGAL);
                }
            }
            Date param = new Date();
            log.info("roomHighService updateRoomInfoRun1111 paramTime:{}", date.getTime() - param.getTime());
            String avatar = req.getAvatar();
            if (StrUtil.isNotBlank(avatar) && !Objects.equals(OssUrlUtil.jointUrl(avatar), OssUrlUtil.jointUrl(room.getAvatar()))) {
                ShumeiAuditImgResp auditResult = privatePhotoHighService.auditShowImg(uid, avatar);
                // 头像审核被拒绝
                if (StrUtil.equalsIgnoreCase(ShumeiConstant.RiskLevel.REJECT, auditResult.getRiskLevel())) {
                    avatar = room.getAvatar();
                    log.warn("modify room info avatar shumei audit reject uid:[{}] roomId:[{}]", uid, roomId);

                    // 系统消息通知
                    LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
                    String[] attachArr = new String[]{auditResult.getRiskLabel1()};

                    TranslationCopyDTO textTranslation = TranslationCopyUtil.translationCopy(CopywritingEnum.AVATAR_REVIEW_FAILED_TEXT.getKey(), attachArr);
                    notifyMessageComponent.pushSystemComplexIMMsg(languageEnum, uid, System.currentTimeMillis(),
                            null, null, CopywritingEnum.AVATAR_REVIEW_FAILED_TEXT, attachArr,
                            textTranslation, null, null);
                }
            }
            Date avatarTime = new Date();
            log.info("roomHighService updateRoomInfoRun3333 paramTime:{}", param.getTime() - avatarTime.getTime());
            room.setAvatar(avatar);
            if (!StringUtils.isEmpty(req.getTitle())) {
                room.setTitle(req.getTitle());
            }
            if (!StringUtils.isEmpty(req.getRoomDesc())) {
                room.setRoomDesc(req.getRoomDesc());
            }

            Boolean roomLock = req.getRoomLock();
            String roomPasswd = req.getRoomPasswd();
            if (Objects.nonNull(roomLock)) {
                room.setIsLock(roomLock);

            }
            if (Objects.nonNull(roomLock) && roomLock && StrUtil.isNotBlank(roomPasswd)) {
                // 直接存密文
                //String roomPasswdAES = SecureUtil.aes(Constant.ROOM_PWD_AES_KEY.getBytes()).encryptBase64(roomPasswd.getBytes(StandardCharsets.UTF_8));
                room.setLockPasswd(roomPasswd);
            } else {
                room.setLockPasswd(StrUtil.EMPTY);
                log.info("RoomHighService room:{}", room);
                roomLiveLowService.roomInLiveUpdate(roomId);
            }

            // 更新房间背景图
            String roomBg = req.getRoomBg();
            if (StringUtils.isNotBlank(roomBg)) {
                room.setBackgroundUrl(roomBg);
            }

            Date updateTime = new Date();
            log.info("roomHighService updateRoomInfoRun4444 paramTime:{}", avatarTime.getTime() - updateTime.getTime());
            room.setUpdateTime(updateTime);
            //room.setQuickWelcomeStr(req.getQuickWelcomeStr());
            roomService.updateById(room);
            //删除，重载
            redissonClient.getMap(roomService.roomKey()).remove(roomId);
            roomLiveLowService.roomInLiveUpdate(roomId);
            //更新房间配置
            //updateRoomConf(roomId, req.getBookSongType(), req.getMicUpType(), req.getMusicStageInterlude(), req.getQuickWelcomeStr(), commonReq.getAppLanguage());
            room.setAvatar(OssUrlUtil.jointUrl(room.getAvatar()));
            log.info("roomHighService updateRoomInfoRun4444e endTime:{}", updateTime.getTime() - new Date().getTime());
            return room;
        } catch (Exception e) {
            if (e instanceof ApiException) {
                throw (ApiException) e;
            }
            log.error("RoomHighService updateRoomInfo fail, uid:{}, msg:{}", uid, e.getMessage(), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        } finally {
            if (rLock.isHeldByCurrentThread()) {
                rLock.unlock();
            }
        }
    }

    /**
     * 获取用户在房间内的配置
     *
     * @param roomId
     * @return
     */
    public List<RoomUserConf> listUserConf(String roomId) {
        Collection<String> roomUserConfStrs = redissonClient.<String, String>getMap(RoomRedisKey.room_user_conf.getKey(StrUtil.format("{{}}", roomId))).readAllValues();
        if (CollectionUtils.isEmpty(roomUserConfStrs)) {
            return Collections.emptyList();
        }

        List<RoomUserConf> pbList = Lists.newArrayListWithCapacity(roomUserConfStrs.size());
        for (String userConf : roomUserConfStrs) {
            final RoomUserConf pb = roomLowService.buildRoomUserConfPB(JSONUtil.toBean(userConf, RoomUserConf.class));
            pbList.add(pb);
        }
        return pbList;
    }

    /**
     * 获取用户房间内信息
     *
     * @param roomId
     * @param uid
     * @return
     */
    public RoomUserBaseDTO getRoomUserBase(String roomId, Long uid, Long targetUid) {
        return roomLowService.getRoomUserBase(roomId, uid, targetUid);
    }

    /**
     * 用户房间内禁麦
     *
     * @param uid
     * @param roomId
     * @param isMute
     */
    public NextEventData userMute(long uid, String roomId, boolean isMute) {
        log.info("RoomHighService userMute, roomId:{}, uid:{}, isMute:{}", roomId, uid, isMute);
        try (Locker locker = distributionLocker.lock(RoomRedisKey.room_audience_lock.getKey(StrUtil.format("{{}}", uid)))) {
            if (Objects.isNull(locker)) {
                log.error("Fail to get room_audience_lock, uid:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }

            String roomUserConfStr = redissonClient.<String, String>getMap(RoomRedisKey.room_user_conf.getKey(StrUtil.format("{{}}", roomId))).get(String.valueOf(uid));

            if (!isMute && StrUtil.isBlank(roomUserConfStr)) {
                return null;
            }
            RoomUserConf roomUserConf = JSONUtil.toBean(roomUserConfStr, RoomUserConf.class);

            long currentTimeMillis = System.currentTimeMillis();
            roomUserConf = Objects.isNull(roomUserConf) ? new RoomUserConf(uid) : roomUserConf;
            roomUserConf.setMute(isMute);
            roomUserConf.setTimestamp(currentTimeMillis);
            redissonClient.<String, String>getMap(RoomRedisKey.room_user_conf.getKey(StrUtil.format("{{}}", roomId))).put(String.valueOf(uid), JSONUtil.toJsonStr(roomUserConf));
            Collection<String> roomUserConfs = redissonClient.<String, String>getMap(RoomRedisKey.room_user_conf.getKey(StrUtil.format("{{}}", roomId))).readAllValues();
            List<RoomUserConf> pbList = Lists.newArrayListWithCapacity(roomUserConfs.size());
            for (String userConf : roomUserConfs) {
                RoomUserConf pb = roomLowService.buildRoomUserConfPB(JSONUtil.toBean(userConf, RoomUserConf.class));
                pbList.add(pb);
            }
            JsonMapper json = new JsonMapper();
            //notifyMessageComponent.publishChatroomDefineMsg(eventData);
            return new NextEventDataPBBuilder().setRoomId(roomId)
                    .addCommonMessage(CommonMessageEvent.RoomUserConfUpdateEvent, json.writeValueAsString(pbList)).build();
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("RoomHighService userMute error, uid:{}, roomId:{}, isMute:{}", uid, roomId, isMute);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    /**
     * 用户进房
     *
     * @param uid
     * @param roomId
     * @param appVersion
     */
    public RoomInDTO inRoom(Long uid, String roomId, String roomPasswd, Long followUid, String appVersion) {
        log.info("RoomHighService inRoom, roomId:{}, uid:{}", roomId, uid);
        try (Locker locker = distributionLocker.lock(RoomRedisKey.room_audience_lock.getKey(StrUtil.format("{{}}", uid)))) {
            if (Objects.isNull(locker)) {
                log.error("fail to get room_audience_lock, uid:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            //处理异常进房记录
            handleAbnormalInRecord(uid);

            //重建房间
            Long roomUid = null;
            if (StrUtil.isBlank(roomId)) {
                roomUid = uid;
            } else {
                Room room = roomService.getRoom(roomId);
                if (Objects.nonNull(room)) {
                    roomUid = room.getUid();
                }
            }
            if (Objects.isNull(roomUid)) {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            Room room = openRoom(roomUid, null);
            String realRoomId = room.getId();
            blockRecordService.blackByUid(room.getUid());
            //判断进房限制
            boolean inRoomBlacklist = roomManagerHighService.isInRoomBlacklist(realRoomId, uid);
            if (inRoomBlacklist) {
                throw new ApiException(CodeEnum.KICK_OUT_ROOM);
            }

            //房间密码校验
            checkRoomPasswd(room, uid, roomPasswd);

            roomUserInRecordService.inRoomRecord(uid, realRoomId, roomUid);
            redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", realRoomId)))
                    .add(Convert.toDouble(System.currentTimeMillis()), uid);
            redissonClient.getMap(RoomRedisKey.room_user_in.getKey()).put(uid, realRoomId);

            redissonClient.getScoredSortedSet(roomHistoryKey(uid)).add(System.currentTimeMillis(), realRoomId);
            redissonClient.getScoredSortedSet(roomHistoryKey(uid)).expire(30, TimeUnit.DAYS);

            RoomInDTO dto = new RoomInDTO();
            dto.setRoomId(room.getId());
            String agoraToken = agoraService.getAgoraToken(roomId, uid);
            dto.setAgoraToken(agoraToken);
            RoomIdentity userIdentity = roomManagerService.getUserIdentityPB(uid, room.getId());
            dto.setIdentity(userIdentity);
            dto.setLongLinkToken(longLinkService.genInRoomToken(room.getId(), uid));
            roomEventCache.setRoomInTime(roomId, uid);
            Long silence = roomLowService.isSilence(roomId, uid);
            dto.setSilenceEndTime(silence == null ? 0 : silence);
            return dto;
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("RoomHighService inRoom error, uid:{}, roomId:{}, msg:{}", uid, roomId, e.getMessage(), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }


    private void pushUserInRoomEventMsg(Room room, Long uid, Long followUid, String appVersion) {
        try {
            UserInRoomMessage eventMessage = new UserInRoomMessage();
            eventMessage.setUid(uid);
            eventMessage.setRoom(room);
            eventMessage.setFollowUid(followUid);
            eventMessage.setAppVersion(appVersion);
            UserInRoomEvent event = new UserInRoomEvent(eventMessage);
            applicationContext.publishEvent(event);
        } catch (Exception e) {
            log.warn("push User InRoom EventMsg fail:[{}] uid:[{}] roomId:[{}]", e.getMessage(), uid, room.getId());
        }
    }


    public void inRoomLongLink(String roomId, Long uid) {
        // vip 隐身，不推送
        if (userVipService.isInvisibleEnter(uid)) {
            log.info("vip 用户设置了隐身设置 inRoomLongLink userId:{}", uid);
            return;
        }
        //如果不是 vip 隐身，走下面流程
        scheduler.schedule(() -> {
            try {
                String system = "";
                String language = MessageSourceUtil.getLang().name();
                Room room = roomService.getRoom(roomId);
                UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);

                if (LanguageEnum.ar.name().equals(language)) {
                    system = systemConfigService.getSysConfValueById(SystemConfigConstant.IN_ROOM_SYSTEM_NOTICE_AR);
                } else {
                    system = systemConfigService.getSysConfValueById(SystemConfigConstant.IN_ROOM_SYSTEM_NOTICE_EN);
                }
                system = "<p><span style='color:#17EBAD'>" + system + "</span></p>";
                longLinkService.pushRoomMsg(roomId, userPB, system, PushEvent.room_screen_system_notice_event, PushToType.EXCLUDE_OTHER);
                //发送房间公告
                String welcome = "<p><span style='color:#17EBAD'>" + room.getRoomDesc() + "  </span></p>";
                longLinkService.pushRoomMsg(roomId, userPB, welcome, PushEvent.room_screen_system_notice_event, PushToType.EXCLUDE_OTHER);
                //房间装扮信息
                log.info("RoomHighService inRoom LongLink user:{}", userPB);
                SendPropInfoDTO sendPropInfoDTO = new SendPropInfoDTO();
                sendPropInfoDTO.setUserBaseInfo(userPB);
                sendPropInfoDTO.setRoomId(roomId);
                longLinkService.pushCustomerGlobalNewMsg(sendPropInfoDTO, null, PushEvent.room_user_prop_info_event, PushToType.MESSAGE_TO_ALL);
                longLinkService.pushCustomerRoomMsg(roomId, userPB, PushEvent.room_user_prop_info_event, PushToType.MESSAGE_TO_ALL);
                //发送party推送
                pushPartyMsg(roomId, uid);

            } catch (Exception e) {
                log.error("RoomHighService inRoomLongLink error, uid:{}", uid, e);
            }
        }, 2, TimeUnit.SECONDS);
    }

    public void sendScreen(String roomId, Long uid) {
        // vip 隐身，不推送
        if (userVipService.isInvisibleEnter(uid)) {
            log.info("vip 用户设置了隐身设置 inRoomLongLink userId:{}", uid);
            return;
        }
        String system = "";
        String language = MessageSourceUtil.getLang().name();
        Room room = roomService.getRoom(roomId);
        UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);

        if (LanguageEnum.ar.name().equals(language)) {
            system = systemConfigService.getSysConfValueById(SystemConfigConstant.IN_ROOM_SYSTEM_NOTICE_AR);
        } else {
            system = systemConfigService.getSysConfValueById(SystemConfigConstant.IN_ROOM_SYSTEM_NOTICE_EN);
        }
        system = "<p><span style='color:#17EBAD'>" + system + "</span></p>";
        longLinkService.pushRoomMsg(roomId, userPB, system, PushEvent.room_screen_system_notice_event, PushToType.EXCLUDE_OTHER);
        //发送房间公告
        String welcome = "<p><span style='color:#17EBAD'>" + room.getRoomDesc() + "  </span></p>";
        longLinkService.pushRoomMsg(roomId, userPB, welcome, PushEvent.room_screen_system_notice_event, PushToType.EXCLUDE_OTHER);
        //发送party推送
        pushPartyMsg(roomId, uid);
    }

    public void sendPropInfo(String roomId, Long uid) {
        // vip 隐身，不推送
        if (userVipService.isInvisibleEnter(uid)) {
            log.info("vip 用户设置了隐身设置 inRoomLongLink userId:{}", uid);
            return;
        }
        UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);
        //房间装扮信息
        log.info("RoomHighService inRoom LongLink user:{}", userPB);
        SendPropInfoDTO sendPropInfoDTO = new SendPropInfoDTO();
        sendPropInfoDTO.setUserBaseInfo(userPB);
        sendPropInfoDTO.setRoomId(roomId);
        longLinkService.pushCustomerGlobalNewMsg(sendPropInfoDTO, null, PushEvent.room_user_prop_info_event, PushToType.MESSAGE_TO_ALL);
        longLinkService.pushCustomerRoomMsg(roomId, userPB, PushEvent.room_user_prop_info_event, PushToType.MESSAGE_TO_ALL);
    }

    private void pushPartyMsg(String roomId, Long uid) {
        RoomPartyFilterReq filterReq = RoomPartyFilterReq.builder()
                .roomId(roomId)
                .history(false)
                .build();
        filterReq.setPageNum(1);
        filterReq.setPageSize(1);
        try {
            List<SingleRoomPartyVO> partyList = partyServerService.getPartyListByRoomId(uid, filterReq, false);
            log.info("pushPartyMsg get party msg result:{}", JSON.toJSONString(partyList));
            if (CollectionUtils.isEmpty(partyList)) {
                return;
            }

            SingleRoomPartyVO singleRoomPartyVO = partyList.get(0);
            List<String> idList = Arrays.asList(uid.toString());
            longLinkService.pushCustomerRoomMsgByUidList(roomId, JSON.toJSONString(singleRoomPartyVO), PushEvent.room_party_event, PushToType.EXCLUDE_OTHER, idList);

        } catch (Exception e) {
            log.error("pushPartyMsg error, uid:{},error msg:{}", uid, ExceptionUtil.formatEx(e));
        }
    }

    /**
     * 处理异常进房记录
     *
     * @param uid
     */
    private void handleAbnormalInRecord(Long uid) {
        Date now = new Date();
        final List<RoomUserInRecord> abnormalInRecords = roomUserInRecordService.listAbnormalInRecord(uid);
        if (CollectionUtils.isNotEmpty(abnormalInRecords)) {
            for (RoomUserInRecord record : abnormalInRecords) {
                outRoom(uid, record.getRoomId(), now);
            }
        }
    }

    /**
     * 用户退出房间
     *
     * @param uid
     * @param roomId
     */
    @MissionComplete({MissionEnum.STAY_IN_ROOM_45_MINS})
    public void outRoom(long uid, String roomId, Date outTime) {
        log.info("RoomHighService outRoom, roomId:{}, uid:{}", roomId, uid);
        try (Locker locker = distributionLocker.lock(RoomRedisKey.room_audience_lock.getKey(StrUtil.format("{{}}", uid)))) {
            if (Objects.isNull(locker)) {
                log.error("fail to get room_audience_lock, uid:{}", uid);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }

            final boolean contains = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId))).contains(uid);
            if (!contains) {
                // 补偿处理旧版本遗留数据不闭环导致退房, 麦位没清理的问题
                checkMicInfoAndDown(uid, roomId);
                return;
            }
            log.info("RoomHighService outRoom, uid:{}, roomId:{}, outTime:{}", uid, roomId, outTime);
            //下麦
            checkMicInfoAndDown(uid, roomId);
            roomUserInRecordService.outRoomRecord(uid, roomId, outTime);
            redissonClient.getMap(RoomRedisKey.room_user_in.getKey()).remove(uid);
            redissonClient.<String, String>getMap(RoomRedisKey.room_user_conf.getKey(StrUtil.format("{{}}", roomId))).remove(String.valueOf(uid));
            redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)))
                    .remove(uid);

            handleMicApplyForOutRoom(roomId, uid);
            roomUserInRecordService.outRoomRecord(uid, roomId, new Date());
            CompletableFuture.runAsync(() -> {
                try {
                    RoomAudienceDTO roomAudienceDTO = audienceTop(roomId, 3);
                    if (roomAudienceDTO != null) {
                        // 清除房间动作行为
                        redissonManager.del(RoomRedisKey.room_action.getKey(roomId, uid));
                        longLinkService.pushCustomerRoomMsg(roomId, roomAudienceDTO, PushEvent.room_audience_update_event, PushToType.MESSAGE_TO_ALL);
                    }
                } catch (Exception e) {
                    log.error("send outRoom longLink error..............{}", e.getMessage());
                }
            });
            try {
                sendRoomEndStayTime(uid, roomId);
                roomExitTime(roomId);
            } catch (Exception e) {
                log.error("RoomHignService sendRoomEndStayTime uid:{}, roomId:{},  msg:{}", uid, roomId, e.getMessage(), e);
            }

            //记录结束直播，对管理员房主执行
            handleCompleteRecord(roomId, uid);
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("RoomHighService outRoom error, uid:{}, roomId:{}, msg:{}", uid, roomId, e.getMessage(), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    private void handleCompleteRecord(String roomId, long uid) {
        try {
            RoomIdentity userIdentityPB = roomManagerService.getUserIdentityPB(uid, roomId);
            if (RoomIdentity.ROOM_AUDIENCE.equals(userIdentityPB)) {
                return;
            }
            Set<String> allManagerMembers = new HashSet<>();
            //管理员
            Set<String> members = redissonManager.sMembers(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId)));
            if (CollectionUtils.isNotEmpty(members)) {
                allManagerMembers.addAll(members);
            }

            Room room = roomService.getRoom(roomId);
            if (Objects.isNull(room)) {
                return;
            }

            Long ownerId = room.getUid();
            allManagerMembers.add(ownerId.toString());
            RScoredSortedSet<Object> scoredSortedSet =
                    redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)));
            //房间列表没有管理员或者是房组，则定义为下播
            for (String managerMember : allManagerMembers) {
                if (scoredSortedSet.contains(managerMember)) {
                    return;
                }
            }

            roomMsgRecordRedisService.recordCompletedRoom(roomId, new Date());
        } catch (Exception e) {
            log.error("handleCompleteRecord error,roomId:{},uid:{},msg:{}", roomId, uid, ExceptionUtil.formatEx(e));
        }
    }

    private void checkMicInfoAndDown(long uid, String roomId) {
        try {
            final List<MicStateInfo> micStateInfos = roomMicLowService.listMicInfo(roomId);
            final MicStateInfo micStateInfo = micStateInfos.stream().filter(x -> Objects.nonNull(x.getUserInfo()) && uid == x.getUserInfo().getUid()).findFirst().orElse(null);
            if (Objects.nonNull(micStateInfo)) {
                roomMicMiddleService.downMic(uid, roomId, micStateInfo.getPosition(), DownMicType.GENERAL_DOWN_MIC.getType());
            }
        } catch (Exception e) {
            log.error("RoomHighService outRoom down mic error, uid:{}, roomId:{}, msg:{}", uid, roomId, e.getMessage(), e);
        }
    }

    private void sendRoomEndStayTime(Long uid, String roomId) {
        Long roomInTimeLong = roomEventCache.getRoomInTime(roomId, uid);
        long secondsBetween = 0;
        if (roomInTimeLong == null || roomInTimeLong <= 0) {
            RoomUserInRecord roomUserInRecord = roomUserInRecordService.getByUidRecord(uid, roomId);
            Date inRoomTime = roomUserInRecord.getInRoomTime();
            secondsBetween = TimeUtils.secondsBetween(inRoomTime, new Date());
        } else {
            Date roomInTime = new Date(roomInTimeLong);
            secondsBetween = TimeUtils.secondsBetween(roomInTime, new Date());
        }
        String deviceId = accountCache.getDeviceId(uid);
        RoomInTimeEventDTO dto = new RoomInTimeEventDTO(roomId, secondsBetween, uid);
        dto.setDeviceId(deviceId);
        eventTrackingService.handleEvent(dto, EventTrackingConstant.EventName.ROOM_END_STAY_TIME);
    }

    private void roomExitTime(String roomId) {
        RoomIdReq req = new RoomIdReq(roomId);
        eventTrackingService.handleEvent(req, EventTrackingConstant.EventName.ROOM_EXIT);
    }

    public Room getRoom(Long uid) {
        return roomService.getRoomByUid(uid);
    }


    /**
     * 处理用户离开房间，麦位申请列表
     *
     * @param roomId
     * @param uid
     */
    private void handleMicApplyForOutRoom(String roomId, long uid) {
        try (Locker locker = distributionLocker.lock(RoomRedisKey.room_mic_apply_list_lock.getKey(StrUtil.format("{{}}", roomId)))) {
            if (Objects.isNull(locker)) {
                log.error("fail to get room_mic_apply_list_lock, roomId:{}", roomId);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            RList<Long> micApplyRList = redissonClient.getList(RoomRedisKey.room_mic_apply_list.getKey(StrUtil.format("{{}}", roomId)));
            if (!micApplyRList.contains(uid)) {
                return;
            }
            micApplyRList.remove(uid);
        } catch (Exception e) {
            log.error("RoomHighService handleMicApplyForOutRoom, roomId:{}, uid:{}, msg:{}", roomId, uid, e.getMessage(), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    public static void main(String[] args) {
        String b = null;
        System.out.println(StringUtils.isNotBlank(b));
    }

    /**
     * 获取观众列表
     *
     * @param roomId
     */
    public List<RoomUserBaseDTO> listRoomAudiencePB(String roomId) {
        RScoredSortedSet<String> audienceRScore = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)));
        Collection<ScoredEntry<String>> audienceEntries = audienceRScore.entryRange(0, -1);
        if (CollectionUtils.isEmpty(audienceEntries)) {
            return Collections.emptyList();
        }
        List<Long> uids = audienceEntries.stream().map(e -> Long.parseLong(e.getValue())).toList();
        // vip 过滤
        uids = uids.stream().filter(k -> {
            if (userVipService.isInvisibleEnter(k)) {
                // 获取该用户在房间是否有送礼、发公屏、上麦
                String value = redissonManager.get(RoomRedisKey.room_action.getKey(roomId, k));
                // 不为空则需要现身
                log.info("Vip 用户开启了隐身进房 。uid:{},value:{}", k, value);
                return StringUtils.isNotBlank(value);
            }
            return true;
        }).collect(Collectors.toList());
        Set<Long> roomUsers = new HashSet<>(uids);
        Map<Long, RoomUserBaseDTO> roomUserInfoMap = roomLowService.batchRoomUserInfo(roomId, uids);
        Map<Long, UserCurAristocracyInfo> userAristocracyRecordsBatch = userAristocracyRecordsService.getUserAristocracyRecordsBatch(uids);
        List<RoomAudienceVo> voList = new ArrayList<>(audienceEntries.stream().map(entry -> {
            Long uid = Long.valueOf(entry.getValue());
            if (!roomUsers.contains(uid)) {
                return null;
            }
            RoomAudienceVo vo = new RoomAudienceVo();
            RoomUserBaseDTO roomUserBaseDTO = roomUserInfoMap.get(uid);
            UserCurAristocracyInfo userCurAristocracyInfo = userAristocracyRecordsBatch.get(uid);
            if (userCurAristocracyInfo != null) {
                roomUserBaseDTO.setCurAristocracy(userCurAristocracyInfo.getCurAristocracy());
            }
            vo.setRoomUserBaseDTO(roomUserBaseDTO);
            vo.setScore(entry.getScore());
            return vo;
        }).filter(Objects::nonNull).toList());

        voList.sort(Comparator.comparing((RoomAudienceVo x) -> x.getRoomUserBaseDTO().getRoomIdentity(), Comparator.reverseOrder())
                .thenComparing(Comparator.comparing((RoomAudienceVo x) -> x.getRoomUserBaseDTO().getCurAristocracy(), Comparator.reverseOrder())
                        .thenComparing(RoomAudienceVo::getScore, Comparator.reverseOrder())));

        return voList.stream().map(RoomAudienceVo::getRoomUserBaseDTO).collect(Collectors.toList());
    }

    public RoomAudienceDTO audienceTop(String roomId, Integer size) {
        RoomAudienceDTO roomAudienceDTO = new RoomAudienceDTO();
        List<RoomUserBaseDTO> dtoList = listRoomAudiencePB(roomId);
        if (!CollectionUtils.isEmpty(dtoList)) {
            if (dtoList.size() > size) {
                roomAudienceDTO = new RoomAudienceDTO(dtoList.subList(0, size), dtoList.size());
            } else {
                roomAudienceDTO = new RoomAudienceDTO(dtoList, dtoList.size());
            }
        } else {
            roomAudienceDTO.setRoomAudience(new ArrayList<>());
        }
        return roomAudienceDTO;
    }

    public RoomCleanDTO getUserIdentity(Long uid, String roomId) {
        RoomCleanDTO roomCleanDTO = new RoomCleanDTO();
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
        if (userBaseInfo == null) {
            return roomCleanDTO;
        }
        RoomIdentity roomIdentity = roomManagerService.getUserIdentityPB(uid, roomId);
        roomCleanDTO.setIdentity(roomIdentity);
        roomCleanDTO.setUid(uid);
        roomCleanDTO.setNick(userBaseInfo.getNick());
        return roomCleanDTO;
    }


    /**
     * 踢出房间
     *
     * @param uid
     */
    public NextEventData kickOutRoom(Long uid, RoomKickOutReq req) throws Exception {
        String roomId = req.getRoomId();
        Long targetUid = req.getTargetUid();
        Integer type = Optional.ofNullable(req.getType()).orElse(1);
        log.info("RoomHighService kickOutRoom, roomUid:{}, uid:{}, targetUid:{} type:{}", roomId, uid, targetUid, type);

        Room room = roomService.getRoom(roomId);
        if (Objects.isNull(room) || Objects.equals(room.getUid(), targetUid)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Set<String> members = redissonManager.sMembers(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId)));
        if (!members.contains(uid.toString()) && !Objects.equals(room.getUid(), uid)) {
            throw new ApiException(CodeEnum.ROOM_NO_AUTHORITY);
        }
        if (members.contains(String.valueOf(targetUid)) && !Objects.equals(room.getUid(), uid)) {
            throw new ApiException(CodeEnum.ROOM_NO_AUTHORITY);
        }

        // VIP
        // 判断被踢用户的VIP等级
        Optional<UserCurVipInfo> userVipCache = userVipService.getUserVipCache(targetUid);
        if (userVipCache.isPresent()) {
            UserCurVipInfo userCurVipInfo = userVipCache.get();
            if (userCurVipInfo.getVipLevel() >= 6) {
                // 判断操作人的VIP 等级
                Optional<UserCurVipInfo> optional = userVipService.getUserVipCache(uid);
                UserCurVipInfo oprVip = optional.orElseThrow(() -> new ApiException(CodeEnum.VIP_KICK_FAIL));
                Integer vipLevel = oprVip.getVipLevel();
                if (vipLevel < userCurVipInfo.getVipLevel()) {
                    throw new ApiException(CodeEnum.VIP_KICK_FAIL);
                }
            }
        }

        Date endTime;
        // 1-踢出1天; 2-永久踢出
        if (Objects.equals(type, 1)) {
            endTime = DateUtil.offsetDay(new Date(), 1);
        } else if (Objects.equals(type, 2)) {
            endTime = DateUtil.offsetMonth(new Date(), 240);
        } else {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        NextEventData nextEventData = dealKickOutRoom(uid, roomId, targetUid);
        RoomBalcklistDTO balcklistDTO = RoomBalcklistDTO.builder()
                .uid(targetUid)
                .endTime(endTime.getTime())
                .type(type)
                .operateUid(uid).build();
        redissonManager.hSet(RoomRedisKey.room_blacklist.getKey(roomId), String.valueOf(targetUid), JSONUtil.toJsonStr(balcklistDTO));
        redissonManager.sRemove(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId)), targetUid.toString());
        try {
            kickOutEvent(roomId, targetUid);
        } catch (Exception e) {
            log.error("RoomHignService sendRoomEndStayTime targetUid:{}, roomId:{},  msg:{}", targetUid, roomId, e.getMessage(), e);
        }
        return nextEventData;
    }

    private void kickOutEvent(String roomId, Long targetUid) {
        KickOutEventDTO dto = new KickOutEventDTO();
        dto.setToUid(targetUid);
        dto.setRoomId(roomId);
        String deviceId = accountCache.getDeviceId(targetUid);
        dto.setDeviceId(deviceId);
        eventTrackingService.handleEvent(dto, EventTrackingConstant.EventName.ROOM_REMOVE);
    }

    public NextEventData dealKickOutRoom(Long uid, String roomId, Long targetUid) {
        log.info("deal kick out room uid:[{}]  room:[{}]  targetUid:[{}]", uid, roomId, targetUid);

        // 用户退出房间
        outRoom(targetUid, roomId, new Date());

        //下发踢出消息
        UserBaseInfoDTO userDTO = userServerService.getUserBaseInfo(targetUid);
        LanguageEnum languageEnum = userServerService.userAppLanguage(targetUid);

        String copywriting = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_10.getKey(), languageEnum);

        RoomCommonCopywriting commonCopywriting = new RoomCommonCopywriting();
        commonCopywriting.setBackgroundImage(copywriting);

        RoomKickOutEventModel roomKickOut = new RoomKickOutEventModel();
        roomKickOut.setUid(targetUid);
        roomKickOut.setCommonCopywriting(commonCopywriting);

        NextEventData eventData = new NextEventDataPBBuilder().setRoomId(roomId)
                .addCommonMessage(CommonMessageEvent.RoomKickOutEvent, Lists.newArrayList(targetUid), JSONUtil.toJsonStr(roomKickOut)).build();

        longLinkService.pushRoomMsg(roomId, userDTO, copywriting, PushEvent.room_kick_out_event, PushToType.EXCLUDE_OTHER);

        try {
            //zegoComponent.kickoutUser(roomId, List.of(targetUid.toString()));
        } catch (Exception e) {
            log.error("login verification is error uid:[{}] e:[{}]", uid, e.getMessage());
        }
        return eventData;
    }

    public void blockKickOutRoom(String roomId, Long targetUid) {
        log.info("deal kick out room  room:[{}]  targetUid:[{}]", roomId, targetUid);
        // 用户退出房间
        outRoom(targetUid, roomId, new Date());
        try {
            //zegoComponent.kickoutUser(roomId, List.of(targetUid.toString()));
        } catch (Exception e) {
            log.error("login verification is error e:[{}]", e.getMessage());
        }

    }

    /**
     * 开启房间
     * 新建房间或重建被融云销毁的房间
     * 初始化麦位
     *
     * @param uid
     * @return
     */
    public Room openRoom(Long uid, String language) {
        String lockKey = RoomRedisKey.room_update_lock.getKey(StrUtil.format("{{}}", uid));
        try (Locker locker = distributionLocker.lock(lockKey)) {
            if (Objects.isNull(locker)) {
                log.error("Fail to get room_update_lock:" + lockKey);
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }

            Room room = roomService.getRoomByUid(uid);
            Date now = new Date();

            if (Objects.isNull(room)) {
                //新建房间
                final UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
                log.info("openRoom userBaseInfo:{}", userBaseInfo);
                language = StrUtil.isBlank(userBaseInfo.getAppLanguage()) ? LanguageEnum.en.name() : userBaseInfo.getAppLanguage();
                String title = userBaseInfo.getNick() + MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_1.getKey(), LanguageEnum.getLang(language));
                String roomDesc = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_9.getKey(), LanguageEnum.getLang(language));
                String quickWelcomeStr = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_19.getKey(), LanguageEnum.getLang(language)) + "\uD83D\uDC4B\uD83C\uDFFB";
                room = Room.builder()
                        .uid(uid)
                        .roomNo(uid)
                        .avatar(userBaseInfo.getAvatar())
                        .title(title)
                        .roomDesc(roomDesc)
                        .type(RoomConstant.RoomType.KTV_TYPE)
                        .valid(true)
                        .manageMax(10)
                        .createTime(now)
                        .updateTime(now)
                        .isLock(false)
                        .quickWelcomeStr(quickWelcomeStr)
                        .recType(RoomRecEnum.common.getRecType())
                        .build();
                roomService.save(room);
                redissonClient.getMap(roomService.roomUidIdKey()).put(room.getUid(), room.getId());
            } else {
                if (room.getValid()) {
                    return room;
                }
                //重建房间
                room = Room.builder()
                        .id(room.getId())
                        .uid(uid)
                        .valid(true)
                        .updateTime(now)
                        .build();
                roomService.updateById(room);
            }
            log.info("RoomHighService openRoom, uid:{}", uid);

/*            RSet<String> set = redissonClient.getSet(RoomRedisKey.room_in_live.getKey());
            set.add(room.getId());*/
            //房间上线
            roomMicLowService.initRoomMic(room.getId());

            //删除房间缓存，重载
            redissonClient.getMap(roomService.roomKey()).remove(room.getId());
            return room;
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("RoomHighService openRoom error, uid:{}, language:{}, msg:{}", uid, language, e.getMessage(), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }

    private void checkRoomPasswd(Room room, Long uid, String roomPasswd) throws Exception {
        if (Objects.isNull(room.getIsLock()) || !room.getIsLock() || room.getUid().equals(uid)) {
            return;
        }
        if (StrUtil.isBlank(roomPasswd)) {
            throw new ApiException(CodeEnum.ROOM_NEED_PWD);
        }

        if (StrUtil.equalsIgnoreCase(XEncryption.decryptX(room.getLockPasswd()), XEncryption.decryptX(roomPasswd))) {
            return;
        }
        throw new ApiException(CodeEnum.ROOM_PWD_ERROR);
    }

    /**
     * 踩房历史
     *
     * @param uid
     * @param page
     * @param pageSize
     * @return
     */
    public List<HomeRoomDTO> history(Long uid, Integer page, Integer pageSize) {
        int start = (page - 1) * pageSize;
        int limit = page * pageSize;
        Collection<ScoredEntry<String>> scoredEntries = redissonClient.<String>getScoredSortedSet(roomHistoryKey(uid)).entryRangeReversed(start, limit);
        List<HomeRoomDTO> homeRoomDTOS = scoredEntries.stream().map(entry -> {
            HomeRoomDTO homeRoomDTO = new HomeRoomDTO();
            homeRoomDTO.setRoomId(entry.getValue());
            homeRoomDTO.setScore(BigDecimal.valueOf(entry.getScore()));
            return homeRoomDTO;
        }).collect(Collectors.toList());
        homeRoomDTOS.sort(Comparator.comparing(HomeRoomDTO::getScore).reversed());
        return homeRoomDTOS;
    }

    /**
     * 获取房间观众数量
     *
     * @param roomId
     * @return
     */
    public int roomAudienceNum(String roomId) {
        return redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId))).size();
    }

    private void speechCensus(String roomId, Long timestamp) {
        redissonClient.getScoredSortedSet(speechCensusKey(roomId)).add(timestamp, timestamp);
        redissonClient.getScoredSortedSet(speechCensusKey(roomId)).expire(300L, TimeUnit.SECONDS);
    }

    /**
     * 获取用户当前所在房间
     *
     * @param uid
     * @return
     */
    public RoomInfoDTO getUserInRoom(long uid) {
        String roomId = redissonClient.<String, String>getMap(RoomRedisKey.room_user_in.getKey()).get(uid);
        if (StrUtil.isBlank(roomId)) {
            return null;
        }

        return roomLowService.getRoomInfoPB(roomId);
    }


    public Integer getRoomInMicNum(Long uid) {
        String roomId = redissonManager.hGet(RoomRedisKey.room_uid_id.getKey(), uid.toString());
        if (StringUtils.isNotBlank(roomId)) {
            List<MicStateInfo> micStateInfoList = roomMicLowService.listMicInfo(roomId);
            if (Objects.nonNull(micStateInfoList)) {
                List<MicStateInfo> micList = micStateInfoList.stream().filter(x -> Objects.nonNull(x.getUserInfo())).toList();
                return micList.size();
            }
        }
        return 0;

    }

    public Map<Long, RoomInfoDTO> getUserInRoom(List<Long> uids) {
        Map<Long, RoomInfoDTO> result = MapUtil.newHashMap();
        if (CollUtil.isEmpty(uids)) {
            return result;
        }
        Set<String> uidsSet = uids.stream().map(String::valueOf).collect(Collectors.toSet());
        Map<String, String> uidRoomIdMap = redissonClient.<String, String>getMap(RoomRedisKey.room_user_in.getKey()).getAll(uidsSet);
        if (CollUtil.isEmpty(uidRoomIdMap)) {
            return result;
        }
        uidRoomIdMap.forEach((uid, roomId) -> {
            RoomInfoDTO roomInfoPB = roomLowService.getRoomInfoPB(roomId);
            result.put(Long.parseLong(uid), roomInfoPB);
        });

        return result;
    }

    public List<RoomUserBaseDTO> listRoomUserBasePB(String roomId, List<Long> uidList) {
        return roomLowService.listRoomUserBasePB(roomId, uidList);
    }

    /**
     * 观众列表，返回观众进房时间
     *
     * @param
     * @return
     */
    public List<Audience> audienceList(AudienceReq audienceReq) {
        RScoredSortedSet<String> scoredSortedSet = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", audienceReq.getRoomId())));
        Collection<ScoredEntry<String>> scoredEntries = scoredSortedSet.entryRange(0, scoredSortedSet.size());
        List<Audience> audiences = scoredEntries.stream().filter(entry -> !Objects.equals(entry.getValue(), audienceReq.getExcludeUid())).map(entry -> new Audience(Long.parseLong(entry.getValue()), entry.getScore().longValue())).collect(Collectors.toList());
        return audiences;
    }

    public RoomConf getRoomConf(String roomId) {
        return roomLowService.getRoomConf(roomId);
    }

    /**
     * 在房人数
     *
     * @param roomId
     * @return
     */
    public int onlineNum(String roomId) {
        RScoredSortedSet<Long> scoredSortedSet = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)));
        return scoredSortedSet.size();
    }

    /**
     * 快速进房
     *
     * @return
     */
    public String roomQuick(long uid) {
        final RScoredSortedSet<String> roomQuickRScored = redissonClient.getScoredSortedSet(RoomRedisKey.room_quick.getKey());
        final Collection<String> roomIds = roomQuickRScored.valueRangeReversed(0, 19);
        if (CollectionUtils.isEmpty(roomIds)) {
            final Room room = roomService.getRoomByUid(uid);
            return room.getId();
        }

        Random random = new Random();
        String roomId = (String) roomIds.toArray()[random.nextInt(roomIds.size())];
        return roomId;
    }

    public RoomInfoDTO getById(String roomId) {
        return roomLowService.getRoomInfoPB(roomId);
    }

    public String defaultRooms() {
        String defaultRooms = systemConfigService.getSysConfValueById(SystemConfigConstant.DEFAULT_SCOPE_SKIP_ROOMS);
        if (StringUtils.isEmpty(defaultRooms)) {
            return "";
        }
        List<String> roomId = Arrays.asList(defaultRooms.split(","));
        Random random = new Random();
        int randomIndex = random.nextInt(roomId.size());
        return roomId.get(randomIndex);
    }

    public String skipRoomIds(Long uid) {
        //国家区域范围房间
        String skipRooms = systemConfigService.getSysConfValueById(SystemConfigConstant.COUNTRY_CODE_SCOPE_SKIP_ROOMS);
        if (StringUtils.isEmpty(skipRooms)) {
            //默认房间
            return defaultRooms();
        }
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
        List<CountryCodeSkipRooms> codeSkipRooms = JSONUtil.toList(skipRooms, CountryCodeSkipRooms.class);
        if (CollectionUtils.isNotEmpty(codeSkipRooms)) {
            //遍历跳转房间配置
            for (CountryCodeSkipRooms countryCodeSkipRooms : codeSkipRooms) {
                String codeScope = countryCodeSkipRooms.getCodeScope();
                //对应国家
                String[] codeScopeList = codeScope.split(",");
                for (String code : codeScopeList) {
                    if (!code.equalsIgnoreCase(userBaseInfo.getCountryCode())) {
                        continue;
                    }
                    RSet<String> roomInLiveRSet = redissonClient.getSet(RoomRedisKey.room_in_live.getKey());
                    if (CollectionUtils.isEmpty(roomInLiveRSet)) {
                        return defaultRooms();
                    }
                    String roomIdStr = countryCodeSkipRooms.getRoomIds();
                    List<String> roomIdList = Arrays.asList(roomIdStr.split(","));
                    List<String> roomInLiveList = new ArrayList<>(roomInLiveRSet);
                    roomInLiveList.retainAll(roomIdList);
                    if (roomInLiveList.size() <= 0) {
                        return defaultRooms();
                    }
                    if (roomInLiveList.size() == 1) {
                        return roomInLiveList.get(0);
                    }

                    Random random = new Random();
                    int randomIndex = random.nextInt(roomIdList.size());
                    return roomInLiveList.get(randomIndex);
                }
            }
        }
        //默认房间
        return defaultRooms();
    }

    public void kickOutRoomAll(Long userNo) {
        Long uid = userServerService.getUidByUserNo(userNo);
        if (uid != null) {
            Room room = roomService.getRoomByUid(uid);
            if (room != null) {
                Collection<String> list = redissonClient.<String>getScoredSortedSet(
                        RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", room.getId()))).readAll();
                List<String> uidStrList = new ArrayList<>(list);
                List<Long> uids = uidStrList.stream().map(Long::parseLong).collect(Collectors.toList());
                log.info("kickOutRoomAll uidStrList[{}] ", uidStrList);
                for (Long uidLong : uids) {
                    try {
                        UserBaseInfoDTO userDTO = userServerService.getUserBaseInfo(uidLong);
                        LanguageEnum languageEnum = userServerService.userAppLanguage(uidLong);

                        String copywriting = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_10.getKey(), languageEnum);
                        longLinkService.pushRoomMsg(room.getId(), userDTO, copywriting, PushEvent.room_kick_out_event, PushToType.EXCLUDE_OTHER);
                        outRoom(uidLong, room.getId(), new Date());
                        roomMicLowService.initRoomMic(room.getId());
                        //zegoComponent.kickoutUser(room.getId(), List.of(uidLong.toString()));
                    } catch (Exception e) {
                        log.error("Block kickOut fail uid:[{}] e:[{}]", uidLong, e.getMessage());
                    }
                }
            }
        } else {
            throw new ApiException(CodeEnum.USER_NO_EXIST);
        }
    }

    public void reconnectReport(Long uid, String roomId) {
        if (Objects.isNull(uid) || Objects.equals(0L, uid)) {
            log.warn("reconnect report userinfo empty");
            return;
        }
        if (StrUtil.isBlank(roomId)) {
            log.warn("reconnect report roomInfo empty");
            return;
        }
        RoomAudienceDTO roomAudienceDTO = audienceTop(roomId, 3);
        if (Objects.nonNull(roomAudienceDTO)) {
            longLinkService.pushCustomerRoomMsg(roomId, roomAudienceDTO, PushEvent.room_audience_update_event, PushToType.MESSAGE_TO_ALL);
        }

        List<MicStateInfoDTO> micStateInfoDTOS = roomMicMiddleService.listMicInfo(roomId, Boolean.TRUE);

        longLinkService.pushMicChangedNotice(roomId, micStateInfoDTOS, PushEvent.room_mic_update_event, PushToType.MESSAGE_TO_ALL);
    }

    public boolean existInRoom(Long uid, Long roomId) {
        RoomInfoDTO userInRoom = getUserInRoom(uid);
        if (Objects.isNull(userInRoom)) {
            return false;
        }
        return Objects.equals(Long.parseLong(userInRoom.getRoomId()), roomId);
    }


    public ListWithTotal<SearchRoomVO> searchRoom(String userNo, Integer page, Integer size) {
        ListWithTotal<SearchRoomVO> listWithTotal = new ListWithTotal<>();
        List<SearchRoomVO> roomVOS = new ArrayList<>();
        LambdaQueryWrapper<Room> wrapper = new LambdaQueryWrapper<>();
        wrapper.like(Room::getRoomNo, userNo);
        List<Room> list = roomService.list(wrapper);
        PageHelper.startPage(page, size);
        PageInfo<Room> pageInfo = new PageInfo<>(list);
        if (CollectionUtils.isEmpty(pageInfo.getList())) {
            return listWithTotal;
        }
        List<Long> uids = pageInfo.getList().stream().map(Room::getUid).toList();
        Map<Long, UserBaseInfoDTO> userMap = userServerService.batchUserSummary(uids);
        for (Room room : pageInfo.getList()) {
            SearchRoomVO vo = new SearchRoomVO();
            vo.setRoomNo(room.getRoomNo());
            vo.setRoomUid(room.getUid());
            vo.setRoomId(room.getId());
            vo.setTitle(room.getTitle());
            vo.setRoomTypeValue((int) room.getType());
            vo.setAvatar(room.getAvatar());
            UserBaseInfoDTO userBaseInfoDTO = userMap.get(room.getUid());
            if (userBaseInfoDTO != null) {
                vo.setCountryCode(userBaseInfoDTO.getCountryCode());
                vo.setHasPrettyNo(userBaseInfoDTO.getHasPrettyNo() != null && userBaseInfoDTO.getHasPrettyNo());
            }
            roomVOS.add(vo);
        }
        listWithTotal.setList(roomVOS);
        listWithTotal.setTotal(pageInfo.getTotal());
        return listWithTotal;
    }
}
