package com.simi.service.room;

import cn.hutool.core.collection.CollUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.ProcessStatusEnum;
import com.simi.common.exception.ApiException;
import com.simi.common.vo.req.RoomFeedbackReq;
import com.simi.entity.room.Room;
import com.simi.entity.room.RoomFeedbackRecord;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * 房间反馈Service
 *
 * <AUTHOR>
 * @date 2024/04/01 14:31
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomFeedbackService {

    private final RoomFeedbackRecordService roomFeedbackRecordService;
    private final RoomService  roomService;

    /**
     * 反馈
     * @param param
     * @param uid
     */
    public void feedback(RoomFeedbackReq param, final long uid){
        if(StringUtils.isBlank(param.getRoomId())){
            log.warn("room id cannot be blank.");
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        if(CollUtil.isEmpty(param.getCategoryList())){
            log.warn("category cannot be empty.");
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Room room = roomService.getRoom(param.getRoomId());
        if(Objects.isNull(room) || !Objects.equals(room.getId(), param.getRoomId())){
            throw new ApiException(CodeEnum.ROOM_MATCH_NOT_FOUND);
        }
        RoomFeedbackRecord record = RoomFeedbackRecord.builder()
                .roomId(param.getRoomId())
                .categories(param.getCategoryList())
                .description(param.getDescription())
                .photos(param.getPhotoList())
                .createTime(new Date())
                .uid(uid)
                .showId(String.valueOf(room.getRoomNo()))
                .status(ProcessStatusEnum.UNPROCESSED.getType())
                .build();
        roomFeedbackRecordService.save(record);
    }
}
