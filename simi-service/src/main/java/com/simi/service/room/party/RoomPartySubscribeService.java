package com.simi.service.room.party;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.room.RoomPartySubscribe;
import com.simi.mapper.room.RoomPartySubscribeMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/07/29 19:55
 **/
@Service
public class RoomPartySubscribeService extends ServiceImpl<RoomPartySubscribeMapper, RoomPartySubscribe> {

    public List<Long> getSubscribePartyIdList(Long uid){
        LambdaQueryWrapper<RoomPartySubscribe> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RoomPartySubscribe::getUid, uid)
                .select(RoomPartySubscribe::getPartyId);
        List<RoomPartySubscribe> list = list(wrapper);
        return list.stream().map(e->Long.valueOf(e.getPartyId())).collect(Collectors.toList());
    }
}
