package com.simi.service.room;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.dto.dataReport.RoomRecordDTO;
import com.simi.entity.room.RoomUserInRecord;
import com.simi.mapper.room.RoomUserInRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * 用户进房记录
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomUserInRecordService extends ServiceImpl<RoomUserInRecordMapper, RoomUserInRecord> {
    private final RoomUserInRecordMapper roomUserInRecordMapper;

    public void inRoomRecord(Long uid, String roomId, Long roomUid) {
        RoomUserInRecord record = RoomUserInRecord.builder()
                .roomId(roomId)
                .roomUid(roomUid)
                .uid(uid)
                .inRoomTime(new Date())
                .inType(null)
                .build();
        this.save(record);
    }

    public RoomUserInRecord getByUidRecord(Long uid, String roomId) {
        LambdaQueryWrapper<RoomUserInRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RoomUserInRecord::getRoomId,roomId);
        wrapper.eq(RoomUserInRecord::getUid,uid);
        wrapper.orderByDesc(RoomUserInRecord::getInRoomTime);
        wrapper.last(" limit 1");
        return this.getOne(wrapper);
    }

    public void outRoomRecord(Long uid, String roomId, Date outTime) {
        LambdaUpdateWrapper<RoomUserInRecord> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RoomUserInRecord::getRoomId, roomId).eq(RoomUserInRecord::getUid, uid).lt(RoomUserInRecord::getInRoomTime, outTime).isNull(RoomUserInRecord::getOutRoomTime);
        updateWrapper.set(RoomUserInRecord::getOutRoomTime, outTime);
        updateWrapper.orderByDesc(RoomUserInRecord::getInRoomTime);
        updateWrapper.last(" limit 1");
        this.update(updateWrapper);
    }

    public List<RoomUserInRecord> listAbnormalInRecord(Long uid) {
        final List<RoomUserInRecord> list = this.lambdaQuery().eq(RoomUserInRecord::getUid, uid).isNull(RoomUserInRecord::getOutRoomTime).list();
        return list;
    }

    public RoomRecordDTO getInRoomRecord(Date beginTime, Date endTime, String roomId, long limitTimestamp) {
        RoomRecordDTO inRoomRecord = roomUserInRecordMapper.getInRoomRecord(beginTime, endTime, roomId, limitTimestamp);
        return inRoomRecord;
    }
}




