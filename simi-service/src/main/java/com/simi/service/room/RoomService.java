package com.simi.service.room;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.annotation.DS;
import com.simi.common.constant.http.Constant;
import com.simi.common.util.RedissonManager;
import com.simi.constant.RoomConstant;
import com.simi.constant.RoomRedisKey;
import com.simi.entity.room.Room;
import com.simi.mapper.room.RoomMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RMap;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 房间基层服务
 * <AUTHOR>
 * @Date: 2023/11/8
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomService extends ServiceImpl<RoomMapper, Room> {

    private final RedissonClient redissonClient;
    private final RedissonManager redissonManager;

    public String roomKey(){
        return RoomRedisKey.room.getKey();
    }

    public String roomUidIdKey(){
        return RoomRedisKey.room_uid_id.getKey();
    }

    public Room getRoom(String id) {
        String roomStr = redissonClient.<String, String>getMap(roomKey()).get(id);
        if (StrUtil.isNotBlank(roomStr) && StrUtil.equalsIgnoreCase(Constant.NULL_STR, roomStr)){
            return null;
        }
        Room room;
        if (StrUtil.isBlank(roomStr)){
            room = this.getById(id);
            if (Objects.isNull(room)){
                redissonClient.getMap(roomKey()).put(id, Constant.NULL_STR);
                return null;
            } else {
                redissonClient.getMap(roomKey()).put(id, JSONUtil.toJsonStr(room));
            }
        } else {
            room = JSONUtil.toBean(roomStr, Room.class);
        }
        return room;
    }

    public Room getRoomByUid(Long uid){
        final RMap<Long, String> uidIdRMap = redissonClient.getMap(roomUidIdKey());
        String roomId = uidIdRMap.get(uid);
        if (StrUtil.isBlank(roomId)){
            final List<Room> list = this.lambdaQuery().eq(Room::getUid, uid).eq(Room::getType, RoomConstant.RoomType.KTV_TYPE).list();
            if (CollectionUtils.isEmpty(list)){
                return null;
            }
            final Room room = list.get(0);
            roomId = room.getId();
            uidIdRMap.put(uid, roomId);
        }
        Room room = getRoom(roomId);
        log.info("获取用户所在房间 uid:{},roomId:{},room:{}", uid, roomId, room);
        return room;
    }

    public String getRoomIdByUid(Long uid){
        return redissonClient.<String, String>getMap(roomUidIdKey()).get(String.valueOf(uid));
    }

    /**
     * 通过uids批量获取房间信息  key:roomId  value: roomInfo
     * @return
     */
    public Map<String, Room> batchGetByUids(List<Long> uids) {
        if (CollUtil.isEmpty(uids)) {
            return MapUtil.empty();
        }
        Set<String> uidsSet = uids.stream().map(String::valueOf).collect(Collectors.toSet());
        Map<String, String> roomIdMap = redissonManager.hMGet(roomUidIdKey(), uidsSet);
        return batchGet(CollUtil.newArrayList(roomIdMap.values()));
    }

    /**
     * 批量获取房间信息  key:roomId  value: roomInfo
     * @return
     */
    public Map<String, Room> batchGet(List<String> roomIds){
        if (CollUtil.isEmpty(roomIds)) {
            return Collections.emptyMap();
        }
        HashSet<String> roomIdSet = CollUtil.newHashSet(roomIds);
        Map<String, String> roomMaps = redissonManager.hMGet(roomKey(), roomIdSet);
        Map<String, Room> result = roomMaps.values().stream()
                .map(roomStr -> {
                    try {
                        return JSONUtil.toBean(roomStr, Room.class);
                    } catch (Exception e) {
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(Room::getId, Function.identity()));

        if (result.size() < roomIdSet.size()) {
            List<String> noCacheIds = roomIdSet.stream().filter(e -> !result.containsKey(e)).toList();
            if (CollUtil.isNotEmpty(noCacheIds)) {
                List<Room> rooms = this.lambdaQuery().in(Room::getId, noCacheIds).list();
                Map<String, String> roomStrMap = rooms.stream()
                        .collect(Collectors.toMap(Room::getId, room -> {
                            result.put(room.getId(), room);
                            return JSONUtil.toJsonStr(room);
                        }));
                redissonManager.hMSet(roomKey(), roomStrMap);
            }
        }
        return result;
    }

    public List<Room> getRoomByRoomNo(String roomNo){
        return this.lambdaQuery()
                .eq(Room::getRoomNo,roomNo)
                .list();
    }

    public Map<String,Room> getRoomByRoomIds(Set<String> roomIds){
        List<Room> roomList = this.lambdaQuery()
                .in(Room::getId,roomIds)
                .list();
        return roomList.stream().collect(Collectors.toMap(Room::getId,Function.identity()));
    }

    @DS("slave")
    public List<Room> searchRoom(long lastId, int pageSize) {
        return this.lambdaQuery()
                .gt(Room::getId, lastId)
                .orderByAsc(Room::getId)
                .last("LIMIT " + pageSize)
                .list();
    }
}
