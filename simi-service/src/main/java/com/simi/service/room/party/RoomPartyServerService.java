package com.simi.service.room.party;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.constant.*;
import com.simi.common.constant.resource.ResourceRedisKey;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.room.*;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.*;
import com.simi.common.vo.AttestationTagInfoVO;
import com.simi.common.vo.UserSimpleVO;
import com.simi.common.vo.req.AttestationTagSaveOrUpdateReq;
import com.simi.common.vo.req.roomParty.*;
import com.simi.common.vo.resp.UserLevelBaseVO;
import com.simi.common.vo.room.RoomPartyDetailVO;
import com.simi.common.vo.room.RoomPartyVO;
import com.simi.common.vo.room.SingleRoomPartyVO;
import com.simi.constant.*;
import com.simi.dto.RoomPartyAttachMsgDTO;
import com.simi.entity.room.Room;
import com.simi.entity.room.RoomParty;
import com.simi.entity.room.RoomPartyRank;
import com.simi.entity.room.RoomPartySubscribe;
import com.simi.entity.tag.AttestationTagInfo;
import com.simi.mapper.expand.RoomPartyMapperExpand;
import com.simi.message.RoomPartyMessage;
import com.simi.service.RocketMqSender;
import com.simi.service.SensitiveVocabularyService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomService;
import com.simi.service.tag.AttestationTagInfoService;
import com.simi.service.thirdparty.ShumeiAuditImgApiComponent;
import com.simi.service.user.UserServerService;
import com.simi.util.TranslationCopyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/07/29 19:58
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomPartyServerService {

    private final SystemConfigService systemConfigService;
    public final UserServerService userServerService;
    private final RoomService roomService;
    private final RoomPartyService partyService;
    public final RedissonManager redissonManager;
    private final RocketMqSender rocketMqSender;
    private final SensitiveVocabularyService sensitiveVocabularyService;
    public final RoomPartyMapperExpand roomPartyMapperExpand;
    public final RoomPartySubscribeService roomPartySubscribeService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final RoomPartyRankService roomPartyRankService;
    private final PurseManageService purseManageService;
    private final TaskExecutor taskExecutor;
    private final ShumeiAuditImgApiComponent shumeiAuditImgApiComponent;
    private final RedissonDistributionLocker distributionLocker;
    private final AttestationTagInfoService attestationTagInfoService;

    private final static Integer SEND_GIFT_TYPE = 1;
    private final static Integer RECEIVE_GIFT_TYPE = 2;

    @Transactional(rollbackFor = Exception.class)
    public Long createParty(RoomPartyCreateReq req, Long uid) {
        List<RoomParty> roomPartyList = partyService.lambdaQuery()
                .eq(RoomParty::getCancle, Boolean.FALSE)
                .gt(RoomParty::getEndTime, new Date())
                .eq(RoomParty::getUid, uid).list();
        if (roomPartyList.size() > 2) {
            log.info("Maximum of 3 Party");
            throw new ApiException(CodeEnum.ROOM_PARTY_CREATE_MAXIMUM_LIMIT);
        }

        if (sensitiveVocabularyService.recognize(req.getTopic())) {
            throw new ApiException(CodeEnum.ROOM_PARTY_TOPIC_SENSITIVE_WORDS);
        }
        if (sensitiveVocabularyService.recognize(req.getDescription())) {
            throw new ApiException(CodeEnum.ROOM_PARTY_DESCRIPTION_SENSITIVE_WORDS);
        }
        Date now = new Date();
        Date beginTime = req.getBeginTime();
        // 仅能选择距离当前时间+1小时后至72小时内时间区间
        //if (!DateUtil.isIn(beginTime, DateUtil.offsetHour(now, 1), DateUtil.offsetHour(now, 73))) { todo 测试环境配合
        if (!DateUtil.isIn(beginTime, DateUtil.offsetMinute(now, 5), DateUtil.offsetHour(now, 73))) {
            log.info("Create room party time illegality");
            throw new ApiException(CodeEnum.ROOM_PARTY_TIME_NON_COMPLIANCE);
        }
        DateTime endTime = DateUtil.offsetMinute(beginTime, req.getDuration());

        roomPartyList = roomPartyList.stream()
                .filter(e -> !e.getCancle())
                .filter(e -> e.getEndTime().after(now)).toList();
        Optional<RoomParty> any = roomPartyList.stream().filter(e -> this.doTimeRangesOverlap(req.getBeginTime(), endTime, e.getBeginTime(), e.getEndTime()))
                .findAny();
        if (any.isPresent()) {
            throw new ApiException(CodeEnum.ROOM_PARTY_TIME_OVERLAP);
        }

        String tagIdsStr = null;
        if (CollUtil.isNotEmpty(req.getTagIdList())) {
            tagIdsStr = CollUtil.join(req.getTagIdList(), StrUtil.COMMA);
        }
        Room room = roomService.getRoomByUid(uid);
        /*String defaultPic = defaultPic();
        String picUrl = req.getPicUrl();
        req.setPicUrl(defaultPic);*/
        String modifyFlag = UUID.fastUUID().toString();
        RoomParty roomParty = RoomParty.builder()
                .uid(uid)
                .roomId(room.getId())
                .picUrl(req.getPicUrl())
                .topic(req.getTopic())
                .description(req.getDescription())
                .duration(req.getDuration())
                .beginTime(beginTime)
                .endTime(endTime)
                .tagIds(tagIdsStr)
                .modifyFlag(modifyFlag)
                .createTime(now)
                .updateTime(now)
                .build();
        boolean save = partyService.save(roomParty);

        RoomPartySubscribeReq partySubscribeReq = new RoomPartySubscribeReq();
        partySubscribeReq.setPartyId(roomParty.getId());
        partySubscribeReq.setType(1);
        this.subscribe(partySubscribeReq, uid);

        String partyJson = JSONUtil.toJsonStr(roomParty);
        if (!save) {
            log.info("Create room party fail,:[{}]", partyJson);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        redissonManager.hSet(RoomPartyRedisKey.room_party.getKey(), roomParty.getId().toString(), partyJson);

        RoomPartyMessage beginSoonMsg = RoomPartyMessage.builder()
                .uid(uid)
                .roomId(room.getId())
                .partyId(roomParty.getId())
                .version(1)
                .modifyFlag(modifyFlag)
                .event(RoomPartyMsgEventEnum.BEGIN_SOON_NOTIFY.getEvent()).build();
        beginSoonMsg.setMessageTime(now.getTime());
        beginSoonMsg.setMessageId(UUID.fastUUID().toString());
        DateTime beginSoonNotifyTime = DateUtil.offsetMinute(beginTime, -15);
        long beginSoonNotifyMS = DateUtil.between(now, beginSoonNotifyTime, DateUnit.MS);
        rocketMqSender.sendDeliverMessage(RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, JSONUtil.toJsonStr(beginSoonMsg), beginSoonNotifyMS);

        RoomPartyMessage hasBegunMsg = RoomPartyMessage.builder()
                .partyId(roomParty.getId())
                .uid(uid)
                .roomId(room.getId())
                .version(1)
                .modifyFlag(modifyFlag)
                .event(RoomPartyMsgEventEnum.HAS_BEGUN_NOTIFY.getEvent()).build();
        beginSoonMsg.setMessageTime(now.getTime());
        beginSoonMsg.setMessageId(UUID.fastUUID().toString());
        long hasBegunNotifyMS = DateUtil.between(now, beginTime, DateUnit.MS);
        rocketMqSender.sendDeliverMessage(RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, JSONUtil.toJsonStr(hasBegunMsg), hasBegunNotifyMS);

        RoomPartyMessage endMsg = RoomPartyMessage.builder()
                .partyId(roomParty.getId())
                .uid(uid)
                .roomId(room.getId())
                .version(1)
                .modifyFlag(modifyFlag)
                .event(RoomPartyMsgEventEnum.END_NOTIFY.getEvent()).build();
        beginSoonMsg.setMessageTime(now.getTime());
        beginSoonMsg.setMessageId(UUID.fastUUID().toString());
        long endMS = DateUtil.between(now, endTime, DateUnit.MS);
        rocketMqSender.sendDeliverMessage(RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, JSONUtil.toJsonStr(endMsg), endMS);
        // 封面
        /*taskExecutor.execute(() ->
                dealCoverPic(uid, picUrl, roomParty.getId(), roomParty.getTopic(), roomParty.getRoomId()));*/
        return roomParty.getId();
    }


    public void updateParty(RoomPartyUpdateReq req, Long uid) {
        RoomParty roomPartyRecord = partyService.getById(req.getPartyId());
        if (Objects.isNull(roomPartyRecord)) {
            throw new ApiException(CodeEnum.ACTIVITY_IS_OUTDATED);
        }
        Date beginTime = req.getBeginTime();
        DateTime endTime = DateUtil.offsetMinute(beginTime, req.getDuration());
        if (roomPartyRecord.getVersion() > 2 && (!Objects.equals(beginTime.getTime(), roomPartyRecord.getBeginTime().getTime())
                || !Objects.equals(endTime.getTime(), roomPartyRecord.getEndTime().getTime()))) {
            log.info("Update room party fail, room party is empty or version bigger than 2. req:[{}] uid:[{}]",
                    JSONUtil.toJsonStr(req), uid);
            throw new ApiException(CodeEnum.ROOM_PARTY_CHANGE_LIMIT_REACHED);
        }
        if (!Objects.equals(uid, roomPartyRecord.getUid())) {
            throw new ApiException(CodeEnum.NO_AUTHORITY);
        }
        if (sensitiveVocabularyService.recognize(req.getTopic())) {
            throw new ApiException(CodeEnum.ROOM_PARTY_TOPIC_SENSITIVE_WORDS);
        }
        if (sensitiveVocabularyService.recognize(req.getDescription())) {
            throw new ApiException(CodeEnum.ROOM_PARTY_DESCRIPTION_SENSITIVE_WORDS);
        }
        updateRoomParty(req, roomPartyRecord, null);
    }

    public void updateRoomParty(RoomPartyUpdateReq req, RoomParty roomPartyRecord, Long adminId) {
        Integer partyStatus = partyStatus(roomPartyRecord.getBeginTime(), roomPartyRecord.getEndTime(), new Date(), roomPartyRecord.getCancle());
        if (!Objects.equals(partyStatus, RoomPartyStatusEnum.NOT_STARTED.getEvent()) && Objects.isNull(adminId)) {
            throw new ApiException(CodeEnum.ROOM_PARTY_CANNOT_BE_MODIFIED);
        }
        boolean sendMQ = false;
        Integer version = roomPartyRecord.getVersion();
        Date now = new Date();
        Date beginTime = req.getBeginTime();
        DateTime endTime = DateUtil.offsetMinute(beginTime, req.getDuration());
        // 仅能选择距离当前时间+1小时后至72小时内时间区间
        if (!Objects.equals(beginTime.getTime(), roomPartyRecord.getBeginTime().getTime())
                || !Objects.equals(endTime.getTime(), roomPartyRecord.getEndTime().getTime())) {
            if (!Objects.equals(partyStatus, RoomPartyStatusEnum.NOT_STARTED.getEvent())) {
                throw new ApiException(CodeEnum.ROOM_PARTY_CANNOT_BE_MODIFIED);
            }
            //if (!DateUtil.isIn(beginTime, DateUtil.offsetHour(now, 1), DateUtil.offsetHour(now, 73))) { todo 测试环境配合
            if (!DateUtil.isIn(beginTime, DateUtil.offsetMinute(now, 5), DateUtil.offsetHour(now, 73))) {
                log.info("Update room party time illegality");
                throw new ApiException(CodeEnum.ROOM_PARTY_TIME_NON_COMPLIANCE);
            }
            List<RoomParty> roomPartyList = partyService.lambdaQuery().eq(RoomParty::getUid, roomPartyRecord.getUid()).ne(RoomParty::getId, req.getPartyId()).list();
            roomPartyList = roomPartyList.stream()
                    .filter(e -> !e.getCancle())
                    .filter(e -> e.getEndTime().after(now)).toList();
            Optional<RoomParty> any = roomPartyList.stream().filter(e -> this.doTimeRangesOverlap(req.getBeginTime(), endTime, e.getBeginTime(), e.getEndTime()))
                    .findAny();
            if (any.isPresent()) {
                throw new ApiException(CodeEnum.ROOM_PARTY_TIME_OVERLAP);
            }
            if (Objects.isNull(adminId)) {
                version += 1;
            }
            sendMQ = true;
        }
        String tagIdsStr = null;
        if (CollUtil.isNotEmpty(req.getTagIdList())) {
            tagIdsStr = CollUtil.join(req.getTagIdList(), StrUtil.COMMA);
        }
        RoomParty roomParty = RoomParty.builder().build();
        roomParty.setId(req.getPartyId());
        roomParty.setTagIds(tagIdsStr);
        roomParty.setUpdateTime(now);
        roomParty.setVersion(version);
        roomParty.setBeginTime(beginTime);
        roomParty.setEndTime(endTime);
        roomParty.setDuration(req.getDuration());
        roomParty.setTopic(req.getTopic());
        roomParty.setDescription(req.getDescription());
        if (StrUtil.isNotBlank(req.getPicUrl())) {
            roomParty.setPicUrl(req.getPicUrl());
        }

        /*if (Objects.nonNull(adminId)) {
            roomParty.setAdminId(adminId);
            if (StrUtil.isNotBlank(req.getPicUrl())) {
                roomParty.setPicUrl(req.getPicUrl());
            }
        } else {
            if (StrUtil.isNotBlank(req.getPicUrl())) {
                taskExecutor.execute(() ->
                        dealCoverPic(roomPartyRecord.getUid(), req.getPicUrl(), roomPartyRecord.getId(), roomParty.getTopic(), roomParty.getRoomId()));
            }
        }*/
        String modifyFlag = UUID.fastUUID().toString();
        if (sendMQ) {
            roomParty.setModifyFlag(modifyFlag);
        }
        boolean updateById = partyService.updateById(roomParty);

        String partyJson = JSONUtil.toJsonStr(roomParty);
        if (!updateById) {
            log.info("Update room party fail,:[{}]", partyJson);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
        redissonManager.hSet(RoomPartyRedisKey.room_party.getKey(), roomParty.getId().toString(), partyJson);

        if (sendMQ) {
            RoomPartyMessage beginSoonMsg = RoomPartyMessage.builder()
                    .uid(roomPartyRecord.getUid())
                    .roomId(roomPartyRecord.getRoomId())
                    .partyId(roomParty.getId())
                    .version(roomParty.getVersion())
                    .modifyFlag(modifyFlag)
                    .event(RoomPartyMsgEventEnum.BEGIN_SOON_NOTIFY.getEvent()).build();
            beginSoonMsg.setMessageTime(now.getTime());
            beginSoonMsg.setMessageId(UUID.fastUUID().toString());
            DateTime beginSoonNotifyTime = DateUtil.offsetMinute(beginTime, -15);
            long beginSoonNotifyMS = DateUtil.between(now, beginSoonNotifyTime, DateUnit.MS);
            rocketMqSender.sendDeliverMessage(RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, JSONUtil.toJsonStr(beginSoonMsg), beginSoonNotifyMS);

            RoomPartyMessage hasBegunMsg = RoomPartyMessage.builder()
                    .uid(roomPartyRecord.getUid())
                    .roomId(roomPartyRecord.getRoomId())
                    .partyId(roomParty.getId())
                    .version(roomParty.getVersion())
                    .modifyFlag(modifyFlag)
                    .event(RoomPartyMsgEventEnum.HAS_BEGUN_NOTIFY.getEvent()).build();
            beginSoonMsg.setMessageTime(now.getTime());
            beginSoonMsg.setMessageId(UUID.fastUUID().toString());
            long hasBegunNotifyMS = DateUtil.between(now, beginTime, DateUnit.MS);
            rocketMqSender.sendDeliverMessage(RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, JSONUtil.toJsonStr(hasBegunMsg), hasBegunNotifyMS);

            RoomPartyMessage endMsg = RoomPartyMessage.builder()
                    .uid(roomPartyRecord.getUid())
                    .roomId(roomPartyRecord.getRoomId())
                    .partyId(roomParty.getId())
                    .version(roomParty.getVersion())
                    .modifyFlag(modifyFlag)
                    .event(RoomPartyMsgEventEnum.END_NOTIFY.getEvent()).build();
            beginSoonMsg.setMessageTime(now.getTime());
            beginSoonMsg.setMessageId(UUID.fastUUID().toString());
            long endMS = DateUtil.between(now, endTime, DateUnit.MS);
            rocketMqSender.sendDeliverMessage(RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, JSONUtil.toJsonStr(endMsg), endMS);
        }
    }

    public List<RoomPartyVO> partyList(Integer type, Long uid, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        Date now = new Date();
        List<RoomPartyDTO> dtos;
        if (Objects.equals(type, RoomPartyStatusEnum.GOING.getEvent())) {
            dtos = roomPartyMapperExpand.goingParty(now);
        } else if (Objects.equals(type, RoomPartyStatusEnum.NOT_STARTED.getEvent())) {
            dtos = roomPartyMapperExpand.beginSoon(now);
        } else if (Objects.equals(type, RoomPartyStatusEnum.SUBSCRIBED.getEvent())) {
            List<RoomPartySubscribe> partySubscribes = roomPartySubscribeService.lambdaQuery().eq(RoomPartySubscribe::getUid, uid).list();
            if (CollUtil.isNotEmpty(partySubscribes)) {
                List<Integer> subscribeIds = partySubscribes.stream().map(RoomPartySubscribe::getPartyId).toList();
                dtos = roomPartyMapperExpand.reminded(now, subscribeIds);
            } else {
                dtos = CollUtil.newArrayList();
            }
        } else if (Objects.equals(type, RoomPartyStatusEnum.MY_CREATION.getEvent())) {
            dtos = roomPartyMapperExpand.myCreate(now, uid);
        } else {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        List<RoomPartyVO> roomPartyVOS = BeanUtil.copyToList(dtos, RoomPartyVO.class);

        List<Long> createUserIds = roomPartyVOS.stream().map(RoomPartyVO::getUid).distinct().toList();
        Map<Long, UserBaseInfoDTO> createUserMap = userServerService.batchUserSummary(createUserIds);
        List<RoomPartyTagDTO> roomPartyTagDTOS = tagList();
        List<RoomPartySubscribe> partySubscribes = roomPartySubscribeService.lambdaQuery().eq(RoomPartySubscribe::getUid, uid).list();
        Set<Long> subscribesPartyIds = partySubscribes.stream().map(e -> e.getPartyId().longValue()).collect(Collectors.toSet());
        long start = System.currentTimeMillis();
        roomPartyVOS.forEach(vo -> {
            UserBaseInfoDTO createUser = createUserMap.get(vo.getUid());
            if (Objects.nonNull(createUser)) {
                UserSimpleVO createUserInfo = BeanUtil.toBean(createUser, UserSimpleVO.class);
                createUserInfo.setTagPicInfos(CollUtil.newArrayList());
                vo.setCreateUserInfo(createUserInfo);
            }
            List<UserSimpleVO> subscribeUserList = getSubscribeUserList(vo.getId());
            vo.setSubscribeUserList(subscribeUserList);
            vo.setStatus(partyStatus(vo.getBeginTime(), vo.getEndTime(), now, vo.getCancle()));
            if (StrUtil.isNotBlank(vo.getTagIds())) {
                List<String> split = StrUtil.split(vo.getTagIds(), StrUtil.C_COMMA);
                List<RoomPartyTagDTO> tagDTOS = roomPartyTagDTOS.stream().filter(e -> split.contains(e.getId().toString())).toList();
                vo.setPartyTags(tagDTOS);
            }
            String roomAudienceKey = RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", vo.getRoomId()));
            long roomAudience = redissonManager.zCard(roomAudienceKey);
            vo.setOnlineNum(roomAudience);
            vo.setIsSubscribe(subscribesPartyIds.contains(vo.getId()));
        });
        long end = System.currentTimeMillis();
        log.info("Query partylist cost:[{}]", end - start);
        return roomPartyVOS;
    }

    public List<UserSimpleVO> getSubscribeUserList(Long id) {
        Collection<ScoredEntry<String>> subscribeEntry = redissonManager.reverseZRange(RoomPartyRedisKey.room_party_subscribe.getKey(id), 0, 11);
        List<UserSimpleVO> subscribeUserSimpleVOS = getUserSimpleVOS(subscribeEntry);
        return subscribeUserSimpleVOS;
    }


    public List<RoomPartyTagDTO> tagList() {
        Map<String, String> tagMap = redissonManager.hGetAll(RoomPartyRedisKey.room_party_tag.getKey());
        if (MapUtil.isEmpty(tagMap)) {
            return CollUtil.newArrayList();
        }
        return tagMap.values().stream()
                .map(e -> JSONUtil.toBean(e, RoomPartyTagDTO.class))
                .sorted(Comparator.comparing(RoomPartyTagDTO::getSeqNo).reversed())
                .toList();
    }

    @Transactional(rollbackFor = Exception.class)
    public RoomPartyVO subscribe(RoomPartySubscribeReq req, Long uid) {
        log.info("User subscribe room party req:[{}] uid:[{}]", JSONUtil.toJsonStr(req), uid);
        if (Objects.isNull(uid)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        RoomParty roomParty = partyService.getById(req.getPartyId());
        if (Objects.isNull(roomParty)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        try (Locker locker = distributionLocker.lock(RoomPartyRedisKey.room_party_subscribe_lock.getKey(uid))) {
            if (Objects.isNull(locker)) {
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            if (Objects.equals(req.getType(), 1)) {
                if (roomParty.getBeginTime().before(new Date())) {
                    throw new ApiException(CodeEnum.ACTIVITY_IS_OUTDATED);
                }
                boolean existsSubscribe = roomPartySubscribeService.lambdaQuery()
                        .eq(RoomPartySubscribe::getPartyId, req.getPartyId())
                        .eq(RoomPartySubscribe::getUid, uid).exists();
                if (existsSubscribe) {
                    throw new ApiException(CodeEnum.PARAM_ILLEGAL);
                }
                RoomPartySubscribe subscribe = RoomPartySubscribe.builder()
                        .uid(uid)
                        .partyId(req.getPartyId().intValue()).createTime(new Date()).build();
                roomPartySubscribeService.save(subscribe);
                roomPartyMapperExpand.updateSubscribeNum(req.getPartyId(), 1);
                redissonManager.zAdd(RoomPartyRedisKey.room_party_subscribe.getKey(req.getPartyId().toString()), System.currentTimeMillis(), uid.toString());
            } else if (Objects.equals(req.getType(), 2)) {
                boolean existsSubscribe = roomPartySubscribeService.lambdaQuery()
                        .eq(RoomPartySubscribe::getPartyId, req.getPartyId())
                        .eq(RoomPartySubscribe::getUid, uid).exists();
                if (!existsSubscribe) {
                    throw new ApiException(CodeEnum.PARAM_ILLEGAL);
                }
                LambdaQueryWrapper<RoomPartySubscribe> wrapper = new LambdaQueryWrapper<RoomPartySubscribe>()
                        .eq(RoomPartySubscribe::getPartyId, req.getPartyId())
                        .eq(RoomPartySubscribe::getUid, uid);
                roomPartySubscribeService.remove(wrapper);
                roomPartyMapperExpand.updateSubscribeNum(req.getPartyId(), -1);
                redissonManager.zRemove(RoomPartyRedisKey.room_party_subscribe.getKey(req.getPartyId().toString()), uid.toString());
            } else {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
        } catch (Exception e) {
            log.info("User subscribe failed room party req:[{}] uid:[{}] msg:[{}]", JSONUtil.toJsonStr(req), uid, ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
        return this.partyDetail(req.getPartyId(), uid);
    }

    public ListWithTotal<UserSimpleVO> followerList(Long partyId, Integer pageNum, Integer pageSize) {
        int start = (pageNum - 1) * pageSize;
        int end = start + pageSize - 1;
        ListWithTotal<UserSimpleVO> result = ListWithTotal.empty();
        String key = RoomPartyRedisKey.room_party_subscribe.getKey(partyId.toString());
        Collection<String> scoredEntries = redissonManager.zrevrange(key, start, end);
        if (CollUtil.isEmpty(scoredEntries)) {
            return result;
        }

        List<Long> uids = scoredEntries.stream().map(Long::parseLong).toList();
        Map<Long, UserBaseInfoDTO> userBaseInfoDTOMap = userServerService.batchUserSummary(uids);
        List<AttestationTagInfoVO> temps = CollUtil.newArrayList();
        List<UserSimpleVO> vos = scoredEntries.stream().map(e -> {
            UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOMap.get(Long.parseLong(e));
            if (Objects.nonNull(userBaseInfoDTO)) {
                UserSimpleVO userSimpleVO = BeanUtil.toBean(userBaseInfoDTO, UserSimpleVO.class);
                userSimpleVO.setTagPicInfos(temps);
                return userSimpleVO;
            }
            return null;
        }).filter(Objects::nonNull).toList();
        long total = redissonManager.zCard(key);
        result.setList(vos);
        result.setTotal(total);
        return result;
    }


    /**
     * 状态: 1-进行中; 2-未开始; 3-已结束; 4-已取消
     */
    public Integer partyStatus(Date beginTime, Date endTime, Date now, boolean cancled) {
        if (cancled) {
            return RoomPartyStatusEnum.MY_CREATION.getEvent();
        }
        if (DateUtil.isIn(now, beginTime, endTime)) {
            return RoomPartyStatusEnum.GOING.getEvent();
        } else if (now.before(beginTime)) {
            return RoomPartyStatusEnum.NOT_STARTED.getEvent();
        } else {
            return RoomPartyStatusEnum.SUBSCRIBED.getEvent();
        }
    }

    public ListWithTotal<RoomPartyUserDataDTO> dataRank(Long partyId, Integer type, Integer pageNum, Integer pageSize, boolean checkGiftCount) {
        RoomParty roomParty = partyService.getById(partyId);
        ListWithTotal<RoomPartyUserDataDTO> result = ListWithTotal.empty();
        if (roomParty.getEndTime().after(new Date())) {
            int start = (pageNum - 1) * pageSize;
            int end = start + pageSize - 1;
            String key;
            String giftCountKey;
            if (Objects.equals(type, SEND_GIFT_TYPE)) {
                key = RoomPartyRedisKey.room_party_send_score.getKey(partyId.toString());
                giftCountKey = RoomPartyRedisKey.room_party_send_gift_count.getKey(partyId.toString());
            } else if (Objects.equals(type, RECEIVE_GIFT_TYPE)) {
                key = RoomPartyRedisKey.room_party_receive_score.getKey(partyId.toString());
                giftCountKey = RoomPartyRedisKey.room_party_receive_gift_count.getKey(partyId.toString());
            } else {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            Collection<ScoredEntry<String>> scoredEntries = redissonManager.reverseZRange(key, start, end);
            if (CollUtil.isEmpty(scoredEntries)) {
                return result;
            }
            List<Long> uids = scoredEntries.stream().map(e -> Long.parseLong(e.getValue())).toList();
            Map<Long, UserBaseInfoDTO> userBaseInfoDTOMap = userServerService.batchUserSummary(uids);
            Map<String, Double> giftCountMap;
            if (checkGiftCount) {
                Collection<ScoredEntry<String>> giftCountMapEntries = redissonManager.zRange(giftCountKey, 0, -1);
                giftCountMap = giftCountMapEntries.stream().collect(Collectors.toMap(ScoredEntry::getValue, ScoredEntry::getScore));
            } else {
                giftCountMap = MapUtil.newHashMap();
            }
            List<RoomPartyUserDataDTO> list = scoredEntries.stream().map(entry -> {
                long uid = Long.parseLong(entry.getValue());
                UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOMap.get(uid);
                RoomPartyUserDataDTO userDataDTO = RoomPartyUserDataDTO.builder()
                        .uid(uid)
                        .avatar(userBaseInfoDTO.getAvatar())
                        .nick(userBaseInfoDTO.getNick())
                        .userNo(userBaseInfoDTO.getUserNo())
                        .gender(userBaseInfoDTO.getGender())
                        .countryCode(userBaseInfoDTO.getCountryCode())
                        .giftVal(entry.getScore().longValue()).build();
                if (checkGiftCount) {
                    Double count = giftCountMap.get(entry.getValue());
                    userDataDTO.setGiftCount(Objects.nonNull(count) ? count.longValue() : 0);
                }
                userDataDTO.setGiftValReek(entry.getScore());
                userDataDTO.setUserLevel(userBaseInfoDTO.getUserLevel());
                return userDataDTO;
            }).sorted(Comparator.comparing(RoomPartyUserDataDTO::getGiftValReek).reversed()).toList();
            long total = redissonManager.zCard(key);
            result.setList(list);
            result.setTotal(total);
        } else {
            PageHelper.startPage(pageNum, pageSize);
            List<RoomPartyRank> partyRanks = roomPartyRankService.lambdaQuery()
                    .eq(RoomPartyRank::getType, type)
                    .eq(RoomPartyRank::getPartyId, partyId)
                    .orderByDesc(RoomPartyRank::getScore).list();
            PageInfo<RoomPartyRank> pageInfo = new PageInfo<>(partyRanks);
            List<Long> uids = partyRanks.stream().map(RoomPartyRank::getUid).toList();
            Map<Long, UserBaseInfoDTO> userBaseInfoDTOMap = userServerService.batchUserSummary(uids);
            List<RoomPartyUserDataDTO> list = partyRanks.stream().map(e -> {
                RoomPartyUserDataDTO userDataDTO = RoomPartyUserDataDTO.builder()
                        .uid(e.getUid())
                        .giftVal(e.getScore())
                        .giftCount(e.getGiftCount())
                        .build();
                UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOMap.get(e.getUid());
                if (Objects.nonNull(userBaseInfoDTO)) {
                    userDataDTO.setAvatar(userBaseInfoDTO.getAvatar());
                    userDataDTO.setNick(userBaseInfoDTO.getNick());
                    userDataDTO.setGender(userBaseInfoDTO.getGender());
                    userDataDTO.setCountryCode(userBaseInfoDTO.getCountryCode());
                    userDataDTO.setUserNo(userBaseInfoDTO.getUserNo());
                    userDataDTO.setUserLevel(userBaseInfoDTO.getUserLevel());
                }
                userDataDTO.setGiftValReek(0.0);
                return userDataDTO;
            }).sorted(Comparator.comparing(RoomPartyUserDataDTO::getGiftVal).reversed()).toList();
            result.setList(list);
            result.setTotal(pageInfo.getTotal());
        }
        return result;
    }

    public RoomPartyDetailVO partyDetail(Long partyId, Long uid) {
        Date now = new Date();
        RoomParty roomParty = partyService.getById(partyId);
        RoomPartyDetailVO roomPartyVO = BeanUtil.toBean(roomParty, RoomPartyDetailVO.class);

        UserBaseInfoDTO createUser = userServerService.getFromCache(roomPartyVO.getUid());

        if (Objects.nonNull(createUser)) {
            UserSimpleVO createUserInfo = BeanUtil.toBean(createUser, UserSimpleVO.class);
            createUserInfo.setTagPicInfos(CollUtil.newArrayList());
            roomPartyVO.setCreateUserInfo(createUserInfo);
        }

        roomPartyVO.setStatus(partyStatus(roomPartyVO.getBeginTime(), roomPartyVO.getEndTime(), now, roomPartyVO.getCancle()));

        List<RoomPartyTagDTO> roomPartyTagDTOS = tagList();
        if (StrUtil.isNotBlank(roomPartyVO.getTagIds())) {
            List<String> split = StrUtil.split(roomPartyVO.getTagIds(), StrUtil.C_COMMA);
            List<RoomPartyTagDTO> tagDTOS = roomPartyTagDTOS.stream().filter(e -> split.contains(e.getId().toString())).toList();
            roomPartyVO.setPartyTags(tagDTOS);
        }
        Collection<ScoredEntry<String>> subscribeEntry = redissonManager.reverseZRange(RoomPartyRedisKey.room_party_subscribe.getKey(partyId), 0, 11);
        List<UserSimpleVO> subscribeUserSimpleVOS = getUserSimpleVOS(subscribeEntry);
        roomPartyVO.setSubscribeUserList(subscribeUserSimpleVOS);
        String roomAudienceKey = RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomPartyVO.getRoomId()));
        long roomAudience = redissonManager.zCard(roomAudienceKey);
        roomPartyVO.setOnlineNum(roomAudience);
        boolean exists = roomPartySubscribeService.lambdaQuery().eq(RoomPartySubscribe::getPartyId, partyId)
                .eq(RoomPartySubscribe::getUid, uid).exists();
        roomPartyVO.setIsSubscribe(exists);
        if (roomParty.getEndTime().before(now)) {
            roomPartyVO.setReceiveTotalVal(Optional.ofNullable(roomParty.getReceiveTotal()).orElse(0L));
            roomPartyVO.setSendTotalVal(Optional.ofNullable(roomParty.getSendTotal()).orElse(0L));
        } else {
            Collection<ScoredEntry<String>> receiveScoredEntries = redissonManager.zRange(RoomPartyRedisKey.room_party_receive_score.getKey(partyId), 0, -1);
            long receiveSum = 0;
            if (CollUtil.isNotEmpty(receiveScoredEntries)) {
                receiveSum = receiveScoredEntries.stream().mapToLong(e -> e.getScore().longValue()).sum();
            }
            roomPartyVO.setReceiveTotalVal(receiveSum);

            Collection<ScoredEntry<String>> sendCcoredEntries = redissonManager.zRange(RoomPartyRedisKey.room_party_send_score.getKey(partyId), 0, -1);
            long sendSum = 0;
            if (CollUtil.isNotEmpty(sendCcoredEntries)) {
                sendSum = sendCcoredEntries.stream().mapToLong(e -> e.getScore().longValue()).sum();
            }
            roomPartyVO.setSendTotalVal(sendSum);
        }
        return roomPartyVO;
    }

    private List<UserSimpleVO> getUserSimpleVOS(Collection<ScoredEntry<String>> subscribeEntry) {
        List<UserSimpleVO> subscribeUserSimpleVOS = CollUtil.newArrayList();
        if (CollUtil.isNotEmpty(subscribeEntry)) {
            List<AttestationTagInfoVO> temp = CollUtil.newArrayList();
            Map<Long, UserBaseInfoDTO> subscribeUserMap = userServerService.batchUserSummary(subscribeEntry.stream().map(e -> Long.parseLong(e.getValue())).toList());
            subscribeUserSimpleVOS = subscribeEntry.stream().map(e -> {
                UserBaseInfoDTO userBaseInfo = subscribeUserMap.get(Long.parseLong(e.getValue()));
                return UserSimpleVO.builder().uid(userBaseInfo.getUid())
                        .avatar(userBaseInfo.getAvatar())
                        .nick(userBaseInfo.getNick())
                        .countryCode(userBaseInfo.getCountryCode())
                        .tagPicInfos(temp)
                        .gender(userBaseInfo.getGender())
                        .userNo(userBaseInfo.getUserNo()).build();
            }).toList();
        }
        return subscribeUserSimpleVOS;
    }

    public void dealRoomPartyEvent(RoomPartyMessage message) {
        log.info("Deal Room Party Event:[{}]", JSONUtil.toJsonStr(message));
        String roomId = message.getRoomId();
        Long partyId = message.getPartyId();
        RoomPartyMsgEventEnum eventEnum = RoomPartyMsgEventEnum.getByEvent(message.getEvent());
        if (Objects.isNull(eventEnum)) {
            log.info("Deal room party event Enum is empty:[{}]", JSONUtil.toJsonStr(message));
            return;
        }
        String roomUrl = ClientRouteUtil.toRoom(roomId);
        RoomParty roomParty = partyService.getById(partyId);
        if (Objects.nonNull(roomParty.getCancle()) && roomParty.getCancle()) {
            log.info("Push room party notify, cancled, skip it:[{}] party:[{}]",
                    JSONUtil.toJsonStr(message), JSONUtil.toJsonStr(roomParty));
            return;
        }
        if (!StrUtil.equals(roomParty.getModifyFlag(), message.getModifyFlag())) {
            log.info("Push room party notify, modifyFlag notEquals, skip it:[{}] party:[{}]",
                    JSONUtil.toJsonStr(message), JSONUtil.toJsonStr(roomParty));
            return;
        }

        Room room = roomService.getRoom(roomId);
        Date now = new Date();
        if (Objects.equals(eventEnum, RoomPartyMsgEventEnum.BEGIN_SOON_NOTIFY)) {
            List<RoomPartySubscribe> partySubscribes = roomPartySubscribeService.lambdaQuery().eq(RoomPartySubscribe::getPartyId, partyId).list();
            log.info("Room Party begin soon:[{}]", JSONUtil.toJsonStr(message));
            TranslationCopyDTO titleCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.ROOM_PARTY_BEGIN_SOON_NOTIFY.getKey(), null);
            pushPartyBeginNotify(eventEnum.getEvent(), roomUrl, room.getRoomNo(), roomParty, partySubscribes, now, titleCopyDTO);
        } else if (Objects.equals(eventEnum, RoomPartyMsgEventEnum.HAS_BEGUN_NOTIFY)) {
            List<RoomPartySubscribe> partySubscribes = roomPartySubscribeService.lambdaQuery().eq(RoomPartySubscribe::getPartyId, partyId).list();
            // 推开始通知
            log.info("Room Party has begun:[{}]", JSONUtil.toJsonStr(message));
            redissonManager.hSet(RoomPartyRedisKey.running_room_party.getKey(), roomId, String.valueOf(partyId));
            TranslationCopyDTO titleCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.ROOM_PARTY_HAS_ALREADY_STARTED.getKey(), null);
            pushPartyBeginNotify(eventEnum.getEvent(), roomUrl, room.getRoomNo(), roomParty, partySubscribes, now, titleCopyDTO);

            pushPartyActMsg(eventEnum, roomParty);
        } else if (Objects.equals(eventEnum, RoomPartyMsgEventEnum.END_NOTIFY)) {
            log.info("Room Party end:[{}]", JSONUtil.toJsonStr(message));
            pushPartyActMsg(eventEnum, roomParty);

            String partyIdTemp = redissonManager.hGet(RoomPartyRedisKey.running_room_party.getKey(), roomId);
            if (!StrUtil.equals(partyIdTemp, String.valueOf(partyId))) {
                log.warn("Settlement room party, current partyId:[{}] nequals partyIdTemp:[{}]", partyId, partyIdTemp);
            } else {
                redissonManager.hDel(RoomPartyRedisKey.running_room_party.getKey(), roomId);
            }
            long totalSend = 0L;
            long totalReceive = 0L;
            Collection<ScoredEntry<String>> sendCcoredEntries = redissonManager.zRange(RoomPartyRedisKey.room_party_send_score.getKey(partyId), 0, -1);
            Collection<ScoredEntry<String>> giftCountMapEntries = redissonManager.zRange(RoomPartyRedisKey.room_party_send_gift_count.getKey(partyId), 0, -1);
            Map<String, Double> sendGiftMap = giftCountMapEntries.stream().collect(Collectors.toMap(ScoredEntry::getValue, ScoredEntry::getScore));
            if (CollUtil.isNotEmpty(sendCcoredEntries)) {
                List<List<RoomPartyRank>> sendSplit = savePartyRank(partyId, now, sendCcoredEntries, sendGiftMap, SEND_GIFT_TYPE);
                sendSplit.forEach(list -> {
                    try {
                        roomPartyRankService.saveBatch(list);
                    } catch (Exception e) {
                        log.error("Settlement room party, save send rank failed:[{}]", JSONUtil.toJsonStr(list));
                    }
                });
                totalSend = sendCcoredEntries.stream().mapToLong(e -> e.getScore().longValue()).sum();
            }

            Collection<ScoredEntry<String>> receiveCcoredEntries = redissonManager.zRange(RoomPartyRedisKey.room_party_receive_score.getKey(partyId), 0, -1);

            Collection<ScoredEntry<String>> receiveGiftMapEntries = redissonManager.zRange(RoomPartyRedisKey.room_party_receive_gift_count.getKey(partyId), 0, -1);
            Map<String, Double> receiveGiftMap = receiveGiftMapEntries.stream().collect(Collectors.toMap(ScoredEntry::getValue, ScoredEntry::getScore));
            if (CollUtil.isNotEmpty(receiveCcoredEntries)) {
                List<List<RoomPartyRank>> reveiceSplit = savePartyRank(partyId, now, receiveCcoredEntries, receiveGiftMap, RECEIVE_GIFT_TYPE);
                reveiceSplit.forEach(list -> {
                    try {
                        roomPartyRankService.saveBatch(list);
                    } catch (Exception e) {
                        log.error("Settlement room party, save reveice rank failed:[{}]", JSONUtil.toJsonStr(list));
                    }
                });
                totalReceive = receiveCcoredEntries.stream().mapToLong(e -> e.getScore().longValue()).sum();
            }
            long rewardCoin = 0L;
            RoomPartyRewardConfig roomPartyRewardConfig = rewardConfig(totalReceive);
            if (Objects.nonNull(roomPartyRewardConfig)) {
                BigDecimal bigDecimal = new BigDecimal(totalReceive);
                BigDecimal ratio = roomPartyRewardConfig.getRatio()
                        .divide(BigDecimal.TEN.multiply(BigDecimal.TEN), 2, RoundingMode.DOWN);
                BigDecimal gold = bigDecimal.multiply(ratio);
                log.info("Room party gold reward:[{}] gold:[{}] uid:[{}], partyId:[{}]",
                        JSONUtil.toJsonStr(roomPartyRewardConfig), gold, message.getUid(), partyId);
                purseManageService.addCoin(message.getUid(), gold.longValue(), BillEnum.ROOM_PARTY_REWARD_GOLD,
                        CommonUtil.genId(), null, Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
                rewardCoin = gold.longValue();
            }
            String onlineNumStr = redissonManager.get(RoomPartyRedisKey.room_party_online_count.getKey(partyId));
            Integer onlineNum = StrUtil.isNotBlank(onlineNumStr) ? Integer.parseInt(onlineNumStr) : 0;
            LambdaUpdateWrapper<RoomParty> wrapper = new LambdaUpdateWrapper<>();
            wrapper.eq(RoomParty::getId, partyId);
            wrapper.set(RoomParty::getSendTotal, totalSend);
            wrapper.set(RoomParty::getReceiveTotal, totalReceive);
            wrapper.set(RoomParty::getSettleGold, rewardCoin);
            wrapper.set(RoomParty::getOnlineCount, onlineNum);
            wrapper.set(RoomParty::getSettleLv, Objects.nonNull(roomPartyRewardConfig) ? roomPartyRewardConfig.getLevel() : "");
            wrapper.set(RoomParty::getSettlePercent, Objects.nonNull(roomPartyRewardConfig) ? roomPartyRewardConfig.getRatio() : 0);
            wrapper.set(RoomParty::getUpdateTime, now);
            partyService.update(wrapper);
            // notify
            RoomPartySystemNotifyDTO partySystemNotifyDTO = RoomPartySystemNotifyDTO.builder()
                    .notifyEvent(eventEnum.getEvent())
                    .partyId(partyId)
                    .roomId(roomId)
                    .roomNo(room.getRoomNo())
                    .topic(roomParty.getTopic())
                    .uid(message.getUid())
                    .settleLv(Objects.nonNull(roomPartyRewardConfig) ? roomPartyRewardConfig.getLevel() : "")
                    .settlePercent(Objects.nonNull(roomPartyRewardConfig) ? roomPartyRewardConfig.getRatio() : BigDecimal.ZERO)
                    .duration(roomParty.getDuration())
                    .settleGold(rewardCoin)
                    .onlineCount(onlineNum)
                    .receiveTotal(totalReceive)
                    .beginTime(roomParty.getBeginTime()).build();
            String topic = roomParty.getTopic();
            TranslationCopyDTO titleCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.ROOM_PARTY_HAS_ENDED.getKey(), null);
            TranslationCopyDTO textCopyDTO = TranslationCopyDTO.builder().en(topic).ar(topic).build();
            Long uid = message.getUid();
            String jsonStr = JSONUtil.toJsonStr(partySystemNotifyDTO);
            log.info("Push system messages about room party settlement:[{}] msgTime:[{}]", jsonStr, now.getTime());
            LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
            notifyMessageComponent.pushSystemComplexIMMsgV2(languageEnum, uid, now.getTime(), null,
                    null, titleCopyDTO, textCopyDTO, SystemComplexIMMsgEventEnum.ROOM_PARTY_NOTIFY, jsonStr);
        }
    }

    public void pushPartyActMsg(RoomPartyMsgEventEnum eventEnum, RoomParty roomParty) {
        try {
            log.error("push room party activity msg, eventEnum:[{}] roomParty:[{}]",
                    eventEnum, JSONUtil.toJsonStr(roomParty));
            RoomPartyAttachMsgDTO msg = RoomPartyAttachMsgDTO.builder().partyEvent(eventEnum).roomParty(roomParty).build();
            rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ROOM_PARTY_ACTIVITY_TOPIC, JSONUtil.toJsonStr(msg));
        } catch (Exception e) {
            log.error("room party activity msg push failed, eventEnum:[{}] roomParty:[{}] err:[{}]",
                    eventEnum, JSONUtil.toJsonStr(roomParty), ExceptionUtil.formatEx(e));
        }
    }

    private void pushPartyBeginNotify(Integer notifyEvent, String roomUrl, Long roomNo, RoomParty roomParty, List<RoomPartySubscribe> partySubscribes, Date now, TranslationCopyDTO titleCopyDTO) {
        if (CollUtil.isEmpty(partySubscribes)) {
            log.info("PushPartyBeginNotify partySubscribes is empty");
            return;
        }
        String topic = roomParty.getTopic();
        TranslationCopyDTO textCopyDTO = TranslationCopyDTO.builder().en(topic).ar(topic).build();
        RoomPartySystemNotifyDTO partySystemNotifyDTO = RoomPartySystemNotifyDTO.builder()
                .notifyEvent(notifyEvent)
                .partyId(roomParty.getId())
                .roomId(roomParty.getRoomId())
                .roomNo(roomNo)
                .duration(roomParty.getDuration())
                .topic(roomParty.getTopic())
                .uid(roomParty.getUid())
                .build();
        partySubscribes.forEach(subscribe -> {
            Long uid = subscribe.getUid();
            LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
            notifyMessageComponent.pushSystemComplexIMMsgV2(languageEnum, uid, now.getTime(), roomUrl,
                    null, titleCopyDTO, textCopyDTO, SystemComplexIMMsgEventEnum.ROOM_PARTY_NOTIFY, JSONUtil.toJsonStr(partySystemNotifyDTO));
        });
    }

    private List<List<RoomPartyRank>> savePartyRank(Long partyId, Date now, Collection<ScoredEntry<String>> sccoredEntries, Map<String, Double> giftMap, Integer giftType) {
        List<RoomPartyRank> sendRank = sccoredEntries.stream().map(e -> {
            Double giftNum = giftMap.get(e.getValue());
            return RoomPartyRank.builder()
                    .partyId(partyId)
                    .score(e.getScore().longValue())
                    .giftCount(Objects.nonNull(giftNum) ? giftNum.longValue() : 0)
                    .uid(Long.parseLong(e.getValue()))
                    .type(giftType)
                    .createTime(now)
                    .build();
        }).toList();
        return CollUtil.split(sendRank, 50);
    }

    private RoomPartyRewardConfig rewardConfig(Long receiveScore) {
        String confValueById = systemConfigService.getSysConfValueById(SystemConfigConstant.ROOM_PARTY_REWARD_CONFIG);
        List<RoomPartyRewardConfig> partyRewardConfigs = JSONUtil.toList(confValueById, RoomPartyRewardConfig.class);
        return partyRewardConfigs.stream().filter(e -> e.getScore() <= receiveScore)
                .max(Comparator.comparing(RoomPartyRewardConfig::getScore)).orElse(null);
    }

    private boolean doTimeRangesOverlap(Date start1, Date end1, Date start2, Date end2) {
        if (DateUtil.isIn(start1, start2, end2)) {
            return true;
        }
        if (DateUtil.isIn(end1, start2, end2)) {
            return true;
        }
        if (DateUtil.isIn(start2, start1, end1)) {
            return true;
        }
        return DateUtil.isIn(end2, start1, end1);
    }

    private String defaultPic() {
        return systemConfigService.getSysConfValueById(SystemConfigConstant.ROOM_PARTY_DEFAULT_PIC);
    }

    private void dealCoverPic(Long uid, String picUrl, Long partyId, String topic, String roomId) {
        picUrl = OssUrlUtil.jointUrl(picUrl);
        boolean imageLegal = shumeiAuditImgApiComponent.isImageLegal(uid, picUrl);
        log.info("Execute room party cover to examine, uid:[{}] picUrl:[{}] partyId:[{}] topic:[{}] result:[{}]",
                uid, picUrl, partyId, topic, imageLegal);
        if (imageLegal) {
            partyService.lambdaUpdate()
                    .eq(RoomParty::getId, partyId)
                    .set(RoomParty::getPicUrl, picUrl)
                    .update();
        } else {
            Room room = roomService.getRoom(roomId);
            TranslationCopyDTO titleCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.ROOM_PARTY_PIC_EXAMINE_FAILED.getKey(), null);
            TranslationCopyDTO textCopyDTO = TranslationCopyDTO.builder().en(topic).ar(topic).build();
            RoomPartySystemNotifyDTO partySystemNotifyDTO = RoomPartySystemNotifyDTO.builder()
                    .notifyEvent(RoomPartyMsgEventEnum.IMAGE_VIOLATION.getEvent())
                    .partyId(partyId)
                    .roomId(roomId)
                    .roomNo(room.getRoomNo())
                    .topic(topic)
                    .uid(uid)
                    .build();
            LanguageEnum languageEnum = userServerService.userAppLanguage(uid);
            notifyMessageComponent.pushSystemComplexIMMsgV2(languageEnum, uid, System.currentTimeMillis(), null,
                    null, titleCopyDTO, textCopyDTO, SystemComplexIMMsgEventEnum.ROOM_PARTY_NOTIFY, JSONUtil.toJsonStr(partySystemNotifyDTO));
        }
    }

    public void cancle(RoomPartyCancleReq req, Long uid) {
        RoomParty roomParty = partyService.getById(req.getPartyId());
        if (Objects.isNull(roomParty)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Date now = new Date();
        if (roomParty.getBeginTime().before(now)) {
            log.info("Party has begun, cannot cancle, partyId:[{}] uid:[{}]", req.getPartyId(), uid);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        if (roomParty.getCancle()) {
            log.info("Party has cancleed, partyId:[{}] uid:[{}]", req.getPartyId(), uid);
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        boolean update = partyService.lambdaUpdate()
                .eq(RoomParty::getId, roomParty.getId())
                .set(RoomParty::getCancle, Boolean.TRUE)
                .set(RoomParty::getVersion, roomParty.getVersion() + 1)
                .set(RoomParty::getUpdateTime, now)
                .update();
        if (!update) {
            log.info("Party failed to cancle, partyId:[{}] uid:[{}]", req.getPartyId(), uid);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    public void preCheck(Long uid) {
        UserBaseInfoDTO userBaseInfoDTO = userServerService.getFromCache(uid);
        String createLevelCon = systemConfigService.getSysConfValueById(SystemConfigConstant.ROOM_PARTY_LEVEL_CONFIG);
        if (StrUtil.isBlank(createLevelCon)) {
            throw new ApiException(CodeEnum.ACTIVITY_IS_OUTDATED);
        }

        RoomPartyLevelConfig lvlCon = JSONUtil.toBean(createLevelCon, RoomPartyLevelConfig.class);
        UserLevelBaseVO userLevel = userBaseInfoDTO.getUserLevel();

        if (userLevel.getWealthLevel() < lvlCon.getMinWealthLevel()) {
            throw new ApiException(CodeEnum.ROOM_PARTY_CREATE_WEALTH_LACK, lvlCon.getMinWealthLevel());
        } else if (userLevel.getCharmLevel() < lvlCon.getMinCharmLevel()) {
            throw new ApiException(CodeEnum.ROOM_PARTY_CREATE_CHARM_LACK, lvlCon.getMinCharmLevel());
        } else if (userLevel.getActiveLevel() < lvlCon.getMinActiveLevel()) {
            throw new ApiException(CodeEnum.ROOM_PARTY_CREATE_ACTIVE_LACK, lvlCon.getMinActiveLevel());
        }

        List<RoomParty> roomPartyList = partyService.lambdaQuery()
                .eq(RoomParty::getCancle, Boolean.FALSE)
                .gt(RoomParty::getEndTime, new Date())
                .eq(RoomParty::getUid, uid).list();
        if (roomPartyList.size() > 2) {
            log.info("Maximum of 3 Party");
            throw new ApiException(CodeEnum.ROOM_PARTY_CREATE_MAXIMUM_LIMIT);
        }
    }

    /**
     * 通过roomId筛选party
     *
     * @param uid
     * @param roomPartyFilterReq
     * @return
     */
    public List<SingleRoomPartyVO> getPartyListByRoomId(Long uid, RoomPartyFilterReq roomPartyFilterReq, boolean count) {

        String roomId = roomPartyFilterReq.getRoomId();
        Boolean history = roomPartyFilterReq.getHistory();
        List<RoomPartyDTO> paryList = new ArrayList<>();
        Date date = new Date();
        PageHelper.startPage(roomPartyFilterReq.getPageNum(), roomPartyFilterReq.getPageSize(), count);
        //历史
        if (Objects.nonNull(history) && history) {
            paryList = roomPartyMapperExpand.getHistoryPartyByUidAndEndTime(roomId, date);
        } else {
            paryList = roomPartyMapperExpand.getPartyListByUidAndEndTime(roomId, date);
        }

        if (CollectionUtils.isEmpty(paryList)) {
            return Collections.EMPTY_LIST;
        }

        PageInfo<RoomPartyDTO> pageInfo = new PageInfo<>(paryList);
        List<SingleRoomPartyVO> roomPartyVOS = BeanUtil.copyToList(paryList, SingleRoomPartyVO.class);
        Long roomUid = roomPartyVOS.get(0).getUid();
        Map<Long, UserBaseInfoDTO> roomUserMap = userServerService.batchUserSummary(Arrays.asList(roomUid));
        List<RoomPartyTagDTO> roomPartyTagDTOS = tagList();

        List<Long> subscribePartyIdList = new ArrayList<>();
        if (!checkIsRoomUserId(uid, roomUid)) {
            //判断是否已订阅
            subscribePartyIdList = roomPartySubscribeService.getSubscribePartyIdList(uid);
        }


        //在线人数
        long roomAudience = getRoomAudience(roomPartyVOS.get(0).getRoomId());
        for (SingleRoomPartyVO vo : roomPartyVOS) {
            UserBaseInfoDTO createUser = roomUserMap.get(vo.getUid());
            if (Objects.nonNull(createUser)) {
                UserSimpleVO createUserInfo = BeanUtil.toBean(createUser, UserSimpleVO.class);
                createUserInfo.setTagPicInfos(CollUtil.newArrayList());
                vo.setCreateUserInfo(createUserInfo);
            }

            //标签类型
            if (StrUtil.isNotBlank(vo.getTagIds())) {
                List<String> split = StrUtil.split(vo.getTagIds(), StrUtil.C_COMMA);
                List<RoomPartyTagDTO> tags = roomPartyTagDTOS.stream().filter(e -> split.contains(e.getId().toString())).toList();
                vo.setPartyTags(tags);
            }
            vo.setOnlineNum(roomAudience);
            vo.setIsSubscribe(subscribePartyIdList.contains(vo.getId()));

            Integer status = partyStatus(vo.getBeginTime(), vo.getEndTime(), date, vo.getCancle());
            vo.setStatus(status);

            List<UserSimpleVO> subscribeUserList = getSubscribeUserList(vo.getId());
            vo.setSubscribeUserList(subscribeUserList);
        }

        log.info("uid:{},roomPartyFilterReq:{},getPartyListByRoomId success,result:{}",
                uid, JSON.toJSONString(roomPartyFilterReq), JSON.toJSONString(roomPartyVOS));
        return roomPartyVOS;
    }

    private boolean checkIsRoomUserId(Long uid, Long roomUid) {
        return uid.equals(roomUid);
    }

    public long getRoomAudience(String roomId) {
        String roomAudienceKey = RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId));
        long roomAudience = redissonManager.zCard(roomAudienceKey);
        return roomAudience;
    }


    public Long sendAttestationTag(AttestationTagSaveOrUpdateReq req) {
        try {
            AttestationTagInfo tagInfo = BeanUtil.toBean(req, AttestationTagInfo.class);
            tagInfo.setAdminId(null);
            tagInfo.setUpdateTime(new Date());
            tagInfo.setCreateTime(new Date());
            tagInfo.setType(TagTypeEnum.ORDINARY.getType());

            attestationTagInfoService.save(tagInfo);

            redissonManager.hSet(ResourceRedisKey.user_attestation_tag.getKey(String.valueOf(req.getUid())), String.valueOf(tagInfo.getId()),
                    JSONUtil.toJsonStr(tagInfo));
            return tagInfo.getId();
        } catch (Exception e) {
            log.error("party sendAttestationTag failed, req:[{}] err:[{}]", JSONUtil.toJsonStr(req), ExceptionUtil.formatEx(e));
            return null;
        }
    }

    public void delAttestationTag(Integer id) {
        try {
            AttestationTagInfo tagInfo = attestationTagInfoService.getById(id);
            if (Objects.isNull(tagInfo)) {
                return;
            }
            boolean removeById = attestationTagInfoService.removeById(id);
            if (removeById) {
                redissonManager.hDel(ResourceRedisKey.user_attestation_tag.getKey(String.valueOf(tagInfo.getUid())), String.valueOf(tagInfo.getId()));
            }
        } catch (Exception e) {
            log.error("party delAttestationTag failed, req:[{}] err:[{}]", JSONUtil.toJsonStr(id), ExceptionUtil.formatEx(e));
        }
    }
}
