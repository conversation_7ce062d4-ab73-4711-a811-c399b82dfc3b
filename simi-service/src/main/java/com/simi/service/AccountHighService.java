package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.user.LoginType;
import com.simi.common.constant.user.UserStatusEnum;
import com.simi.common.dto.ThirdPartyUserinfo;
import com.simi.common.dto.banding.BindingAccount;
import com.simi.common.dto.banding.BindingDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.XEncryption;
import com.simi.common.vo.login.LoginReq;
import com.simi.common.vo.req.bindingReq;
import com.simi.common.vo.req.user.ReplacementPhoneReq;
import com.simi.entity.account.Account;
import com.simi.service.infrastructure.SmsService;
import com.simi.service.oauth2.AccountService;
import com.simi.service.oauth2.login.ILoginStrategy;
import com.simi.service.oauth2.login.LoginService;
import com.simi.service.oauth2.login.LoginStrategyFactory;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @Author: Andy
 * @Date: 2023-12-27
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AccountHighService {

    private final AccountService accountService;
    private final SmsService smsService;
    private final LoginStrategyFactory loginStrategyFactory;
    private final UserServerService userServerService;
    private final LoginService loginService;

    public boolean accountIsSign(String code, String areaCode) {
        final Account account = accountService.getByPhone(code, areaCode);
        return !Objects.isNull(account);
    }

    public void resetPasswd(String code, String newPasswd, String smsCode, String areaCode) {
        if (StrUtil.isBlank(code) || StrUtil.isBlank(newPasswd) || StrUtil.isBlank(smsCode)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Account account = accountService.getByPhone(code, areaCode);
        if (Objects.isNull(account)) {
            throw new ApiException(CodeEnum.ACCOUNT_NOT_EXIST);
        }
        final boolean verifyResult = smsService.verifySms(areaCode, code, smsCode);
        if (!verifyResult) {
            throw new ApiException(CodeEnum.SMS_VERIFY_ERROR);
        }
        account.setPasswd(XEncryption.calculatePassword(newPasswd));
        accountService.updateById(account);
        userServerService.updateStatus(account.getUid());
    }

    public List<BindingAccount> bindingAccount(Long uid) {
        Account account = accountService.getByUid(uid);
        List<BindingAccount> bandingAccounts = new ArrayList<>();
        BindingAccount phone = new BindingAccount();
        phone.setBandingType(LoginType.phone.getType());

        BindingAccount googleId = new BindingAccount();
        googleId.setBandingType(LoginType.google.getType());

        BindingAccount facebookId = new BindingAccount();
        facebookId.setBandingType(LoginType.facebook.getType());

        BindingAccount appleId = new BindingAccount();
        appleId.setBandingType(LoginType.apple.getType());

        if (account != null) {
            if (StringUtils.isNotBlank(account.getPhone())) {
                phone.setIsBanding(true);
                phone.setAttachCode(account.getAreaCode());
                phone.setAccountId(account.getPhone());
            }
            if (StringUtils.isNotBlank(account.getGoogleId())) {
                googleId.setIsBanding(true);
                googleId.setAccountId(account.getGoogleId());
            }
            if (StringUtils.isNotBlank(account.getFacebookId())) {
                facebookId.setIsBanding(true);
                facebookId.setAccountId(account.getFacebookId());
            }
            if (StringUtils.isNotBlank(account.getAppleId())) {
                appleId.setIsBanding(true);
                appleId.setAccountId(account.getAppleId());
            }
            bandingAccounts.add(phone);
            bandingAccounts.add(appleId);
            bandingAccounts.add(googleId);
            bandingAccounts.add(facebookId);

        }
        return bandingAccounts;
    }

    public BindingDTO binding(Long uid, bindingReq req) {
        Account account = new Account();
        BindingDTO dto = new BindingDTO();
        List<BindingAccount> bandingAccounts = new ArrayList<>();
        ILoginStrategy loginStrategy = loginStrategyFactory.getStrategy(
                LoginType.getByType(req.getBandingType()).name());

        LoginReq loginReq = BeanUtil.toBean(req, LoginReq.class);
        ThirdPartyUserinfo userinfo = loginStrategy.getUserinfo(loginReq);
        if (req.getIsBanding()) {
            if (Objects.isNull(userinfo)) {
                throw new ApiException(CodeEnum.BANDING_ERROR);
            }
            account = loginStrategy.getAccountById(userinfo.getId(), req.getAreaCode());
            if (account != null) {
                throw new ApiException(CodeEnum.ALREADY_BANDING);
            }
            account = loginStrategy.bindingAccount(uid, userinfo.getId(), req.getPasswd());
        } else {
            account = loginStrategy.bindingAccount(uid, null, null);
        }
        if (account != null) {
            dto.setUid(account.getUid());
            BindingAccount phone = new BindingAccount();
            phone.setIsBanding(false);
            phone.setBandingType(LoginType.phone.getType());

            BindingAccount googleId = new BindingAccount();
            googleId.setIsBanding(false);
            googleId.setBandingType(LoginType.google.getType());

            BindingAccount facebookId = new BindingAccount();
            facebookId.setIsBanding(false);
            facebookId.setBandingType(LoginType.facebook.getType());

            BindingAccount appleId = new BindingAccount();
            appleId.setIsBanding(false);
            appleId.setBandingType(LoginType.apple.getType());

            dto.setUid(account.getUid());
            if (StringUtils.isNotBlank(account.getPhone())) {
                phone.setIsBanding(true);
                phone.setAccountId(account.getPhone());
            }
            if (StringUtils.isNotBlank(account.getGoogleId())) {
                googleId.setIsBanding(true);
                googleId.setAccountId(account.getGoogleId());
            }
            if (StringUtils.isNotBlank(account.getFacebookId())) {
                facebookId.setIsBanding(true);
                facebookId.setAccountId(account.getFacebookId());
            }
            if (StringUtils.isNotBlank(account.getAppleId())) {
                appleId.setIsBanding(true);
                appleId.setAccountId(account.getAppleId());
            }
            bandingAccounts.add(phone);
            bandingAccounts.add(googleId);
            bandingAccounts.add(facebookId);
            bandingAccounts.add(appleId);
        }
        dto.setBandingAccounts(bandingAccounts);
        return dto;
    }

    public void verifyCode(String areaCode, String code, String smsCode) {
        if (StrUtil.isBlank(code) || StrUtil.isBlank(smsCode)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }

        final boolean verifyResult = smsService.verifySms(areaCode, code, smsCode);
        if (!verifyResult) {
            throw new ApiException(CodeEnum.SMS_VERIFY_ERROR);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void replacementPhone(Long uid, ReplacementPhoneReq req) {
        log.info("User replacement phone, req:[{}] uid:[{}]", JSONUtil.toJsonStr(req), uid);
        try {
            Account account = accountService.getByPhone(req.getPhone(), req.getAreaCode());
            if (Objects.nonNull(account)) {
                Long oldUserUid = account.getUid();
                if (Objects.equals(uid, oldUserUid)) {
                    log.info("User replacement phone, binding self, uid:[{}] req:[{}]", uid, JSONUtil.toJsonStr(req));
                    return;
                }
                UserStatusEnum userStatus = loginService.getUserStatus(req.getPhone(), req.getAreaCode());
                if (Objects.equals(userStatus, UserStatusEnum.USER_STATUS_NORMAL)) {
                    log.info("User replacement phone failed, phone number has already been used. uid:[{}] req:[{}]", uid, JSONUtil.toJsonStr(req));
                    throw new ApiException(CodeEnum.PHONE_ALREADY_BEEN_USED);
                }

                boolean updateOldAccount = accountService.lambdaUpdate()
                        .set(Account::getAreaCode, null)
                        .set(Account::getPhone, null)
                        .eq(Account::getUid, account.getUid())
                        .update();
                if (!updateOldAccount) {
                    log.error("User replacement updateOldAccount failed, uid:[{}] req:[{}]", uid, JSONUtil.toJsonStr(req));
                    throw new ApiException(CodeEnum.SERVER_BUSY);
                }
                userServerService.clearUserSummaryCache(account.getUid());
            }
            LambdaUpdateWrapper<Account> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
            LambdaUpdateWrapper<Account> updateWrapper = lambdaUpdateWrapper.set(Account::getAreaCode, req.getAreaCode())
                    .set(Account::getPhone, req.getPhone())
                    .eq(Account::getUid, uid);
            if (StrUtil.isNotBlank(req.getPasswd())) {
                String decryptX = XEncryption.decryptX(req.getPasswd());
                updateWrapper.set(Account::getPasswd, XEncryption.calculatePassword(decryptX));
            }
            boolean updateNewAccount = accountService.update(updateWrapper);
            if (!updateNewAccount) {
                log.error("User replacement updateNewAccount failed, uid:[{}] req:[{}]", uid, JSONUtil.toJsonStr(req));
                throw new ApiException(CodeEnum.SERVER_BUSY);
            }
            userServerService.clearUserSummaryCache(uid);
        } catch (Exception e) {
            log.info("Failed to replacementPhone, req:[{}] uid:[{}] e:[{}]", JSONUtil.toJsonStr(req), uid, ExceptionUtil.formatEx(e));
            throw new RuntimeException(e);
        }
    }
}

