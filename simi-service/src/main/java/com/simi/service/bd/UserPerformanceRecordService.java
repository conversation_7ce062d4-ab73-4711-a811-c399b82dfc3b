package com.simi.service.bd;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.constant.YesOrNoEnums;
import com.simi.common.dto.bd.BDPerformanceDTO;
import com.simi.common.vo.bd.AnchorPerformanceItem;
import com.simi.common.vo.bd.BDPerformanceItem;
import com.simi.common.vo.bd.MemberPerformanceItem;
import com.simi.entity.bd.UserPerformanceRecord;
import com.simi.mapper.bd.UserPerformanceRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;


@Slf4j
@Service
@RequiredArgsConstructor
public class UserPerformanceRecordService extends ServiceImpl<UserPerformanceRecordMapper, UserPerformanceRecord> {
    private final UserPerformanceRecordMapper userPerformanceRecordMapper;

    public void batchSaveOrUpdate(List<UserPerformanceRecord> records){
        saveOrUpdateBatch(records);
    }


    public void deleteByMonth(String month, Date currentDate, Long bdId,Long guideId,Long anchorId,Integer level) {
        this.lambdaUpdate()
                .eq(UserPerformanceRecord::getPerformanceMonth,month)
                .lt(UserPerformanceRecord::getUpdateTime,currentDate.getTime())
                .eq(Objects.nonNull(bdId),UserPerformanceRecord::getBdUid,bdId)
                .eq(Objects.nonNull(guideId),UserPerformanceRecord::getGuildUid,guideId)
                .eq(Objects.nonNull(anchorId),UserPerformanceRecord::getAnchorUid,anchorId)
                .eq(Objects.nonNull(level),UserPerformanceRecord::getLevel,level)
                .set(UserPerformanceRecord::getIsDeleted, YesOrNoEnums.YES.getType())
                .update();
    }

    public void deleteByDay(String day, Date currentDate, Long bdId,Long guideId,Long anchorId,Integer level) {
        this.lambdaUpdate()
                .eq(UserPerformanceRecord::getPerformanceDay,day)
                .lt(UserPerformanceRecord::getUpdateTime,currentDate.getTime())
                .eq(Objects.nonNull(bdId),UserPerformanceRecord::getBdUid,bdId)
                .eq(Objects.nonNull(guideId),UserPerformanceRecord::getGuildUid,guideId)
                .eq(Objects.nonNull(anchorId),UserPerformanceRecord::getAnchorUid,anchorId)
                .eq(Objects.nonNull(level),UserPerformanceRecord::getLevel,level)
                .set(UserPerformanceRecord::getIsDeleted, YesOrNoEnums.YES.getType())
                .update();
    }

    public void deleteByBdId(Long bdId) {
        this.lambdaUpdate()
                .eq(UserPerformanceRecord::getBdUid,bdId)
                .set(UserPerformanceRecord::getIsDeleted, YesOrNoEnums.YES.getType())
                .update();
    }

    public List<BDPerformanceItem> listGroupByBdId(Long bdUid, String monthGte, String monthLte, Integer issueStatus) {
        List<BDPerformanceItem> bdPerformanceItems = userPerformanceRecordMapper.listGroupByBdId(bdUid, monthGte, monthLte, issueStatus);
        return bdPerformanceItems;
    }

    public BDPerformanceDTO sumTotalPerformance(Long bdUid, Long guideId,String monthGte, String monthLte, Integer issueStatus) {
        return userPerformanceRecordMapper.sumTotalPerformance(bdUid,guideId,monthGte,monthLte,issueStatus);
    }

    public void updateIssueStatus(String performanceMonth, Long bdUid) {
        this.lambdaUpdate()
                .eq(UserPerformanceRecord::getPerformanceMonth,performanceMonth)
                .eq(UserPerformanceRecord::getBdUid,bdUid)
                .set(UserPerformanceRecord::getIssueStatus,2)
                .update();
    }

    public void updateIncome(String performanceMonth, Long bdUid,Double proportion) {
        userPerformanceRecordMapper.updateIncome(performanceMonth,bdUid,proportion);
    }

    public List<AnchorPerformanceItem> selectAnchorList(Long anchorId, Long guideId, String monthGte, String monthLte, Long bdUid) {
        return userPerformanceRecordMapper.selectAnchorList(anchorId,guideId,monthGte,monthLte,bdUid);
    }

    public List<MemberPerformanceItem> selectByParentUserId(Long parentUserId, String performanceMonth, String groupField, String parentUserField, Integer level) {
        return userPerformanceRecordMapper.selectByParentUserId(parentUserId,performanceMonth,groupField,parentUserField,level);
    }
}
