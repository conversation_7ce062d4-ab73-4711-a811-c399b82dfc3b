package com.simi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.GooglePayRecord;
import com.simi.entity.dealer.GoldenTicketRecord;
import com.simi.mapper.GoldenTicketRecordMapper;
import com.simi.mapper.GooglePayRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class GooglePayRecordService  extends ServiceImpl<GooglePayRecordMapper, GooglePayRecord> {

    public GooglePayRecord getGooglePayRecord(String purchaseToken){
        LambdaQueryWrapper<GooglePayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(GooglePayRecord::getPurchaseToken,purchaseToken);
        return getOne(wrapper);
    }

    public void creation(String sku,String version,String purchaseToken,Integer notificationType){
        GooglePayRecord googlePayRecord  = new GooglePayRecord();
        googlePayRecord.setSku(sku);
        googlePayRecord.setVersion(version);
        googlePayRecord.setPurchaseToken(purchaseToken);
        googlePayRecord.setNotificationType(notificationType);
        save(googlePayRecord);
    }


}
