package com.simi.service.aristocracy;

import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.dto.aristocracy.*;
import com.simi.common.util.ExceptionUtil;
import com.simi.config.AristocracyConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-29 11:31
 **/
@Slf4j
@Data
@Service
public class AristocracyConfigService {

    @Autowired
    private  AristocracyConfig aristocracyConfig;
    /**
     * 根据贵族ID获取奖励信息
     * @param aristocracyId
     * @return
     */
    public Optional<RewardIdConfigDTO> getRewardIdConfigDTO(int aristocracyId) {
        for (RewardIdConfigDTO rewardIdConfigDTO : aristocracyConfig.rewardIdConfig) {
            if (rewardIdConfigDTO.getAristocracyId() == aristocracyId) {
                return Optional.of(rewardIdConfigDTO);
            }
        }
        return Optional.empty();
    }

    /**
     * 获取退订配置
     */
    public Optional<UnSubscribeConfigDTO> getUnSubscribeConfig(int aristocracyId) {
        for (UnSubscribeConfigDTO unSubscribeConfigDTO : aristocracyConfig.unSubscribeConfig) {
            if (aristocracyId == unSubscribeConfigDTO.getAristocracyId()) {
                return Optional.of(unSubscribeConfigDTO);
            }
        }
        return Optional.empty();
    }

    /**
     * 贵族剩余天数提醒
     */
    public AristocracyRemainDayConfigDTO getAristocracyRemainDayConfig() {
        return aristocracyConfig.aristocracyRemainDayConfig;
    }

    /**
     * 贵族剩余天数
     */
    public AristocracyRemainDayConfigDTO getAristocracyExpireConfig() {
        return aristocracyConfig.aristocracyExpireConfig;
    }

    /**
     * 贵族赠送
     */
    public AristocracyRemainDayConfigDTO getAristocracySendConfig() {
        return aristocracyConfig.aristocracySendConfig;
    }

    /**
     * 获取贵族道具配置
     * @param aristocracyId
     * @return
     */
    public Optional<AristocracyPropConfigDTO> getAristocracyPropConfigById(int aristocracyId) {
        Map<Integer, AristocracyPropConfigDTO> aristocracyPropConfig = aristocracyConfig.aristocracyPropConfig.stream()
                .collect(Collectors.toMap(AristocracyPropConfigDTO::getId, k -> k));
        return Optional.ofNullable(aristocracyPropConfig.get(aristocracyId));
    }

    /**
     * 获取 贵族配置信息
     * @return
     */
    public List<AristocracyConfigDTO> getAristocracyConfig() {
        return aristocracyConfig.aristocracyConfig;
    }

    /**
     * 获取 贵族配置信息 BY ID
     * @return
     */
    public Optional<AristocracyConfigDTO> getAristocracyConfigById(int id) {
        return aristocracyConfig.aristocracyConfig.stream().filter(item -> item.getId() == id).findAny();
    }

    /**
     * 根据ID获取贵族配置信息
     * @param id
     * @return
     */
    public Optional<AristocracyConfigDTO> aristocracyConfigById(int id){
        try {
            List<AristocracyConfigDTO> aristocracyConfigDTOS = aristocracyConfig.aristocracyConfig;
            return aristocracyConfigDTOS.stream().filter(item -> item.getId() == id).findAny();
        } catch (Exception e) {
            log.error("aristocracy error.{}", ExceptionUtil.formatEx(e), e);
        }
        return Optional.empty();
    }

    public List<PrivilegeConfigDTO> getPrivilegeConfig() {
        return aristocracyConfig.equityConfig;
    }

    public List<PrivilegeDescribeDTO> getPrivilegeDescribe(int level) {
        AristocracyConstant.Level levelConstant = AristocracyConstant.Level.getLevel(level);
        if (levelConstant == null) {
            return new ArrayList<>();
        }
        return getPrivilegeConfig(levelConstant);
    }

    /**
     * 获取发送限制
     * @return
     */
    public Map<Integer,Integer> getAristocracySendLimitConfig() {
        return aristocracyConfig.aristocracySendLimitConfig;
    }

    public Map<Integer,Map<Integer, PrivilegeDescribeDTO>> getPrivilegeDescribeMap() {

        Map<Integer, PrivilegeDescribeDTO> level1
                = getPrivilegeConfig(AristocracyConstant.Level.LEVEL_1)
                .stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, k -> k));
        Map<Integer, PrivilegeDescribeDTO> level2
                = getPrivilegeConfig(AristocracyConstant.Level.LEVEL_2)
                .stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, k -> k));
        Map<Integer, PrivilegeDescribeDTO> level3
                = getPrivilegeConfig(AristocracyConstant.Level.LEVEL_3)
                .stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, k -> k));
        Map<Integer, PrivilegeDescribeDTO> level4
                = getPrivilegeConfig(AristocracyConstant.Level.LEVEL_4)
                .stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, k -> k));
        Map<Integer, PrivilegeDescribeDTO> level5
                = getPrivilegeConfig(AristocracyConstant.Level.LEVEL_5)
                .stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, k -> k));
        Map<Integer, PrivilegeDescribeDTO> level6
                = getPrivilegeConfig(AristocracyConstant.Level.LEVEL_6)
                .stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, k -> k));
        Map<Integer, Map<Integer, PrivilegeDescribeDTO>> map = new HashMap<>();
        map.put(1, level1);
        map.put(2, level2);
        map.put(3, level3);
        map.put(4, level4);
        map.put(5, level5);
        map.put(6, level6);
        return map;
    }

    private List<PrivilegeDescribeDTO> getPrivilegeConfig(AristocracyConstant.Level level) {
       switch (level){
           case LEVEL_1: return aristocracyConfig.privilegeDescribe1;
           case LEVEL_2: return aristocracyConfig.privilegeDescribe2;
           case LEVEL_3: return aristocracyConfig.privilegeDescribe3;
           case LEVEL_4: return aristocracyConfig.privilegeDescribe4;
           case LEVEL_5: return aristocracyConfig.privilegeDescribe5;
           case LEVEL_6: return aristocracyConfig.privilegeDescribe6;
           default:
       }
        log.error("aristocracy  getPrivilegeConfig is null .{}", level);
        return new ArrayList<>();
    }
}
