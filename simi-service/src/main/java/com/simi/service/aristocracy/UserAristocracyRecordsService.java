package com.simi.service.aristocracy;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.aristocracy.*;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.RedissonManager;
import com.simi.constant.BillEnum;
import com.simi.constant.Oauth2RedisKey;
import com.simi.constant.UserRedisKey;
import com.simi.entity.aristocracy.UserAristocracyRecordsDO;
import com.simi.mapper.aristocracy.UserAristocracyRecordsMapper;
import com.simi.service.RocketMqSender;
import com.simi.service.purse.PurseManageService;
import com.simi.common.vo.aristocracy.*;
import jodd.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-26 16:28
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class UserAristocracyRecordsService extends ServiceImpl<UserAristocracyRecordsMapper, UserAristocracyRecordsDO> {

    private final RedissonDistributionLocker distributionLocker;
    private final AristocracyConfigService aristocracyConfigService;
    private final PurseManageService purseManageService;
    private final RocketMqSender rocketMqSender;
    private final RedissonManager redissonManager;
    private final AristocracyPlaqueRetrievalService aristocracyPlaqueRetrievalService;

    // 一天的毫秒数
    private final static long DAY_MILLIS = 24 * 60 * 60 * 1000;

    /**
     * 批量获取用户贵族信息
     */
    public Map<Long, UserCurAristocracyInfo> getUserAristocracyRecordsBatch(List<Long> userIds) {
        Set<String> userIdSet = userIds.stream().map(String::valueOf).collect(Collectors.toSet());
        Map<String, String> userStrMap = redissonManager.hMGet(UserRedisKey.user_aristocracy.getKey(), userIdSet);
        // userStrMap to Map<Long,UserCurAristocracyInfo>
        Map<Long, UserCurAristocracyInfo> userCurAristocracyInfoMap = new HashMap<>();
        for (Map.Entry<String, String> entry : userStrMap.entrySet()) {
            String userId = entry.getKey();
            String userStr = entry.getValue();
            UserCurAristocracyInfo bean = JSONUtil.toBean(userStr, UserCurAristocracyInfo.class);
            Optional<AristocracyPropConfigDTO> aristocracyPropConfigDTOOptional = aristocracyConfigService.getAristocracyPropConfigById(bean.getCurAristocracy());
            aristocracyPropConfigDTOOptional.ifPresent(item->{
                UserCurAristocracyInfo.AristocracyPropInfo aristocracyPropInfo = BeanUtil.copyProperties(item, UserCurAristocracyInfo.AristocracyPropInfo.class);
                bean.setAristocracyPropInfo(aristocracyPropInfo);
                aristocracyPlaqueRetrievalService.getFromCache(Long.parseLong(userId)).ifPresent(k->{
                    aristocracyPropInfo.setPlaqueEn(k.getEnglishImageUrl());
                    aristocracyPropInfo.setPlaqueAr(k.getArabicImageUrl());
                });
            } );
            userCurAristocracyInfoMap.put(Long.parseLong(userId), bean);
        }
        return userCurAristocracyInfoMap;
    }


    /**
     * 使用缓存-获取用户贵族信息
     * @param userId
     * @return
     */
    public Optional<UserCurAristocracyInfo> getUserAristocracyRecordsCache(long userId) {
        // 查询缓存
        Set<String> userIds = Sets.newHashSet("" + userId);
        Map<String, String> userStrMap = redissonManager.hMGet(UserRedisKey.user_aristocracy.getKey(), userIds);
        String UserCurAristocracyInfoStr = userStrMap.get(userId + "");
        if (StringUtils.isBlank(UserCurAristocracyInfoStr)) {
            // 刷新缓存
            String value = redissonManager.get(UserRedisKey.user_aristocracy_refresh_time.getKey(userId));
            if (StringUtils.isBlank(value)) {
                return Optional.ofNullable(flushCache(userId));
            }
            return Optional.empty();
        }
        UserCurAristocracyInfo bean = JSONUtil.toBean(UserCurAristocracyInfoStr, UserCurAristocracyInfo.class);
        long now = System.currentTimeMillis();
        // 判断是否已过期
        if (now >= bean.getEndTime()) {
            return Optional.empty();
        }
        Optional<AristocracyPropConfigDTO> aristocracyPropConfigDTOOptional = aristocracyConfigService.getAristocracyPropConfigById(bean.getCurAristocracy());
        aristocracyPropConfigDTOOptional.ifPresent(item->{
            UserCurAristocracyInfo.AristocracyPropInfo aristocracyPropInfo = BeanUtil.copyProperties(item, UserCurAristocracyInfo.AristocracyPropInfo.class);
            bean.setAristocracyPropInfo(aristocracyPropInfo);
           aristocracyPlaqueRetrievalService.getFromCache(userId).ifPresent(k->{
                aristocracyPropInfo.setPlaqueEn(k.getEnglishImageUrl());
                aristocracyPropInfo.setPlaqueAr(k.getArabicImageUrl());
            });
        } );

        return Optional.of(bean);
    }

    /**
     * 获取称号信息
     * @return
     */
    public List<AristocracyPlaqueRetrievalDTO> plaque() {
        return aristocracyPlaqueRetrievalService.getAll();
    }

    /**
     * 获取用户贵族信息
     * return UserCurAristocracyInfo 返回 null 时，用户无贵族信息
     */
    private Optional<UserCurAristocracyInfo> getUserAristocracyRecords(long userId) {
        List<UserAristocracyRecordsDO> aristocracyRecordsDOList = getUserEffectiveAristocracy(userId);
        if (CollectionUtil.isEmpty(aristocracyRecordsDOList)) {
            return Optional.empty();
        }
        log.info("Aristocracy get user effective: {}", aristocracyRecordsDOList);
        UserCurAristocracyInfo userCurAristocracyInfo = new UserCurAristocracyInfo();
        userCurAristocracyInfo.setUserId(userId);
        UserAristocracyRecordsDO userAristocracyRecordsDO = aristocracyRecordsDOList.get(0);
        // 计算用户当前生效的贵族等级
        int curAristocracy = userAristocracyRecordsDO.getAristocracyLevel();
        // 当前开始时间
        Long startTime = userAristocracyRecordsDO.getStartTime();
        // 当前生效的结束时间
        Long curEndTime = userAristocracyRecordsDO.getEndTime();
        // 来源
        Integer getSource = userAristocracyRecordsDO.getGetSource();
        // 最后时间
        UserAristocracyRecordsDO last = aristocracyRecordsDOList.get(aristocracyRecordsDOList.size() - 1);
        Long endTime = last.getEndTime();
        userCurAristocracyInfo.setEndTime(endTime);
        userCurAristocracyInfo.setSource(getSource);
        userCurAristocracyInfo.setCurAristocracy(curAristocracy);
        userCurAristocracyInfo.setCurEndTime(curEndTime);
        userCurAristocracyInfo.setStartTime(startTime);
        userCurAristocracyInfo.setCreateTime(userAristocracyRecordsDO.getCreateTime());
        Optional<AristocracyPropConfigDTO> aristocracyPropConfigDTOOptional = aristocracyConfigService.getAristocracyPropConfigById(curAristocracy);
        aristocracyPropConfigDTOOptional.ifPresent(item->{
            UserCurAristocracyInfo.AristocracyPropInfo aristocracyPropInfo = BeanUtil.copyProperties(item, UserCurAristocracyInfo.AristocracyPropInfo.class);
            userCurAristocracyInfo.setAristocracyPropInfo(aristocracyPropInfo);
            aristocracyPlaqueRetrievalService.getFromCache(userId).ifPresent(k->{
                aristocracyPropInfo.setPlaqueEn(k.getEnglishImageUrl());
                aristocracyPropInfo.setPlaqueAr(k.getArabicImageUrl());
            });
        } );
        return Optional.of(userCurAristocracyInfo);
    }

    /**
     * 获取特权描述
     */
    public List<PrivilegeDescribeVO> privilegeDescribes(Integer aristocracyLevel) {
        List<PrivilegeDescribeDTO> privilegeDescribe = aristocracyConfigService.getPrivilegeDescribe(aristocracyLevel);
        LanguageEnum lang = MessageSourceUtil.getLang();
        return privilegeDescribe.stream().map(k->{
            PrivilegeDescribeVO vo = new PrivilegeDescribeVO();
            vo.setId(k.getId());
            vo.setDescribe(lang==LanguageEnum.ar?k.getArDescribe():k.getEnDescribe());
            vo.setTitle(lang==LanguageEnum.ar?k.getArTitle():k.getEnTitle());
            vo.setImage(lang == LanguageEnum.ar ? k.getArImage() : k.getEnImage());
            vo.setIconUrl(k.getIconUrl());
            return vo;
        }).collect(Collectors.toList());
    }

    public ListWithTotal<AristocracyHistoryVO> getUserHistory(long userId, Integer pageNum, Integer pageSize) {
        LambdaQueryWrapper<UserAristocracyRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAristocracyRecordsDO::getUserId, userId);
        wrapper.orderByDesc(UserAristocracyRecordsDO::getCreateTime);

        Page<UserAristocracyRecordsDO> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);

        List<AristocracyConfigDTO> aristocracyConfigDTOS = aristocracyConfigService.getAristocracyConfig();

        Page<UserAristocracyRecordsDO> result = this.page(page, wrapper);
        List<UserAristocracyRecordsDO> records = result.getRecords();
        List<AristocracyHistoryVO> collect = records.stream().map(k -> {
            AristocracyHistoryVO vo = new AristocracyHistoryVO();
            vo.setId(k.getId());
            vo.setSource(k.getGetSource());
            long time = k.getCreateTime().getTime() / 1000;
            vo.setGetTime(time);
            vo.setEffectiveDays(k.getDurationDays());
            Optional<AristocracyConfigDTO> first
                    = aristocracyConfigDTOS.stream().filter(i -> i.getId() == k.getAristocracyLevel()).findFirst();
            if (first.isEmpty()) {
                log.error("aristocracyConfig is not exist,aristocracyLevel:{},record:{}", k.getAristocracyLevel(), k);
                return null;
            }
            AristocracyConfigDTO aristocracyConfigDTO = first.get();
            vo.setIconUrl(aristocracyConfigDTO.getIconUrl());
            return vo;
        }).filter(Objects::nonNull).collect(Collectors.toList());

        return ListWithTotal.<AristocracyHistoryVO>builder().total(result.getTotal()).list(collect).build();
    }

    /**
     * 根据ID批量获取记录
     */
    public List<UserAristocracyRecordsDO> getRecordsByIds(List<Integer> ids) {
        LambdaQueryWrapper<UserAristocracyRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(UserAristocracyRecordsDO::getId, ids);
        return this.list(wrapper);
    }


    /**
     * 购买贵族
     *
     * @param userId        用户ID
     * @param aristocracyId 贵族类型
     * @param gradeId       档位ID
     * @param sendUserId    赠送用户
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void buyAristocracy(long userId, int aristocracyId, int gradeId, Long sendUserId) {
        /**
         * 上锁
         * 获取贵族等级
         * 获取用户当前贵族情况
         * 扣款
         * 是否升级
         * 是否续期
         * 购买成功后的行为
         * 解锁
         */
        try (Locker locker = distributionLocker.lock(
                Oauth2RedisKey.buy_aristocracy_lock.getKey(userId))) {
            List<AristocracyConfigDTO> aristocracyConfigDTOS = aristocracyConfigService.getAristocracyConfig();
            // 获取贵族配置
            Optional<AristocracyConfigDTO> anyOpt = aristocracyConfigDTOS.stream().filter(k -> k.getId() == aristocracyId).findAny();
            AristocracyConfigDTO aristocracyConfigDTO = anyOpt.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL));
            if (aristocracyConfigDTO.getStatus() != AristocracyConstant.Status.EFFECTIVE.getStatus()) {
                // 该贵族等级未开发
                log.info("aristocracy this aristocracy level is not effective.{},userId:{}", aristocracyId, userId);
                throw new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL);
            }
            List<AristocracyConfigDTO.GradeDTO> grades = aristocracyConfigDTO.getGrades();
            Optional<AristocracyConfigDTO.GradeDTO> gradeDTOOptional = grades.stream().filter(item -> item.getId() == gradeId).findAny();
            gradeDTOOptional.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_DUTY_NOT_EXIST));
            AristocracyConfigDTO.GradeDTO gradeDTO = gradeDTOOptional.get();
            if (gradeDTO.getPrice() <= 0) {
                // 该贵族价格档位有异常
                log.info("aristocracy this grade is error.{},userID:{},aristocracyConfigDTO:{}", gradeDTO, userId, aristocracyConfigDTO);
                throw new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL);
            }
            long deductUserId = sendUserId == null ? userId : sendUserId;
            PurseDTO purseDTO = purseManageService.getPurse(deductUserId);
            int price = gradeDTO.getPrice();
            // 处理折扣
            Optional<UserCurAristocracyInfo> userCurAristocracyInfoOptional = getUserAristocracyRecordsCache(deductUserId);
            if (userCurAristocracyInfoOptional.isPresent()) {
                UserCurAristocracyInfo userCurAristocracyInfo = userCurAristocracyInfoOptional.get();
                Integer discount = userCurAristocracyInfo.getAristocracyPropInfo().getDiscount();
                price = price - (price - price * discount / 100);
                log.info("aristocracy buy uid:{},gradeId:{},aristocracyId:{},price:{},discount:{},sendUserId:{}",
                        userId, gradeId, aristocracyId, price, discount, sendUserId);
            }

            if (purseDTO.getCoin() < price) {
                log.error("aristocracy buy user[{}] current coin is {}, spend coin:{}", deductUserId, purseDTO.getCoin(), gradeDTO.getPrice());
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            // 判断购买行为
            AristocracyConstant.BuyType buyType = buyType(userId, aristocracyConfigDTO);
            switch (buyType) {

                case ADD ->
                        addAristocracy(userId, aristocracyConfigDTO, gradeDTO, sendUserId, AristocracyConstant.BuyType.ADD);
                case RENEW -> renewAristocracy(userId, aristocracyConfigDTO, gradeDTO, sendUserId);
                case UPGRADE -> upgradeAristocracy(userId, aristocracyConfigDTO, gradeDTO, sendUserId);
                case DOWNGRADE -> {
                    if (Objects.isNull(sendUserId)) {
                        throw new ApiException(CodeEnum.ARISTOCRACY_CANNOT_DOWNGRADE);
                    }
                    throw new ApiException(CodeEnum.ARISTOCRACY_GIFT_CANNOT_DOWNGRADE);
                }
            }
            // 刷新缓存
            flushCache(userId);
        } catch (ApiException e) {
            log.error("aristocracy buy error.userId:{},aristocracyLevel:{},e:{}", userId, aristocracyId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("aristocracy buy error.userId:{},aristocracyLevel:{},e:{}", userId, aristocracyId, ExceptionUtil.message(e), e);
            throw new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL);
        }

    }


    /**
     * 下发贵族-不会扣款
     *
     * @param userId        用户ID
     * @param aristocracyId 贵族类型
     * @param gradeId       档位ID
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public Integer grantAristocracy(long userId, int aristocracyId, int gradeId, AristocracyConstant.Source source,Long sendUserId) {
        /**
         * 上锁
         * 获取贵族等级
         * 获取用户当前贵族情况
         * 扣款
         * 是否升级
         * 是否续期
         * 购买成功后的行为
         * 解锁
         */
        try (Locker locker = distributionLocker.lock(
                Oauth2RedisKey.buy_aristocracy_lock.getKey(userId))) {
            List<AristocracyConfigDTO> aristocracyConfigDTOS = aristocracyConfigService.getAristocracyConfig();
            // 获取贵族配置
            Optional<AristocracyConfigDTO> anyOpt = aristocracyConfigDTOS.stream().filter(k -> k.getId() == aristocracyId).findAny();
            AristocracyConfigDTO aristocracyConfigDTO = anyOpt.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL));
            if (aristocracyConfigDTO.getStatus() != AristocracyConstant.Status.EFFECTIVE.getStatus()) {
                // 该贵族等级未开发
                log.info("grantAristocracy aristocracy this aristocracy level is not effective.{},userId:{}", aristocracyId, userId);
                throw new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL);
            }
            List<AristocracyConfigDTO.GradeDTO> grades = aristocracyConfigDTO.getGrades();
            Optional<AristocracyConfigDTO.GradeDTO> gradeDTOOptional = grades.stream().filter(item -> item.getId() == gradeId).findAny();
            gradeDTOOptional.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_DUTY_NOT_EXIST));
            AristocracyConfigDTO.GradeDTO gradeDTO = gradeDTOOptional.get();
            if (gradeDTO.getPrice() <= 0) {
                // 该贵族价格档位有异常
                log.info("grantAristocracy aristocracy this grade is error.{},userID:{},aristocracyConfigDTO:{}", gradeDTO, userId, aristocracyConfigDTO);
                throw new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL);
            }
            // 判断行为
            AristocracyConstant.BuyType buyType = buyType(userId, aristocracyConfigDTO);
            Integer recordId = null;
            switch (buyType) {
                case ADD ->
                        recordId = addAristocracyGrant(userId, aristocracyConfigDTO, gradeDTO, source, AristocracyConstant.BuyType.ADD, sendUserId);
                case RENEW ->
                        recordId = renewAristocracyGrant(userId, aristocracyConfigDTO, gradeDTO, source, sendUserId);
                case UPGRADE ->
                        recordId = upgradeAristocracyGrant(userId, aristocracyConfigDTO, gradeDTO, source, sendUserId);
                case DOWNGRADE -> downGradeGrant(userId, aristocracyConfigDTO, gradeDTO, source, sendUserId);
            }
            // 刷新缓存
            flushCache(userId);
            return recordId;
        } catch (ApiException e) {
            log.error("aristocracy buy error.userId:{},aristocracyLevel:{},e:{}", userId, aristocracyId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("aristocracy buy error.userId:{},aristocracyLevel:{},e:{}", userId, aristocracyId, ExceptionUtil.message(e), e);
            throw new ApiException(CodeEnum.ARISTOCRACY_BUY_FAIL);
        }
    }

    /**
     * 清楚某个用户的缓存
     */
    public void cleanCache(long userId) {
        redissonManager.hDel(UserRedisKey.user_aristocracy.getKey(), userId + "");
    }

    /**
     * 降级
     */
    private void downGradeGrant(long userId, AristocracyConfigDTO aristocracyConfigDTO, AristocracyConfigDTO.GradeDTO gradeDTO,
                                AristocracyConstant.Source source, Long sendUserId) {
        /**
         * 不存在降级行为，但如果来自奖励的，则会插入一条失效的记录
         */
        if (AristocracyConstant.Source.REWARD != source
                && AristocracyConstant.Source.ADMIN != source) {
            throw new ApiException(CodeEnum.ARISTOCRACY_CANNOT_DOWNGRADE);
        }
        Optional<UserCurAristocracyInfo> userAristocracyRecordsOpt = getUserAristocracyRecords(userId);
        UserCurAristocracyInfo userCurAristocracyInfo = userAristocracyRecordsOpt.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_RECORD_NOT_EXIST));
        int aristocracyConfigDTOId = aristocracyConfigDTO.getId();
        int gradeId = gradeDTO.getId();
        int day = gradeDTO.getDay();
        // 获取用户最后的结束时间
        long now = userCurAristocracyInfo.getEndTime() + 1001;
        long endTime = now + day * DAY_MILLIS;

        UserAristocracyRecordsDO userAristocracyRecordsDO = new UserAristocracyRecordsDO();
        userAristocracyRecordsDO.setUserId(userId);
        userAristocracyRecordsDO.setDurationDays(day);
        userAristocracyRecordsDO.setGiftUserId(sendUserId);
        userAristocracyRecordsDO.setAristocracyLevel(aristocracyConfigDTOId);
        userAristocracyRecordsDO.setGradeId(gradeId);
        userAristocracyRecordsDO.setGetSource(source.getSource());
        userAristocracyRecordsDO.setStartTime(now);
        userAristocracyRecordsDO.setEndTime(endTime);
        userAristocracyRecordsDO.setCreateTime(new Date());
        userAristocracyRecordsDO.setRecordStatus(AristocracyConstant.Status.INVALID.getStatus());
        this.save(userAristocracyRecordsDO);

        // 发送 mq 消息
        BuySuccessMsg buySuccessMsg = new BuySuccessMsg();
        buySuccessMsg.setRecordId(userAristocracyRecordsDO.getId());
        buySuccessMsg.setAristocracyId(aristocracyConfigDTOId);
        buySuccessMsg.setSource(source.getSource());
        buySuccessMsg.setUserId(userId);
        buySuccessMsg.setGiveUserId(null);
        buySuccessMsg.setGrade(gradeId);

        buySuccessMsg.setBuyType(AristocracyConstant.BuyType.DOWNGRADE);
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_BUY_SUCCESS, JSONUtil.toJsonStr(buySuccessMsg));
        log.info("aristocracy downGradeGrant userId:{},gradeId:{},aristocracyConfigDTOId:{}", userId, gradeId, aristocracyConfigDTOId);
    }

    private int renewAristocracyGrant(long userId, AristocracyConfigDTO aristocracyConfigDTO, AristocracyConfigDTO.GradeDTO gradeDTO,
                                      AristocracyConstant.Source source, Long sendUserId) {

        /**
         * 插入记录
         * 下发权益
         */
        Optional<UserCurAristocracyInfo> userAristocracyRecordsOpt = getUserAristocracyRecords(userId);
        UserCurAristocracyInfo userCurAristocracyInfo = userAristocracyRecordsOpt.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_RECORD_NOT_EXIST));
        int aristocracyConfigDTOId = aristocracyConfigDTO.getId();
        int gradeId = gradeDTO.getId();
        int day = gradeDTO.getDay();
        // 获取用户最后的结束时间
        long now = userCurAristocracyInfo.getEndTime() + 1001;
        long endTime = now + day * DAY_MILLIS;

        UserAristocracyRecordsDO userAristocracyRecordsDO = new UserAristocracyRecordsDO();
        userAristocracyRecordsDO.setUserId(userId);
        userAristocracyRecordsDO.setDurationDays(day);
        userAristocracyRecordsDO.setGiftUserId(sendUserId);
        userAristocracyRecordsDO.setAristocracyLevel(aristocracyConfigDTOId);
        userAristocracyRecordsDO.setGradeId(gradeId);
        userAristocracyRecordsDO.setGetSource(source.getSource());
        userAristocracyRecordsDO.setStartTime(now);
        userAristocracyRecordsDO.setEndTime(endTime);
        userAristocracyRecordsDO.setCreateTime(new Date());
        userAristocracyRecordsDO.setRecordStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());
        this.save(userAristocracyRecordsDO);
        log.info("aristocracy renewAristocracyGrant success uid:{} ,aristocracyId:{},gradeId:{}", userId, aristocracyConfigDTOId, gradeId);
        // 发送 mq 消息
        BuySuccessMsg buySuccessMsg = new BuySuccessMsg();
        buySuccessMsg.setRecordId(userAristocracyRecordsDO.getId());
        buySuccessMsg.setAristocracyId(aristocracyConfigDTOId);
        buySuccessMsg.setSource(source.getSource());
        buySuccessMsg.setUserId(userId);
        buySuccessMsg.setGiveUserId(sendUserId);
        buySuccessMsg.setGrade(gradeId);

        buySuccessMsg.setBuyType(AristocracyConstant.BuyType.RENEW);
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_BUY_SUCCESS, JSONUtil.toJsonStr(buySuccessMsg));
        return userAristocracyRecordsDO.getId();
    }

    private int upgradeAristocracyGrant(long userId, AristocracyConfigDTO aristocracyConfigDTO, AristocracyConfigDTO.GradeDTO gradeDTO,
                                        AristocracyConstant.Source source, Long sendUserId) {
        /**
         * 退还权益
         * 重新购买贵族
         */
        // 获取用户当前生效的贵族
        List<UserAristocracyRecordsDO> userEffectiveAristocracy = getUserEffectiveAristocracy(userId);
        List<Integer> invalidIds = userEffectiveAristocracy.stream().map(UserAristocracyRecordsDO::getId).toList();
        // 将所有贵族失效
        userEffectiveAristocracy.forEach(item -> {
            item.setRecordStatus(AristocracyConstant.Status.INVALID.getStatus());
            baseMapper.updateById(item);
        });

        // 失效当前生效的贵族
        log.info("aristocracy upgradeAristocracyGrant uid:{} invalidIds:{} ", userId, invalidIds);
        //  发送 退订Mq
        UnsubscribeMsg unsubscribeMsg = new UnsubscribeMsg();
        unsubscribeMsg.setUserId(userId);
        unsubscribeMsg.setRecordIds(Lists.newArrayList(invalidIds));
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_UNSUBSCRIBE, JSONUtil.toJsonStr(unsubscribeMsg));

        // 购买贵族
        return addAristocracyGrant(userId, aristocracyConfigDTO, gradeDTO, source, AristocracyConstant.BuyType.UPGRADE, sendUserId);
    }

    /**
     * 升级
     * @param userId               用户ID
     * @param aristocracyConfigDTO 贵族配置
     * @param gradeDTO             档位ID
     * @param sendUserId           赠送用户
     */

    public void upgradeAristocracy(long userId, AristocracyConfigDTO aristocracyConfigDTO, AristocracyConfigDTO.GradeDTO gradeDTO, Long sendUserId) {
        /**
         * 退还权益
         * 重新购买贵族
         */
        // 获取用户当前生效的贵族
        List<UserAristocracyRecordsDO> userEffectiveAristocracy = getUserEffectiveAristocracy(userId);
        List<Integer> invalidIds = userEffectiveAristocracy.stream().map(UserAristocracyRecordsDO::getId).toList();
        // 将所有贵族失效
        userEffectiveAristocracy.forEach(item->{
            item.setRecordStatus(AristocracyConstant.Status.INVALID.getStatus());
            baseMapper.updateById(item);
        });

        // 购买贵族
        addAristocracy(userId, aristocracyConfigDTO, gradeDTO, sendUserId, AristocracyConstant.BuyType.UPGRADE);
        // 失效当前生效的贵族
        log.info("aristocracy upgradeAristocracy uid:{} invalidIds:{} ,{}", userId, invalidIds, baseMapper.selectBatchIds(invalidIds));
        //  发送 退订Mq
        UnsubscribeMsg unsubscribeMsg = new UnsubscribeMsg();
        unsubscribeMsg.setUserId(userId);
        unsubscribeMsg.setRecordIds(Lists.newArrayList(invalidIds));
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_UNSUBSCRIBE, JSONUtil.toJsonStr(unsubscribeMsg));
    }

    /**
     * 续费
     * @param userId               用户ID
     * @param aristocracyConfigDTO 贵族配置
     * @param gradeDTO             档位ID
     * @param giveUserId           赠送用户
     */
    private void renewAristocracy(long userId, AristocracyConfigDTO aristocracyConfigDTO, AristocracyConfigDTO.GradeDTO gradeDTO, Long giveUserId) {
        /**
         * 扣款
         * 插入记录
         * 下发权益
         */
        Optional<UserCurAristocracyInfo> userAristocracyRecordsOpt = getUserAristocracyRecords(userId);
        UserCurAristocracyInfo userCurAristocracyInfo = userAristocracyRecordsOpt.orElseThrow(() -> new ApiException(CodeEnum.ARISTOCRACY_RECORD_NOT_EXIST));
        int aristocracyConfigDTOId = aristocracyConfigDTO.getId();
        int gradeId = gradeDTO.getId();
        int price = gradeDTO.getPrice();
        int day = gradeDTO.getDay();
        // 获取用户最后的结束时间
        long now = userCurAristocracyInfo.getEndTime() + 1001;
        long endTime = now + day * DAY_MILLIS;
        // 扣款
        long deductUserId = giveUserId == null ? userId : giveUserId;
        int source = Objects.isNull(giveUserId)
                ? AristocracyConstant.Source.BUY.getSource() : AristocracyConstant.Source.GIFT.getSource();
        // 处理折扣
        // 处理折扣
        Optional<UserCurAristocracyInfo> userCurAristocracyInfoOptional = getUserAristocracyRecordsCache(deductUserId);
        if (userCurAristocracyInfoOptional.isPresent()) {
            UserCurAristocracyInfo deductUserCurAristocracyInfo = userCurAristocracyInfoOptional.get();
            Integer discount = deductUserCurAristocracyInfo.getAristocracyPropInfo().getDiscount();
            price = price - (price - price * discount / 100);
            log.info("aristocracy  renewAristocracy ,uid:{},gradeId:{},aristocracyId:{},price:{},discount:{}",
                    userId, gradeId, aristocracyConfigDTOId, price, discount);
        }
        log.info("aristocracy  renewAristocracy ,uid:{},gradeId:{},aristocracyId:{},price:{}",
                userId, gradeId, aristocracyConfigDTOId, price);
        purseManageService
                .deductCoin(deductUserId, (long) price, BillEnum.ARISTOCRACY,
                        CommonUtil.genId(), "", Collections.emptyMap(), 0L, PurseRoleTypeEnum.USER.getType());
        UserAristocracyRecordsDO userAristocracyRecordsDO = new UserAristocracyRecordsDO();
        userAristocracyRecordsDO.setUserId(userId);
        userAristocracyRecordsDO.setDurationDays(day);
        userAristocracyRecordsDO.setGiftUserId(giveUserId);
        userAristocracyRecordsDO.setAristocracyLevel(aristocracyConfigDTOId);
        userAristocracyRecordsDO.setGradeId(gradeId);
        userAristocracyRecordsDO.setGetSource(source);
        userAristocracyRecordsDO.setStartTime(now);
        userAristocracyRecordsDO.setEndTime(endTime);
        userAristocracyRecordsDO.setCreateTime(new Date());
        userAristocracyRecordsDO.setRecordStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());
        this.save(userAristocracyRecordsDO);
        log.info("aristocracy renewAristocracy success uid:{} ,aristocracyId:{},gradeId:{}", userId, aristocracyConfigDTOId, gradeId);
        // 发送 mq 消息
        BuySuccessMsg buySuccessMsg = new BuySuccessMsg();
        buySuccessMsg.setRecordId(userAristocracyRecordsDO.getId());
        buySuccessMsg.setAristocracyId(aristocracyConfigDTOId);
        buySuccessMsg.setSource(source);
        buySuccessMsg.setUserId(userId);
        buySuccessMsg.setGiveUserId(giveUserId);
        buySuccessMsg.setGrade(gradeId);
        buySuccessMsg.setBuyType(AristocracyConstant.BuyType.RENEW);
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_BUY_SUCCESS, JSONUtil.toJsonStr(buySuccessMsg));

    }

    /**
     * 新增行为
     *
     * @param userId               用户ID
     * @param aristocracyConfigDTO 贵族配置
     * @param gradeDTO             档位ID
     * @param giveUserId           赠送用户
     */
    private void addAristocracy(long userId, AristocracyConfigDTO aristocracyConfigDTO,
                                AristocracyConfigDTO.GradeDTO gradeDTO, Long giveUserId, AristocracyConstant.BuyType buyType) {
        /**
         * 扣款
         * 插入记录
         * 下发权益
         *
         */
        int aristocracyConfigDTOId = aristocracyConfigDTO.getId();
        int gradeId = gradeDTO.getId();

        int price = gradeDTO.getPrice();
        long deductUserId = giveUserId == null ? userId : giveUserId;
        // 处理折扣
        Optional<UserCurAristocracyInfo> userCurAristocracyInfoOptional = getUserAristocracyRecordsCache(deductUserId);
        if (userCurAristocracyInfoOptional.isPresent()) {
            UserCurAristocracyInfo userCurAristocracyInfo = userCurAristocracyInfoOptional.get();
            Integer discount = userCurAristocracyInfo.getAristocracyPropInfo().getDiscount();
            price = price - (price - price * discount / 100);
            log.info("aristocracy addAristocracy buyType:{},uid:{},gradeId:{},aristocracyId:{},price:{},discount:{}",
                    buyType, userId, gradeId, aristocracyConfigDTOId, price, discount);
        }
        int day = gradeDTO.getDay();
        long now = System.currentTimeMillis();
        long endTime = now + day * DAY_MILLIS;
        // 扣款
        int source = Objects.isNull(giveUserId)
                ? AristocracyConstant.Source.BUY.getSource() : AristocracyConstant.Source.GIFT.getSource();
        UserAristocracyRecordsDO userAristocracyRecordsDO = new UserAristocracyRecordsDO();
        userAristocracyRecordsDO.setUserId(userId);
        userAristocracyRecordsDO.setGiftUserId(giveUserId);
        userAristocracyRecordsDO.setDurationDays(day);
        userAristocracyRecordsDO.setAristocracyLevel(aristocracyConfigDTOId);
        userAristocracyRecordsDO.setGradeId(gradeId);
        userAristocracyRecordsDO.setGetSource(source);
        userAristocracyRecordsDO.setStartTime(now);
        userAristocracyRecordsDO.setEndTime(endTime);
        userAristocracyRecordsDO.setCreateTime(new Date());
        userAristocracyRecordsDO.setRecordStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());

        purseManageService
                .deductCoin(deductUserId, (long) price, BillEnum.ARISTOCRACY,
                        CommonUtil.genId(), "", Collections.emptyMap(), 0L);
        this.save(userAristocracyRecordsDO);
        log.info("aristocracy addAristocracy success uid:{} ,aristocracyId:{},gradeId:{}", userId, aristocracyConfigDTOId, gradeId);
        // 发送 mq 消息
        BuySuccessMsg buySuccessMsg = new BuySuccessMsg();
        buySuccessMsg.setRecordId(userAristocracyRecordsDO.getId());
        buySuccessMsg.setAristocracyId(aristocracyConfigDTOId);
        buySuccessMsg.setSource(source);
        buySuccessMsg.setUserId(userId);
        buySuccessMsg.setGiveUserId(giveUserId);
        buySuccessMsg.setGrade(gradeId);
        buySuccessMsg.setBuyType(buyType);
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_BUY_SUCCESS, JSONUtil.toJsonStr(buySuccessMsg));
    }

    /**
     * 新增行为
     *
     * @param userId               用户ID
     * @param aristocracyConfigDTO 贵族配置
     * @param gradeDTO             档位ID
     */
    private int addAristocracyGrant(long userId, AristocracyConfigDTO aristocracyConfigDTO,
                                    AristocracyConfigDTO.GradeDTO gradeDTO,
                                    AristocracyConstant.Source source, AristocracyConstant.BuyType buyType, Long sendUserId) {
        /**
         * 扣款
         * 插入记录
         * 下发权益
         *
         */
        int aristocracyConfigDTOId = aristocracyConfigDTO.getId();
        int gradeId = gradeDTO.getId();

        int price = gradeDTO.getPrice();
        int day = gradeDTO.getDay();
        long now = System.currentTimeMillis();
        long endTime = now + day * DAY_MILLIS;
        // 扣款
        UserAristocracyRecordsDO userAristocracyRecordsDO = new UserAristocracyRecordsDO();
        userAristocracyRecordsDO.setUserId(userId);
        userAristocracyRecordsDO.setGiftUserId(sendUserId);
        userAristocracyRecordsDO.setDurationDays(day);
        userAristocracyRecordsDO.setAristocracyLevel(aristocracyConfigDTOId);
        userAristocracyRecordsDO.setGradeId(gradeId);
        userAristocracyRecordsDO.setGetSource(source.getSource());
        userAristocracyRecordsDO.setStartTime(now);
        userAristocracyRecordsDO.setEndTime(endTime);
        userAristocracyRecordsDO.setCreateTime(new Date());
        userAristocracyRecordsDO.setRecordStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());

        this.save(userAristocracyRecordsDO);
        log.info("aristocracy addAristocracyGrant success uid:{} ,aristocracyId:{},gradeId:{},source:{}", userId, aristocracyConfigDTOId, gradeId, source);
        // 发送 mq 消息
        BuySuccessMsg buySuccessMsg = new BuySuccessMsg();
        buySuccessMsg.setRecordId(userAristocracyRecordsDO.getId());
        buySuccessMsg.setAristocracyId(aristocracyConfigDTOId);
        buySuccessMsg.setSource(source.getSource());
        buySuccessMsg.setUserId(userId);
        buySuccessMsg.setGiveUserId(sendUserId);
        buySuccessMsg.setGrade(gradeId);
        buySuccessMsg.setBuyType(buyType);
        rocketMqSender.sendAsynchronousMsg(RocketMQTopic.ARISTOCRACY_BUY_SUCCESS, JSONUtil.toJsonStr(buySuccessMsg));
        return userAristocracyRecordsDO.getId();
    }

    public UserAristocracyRecordsDO getUserAristocracyLv(Long uid){
        PageHelper.startPage(1,1);
        LambdaQueryWrapper<UserAristocracyRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserAristocracyRecordsDO::getUserId,uid);
        wrapper.orderByDesc(UserAristocracyRecordsDO::getAristocracyLevel);
        return getOne(wrapper);
    }

    /**
     * 构建AristocracyGradeVO 列表
     */
    public List<AristocracyGradeVO> aristocracyGradeList(long userId) {
        try {
            Optional<UserCurAristocracyInfo> userCurAristocracyInfoOptional = getUserAristocracyRecordsCache(userId);
            LanguageEnum lang = MessageSourceUtil.getLang();

            List<AristocracyConfigDTO> aristocracyConfigDTOS = aristocracyConfigService.getAristocracyConfig();
            List<PrivilegeConfigDTO> privilegeConfigDTOS = aristocracyConfigService.getPrivilegeConfig();
            Map<Integer, Map<Integer, PrivilegeDescribeDTO>> privilegeDescribeMap = aristocracyConfigService.getPrivilegeDescribeMap();

            List<AristocracyGradeVO> aristocracyGradeVOS = new ArrayList<>();
            aristocracyConfigDTOS.forEach(item->{
                int id = item.getId();
                AristocracyGradeVO aristocracyGradeVO = new AristocracyGradeVO();
                aristocracyGradeVOS.add(aristocracyGradeVO);
                aristocracyGradeVO.setId(item.getId());
                aristocracyGradeVO.setMp4Url(item.getMp4Url());
                aristocracyGradeVO.setName(lang == LanguageEnum.ar
                        ? item.getArName() : item.getEnName());
                aristocracyGradeVO.setIconUrl(item.getIconUrl());
                aristocracyGradeVO.setStatus(item.getStatus());
                aristocracyGradeVO.setAnimationUrl(item.getAnimationUrl());
                Map<Integer, PrivilegeDescribeDTO> integerPrivilegeDescribeDTOMap = privilegeDescribeMap.get(id);

                // 特权
                List<Integer> privileges = item.getPrivileges();
                List<PrivilegeInfoVO> privilegeInfoVOS = new ArrayList<>();

                privilegeConfigDTOS.forEach(privilegeConfigDTO -> {
                    PrivilegeInfoVO privilegeInfoVO = new PrivilegeInfoVO();
                    privilegeInfoVOS.add(privilegeInfoVO);
                    privilegeInfoVO.setId(privilegeConfigDTO.getId());
                    privilegeInfoVO.setName(lang == LanguageEnum.ar ?
                            privilegeConfigDTO.getArName() : privilegeConfigDTO.getEnName());
                    privilegeInfoVO.setDescribe(lang == LanguageEnum.ar ?
                            privilegeConfigDTO.getArDescribe() : privilegeConfigDTO.getEnDescribe());
                    if (privileges.contains(privilegeInfoVO.getId())) {
                        privilegeInfoVO.setStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());
                    }else {
                        privilegeInfoVO.setStatus(AristocracyConstant.Status.INVALID.getStatus());
                    }
                    if (privilegeConfigDTO.getIconUrls().size() >= id) {
                        privilegeInfoVO.setIconUrl(privilegeConfigDTO.getIconUrls().get(id - 1));
                    } else {
                        log.error("aristocracy config error.id:{},privilegeId:{},iconUrls:{}", id, privilegeConfigDTO.getId(), privilegeConfigDTO.getIconUrls());
                        privilegeInfoVO.setIconUrl(privilegeConfigDTO.getIconUrls().get(0));
                    }
                    PrivilegeDescribeDTO privilegeDescribeDTO = integerPrivilegeDescribeDTOMap.get(privilegeInfoVO.getId());
                    PrivilegeInfoVO.Detail detail = new PrivilegeInfoVO.Detail();
                    detail.setId(privilegeDescribeDTO.getId());
                    detail.setDescribe(lang==LanguageEnum.ar?privilegeDescribeDTO.getArDescribe():privilegeDescribeDTO.getEnDescribe());
                    detail.setTitle(lang==LanguageEnum.ar?privilegeDescribeDTO.getArTitle():privilegeDescribeDTO.getEnTitle());
                    detail.setImage(lang == LanguageEnum.ar ? privilegeDescribeDTO.getArImage() : privilegeDescribeDTO.getEnImage());
                    detail.setIconUrl(privilegeDescribeDTO.getIconUrl());
                    // 特权描述
                    if (privileges.contains(privilegeInfoVO.getId())) {
                        privilegeInfoVO.setDetail(detail);
                    }
                });
                aristocracyGradeVO.setPrivileges(privilegeInfoVOS);

                // 档位
                List<GradeVO> gradeVOS = item.getGrades().stream().filter(AristocracyConfigDTO.GradeDTO::isShow).map(gradeDTO -> {
                    GradeVO gradeVO = new GradeVO();
                    gradeVO.setId(gradeDTO.getId());
                    gradeVO.setPrice(gradeDTO.getPrice());
                    gradeVO.setDiscountPrice(gradeDTO.getPrice());
                    gradeVO.setDay(gradeDTO.getDay());

                    // 处理折扣
                    userCurAristocracyInfoOptional.ifPresent(userCurAristocracyInfo -> {
                        Integer discount = userCurAristocracyInfo.getAristocracyPropInfo().getDiscount();
                        int price = gradeVO.getPrice();
                        gradeVO.setDiscountPrice(price - (price - (price * discount) / 100));
                    });

                    return gradeVO;
                }).sorted(Comparator.comparing(GradeVO::getId).reversed()).toList();
                aristocracyGradeVO.setGrades(gradeVOS);

                // 判断用户是否该档位
                userCurAristocracyInfoOptional.ifPresent(userCurAristocracyInfo -> {
                    Integer curAristocracy = userCurAristocracyInfo.getCurAristocracy();
                    if (curAristocracy == item.getId()) {
                        AristocracyGradeVO.UserInfo userInfo = new AristocracyGradeVO.UserInfo();
                        // 计算剩余多少秒
                        Long endTime = userCurAristocracyInfo.getEndTime();
                        userInfo.setRemainTime(endTime / 1000);
                        aristocracyGradeVO.setUserInfo(userInfo);
                    }
                });
            });
            return aristocracyGradeVOS;
        } catch (Exception e) {
            log.error("aristocracy error.{}", com.simi.common.util.ExceptionUtil.formatEx(e), e);
        }
        return new ArrayList<>();
    }


    /**
     * 获取用户当前生效的贵族
     * 会存在多条生效的记录
     * @return List 按 ID 排序
     */
    private List<UserAristocracyRecordsDO> getUserEffectiveAristocracy(long userId) {
        log.info("获取用户当前生效的贵族");
        // 获取当前时间戳
        long now = System.currentTimeMillis();
        LambdaQueryWrapper<UserAristocracyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserAristocracyRecordsDO::getUserId, userId);
        queryWrapper.eq(UserAristocracyRecordsDO::getRecordStatus, AristocracyConstant.Status.EFFECTIVE.getStatus());
        // 结束时间大于当前时间
        queryWrapper.gt(UserAristocracyRecordsDO::getEndTime, now);
        List<UserAristocracyRecordsDO> userAristocracyRecords = this.list(queryWrapper);
        userAristocracyRecords.sort(Comparator.comparing(UserAristocracyRecordsDO::getId));
        return userAristocracyRecords;
    }

    /**
     * 判断购买行为
     */
    private AristocracyConstant.BuyType buyType(long userId, AristocracyConfigDTO aristocracyConfigDTO) {
        // 获取用户当前贵族情况
        int id = aristocracyConfigDTO.getId();
        Optional<UserCurAristocracyInfo> userAristocracyRecordsOpt = getUserAristocracyRecords(userId);
        if (userAristocracyRecordsOpt.isPresent()) {
            UserCurAristocracyInfo userCurAristocracyInfo = userAristocracyRecordsOpt.get();
            Integer curAristocracy = userCurAristocracyInfo.getCurAristocracy();
            // 同等级，续费
            if (curAristocracy == id) {
                return AristocracyConstant.BuyType.RENEW;
            }
            // 大于，降级
            if (curAristocracy > id) {
                return AristocracyConstant.BuyType.DOWNGRADE;
            }
            // 小于 升级
            return AristocracyConstant.BuyType.UPGRADE;
        }else {
            // 新增
            return AristocracyConstant.BuyType.ADD;
        }
    }

    /**
     * 刷新缓存
     */
    private UserCurAristocracyInfo flushCache(long userId) {
        // 刷新缓存
        // 记录刷新时间
        redissonManager.setnx(UserRedisKey.user_aristocracy_refresh_time.getKey(userId), "1", 30, TimeUnit.MINUTES);
        Optional<UserCurAristocracyInfo> optional = getUserAristocracyRecords(userId);
        if (optional.isEmpty()) {
            log.info("aristocracy flushCache getUserAristocracyRecords is empty.{}", userId);
            return null;
        }
        log.info("aristocracy flushCache.userId:{}", userId);
        UserCurAristocracyInfo userCurAristocracyInfo = optional.get();
        Map<String, String> map = new HashMap<>();
        map.put("" + userId, JSONUtil.toJsonStr(userCurAristocracyInfo));
        redissonManager.hMSet(UserRedisKey.user_aristocracy.getKey(), map);


        Optional<AristocracyPropConfigDTO> aristocracyPropConfigDTOOptional = aristocracyConfigService.getAristocracyPropConfigById(userCurAristocracyInfo.getCurAristocracy());
        aristocracyPropConfigDTOOptional.ifPresent(item->{
            UserCurAristocracyInfo.AristocracyPropInfo aristocracyPropInfo = BeanUtil.copyProperties(item, UserCurAristocracyInfo.AristocracyPropInfo.class);
            userCurAristocracyInfo.setAristocracyPropInfo(aristocracyPropInfo);
           aristocracyPlaqueRetrievalService.getFromCache(userId).ifPresent(k->{
                aristocracyPropInfo.setPlaqueEn(k.getEnglishImageUrl());
                aristocracyPropInfo.setPlaqueAr(k.getArabicImageUrl());
            });
        } );
        return userCurAristocracyInfo;
    }

    public List<UserAristocracyRecordsDO> getUserEffectiveAristocracyByUserIds(List<Long> uidList) {
        // 获取当前时间戳
        long now = System.currentTimeMillis();
        LambdaQueryWrapper<UserAristocracyRecordsDO> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(UserAristocracyRecordsDO::getUserId, uidList);
        queryWrapper.eq(UserAristocracyRecordsDO::getRecordStatus, AristocracyConstant.Status.EFFECTIVE.getStatus());
        // 结束时间大于当前时间
        queryWrapper.gt(UserAristocracyRecordsDO::getEndTime, now);
        List<UserAristocracyRecordsDO> userAristocracyRecords = this.list(queryWrapper);
        return userAristocracyRecords;
    }
}
