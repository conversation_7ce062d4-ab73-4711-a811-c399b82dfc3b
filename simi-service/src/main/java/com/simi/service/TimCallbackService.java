package com.simi.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.util.RedissonManager;
import com.simi.dto.*;
import com.simi.constant.UserRedisKey;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Service
@RequiredArgsConstructor
public class TimCallbackService {

    private final RedissonManager redissonManager;


    private final SensitiveVocabularyService sensitiveVocabularyService;

    private final RocketMqSender rocketMqSender;

    private static String ACTION  = "";


    // 创建一个可并发调度的线程池
    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(50);

    private static final List<String> SYSTEM_ACCOUNT_LIST = Arrays.asList("10000", "10001", "10002", "10003", "10004", "10005", "10006", "10007"); // 你的系统账号

    public ResponseEntity<BeforeSendC2CMsgResponse> handleMessage(CallbackCommandDTO dto) {

        if (dto == null) {
            log.error("recv tim callback error  dto is null");
            return null;
        }
        log.info("recv tim callback:{}", JSONUtil.toJsonStr(dto));
        try {
            if (Objects.equals(dto.getCallbackCommand(), "C2C.CallbackBeforeSendMsg")) {
                return handleC2CCallbackBeforeSendMsg(dto);
            } else if (Objects.equals(dto.getCallbackCommand(), "State.StateChange")) {
                return handleStateStateChange(dto);
            } else {
                return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,null));
            }
        } catch (Exception e) {
            log.error("recv tim callback error :{}",e.getMessage());
        }
        return null;
    }

    public ResponseEntity<BeforeSendC2CMsgResponse> handleC2CCallbackBeforeSendMsg(CallbackCommandDTO req) throws Exception {

        // **1. 系统账户消息直接通过**
        if (SYSTEM_ACCOUNT_LIST.contains(req.getFromAccount())) {
            return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,null));
        }

        // **2. 解析 TIMCustomElem 自定义消息**
        if (!req.getMsgBodyList().isEmpty() && "TIMCustomElem".equals(req.getMsgBodyList().get(0).getMsgType())) {
            String data = req.getMsgBodyList().get(0).getMsgContent().getData();
            if ("transferAccounts".equals(data) || "SystemSendGiftStore".equals(data)) {
                return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,null));
            }

            boolean isFollowing = redissonManager.sIsMember(UserRedisKey.following.getKey(StrUtil.format("{{}}", req.getToAccount())), req.getFromAccount());

            if (Boolean.FALSE.equals(isFollowing)) {
                String limitKey = String.format("tim:limit:%s:%s", req.getFromAccount(), req.getToAccount());
                Long count = redissonManager.increment(limitKey, 1L);

                if (count == 1) {
                    redissonManager.expire(limitKey,30, TimeUnit.DAYS);
                } else if (count > 3) {
                    return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "limit3_error", 120001,null));
                }
            }

            // **4. 处理普通文本消息**
            if (req.getMsgBodyList().isEmpty()) {
                return ResponseEntity.ok(new BeforeSendC2CMsgResponse("FAIL", "msg body is empty", 1,null));
            }

            TimMsgBody msg = req.getMsgBodyList().get(0);
            if (!"TIMTextElem".equals(msg.getMsgType())) {
                return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,null));
            }

            if ("https://t.me/+102gGNa_wsliOWFl".equals(msg.getMsgContent().getText())) {
                return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,null));
            }

            // **5. 过滤敏感词**
            String filteredContent = sensitiveVocabularyService.filter(msg.getMsgContent().getText());
            TimMsgContent timMsgContent = new TimMsgContent();
            timMsgContent.setText(filteredContent);

            // **6. 返回处理后的消息**
            return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,
                    Collections.singletonList(new TimMsgBody("TIMTextElem", timMsgContent))));
        }
        return null;
    }


    public ResponseEntity<BeforeSendC2CMsgResponse> handleStateStateChange(CallbackCommandDTO req) {

        String action = req.getInfo().getAction();
        String toAccount = req.getInfo().getToAccount();

        if ("Login".equals(action)) {
            try {
                // 设置用户在线状态到 Redis
                redissonManager.set("tim_online:" + toAccount, action);
                // 发送 TIM 登录回调消息到 MQ
                rocketMqSender.sendAsynchronousMsg("tim_login_callback_topic", toAccount);
            } catch (Exception e) {
                log.error("set tim online status error", e);
            }
        } else if ("Logout".equals(action) || "Disconnect".equals(action)) {
            try {
                // 发送 TIM 退出消息到 MQ
                rocketMqSender.sendAsynchronousMsg(RocketMQTopic.TIM_LOGOUT_CALLBACK_TOPIC, toAccount);
                // 从 Redis 删除用户在线状态
                redissonManager.del("tim_online:" + toAccount);

            } catch (Exception e) {
                log.error("del tim online status error", e);
            }
        }
        scheduler.schedule(() -> {
            String accountAction = redissonManager.get("tim_online:" + toAccount);
            if (!"Login".equals(accountAction)) {
                rocketMqSender.sendAsynchronousMsg(RocketMQTopic.CONSTRAINT_DROP_OUT, toAccount);
            }
        }, 60, TimeUnit.SECONDS);

        // 返回响应
        return ResponseEntity.ok(new BeforeSendC2CMsgResponse("OK", "", 0,null));
    }
}