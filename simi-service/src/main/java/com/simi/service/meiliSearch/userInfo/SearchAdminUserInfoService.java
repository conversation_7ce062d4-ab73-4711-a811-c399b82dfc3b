//package com.simi.service.meiliSearch.userInfo;
//
//
//import com.simi.common.dto.user.search.SearchAdminUserInfoDTO;
//import com.simi.common.vo.SearchResult;
//import com.simi.mapper.search.MeiliSearchAdminUserInfoMapper;
//import com.meilisearch.sdk.SearchRequest;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class SearchAdminUserInfoService {
//    private final MeiliSearchAdminUserInfoMapper meiliSearchAdminUserInfoMapper;
//
//    public long batchSave(List<SearchAdminUserInfoDTO> userInfoList){
//        if (CollectionUtils.isEmpty(userInfoList)){
//            return 0l;
//        }
//
//        long result = meiliSearchAdminUserInfoMapper.add(userInfoList);
//        return result;
//    }
//
//    public Long countTotal(String[] fields){
//        return meiliSearchAdminUserInfoMapper.getDocumentTotal(fields);
//    }
//
//    public SearchResult<SearchAdminUserInfoDTO> filterInfo(String[] filter,Integer pageNum,Integer pageSize){
//        Integer offset = (pageNum-1)*pageSize;
//        SearchRequest searchRequest = SearchRequest
//                .builder()
//                .filter(filter)
//                .offset(offset)
//                .limit(pageSize)
//                .sort(new String[]{"signTimestamp:desc"})
//                .build();
//        SearchResult<SearchAdminUserInfoDTO> searchResult = meiliSearchAdminUserInfoMapper.search(searchRequest);
//        return searchResult;
//    }
//
//    public SearchResult<SearchAdminUserInfoDTO> filterLimitSize(String[] filter,Integer pageSize,String order){
//        SearchRequest searchRequest = SearchRequest
//                .builder()
//                .filter(filter)
//                .limit(pageSize)
//                .sort(new String[]{order})
//                .build();
//        SearchResult<SearchAdminUserInfoDTO> searchResult = meiliSearchAdminUserInfoMapper.search(searchRequest);
//        return searchResult;
//    }
//
//    public SearchResult<SearchAdminUserInfoDTO> filterInfoOrderBy(String[] filter,Integer pageNum,Integer pageSize,String order){
//        Integer offset = (pageNum-1)*pageSize;
//        SearchRequest searchRequest = SearchRequest
//                .builder()
//                .filter(filter)
//                .offset(offset)
//                .limit(pageSize)
//                .sort(new String[]{order})
//                .build();
//        SearchResult<SearchAdminUserInfoDTO> searchResult = meiliSearchAdminUserInfoMapper.search(searchRequest);
//        return searchResult;
//    }
//}
