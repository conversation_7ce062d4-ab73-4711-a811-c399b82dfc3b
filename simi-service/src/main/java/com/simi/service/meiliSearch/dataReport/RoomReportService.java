//package com.simi.service.meiliSearch.dataReport;
//
//
//import com.google.common.collect.Lists;
//import com.simi.common.dto.dataReport.SearchRoomReportDTO;
//import com.simi.common.vo.SearchResult;
//import com.simi.mapper.search.MeiliSearchRoomReportMapper;
//import com.meilisearch.sdk.SearchRequest;
//import lombok.RequiredArgsConstructor;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.collections.CollectionUtils;
//import org.springframework.stereotype.Service;
//
//import java.util.List;
//
//@Slf4j
//@Service
//@RequiredArgsConstructor
//public class RoomReportService {
//    private final MeiliSearchRoomReportMapper meiliSearchRoomReportMapper;
//
//    public long batchSave(List<SearchRoomReportDTO> room){
//        if (CollectionUtils.isEmpty(room)){
//            return 0l;
//        }
//
//        List<List<SearchRoomReportDTO>> partition = Lists.partition(room, 500);
//        long result = 0l;
//        for (List<SearchRoomReportDTO> searchRoomReportDTOS : partition) {
//             result += meiliSearchRoomReportMapper.add(searchRoomReportDTOS);
//
//        }
//        return result;
//    }
//
//
//    public SearchResult<SearchRoomReportDTO> getPartReport(String[] params, Integer pageNum, Integer pageSize) {
//        Integer offset = (pageNum-1) * pageSize;
//        SearchRequest searchRequest = SearchRequest
//                .builder()
//                .filter(params)
//                .offset(offset)
//                .limit(pageSize)
//                .sort(new String[]{"beginTimestamp:desc"})
//                .build();
//        SearchResult<SearchRoomReportDTO> search = meiliSearchRoomReportMapper.search(searchRequest);
//        return search;
//    }
//}
