package com.simi.service;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.ConfigGroup;
import com.simi.common.dto.shop.prop.PropNewProductTimestampDTO;
import com.simi.entity.PropInfo;
import com.simi.entity.group.AppFunctionsGroupConfig;
import com.simi.mapper.PropInfoMapper;
import com.simi.service.cache.PropInfoCache;
import com.simi.service.group.UserFunctionCountryGroupService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

@Service
public class PropInfoService extends ServiceImpl<PropInfoMapper, PropInfo> {

    @Resource
    private PropInfoMapper propInfoMapper;
    @Resource
    private PropInfoCache propInfoCache;
    @Resource
    private UserFunctionCountryGroupService countryGroupService;

    public PropInfo getPropInfo(Long id) {
        String propInfo = propInfoCache.getPropInfo(id);
        if (StringUtils.isNotBlank(propInfo)) {
            return JSONUtil.toBean(propInfo, PropInfo.class);
        }
        PropInfo info = getById(id);
        if (info != null) {
            propInfoCache.setPropInfoById(id, info);
        }
        return info;
    }

    public List<PropNewProductTimestampDTO> getOnSaleNewProductTimestamp(Integer newProductTimestampGt, Integer type) {
        return propInfoMapper.getNewProductTimestamp(newProductTimestampGt, type);
    }

    public List<PropInfo> getOnSalePropByType(Long uid, Integer type) {
        AppFunctionsGroupConfig config = countryGroupService.getFunctionsGroup(ConfigGroup.OPERATIONAL_BACKEND_COMMON, uid);

        if (config == null) {
            return Collections.emptyList();
        }
        return propInfoMapper.listPropByType2(type, config.getGroupId());
    }
}