package com.simi.service;

import cn.hutool.core.util.StrUtil;
import com.simi.common.constant.AppVersionEnum;
import com.simi.config.AppVersionConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-12 11:15
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class AppVersionCheckService {

    private final AppVersionConfig appVersionConfig;

    /**
     * 判断版本大小，大于返回true
     *
     * @param versionCode 版本入参数
     */
    public boolean checkVersion(AppVersionEnum key, String versionCode) {

        if (StrUtil.isBlank(versionCode)) {
            return false;
        }
        /*
         * versionCode 值是：1.7.5  需要转成数字
         */
        int versionConfig = Integer.parseInt(Optional.ofNullable(appVersionConfig.keyVersion.get(key.getKey())).orElseGet(() -> "0"));

        return versionConfig > Integer.parseInt(versionCode.replace(".", ""));
    }

    public boolean checkVersionGt(AppVersionEnum key, String versionCode) {
        if (StrUtil.isBlank(versionCode)) {
            return false;
        }

        int versionConfig = Integer.parseInt(Optional.ofNullable(appVersionConfig.keyVersion.get(key.getKey())).orElseGet(() -> "0"));
        int version = Integer.parseInt(versionCode.replace(".", ""));
        return version > versionConfig;
    }
}
