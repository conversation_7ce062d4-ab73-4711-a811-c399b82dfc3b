package com.simi.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.RegisterLimitRecord;
import com.simi.mapper.RegisterLimitRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class RegisterLimitRecordService extends ServiceImpl<RegisterLimitRecordMapper, RegisterLimitRecord> {
}
