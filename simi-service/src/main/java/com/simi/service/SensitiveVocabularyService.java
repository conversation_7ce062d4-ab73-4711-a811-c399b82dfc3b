package com.simi.service;

import com.simi.config.SensitiveConfig;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class SensitiveVocabularyService {

    /**
     * 敏感词替换成 *
     * @param content
     * @return
     * @throws Exception
     */
    public String filter(String content) throws Exception {
        String[] parts = SensitiveConfig.words.split(",");
        for (String part : parts) {
            content = content.replaceAll(part, "*".repeat(part.length()));
        }
        return content;
    }

    /**
     * 判断是否包含敏感词
     * @param content
     * @return
     * @throws Exception
     */
    public boolean recognize(String content) {
        String[] parts = SensitiveConfig.words.split(",");
        for (String part : parts) {
            boolean contains = content.contains(part);
            if (contains) {
                return true;
            }
        }
        return false;
    }

}
