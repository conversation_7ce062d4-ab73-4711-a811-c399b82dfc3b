package com.simi.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.entity.ApplePayRecord;
import com.simi.entity.GooglePayRecord;
import com.simi.mapper.ApplePayRecordMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApplePayRecordService extends ServiceImpl<ApplePayRecordMapper, ApplePayRecord> {

    public ApplePayRecord getApplePayRecord(String purchaseToken){
        LambdaQueryWrapper<ApplePayRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApplePayRecord::getPurchaseToken,purchaseToken);
        return getOne(wrapper);
    }

    public void creation(String sku,String version,String purchaseToken,Integer notificationType){
        ApplePayRecord applePayRecord  = new ApplePayRecord();
        applePayRecord.setSku(sku);
        applePayRecord.setVersion(version);
        applePayRecord.setPurchaseToken(purchaseToken);
        applePayRecord.setNotificationType(notificationType);
        save(applePayRecord);
    }


}
