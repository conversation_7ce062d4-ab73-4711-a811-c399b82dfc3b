package com.simi.service;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.aliyun.oss.OSS;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.MatchMode;
import com.aliyun.oss.model.PolicyConditions;

import com.aliyun.oss.model.PutObjectRequest;
import com.simi.common.config.ResourceConfig;
import com.simi.common.constant.FileTypeEnum;
import com.simi.common.dto.OssPolicyDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.MalformedURLException;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Date;

/**
 * Oss policy服务
 *
 * <AUTHOR>
 * @date 2023/11/3 11:00
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OssPolicyService {

    @Lazy
    private final OSS ossClient;

    public OssPolicyDTO getPolicy(FileTypeEnum fileTypeEnum) {
        String host = StrUtil.format("https://{}.{}", ResourceConfig.bucket, ResourceConfig.endpoint);
        PolicyConditions policyConditions = new PolicyConditions();
        // 设置内容最大值100M
        policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 104857600);
        policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, fileTypeEnum.getDir());
        Date now = new Date();
        Date expireTime = DateUtil.offsetMinute(now, 10);
        String postPolicy = ossClient.generatePostPolicy(expireTime, policyConditions);
        byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
        String encodePolicy = BinaryUtil.toBase64String(binaryData);
        String postSignature = ossClient.calculatePostSignature(postPolicy);
        OssPolicyDTO dto = new OssPolicyDTO();
        dto.setAccessKeyId(ResourceConfig.accessKey);
        dto.setPolicy(encodePolicy);
        dto.setSignature(postSignature);
        dto.setDir(fileTypeEnum.getDir());
        dto.setHost(host);
        dto.setExpire(expireTime.getTime() / 1000);
        dto.setDomain(ResourceConfig.domain);
        return dto;
    }

    /**
     * 根据文件url，将文件导入到oss
     * @param fileUrl
     * @throws MalformedURLException
     */
    public String importFile(String fileUrl, FileTypeEnum fileType, boolean coverFormat) throws IOException {
        String filename = SecureUtil.md5(fileUrl);
        String objName = StrUtil.format("{}{}", fileType.getDir(), filename);
        URL url = new URL(fileUrl);
        String finalUrl = StrUtil.format("{}{}", ResourceConfig.domain, objName);
        try (InputStream in = url.openStream()){
            // 创建PutObjectRequest对象。
            PutObjectRequest putObjectRequest = new PutObjectRequest(ResourceConfig.bucket, objName, in);
            ossClient.putObject(putObjectRequest);
            if(coverFormat){
                finalUrl = StrUtil.format("{}?x-oss-process=image/format,png", finalUrl);
            }
            return finalUrl;
        }

    }


}
