package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.JsonUtils;
import com.simi.common.vo.UserSimpleVO;
import com.simi.common.vo.rank.weeklyStar.HistoryDataVO;
import com.simi.dto.weeklyStar.WeekStarHistoryRankInfo;
import com.simi.entity.GiftInfo;
import com.simi.entity.weeklyStar.WeeklyStarRankingHistory;
import com.simi.mapper.weeklyStar.WeeklyStarRankingHistoryMapper;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-16 14:49
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class WeeklyStarRankingHistoryService extends ServiceImpl<WeeklyStarRankingHistoryMapper, WeeklyStarRankingHistory> {


    private final GiftInfoService giftInfoService;
    private final UserServerService userServerService;
    private final static String GMT = "GMT+3";

    /**
     * 分页获取历史数据
     */
    public ListWithTotal<HistoryDataVO> pageHistoryRanking(Integer curPage, Integer pageSize) {

        Page<WeeklyStarRankingHistory> page = new Page(curPage, pageSize);
        LambdaQueryWrapper<WeeklyStarRankingHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.orderByDesc(WeeklyStarRankingHistory::getWeekNumber);

         page = page(page, queryWrapper);
        List<WeeklyStarRankingHistory> records = page.getRecords();



        List<HistoryDataVO> list = records.stream().map(item -> {
            try {
                HistoryDataVO historyDataVO = new HistoryDataVO();
                Date startTime = item.getStartTime();
                Date endTime = item.getEndTime();
                String startStr = DateTimeUtil.formatTimeMillis(startTime.getTime(), DateTimeUtil.DAY_PATTERN_2, GMT);
                String endStr = DateTimeUtil.formatTimeMillis(endTime.getTime(), DateTimeUtil.DAY_PATTERN_2, GMT);
                historyDataVO.setStartTime(startStr);
                historyDataVO.setEndTime(endStr);
                String rank = item.getRankInfo();
                WeekStarHistoryRankInfo weekStarHistoryRankInfo = JsonUtils.fromJson(rank, WeekStarHistoryRankInfo.class);
                List<WeekStarHistoryRankInfo.RankInfo> rankInfoList = weekStarHistoryRankInfo.getRankInfoList();
                historyDataVO.setTopList(rankInfoList.stream().map(rankInfo -> {
                    HistoryDataVO.TopInfo topInfo = new HistoryDataVO.TopInfo();
                    topInfo.setCount(rankInfo.getGiftCount());
                    // 礼物信息
                    GiftInfo giftInfoFromCache = giftInfoService.getGiftInfoFromCache(rankInfo.getGiftId());
                    Optional.ofNullable(giftInfoFromCache).ifPresent(k -> topInfo.setImageUrl(k.getIcon()));
                    // 存在礼物榜单没有用户的情况
                    if (Objects.isNull(rankInfo.getUserId())) {
                        return topInfo;
                    }
                    // 用户信息
                    UserBaseInfoDTO fromCache = userServerService.getFromCache(rankInfo.getUserId());
                    Optional.ofNullable(fromCache)
                            .ifPresent(k -> topInfo.setUser(BeanUtil.copyProperties(fromCache, UserSimpleVO.class)));

                    return topInfo;
                }).toList());
                return historyDataVO;
            } catch (Exception e) {
                log.error("weeklyStar pageHistoryRanking item:{}", item, e);
            }
            return null;
        }).filter(Objects::nonNull).toList();

        return ListWithTotal.<HistoryDataVO>builder().total(page.getTotal()).list(list).build();

    }

}
