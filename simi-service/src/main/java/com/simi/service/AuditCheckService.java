package com.simi.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.CompareVersionUtil;
import com.simi.constant.PlatformEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/25 23:59
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class AuditCheckService {

    private final SystemConfigService systemConfigService;

    public boolean isAuditMode(XAuthToken param) {
        log.info("Check audit mode :[{}]", JSONUtil.toJsonStr(param));
        if (Objects.isNull(param)) {
            return false;
        }

        String os = param.getOs().substring(0, param.getOs().indexOf(StrUtil.C_SPACE));
        int iend = param.getAppVersionCode().indexOf("+");
        String version = iend != -1 ? param.getAppVersionCode().substring(0, iend) : param.getAppVersionCode();

        if (StrUtil.endWithIgnoreCase(os, PlatformEnum.IOS.getName())) {
            String auditingIosVersion = systemConfigService.getSysConfValueById(SystemConfigConstant.APP_VERSION_AUDITING_IOS);
            return CompareVersionUtil.compareVersion(version, auditingIosVersion) >= 0;
        } else if (StrUtil.endWithIgnoreCase(os, PlatformEnum.ANDROID.getName())) {
            String auditingAndroidVersion = systemConfigService.getSysConfValueById(SystemConfigConstant.APP_VERSION_AUDITING_ANDROID);
            return CompareVersionUtil.compareVersion(version, auditingAndroidVersion) >= 0;
        }

        return false;
    }

    public boolean isAuditUser(Long uid) {
        if (Objects.isNull(uid)) {
            return false;
        }
        String versionUidStr = systemConfigService.getSysConfValueById(SystemConfigConstant.APP_VERSION_AUDITING_UID);
        if (StringUtils.isNotBlank(versionUidStr)) {
            List<String> auditUids = StrUtil.split(versionUidStr, StrUtil.C_COMMA).stream().map(String::trim).toList();
            return auditUids.contains(String.valueOf(uid));
        }
        return false;
    }
}
