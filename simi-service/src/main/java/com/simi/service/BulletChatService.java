package com.simi.service;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.room.BulletChatDTO;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.room.RoomInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.CommonUtil;
import com.simi.constant.BillEnum;
import com.simi.entity.BulletChat;
import com.simi.mapper.BulletChatMapper;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomHighService;
import com.simi.service.user.UserServerService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class BulletChatService extends ServiceImpl<BulletChatMapper, BulletChat> {

    @Autowired
    private SensitiveVocabularyService sensitiveVocabularyService;
    @Autowired
    private UserServerService userServerService;
    @Autowired
    private SystemConfigService systemConfigService;
    @Autowired
    private PurseManageService purseManageService;
    @Autowired
    private RoomHighService roomHighService;

    private static final Integer BULLET_CHAT_MAX = 100;

    public BulletChat getNew(Long uid,String text){
        PageHelper.startPage(1,1);
        LambdaQueryWrapper<BulletChat> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(uid != null,BulletChat::getUid,uid);
        wrapper.eq(StringUtils.isNotBlank(text),BulletChat::getText,text);
        return getOne(wrapper);
    }


    public BulletChatDTO sendBullet(Long uid,String text) {
        if (sensitiveVocabularyService.recognize(text)) {
            throw new ApiException(CodeEnum.MOMENT_CONTAINS_SENSITIVE_WORD);
        }
        String deductGold = systemConfigService.getSysConfValueById(SystemConfigConstant.BULLET_CHAT_DEDUCT_GOLD);
        purseManageService.deductCoin(uid, Long.parseLong(deductGold) , BillEnum.BULLET_CHAT_DEDUCT_GOLD, CommonUtil.genId(),"", Collections.emptyMap(),0L);
        BulletChat chat = new BulletChat();
        chat.setUid(uid);
        chat.setText(text);
        chat.setStatus(true);
        this.save(chat);
        BulletChat bulletChat = getNew(uid, text);
        BulletChatDTO dto = new BulletChatDTO();
        if (bulletChat != null) {
            dto.setId(bulletChat.getId());
            dto.setCreateTime(bulletChat.getCreateTime());
        }
        dto.setUid(uid);
        dto.setText(text);
        dto.setUserBaseInfoDTO(userServerService.getUserBaseInfo(uid));
        return dto;
    }

    public void delete(Long id){
        LambdaUpdateWrapper<BulletChat> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(BulletChat::getStatus,false);
        wrapper.eq(BulletChat::getId,id);
        this.update(wrapper);
    }

    public List<BulletChatDTO> queryList(){
        List<BulletChatDTO> dtos = new ArrayList<>();
        String max = systemConfigService.getSysConfValueById(SystemConfigConstant.BULLET_CHAT_LIST_MAX);
        int pageSize = BULLET_CHAT_MAX;
        if (StringUtils.isNotBlank(max)) {
            pageSize = Integer.parseInt(max);
        }
        PageHelper.startPage(1,1);
        LambdaQueryWrapper<BulletChat> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(BulletChat::getStatus,true);
        wrapper.orderByDesc(BulletChat::getCreateTime);

        List<BulletChat> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return dtos;
        }
        Set<Long> uidSet = list.stream().map(BulletChat::getUid).collect(Collectors.toSet());
        List<Long> uidList = new ArrayList<>(uidSet);
        List<UserBaseInfoDTO> userBaseInfoDTOS = userServerService.listUserDTO(uidList);
        Map<Long, UserBaseInfoDTO> userMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(userBaseInfoDTOS)) {
            userMap = userBaseInfoDTOS.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
        }

        List<Long> uids = list.stream().map(BulletChat::getUid).toList();
        Map<Long, RoomInfoDTO> userInRoomMap = roomHighService.getUserInRoom(uids);
        for (BulletChat chat : list) {
            BulletChatDTO dto = new BulletChatDTO();
            dto.setId(chat.getId());
            dto.setUid(chat.getUid());
            UserBaseInfoDTO userBaseInfoDTO = userMap.get(chat.getUid());
            if (userBaseInfoDTO != null) {
                dto.setUserBaseInfoDTO(userBaseInfoDTO);
            }
            dto.setText(chat.getText());
            dto.setCreateTime(chat.getCreateTime());
            RoomInfoDTO roomInfoDTO = userInRoomMap.get(chat.getUid());
            dto.setUserInRoomInfo(roomInfoDTO);
            dtos.add(dto);
        }
        return dtos;
    }

    public ListWithTotal<BulletChatDTO> adminList(Long uid, Long stime, Long etime, Integer page ,Integer size) {
        ListWithTotal<BulletChatDTO> dtoListWithTotal = new ListWithTotal<>();
        List<BulletChatDTO> dtos = new ArrayList<>();
        PageHelper.startPage(page, size);
        LambdaQueryWrapper<BulletChat> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(uid != null && uid > 0,BulletChat::getUid,uid);
        wrapper.gt(stime != null && stime > 0,BulletChat::getCreateTime,stime);
        wrapper.lt(etime != null && etime > 0,BulletChat::getCreateTime,etime);
        wrapper.eq(BulletChat::getStatus,true);
        wrapper.orderByDesc(BulletChat::getCreateTime);
        List<BulletChat> list = this.list(wrapper);
        PageInfo<BulletChat> pageInfo = new PageInfo<>(list);
        for (BulletChat chat : pageInfo.getList()) {
            BulletChatDTO dto = new BulletChatDTO();
            dto.setId(chat.getId());
            dto.setUid(chat.getUid());
            dto.setText(chat.getText());
            dto.setCreateTime(chat.getCreateTime());
            dtos.add(dto);
        }
        dtoListWithTotal.setTotal(pageInfo.getTotal());
        dtoListWithTotal.setList(dtos);
        return dtoListWithTotal;
    }
}
