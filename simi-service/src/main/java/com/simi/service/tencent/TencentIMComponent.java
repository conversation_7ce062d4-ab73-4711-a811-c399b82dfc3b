package com.simi.service.tencent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.simi.common.constant.CloudCustomType;
import com.simi.common.dto.tencent.*;
import com.simi.common.vo.DefaultPicDTO;
import com.simi.common.constant.http.Constant;
import com.simi.common.constant.tim.TencentConstant;
import com.simi.common.constant.tim.TencentRedisKey;
import com.simi.common.dto.ImageBody;
import com.simi.common.dto.TimModifyC2cMsgDTO;
import com.simi.common.dto.TokenDTO;
import com.simi.common.dto.tencent.callback.UserIdDTO;
import com.simi.common.util.GsonUtil;
import com.simi.common.util.RedissonManager;
import com.simi.config.TencentConfig;
import com.tencentyun.TLSSigAPIv2;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.assertj.core.util.Lists;
import org.jetbrains.annotations.NotNull;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpHeaders;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 腾讯IM组件
 *
 * <AUTHOR>
 * @date 2023/11/20 17:28
 */
@Order(0)
@Slf4j
@Component
@RequiredArgsConstructor
public class TencentIMComponent {

    @Autowired
    private GenerateUserSig generateUserSig;


    private TLSSigAPIv2 tlsSigAPIv2;

    @Resource
    private RedissonClient redissonClient;

    @Resource
    private RedissonManager redissonManager;

    /**
     * 获取token
     *
     * @param uid
     * @return
     */
    public TokenDTO genUserSig(String uid) {
        return genUserSig(uid, true);
    }

    /**
     * 生成tim签名
     */
    public TokenDTO genUserSig(String uid, boolean force) {
        if (Objects.isNull(tlsSigAPIv2)) {
            tlsSigAPIv2 = new TLSSigAPIv2(TencentConfig.sdkAppId, TencentConfig.secretKey);
        }
        String token = tlsSigAPIv2.genUserSig(uid, Constant.DAY_SEC);
        var current = System.currentTimeMillis();
        log.info("genUserSig token:{}", token);
        var expires = current + Constant.DAY_SEC * 1000 * 7; //改为7天
        return TokenDTO.builder().appID(TencentConfig.sdkAppId).token(token).expiresIn(Constant.DAY_SEC).expires(expires).build();
    }


    /**
     * 修改消息
     */
    public void modifyC2cMsg(String fromAccount, String toAccount,String msgKey, List<ImageBody> msgBody,String defaultPic) {
        log.info("modifyC2cMsg fromAccount:{} toAccount:{} msgKey:{} msgBody:{}",fromAccount, toAccount,msgKey,JSONUtil.toJsonStr(msgBody));
        HttpClient client = HttpClient.newHttpClient();
        TimModifyC2cMsgDTO dto = new TimModifyC2cMsgDTO();
        dto.setMsgKey(msgKey);
        dto.setFrom_Account(fromAccount);
        dto.setTo_Account(toAccount);
        dto.setMsgBody(msgBody);
        DefaultPicDTO defaultPicDTO = new DefaultPicDTO();
        defaultPicDTO.setType(CloudCustomType.defaultPic.getType());
        defaultPicDTO.setUrl(defaultPic);
        dto.setCloudCustomData(JSONUtil.toJsonStr(defaultPicDTO));
        int random = ThreadLocalRandom.current().nextInt(0, *********);
        TokenDTO tokenDTO = genUserSig(TencentConfig.administrator, true);
        Gson gson = new Gson();
        String json = gson.toJson(dto);
        log.info("modifyC2cMsg json:{}",json);
        log.info("modifyC2cMsg token:{}", tokenDTO.getToken());
        String url = TencentConfig.rootDomainUrl + StrUtil.format("v4/openim/modify_c2c_msg?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json", TencentConfig.sdkAppId, TencentConfig.administrator, tokenDTO.getToken(), random);
        HttpRequest request = HttpRequest.newBuilder().uri(URI.create(StrUtil.format(url))).POST(HttpRequest.BodyPublishers.ofString(json)).build();
        log.info("modifyC2cMsg bodyPublisher:{} json:{} ",JSONUtil.toJsonStr(request.bodyPublisher()),HttpRequest.BodyPublishers.ofString(json));
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("modifyC2cMsg response:{}",response.body());
            HttpHeaders headers = response.headers();
            headers.map().forEach((k, v) -> System.out.println(k + ":" + v));
        } catch (Exception e) {
            log.error("user complete tim exportAccount error:[{}] uid:[{}]", e.getMessage(), fromAccount);
        }
    }

//    public static void main(String[] args) {
//        String json = "[{\"MsgType\":\"TIMImageElem\",\"MsgContent\":{\"UUID\":\"1741000227_144115286932211407_e17a2f5a1fc40512eab8f9785d297ded.jpg\",\"ImageFormat\":1,\"ImageInfoArray\":[{\"Type\":\"1\",\"Size\":73058,\"Width\":460,\"Height\":690,\"URL\":\"https://reosurce-preview.similive.net/admin/other/0414b7a7-850e-4328-a82c-cd347cf49da6.png\"},{\"Type\":\"2\",\"Size\":0,\"Width\":460,\"Height\":690,\"URL\":\"https://reosurce-preview.similive.net/admin/other/0414b7a7-850e-4328-a82c-cd347cf49da6.png\"},{\"Type\":\"3\",\"Size\":0,\"Width\":198,\"Height\":297,\"URL\":\"https://reosurce-preview.similive.net/admin/other/0414b7a7-850e-4328-a82c-cd347cf49da6.png\"}]}}]";
//        List<ImageBody> bodyList = JSONUtil.toList(json, ImageBody.class);
//        System.out.println(JSONUtil.toJsonStr(bodyList));
//        modifyC2cMsg("********","********","2871433513_1362081221_1729136052",********L,bodyList);
//    }

    /**
     * 导入账号
     */

    public void exportAccount(Long uid, String nick, String avatar) {

        HttpClient client = HttpClient.newHttpClient();
        AccountExportDTO dto = AccountExportDTO.builder().UserID(uid.toString()).Nick(nick).FaceUrl(avatar).build();
        int random = ThreadLocalRandom.current().nextInt(0, *********);

        TokenDTO tokenDTO = genUserSig(TencentConfig.administrator, true);
        Gson gson = new Gson();
        String json = gson.toJson(dto);
        log.info("exportAccount tokenDTO:{}", tokenDTO);
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/im_open_login_svc/account_import?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, TencentConfig.administrator, tokenDTO.getToken(), random);
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(StrUtil.format(url)))
                .POST(HttpRequest.BodyPublishers.ofString(json))
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            HttpHeaders headers = response.headers();
            headers.map().forEach((k, v) -> System.out.println(k + ":" + v));
        } catch (Exception e) {
            log.error("user complete tim exportAccount error:[{}] uid:[{}]", e.getMessage(), uid);
        }
    }


    /**
     * 设置用户资料
     */
    public void subscriber(Long uid, String nick, String avatar, String gender) {

        HttpClient client = HttpClient.newHttpClient();
        List<SubscriberTagValue> profileItem = getSubscriberTagValues(nick, avatar, gender);
        SubscriberDTO dto = new SubscriberDTO();
        dto.setFrom_Account(uid.toString());
        dto.setProfileItem(profileItem);
        int random = ThreadLocalRandom.current().nextInt(0, *********);

        TokenDTO tokenDTO = genUserSig(TencentConfig.administrator, true);
        Gson gson = new Gson();
        String json = gson.toJson(dto);
        log.info("exportAccount tokenDTO:{}", json);
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/profile/portrait_set?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, TencentConfig.administrator, tokenDTO.getToken(), random);
        log.info("exportAccount url:{}", url);
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .POST(HttpRequest.BodyPublishers.ofString(json))
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            HttpHeaders headers = response.headers();
            headers.map().forEach((k, v) -> System.out.println(k + ":" + v));
        } catch (Exception e) {
            log.error("subscriber error msg:[{}]", e.getMessage(), e);
        }
    }

    @NotNull
    private static List<SubscriberTagValue> getSubscriberTagValues(String nick, String avatar, String gender) {
        List<SubscriberTagValue> profileItem = new ArrayList<>();
        SubscriberTagValue tagNick = new SubscriberTagValue();
        tagNick.setTag(TencentConstant.PortraitConst.TAG_PROFILE_IM_NICK);
        tagNick.setValue(nick);
        SubscriberTagValue tagAvatar = new SubscriberTagValue();
        tagAvatar.setTag(TencentConstant.PortraitConst.TAG_PROFILE_IM_IMAGE);
        tagAvatar.setValue(avatar);
        SubscriberTagValue tagGender = new SubscriberTagValue();
        tagGender.setTag(TencentConstant.PortraitConst.TAG_PROFILE_IM_GENDER);
        tagGender.setValue(gender);
        profileItem.add(tagNick);
        profileItem.add(tagAvatar);
        profileItem.add(tagGender);
        return profileItem;
    }

    /**
     * 单发单聊消息
     */

    public void sendMsg(String fromUid, String toUid, String msgData, String identifier, String usersig, OfflinePushInfo offlinePushInfo) throws IOException {
        Long msgRandom = new Random().nextInt() & 0xffffffffL;
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/openim/sendmsg?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, identifier, usersig, msgRandom);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        Map<String, Object> reqParam = Maps.newHashMap();
        //1消息同步到 From_Account，2消息不同步至 From_Account
        reqParam.put("SyncOtherMachine", 1);
        reqParam.put("From_Account", fromUid);
        reqParam.put("To_Account", toUid);
        //消息离线保存时长（单位：秒），最长为7天（604800秒）
        reqParam.put("MsgLifeTime", 604800);
        //消息随机数（32位无符号整数），后台用于同一秒内的消息去重。
        reqParam.put("MsgRandom", msgRandom);
        reqParam.put("MsgType", TencentConstant.TIMCustomElem);

        MsgBody msgBody = new MsgBody(TencentConstant.TIMCustomElem, new CustomMsgContent(msgData));
        reqParam.put("MsgBody", Lists.newArrayList(msgBody));
        if (offlinePushInfo != null) {
            reqParam.put("OfflinePushInfo", offlinePushInfo);
        }
        log.info("Tim send single url:[{}]", url);
        String json = GsonUtil.getGson().toJson(reqParam);
        log.info("Tim send single msg:[{}]", json);
        httpPost.setEntity(new StringEntity(json, "utf-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String respContent = EntityUtils.toString(response.getEntity(), "GBK").trim();
            final Map<String, Object> respMap = GsonUtil.getGson().fromJson(respContent, Map.class);
            final String actionStatus = Convert.toStr(respMap.get("ActionStatus"));
            if (!StrUtil.equalsIgnoreCase(actionStatus, TencentConstant.SUCCESS_ACTION_STATUS)) {
                log.error("TencentIMComponent sendMsg error, respContent:{}", respContent);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            httpClient.close();
        }
    }



    /**
     * 批量发送单聊消息
     * @param fromUid
     * @param toUids
     * @param offlinePushInfo
     */
    public void batchSendMsg(String msgData, String fromUid, List<String> toUids, String identifier, String usersig, OfflinePushInfo offlinePushInfo) throws IOException {
        Long msgRandom = new Random().nextInt() & 0xffffffffL;
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/openim/batchsendmsg?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, identifier, usersig, msgRandom);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        MsgBody msgBody = new MsgBody(TencentConstant.TIMCustomElem, new CustomMsgContent(msgData));
        BatchSendMsgDTO dto = BatchSendMsgDTO.builder().From_Account(fromUid)
                .To_Account(toUids)
                .MsgBody(Lists.newArrayList(msgBody))
                .OfflinePushInfo(offlinePushInfo)
                .MsgRandom(msgRandom)
                .build();

        String json = GsonUtil.getGson().toJson(dto);
        log.info("Tim batch send offlinePushInfo :[{}]", json);
        httpPost.setEntity(new StringEntity(json, "utf-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String respContent = EntityUtils.toString(response.getEntity(), "GBK").trim();
            final Map<String, Object> respMap = GsonUtil.getGson().fromJson(respContent, Map.class);
            final String actionStatus = Convert.toStr(respMap.get("ActionStatus"));
            if (!StrUtil.equalsIgnoreCase(actionStatus, TencentConstant.SUCCESS_ACTION_STATUS)) {
                log.error("TencentIMComponent batch sendMsg error, respContent:{}", respContent);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            httpClient.close();
        }
    }

    /**
     * TIM 推送服务
     * 全员PUSH推送
     */
    public void allStaffPushService(String fromUid, String msgData, String identifier, String usersig, OfflinePushInfo offlinePushInfo) throws IOException {
        Long msgRandom = new Random().nextInt() & 0xffffffffL;
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/timpush/push?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, identifier, usersig, msgRandom);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        Map<String, Object> reqParam = Maps.newHashMap();
        reqParam.put("From_Account", fromUid);
        reqParam.put("MsgRandom", msgRandom);
        // 不保存漫游
        reqParam.put("OnlineOnlyFlag", 1);

        MsgBody msgBody = new MsgBody(TencentConstant.TIMCustomElem, new CustomMsgContent(msgData));
        reqParam.put("MsgBody", Lists.newArrayList(msgBody));

        if (Objects.nonNull(offlinePushInfo)) {
            reqParam.put("OfflinePushInfo", offlinePushInfo);
        }
        String json = GsonUtil.getGson().toJson(reqParam);
        log.info("Tim allStaffPushService json:[{}]", json);
        httpPost.setEntity(new StringEntity(json, "utf-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String respContent = EntityUtils.toString(response.getEntity(), "GBK").trim();
            Map<String, Object> respMap = GsonUtil.getGson().fromJson(respContent, Map.class);
            String actionStatus = Convert.toStr(respMap.get("ActionStatus"));
            if (!StrUtil.equalsIgnoreCase(actionStatus, TencentConstant.SUCCESS_ACTION_STATUS)) {
                log.error("TencentIMComponent allStaffPush error, respContent:{}", respContent);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            httpClient.close();
        }
    }

    /**
     * TIM 推送服务
     * 单发推送
     */
    public void singleShotPushService(String fromUid, List<String> uids, String identifier, String usersig, OfflinePushInfo offlinePushInfo) throws IOException {
        Long msgRandom = new Random().nextInt() & 0xffffffffL;
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/tpush/batch?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, identifier, usersig, msgRandom);

        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        Map<String, Object> reqParam = Maps.newHashMap();
        reqParam.put("From_Account", fromUid);
        reqParam.put("To_Account", uids);
        reqParam.put("MsgRandom", msgRandom);
        if (Objects.nonNull(offlinePushInfo)) {
            reqParam.put("OfflinePushInfo", offlinePushInfo);
        }
        String json = GsonUtil.getGson().toJson(reqParam);
        log.info("Tim singleShotPushService json:[{}]", json);
        httpPost.setEntity(new StringEntity(json, "utf-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String respContent = EntityUtils.toString(response.getEntity(), "GBK").trim();
            Map<String, Object> respMap = GsonUtil.getGson().fromJson(respContent, Map.class);
            String actionStatus = Convert.toStr(respMap.get("ActionStatus"));
            if (!StrUtil.equalsIgnoreCase(actionStatus, TencentConstant.SUCCESS_ACTION_STATUS)) {
                log.error("TencentIMComponent singleShotPushService error, respContent:{}", respContent);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            httpClient.close();
        }
    }


    /**
     * 踢下线
     */
    public void kick(Long targetUid) throws IOException {
        Long msgRandom = RandomUtil.randomLong(0, 4294967295L);
        String identifier = TencentConfig.administrator;
        String token = this.genUserSig(identifier).getToken();
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/im_open_login_svc/kick?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, identifier, token, msgRandom);
        CloseableHttpClient httpClient = HttpClients.createDefault();

        HttpPost httpPost = new HttpPost(url);

        UserIdDTO dto = UserIdDTO.builder().UserID(String.valueOf(targetUid)).build();

        httpPost.setEntity(new StringEntity(GsonUtil.getGson().toJson(dto), "utf-8"));
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            String respContent = EntityUtils.toString(response.getEntity(), "GBK").trim();
            final Map<String, Object> respMap = GsonUtil.getGson().fromJson(respContent, Map.class);
            final String actionStatus = Convert.toStr(respMap.get("ActionStatus"));
            if (!StrUtil.equalsIgnoreCase(actionStatus, TencentConstant.SUCCESS_ACTION_STATUS)) {
                log.error("TencentIMComponent kick error, respContent:{}", respContent);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            httpClient.close();
        }
    }

    public void cancelNewbieFlag(Long uid) {
        HttpClient client = HttpClient.newHttpClient();
        SubscriberTagValue subscriberTagValue = new SubscriberTagValue();
        subscriberTagValue.setTag(TencentConstant.PortraitConst.TAG_PROFILE_IM_LEVEL);
        subscriberTagValue.setValue("0");

        SubscriberDTO dto = new SubscriberDTO();
        dto.setFrom_Account(String.valueOf(uid));
        dto.setProfileItem(CollUtil.newArrayList(subscriberTagValue));

        int random = ThreadLocalRandom.current().nextInt(0, *********);

        TokenDTO tokenDTO = genUserSig(TencentConfig.administrator, true);
        Gson gson = new Gson();
        String json = gson.toJson(dto);
        log.info("cancelNewbieFlag tim tokenDTO:{}", json);
        String url = TencentConfig.rootDomainUrl +
                StrUtil.format("v4/profile/portrait_set?sdkappid={}&identifier={}&usersig={}&random={}&contenttype=json",
                        TencentConfig.sdkAppId, TencentConfig.administrator, tokenDTO.getToken(), random);
        log.info("cancelNewbieFlag tim url:{}", url);
        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create(url))
                .POST(HttpRequest.BodyPublishers.ofString(json))
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            HttpHeaders headers = response.headers();
            headers.map().forEach((k, v) -> System.out.println(k + ":" + v));
        } catch (Exception e) {
            log.error("cancelNewbieFlag tim error msg:[{}]", e.getMessage(), e);
        }
    }

    private TokenDTO tokenFromCache(String uid) {

        RBucket<String> bucket = redissonClient.getBucket(tokenRedisKey(uid));
        return bucket.isExists() ? JSONUtil.toBean(bucket.get(), TokenDTO.class) : null;
    }

    private String tokenRedisKey(String uid) {
        return TencentRedisKey.im_token.getKey(StrUtil.format("{{}}", uid));
    }

    private void cacheToken(String uid, TokenDTO tokenDTO) {
        redissonClient.getBucket(tokenRedisKey(uid)).set(JSONUtil.toJsonStr(tokenDTO), Duration.ofSeconds(tokenDTO.getExpiresIn()));
    }
}
