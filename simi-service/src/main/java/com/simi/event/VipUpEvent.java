package com.simi.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-23 13:53
 **/
@Getter
@Setter
public class VipUpEvent extends ApplicationEvent {

    private Long userId;
    /**
     * 升级的等级
     */
    private Integer level;

    public VipUpEvent(Object source) {
        super(source);
    }
}
