package com.simi.event.listener;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.alibaba.fastjson2.JSON;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.RoomCopywritingEnum;
import com.simi.common.constant.AppVersionEnum;
import com.simi.common.constant.CustomPublicScreenEventEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.aristocracy.AristocracyConfigDTO;
import com.simi.common.dto.aristocracy.UserCurAristocracyInfo;
import com.simi.common.dto.room.CustomPublicScreenDTO;
import com.simi.common.dto.room.WelcomeContentDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.entity.room.Room;
import com.simi.event.UserInRoomEvent;
import com.simi.message.UserInRoomMessage;
import com.simi.service.AppVersionCheckService;
import com.simi.service.LongLinkService;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.cache.RoomInCache;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import com.simi.util.TranslationCopyUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-06 11:06
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class PushInRoomMsgListener implements ApplicationListener<UserInRoomEvent> {
    private final UserServerService userServerService;
    private final RoomInCache roomInCache;
    private final LongLinkService longLinkService;
    private final UserAristocracyRecordsService service;
    private final AristocracyConfigService aristocracyConfigService;
    private final UserVipService userVipService;
    private static final String A_LABEL_STR = "<a href=\"{0}\" style=\"text-decoration: none;\"><span style=\"color: rgb(23, 235, 173, 1);\">{1}</span></a>";
    private final SystemConfigService systemConfigService;
    private final AppVersionCheckService appVersionCheckService;


    @Override
    public void onApplicationEvent(UserInRoomEvent event) {
        UserInRoomMessage message = (UserInRoomMessage) event.getSource();
        Room room = message.getRoom();
        String roomId = room.getId();
        Long followUid = message.getFollowUid();
        String language = MessageSourceUtil.getLang().name();
        long uid = message.getUid();
        UserBaseInfoDTO userPB = userServerService.getUserBaseInfo(uid);
        if (userPB != null) {
            // vip 隐身，不推送
            if (userVipService.isInvisibleEnter(uid)) {
                log.info("vip 用户设置了隐身设置 userId:{}", uid);
                return;
            }

            //进房系统公告
            if (!Objects.equals(roomInCache.hasRoomInUid(uid, roomId),roomId)){
                int min = 10;
                String minValue = systemConfigService
                        .getSysConfValueById(SystemConfigConstant.SEND_ENTER_ROOM_NOTICE_RATE);
                if (StringUtils.isNotBlank(minValue)) {
                    log.info("发送进房公告频率 {}", minValue);
                    min = Integer.parseInt(minValue);
                }
                roomInCache.setRoomInUidId(uid, roomId, min);
                String msg = "";
                if (aristocracyInRoomMessage(userPB, roomId)) {
                    log.info("aristocracy InRoomMessage .userId:{},roomId:{}", uid, roomId);
                    return;
                }
                WelcomeContentDTO welcomeContentDTO = null;
                String enWelcome = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_8.getKey(), LanguageEnum.en);
                String arWelcome = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_8.getKey(), LanguageEnum.ar);
                String enFollow = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_22.getKey(), LanguageEnum.en);
                String arFollow = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_22.getKey(), LanguageEnum.ar);

                String welcomeRoom = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_8.getKey(), LanguageEnum.getLang(language));
                if (followUid != null && followUid > 0) {
                    UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(followUid);
                    String followed = MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_22.getKey(), LanguageEnum.getLang(language));
                    String home = ClientRouteUtil.toHome(1, userPB.getUid());
                    String followHome = ClientRouteUtil.toHome(1, userBaseInfo.getUid());
                    msg = "<p><a href='"+ home + "' style='text-decoration: none;color:#17EBAD'>"
                            + userPB.getNick() + "</a> " + followed
                            + " <a href='"+ followHome+"' style='text-decoration: none;color:#17EBAD'>"
                            + userBaseInfo.getNick() + "</a> "
                            + welcomeRoom + "</p>";

                    welcomeContentDTO = WelcomeContentDTO.builder()
                            .enEnterRoomDesc(enWelcome)
                            .arEnterRoomDesc(arWelcome)
                            .homeUrl(home)
                            .nick(userPB.getNick())
                            .followHomeUrl(followHome)
                            .followNick(userBaseInfo.getNick())
                            .enFollowedDesc(enFollow)
                            .arFollowedDesc(arFollow)
                            .followFlag(true)
                            .build();
                } else {
                    msg = "<p><span style='color:#17EBAD'>" + userPB.getNick() + "</span>" + MessageSourceUtil.i18nByCode(RoomCopywritingEnum.CP_8.getKey(), LanguageEnum.getLang(language)) + "</p>";
                    welcomeContentDTO = WelcomeContentDTO.builder()
                            .nick(userPB.getNick())
                            .enEnterRoomDesc(enWelcome)
                            .arEnterRoomDesc(arWelcome)
                            .followFlag(false)
                            .build();
                }
                String appVersion = message.getAppVersion();
                if (appVersionCheckService.checkVersionGt(AppVersionEnum.WELCOME_CONTENT,appVersion)) {
                    String newMsg = JSON.toJSONString(welcomeContentDTO);
                    log.info("RoomHighService inRoom appVersion:{}, push new msg:{}",appVersion,newMsg);
                    //新的欢迎公告
                    longLinkService.pushRoomMsg(roomId, userPB,newMsg , PushEvent.in_room_welcome_content_event, PushToType.MESSAGE_TO_ALL);
                }else {
                    log.info("RoomHighService inRoom appVersion:{}, push old msg:{}",appVersion,msg);
                    longLinkService.pushRoomMsg(roomId, userPB, msg, PushEvent.room_screen_system_notice_event, PushToType.MESSAGE_TO_ALL);
                }

            }
        }
    }

    private boolean aristocracyInRoomMessage(UserBaseInfoDTO userPB, String roomId) {
        try {
            long uid = userPB.getUid();
            // 判断用户是否贵族
            Optional<UserCurAristocracyInfo> aristocracyInfoOptional
                    = service.getUserAristocracyRecordsCache(userPB.getUid());
            aristocracyInfoOptional.ifPresent(k -> {
                String backgroundImg = k.getAristocracyPropInfo().getRoomNoticeBg();
                Optional<AristocracyConfigDTO> optional = aristocracyConfigService.getAristocracyConfigById(k.getCurAristocracy());
                optional.ifPresent(item -> {
                    Map<String, String> textMap = new HashMap<>();
                    String iconUrl = item.getIconUrl();
                    textMap.put("ar", item.getArName());
                    textMap.put("en", item.getEnName());
                    String home = ClientRouteUtil.toHome(1, uid);
                    String nickLabel = StrUtil.indexedFormat(A_LABEL_STR, home, userPB.getNick());
                    TranslationCopyDTO translationCopyDTO = TranslationCopyUtil.translationCopy(CopywritingEnum.ARISTOCRACY_IN_ROOM_MESSAGE.getKey(), new String[]{nickLabel});

                    JSONObject dataObject = new JSONObject();
                    Integer publicScreenEvent = CustomPublicScreenEventEnum.ARISTOCRACY_IN_ROOM_MESSAGE.getEvent();
                    dataObject.putOnce("iconUrl", iconUrl);
                    dataObject.putOnce("textMap", textMap);
                    dataObject.putOnce("backgroundUrl", backgroundImg);
                    dataObject.putOnce("uid", uid);
                    CustomPublicScreenDTO<JSONObject> messageDTO = CustomPublicScreenDTO.<JSONObject>builder()
                            .event(publicScreenEvent)
                            .data(dataObject)
                            .textMap(translationCopyDTO)
                            .build();

                    longLinkService.pushCustomerRoomMsg(roomId, messageDTO, PushEvent.custom_public_screen_msg, PushToType.MESSAGE_TO_ALL);
                });
            });
            return aristocracyInfoOptional.isPresent();
        } catch (Exception e) {
            log.error("aristocracy push in room msg error.uid:{},roomId:{},{}", userPB.getUid(), roomId, ExceptionUtil.formatEx(e), e);
            return false;
        }
    }
}
