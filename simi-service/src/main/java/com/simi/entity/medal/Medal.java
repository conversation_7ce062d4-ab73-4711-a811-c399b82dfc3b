package com.simi.entity.medal;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/19 11:16
 **/
@Data
@TableName("medal")
public class Medal implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 唯一标识符
     */
    private Integer id;

    /**
     * 勋章类型
     */
    private Integer medalType;

    /**
     * 勋章名称（英）
     */
    private String medalNameEn;

    /**
     * 勋章名称（阿）
     */
    private String medalNameAr;

    /**
     * 描述（英）
     */
    private String descEn;

    /**
     * 描述（阿）
     */
    private String descAr;

    /**
     * 条件字段
     */
    private String conditionField;

    /**
     * 初级图标
     */
    private String beginnerIcon;

    /**
     * 初级动画
     */
    private String beginnerAnimation;

    /**
     * 中级图标
     */
    private String intermediateIcon;

    /**
     * 中级动画
     */
    private String intermediateAnimation;

    /**
     * 高级图标
     */
    private String advancedIcon;

    /**
     * 高级动画
     */
    private String advancedAnimation;

    /**
     * 排序
     */
    private Integer sortingOrder;

    /**
     * 备注
     */
    private String notes;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人ID
     */
    private Integer adminId;

    /**
     * 修改人
     */
    private String adminName;

    /**
     * 状态: 1-上架; 2-下架
     */
    private Integer enableStatus;
}
