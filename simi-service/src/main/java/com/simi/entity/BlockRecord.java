package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/2/2 11:59
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("block_record")
public class BlockRecord {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户uid
     */
    private Long uid;
    /**
     * 内容（包含设备、ip）
     */
    private String content;
    /**
     * 封禁类型【1：uid；2：设备 3：ip】
     */
    private Integer type;
    /**
     * 原由类型，1后管，2充值退款
     */
    private Integer reasonType;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 操作人ID
     */
    private Integer adminId;
    /**
     * 封禁开始时间
     */
    private Date startTime;
    /**
     * 封禁结束时间
     */
    private Date endTime;
    /**
     * 备注英语
     */
    private String remarkEn;
    /**
     * 备注阿语
     */
    private String remarkAr;
    /**
     * 封禁状态【1、是  2、否】
     */
    private Integer status;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 上级
     */
    private Long bizId;
}
