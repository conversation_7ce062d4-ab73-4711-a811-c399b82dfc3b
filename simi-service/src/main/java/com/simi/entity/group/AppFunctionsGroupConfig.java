package com.simi.entity.group;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("simi_app_functions_group_config")
public class AppFunctionsGroupConfig {
    //功能应用ID
    private Integer functionsId;
    //国家组ID
    private Integer groupId;
    //国家组
    private String groupName;
    //国家编码
    private String code;
    //权重
    private Integer weight;
}
