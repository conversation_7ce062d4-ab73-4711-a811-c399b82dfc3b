package com.simi.entity.following;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("follow_room")
public class FollowRoom {

    @TableId(type = IdType.INPUT)
    private Long id;

    private Long uid;

    private String roomId;
    //收藏状态 1、收藏 2、取消收藏
    private Integer status;

    private Date createTime;
    //取消收藏时间
    private Date cancelTime;
}
