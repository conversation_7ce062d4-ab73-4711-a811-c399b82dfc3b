package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value ="apple_pay_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ApplePayRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    private String version;

    private Integer notificationType;

    private String purchaseToken;

    private String sku;

    private Date createTime;
}
