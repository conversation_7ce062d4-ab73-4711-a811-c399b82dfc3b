package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value ="bullet_chat")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BulletChat {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long uid;

    private String text;

    private Date createTime;

    private Boolean status;
}
