package com.simi.entity.wheel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;
import java.io.Serializable;

/**
 * 幸运转盘参与人数据(LuckyWheelParticipantsInfo)实体类
 *
 * <AUTHOR>
 * @since 2024-10-08 11:17:20
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LuckyWheelParticipantsInfo implements Serializable {
    private static final long serialVersionUID = 200695951378746681L;
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 转盘id
     */
    private Long wheelId;
    /**
     * 参与人id
     */
    private Long uid;
    /**
     * 入场费
     */
    private BigDecimal fee;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date updateTime;
    /**
     * 被淘汰的时间戳
     */
    private Long disuseTimestamp;
}

