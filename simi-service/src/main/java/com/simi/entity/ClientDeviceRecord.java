package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("client_device_record")
public class ClientDeviceRecord {

    @TableId(type = IdType.INPUT)
    private String id;
    private String os;
    private String osVersion;
    private String app;
    private String appVersion;
    private String appVersionCode;
    private String channel;
    private Date firstTime;
    private String bundleId;
    private Boolean isPhysicalDevice;
}
