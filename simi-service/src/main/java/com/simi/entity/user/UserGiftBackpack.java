package com.simi.entity.user;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * 用户礼物包裹
 *
 * <AUTHOR>
 * @date 2024/03/30 17:40
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "user_gift_backpack")
public class UserGiftBackpack implements Serializable {

    @Serial
    @TableField(exist = false)
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 物品id
     */
    private Integer giftId;

    /**
     * 修改版本:乐观锁实现数据更新
     */
    private Integer version;

    /**
     * 包裹物品增加时间
     */
    private Date updateTime;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * amount
     */
    private Integer amount;


}