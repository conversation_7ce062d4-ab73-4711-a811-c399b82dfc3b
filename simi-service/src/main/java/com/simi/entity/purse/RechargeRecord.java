package com.simi.entity.purse;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 
 * @TableName recharge_record
 */
@TableName(value ="recharge_record")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RechargeRecord implements Serializable {
    /**
     * 
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    /**
     * 
     */
    private Long uid;

    /**
     * 充值套餐id
     */
    private String packageId;

    /**
     * 渠道【1：google；2：apple】
     */
    private String channel;

    /**
     * 货币【1：USD】
     */
    private String currency;

    /**
     * 货币数量
     */
    private BigDecimal currencyAmount;

    /**
     * 金币/金票数量
     */
    private Long coinAmount;

    /**
     * 渠道订单号
     */
    private String channelOrderId;

    /**
     * 渠道唯一验证id
     */
    private String channelVerifyId;

    /**
     * 订单状态，1创建，2成功，3退款
     */
    @TableField("`status`")
    private Integer status;

    /**
     * 订单创建时间
     */
    private Date createTime;

    /**
     * 订单充值成功时间
     */
    private Date successTime;

    /**
     * 订单退款时间
     */
    private Date refundTime;

    /**
     * 
     */
    private Date updateTime;

    /**
     * 是否沙盒充值
     */
    private Boolean isSandbox;

    /**
     * 系统
     */
    private String os;

    /**
     * 设备ID
     */
    private String deviceId;

    /**
     * 应用版本
     */
    private String appVersion;

    private BigDecimal dollarAmount;

    /**
     * ip
     */
    private String ip;

    /**
     * 平台货币类型: 1-金币; 2-金票
     */
    private Integer platformCurrencyType;

    /**
     * 订单说明
     */
    @TableField("`explain`")
    private String explain;

    /**
     * 付款用户
     */
    private Long targetUid;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}