package com.simi.entity.purse;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("purse")
public class Purse {
    /**
     * 用户UID
     */
    @TableId(type = IdType.INPUT)
    private Long uid;
    /**
     * 金币余额
     */
    private Long coin;
    /**
     * 钻石余额
     */
    private Integer diamond;

    /**
     * 美金余额
     */
    private Long usd;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
}
