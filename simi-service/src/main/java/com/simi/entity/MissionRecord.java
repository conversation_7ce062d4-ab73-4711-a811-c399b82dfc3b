package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/1/25 14:16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("mission_record")
public class MissionRecord {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户UID
     */
    private Long uid;
    /**
     * 任务ID
     */
    private Integer missionId;
    /**
     * 完成数量
     */
    private Integer completeNum;
    /**
     * 目标数量
     */
    private Integer targetNum;
    /**
     * 任务状态
     */
    private Integer status;
    /**
     * 奖励包ID
     */
    private Integer rewardPackId;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 完成时间
     */
    private Date completeTime;

    /**
     * 领取时间
     */
    private Date claimTime;

    /**
     * 任务类型
     */
    private Integer type;

}
