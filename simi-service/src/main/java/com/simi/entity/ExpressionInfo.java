package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@TableName(value ="expression_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ExpressionInfo {

    @TableId(type = IdType.AUTO)
    private Long id;
    //类型 1、普通 2、随机
    private Integer type;
    //分类  表情
    private Integer classify;
    //名称(英文)
    private String enName;
    //名称(阿拉伯)
    private String arName;
    //静态图片
    private String staticPic;
    //动态图片
    private String dynamicPic;
    //结束静态图片
    private String endPic;
    //排序 -1 客户端隐藏
    private Integer sort;
    //是否删除
    private Integer status;
    //操作人id
    private Integer adminId;
    //标签（英语）
    private String tagEn;
    //标签（阿语）
    private String tagAr;
    //国家组id
    private String groupIds;

    private Integer direction;
    private Date createTime;
    private Date updateTime;
    private Integer aristocracyLevel;
}
