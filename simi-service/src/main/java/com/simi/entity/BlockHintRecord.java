package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@TableName(value = "block_hint_record", autoResultMap = true)
@AllArgsConstructor
@NoArgsConstructor
public class BlockHintRecord {

    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 内容（包含设备、ip）
     */
    private String content;
    /**
     * 封禁类型【1：uid；2：设备 3：ip】
     */
    private Integer type;
    /**
     * 错误码
     */
    private String code;
    /**
     * 创建时间
     */
    private Date createTime;

}
