package com.simi.entity.burypoint;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * APP埋点数据对象 app_bury_point
 * 
 * <AUTHOR>
 * @date 2025-02-24
 */
@TableName(value ="app_bury_point")
public class AppBuryPoint
{
    private static final long serialVersionUID = 1L;

    /** ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 用户ID */
    private Long uid;

    /** 设备ID */
    private String deviceId;

    /** 设备信息 */
    private String deviceInfo;

    /** 手机型号/版本号 */
    private String deviceType;

    /** 国家码 */
    private String countryCode;

    /** 包版本号 */
    private String appVersion;

    /** 包名 */
    private String appPkg;

    /** 埋点业务Key，唯一（1级+2级+3级+4级） */
    private String buryPointKey;

    /** APP请求时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date appRequestTime;

    /** 消息队列处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date mqHandleTime;

    /** DB创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date dbCreateTime;

    /** 业务值1 */
    private String field1;

    /** 业务值2 */
    private String field2;

    /** 业务值3 */
    private String field3;

    /** 业务值4 */
    private String field4;

    /** 业务值5 */
    private String field5;

    /** 业务值6 */
    private String field6;

    /** 业务值7 */
    private String field7;

    /** 业务值8 */
    private String field8;

    /** 业务值9 */
    private String field9;

    /** 业务值10 */
    private String field10;

    /** 埋点位置 4级 */
    private String routeLevelFour;

    /** 埋点位置 3级 */
    private String routeLevelThree;

    /** 埋点位置 2级 */
    private String routeLevelTwo;

    /** 埋点位置 1级 */
    private String routeLevelOne;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setUid(Long uid) 
    {
        this.uid = uid;
    }

    public Long getUid() 
    {
        return uid;
    }

    public void setDeviceId(String deviceId) 
    {
        this.deviceId = deviceId;
    }

    public String getDeviceId() 
    {
        return deviceId;
    }

    public void setDeviceInfo(String deviceInfo) 
    {
        this.deviceInfo = deviceInfo;
    }

    public String getDeviceInfo() 
    {
        return deviceInfo;
    }

    public void setDeviceType(String deviceType) 
    {
        this.deviceType = deviceType;
    }

    public String getDeviceType() 
    {
        return deviceType;
    }

    public void setCountryCode(String countryCode) 
    {
        this.countryCode = countryCode;
    }

    public String getCountryCode() 
    {
        return countryCode;
    }

    public void setAppVersion(String appVersion) 
    {
        this.appVersion = appVersion;
    }

    public String getAppVersion() 
    {
        return appVersion;
    }

    public void setAppPkg(String appPkg) 
    {
        this.appPkg = appPkg;
    }

    public String getAppPkg() 
    {
        return appPkg;
    }

    public void setBuryPointKey(String buryPointKey) 
    {
        this.buryPointKey = buryPointKey;
    }

    public String getBuryPointKey() 
    {
        return buryPointKey;
    }

    public void setAppRequestTime(Date appRequestTime) 
    {
        this.appRequestTime = appRequestTime;
    }

    public Date getAppRequestTime() 
    {
        return appRequestTime;
    }

    public void setMqHandleTime(Date mqHandleTime) 
    {
        this.mqHandleTime = mqHandleTime;
    }

    public Date getMqHandleTime() 
    {
        return mqHandleTime;
    }

    public void setDbCreateTime(Date dbCreateTime) 
    {
        this.dbCreateTime = dbCreateTime;
    }

    public Date getDbCreateTime() 
    {
        return dbCreateTime;
    }

    public void setField1(String field1) 
    {
        this.field1 = field1;
    }

    public String getField1() 
    {
        return field1;
    }

    public void setField2(String field2) 
    {
        this.field2 = field2;
    }

    public String getField2() 
    {
        return field2;
    }

    public void setField3(String field3) 
    {
        this.field3 = field3;
    }

    public String getField3() 
    {
        return field3;
    }

    public void setField4(String field4) 
    {
        this.field4 = field4;
    }

    public String getField4() 
    {
        return field4;
    }

    public void setField5(String field5) 
    {
        this.field5 = field5;
    }

    public String getField5() 
    {
        return field5;
    }

    public void setField6(String field6) 
    {
        this.field6 = field6;
    }

    public String getField6() 
    {
        return field6;
    }

    public void setField7(String field7) 
    {
        this.field7 = field7;
    }

    public String getField7() 
    {
        return field7;
    }

    public void setField8(String field8) 
    {
        this.field8 = field8;
    }

    public String getField8() 
    {
        return field8;
    }

    public void setField9(String field9) 
    {
        this.field9 = field9;
    }

    public String getField9() 
    {
        return field9;
    }

    public void setField10(String field10) 
    {
        this.field10 = field10;
    }

    public String getField10() 
    {
        return field10;
    }

    public void setRouteLevelFour(String routeLevelFour) 
    {
        this.routeLevelFour = routeLevelFour;
    }

    public String getRouteLevelFour() 
    {
        return routeLevelFour;
    }

    public void setRouteLevelThree(String routeLevelThree) 
    {
        this.routeLevelThree = routeLevelThree;
    }

    public String getRouteLevelThree() 
    {
        return routeLevelThree;
    }

    public void setRouteLevelTwo(String routeLevelTwo) 
    {
        this.routeLevelTwo = routeLevelTwo;
    }

    public String getRouteLevelTwo() 
    {
        return routeLevelTwo;
    }

    public void setRouteLevelOne(String routeLevelOne) 
    {
        this.routeLevelOne = routeLevelOne;
    }

    public String getRouteLevelOne() 
    {
        return routeLevelOne;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("uid", getUid())
            .append("deviceId", getDeviceId())
            .append("deviceInfo", getDeviceInfo())
            .append("deviceType", getDeviceType())
            .append("countryCode", getCountryCode())
            .append("appVersion", getAppVersion())
            .append("appPkg", getAppPkg())
            .append("buryPointKey", getBuryPointKey())
            .append("appRequestTime", getAppRequestTime())
            .append("mqHandleTime", getMqHandleTime())
            .append("dbCreateTime", getDbCreateTime())
            .append("field1", getField1())
            .append("field2", getField2())
            .append("field3", getField3())
            .append("field4", getField4())
            .append("field5", getField5())
            .append("field6", getField6())
            .append("field7", getField7())
            .append("field8", getField8())
            .append("field9", getField9())
            .append("field10", getField10())
            .append("routeLevelFour", getRouteLevelFour())
            .append("routeLevelThree", getRouteLevelThree())
            .append("routeLevelTwo", getRouteLevelTwo())
            .append("routeLevelOne", getRouteLevelOne())
            .toString();
    }
}
