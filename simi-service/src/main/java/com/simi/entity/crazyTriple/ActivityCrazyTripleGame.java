package com.simi.entity.crazyTriple;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 疯狂triple场次表
 * @TableName activity_crazy_triple_game
 */
@TableName(value ="activity_crazy_triple_game")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityCrazyTripleGame implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 状态：1.进行中; 2.结束待开奖; 3.结束
     */
    private Integer status;

    /**
     * 游戏持续时长秒
     */
    private Integer duration;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 结算失败重试次数
     */
    private Integer retryNum;

    /**
     * 扩展信息
     */
    private String extra;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}