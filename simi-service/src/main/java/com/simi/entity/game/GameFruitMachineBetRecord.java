package com.simi.entity.game;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("game_fruit_machine_bet_record")
public class GameFruitMachineBetRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    private Long uid;

    private String bet;

    private Long win;

    private Long amount;

    private String roundId;

    private Long round;

    private Long resultId;

    private String result;

    private Integer isDrawn;

    private Date createTime;

    private String roomId;

    private Integer gameType;

}
