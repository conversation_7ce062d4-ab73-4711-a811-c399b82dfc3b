package com.simi.entity.vip;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: lrq
 * @create: 2024-10-12 11:38
 **/
@Data
@TableName("backpack_gift_records")
public class BackpackGiftRecord {
    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 赠送者ID
     */
    private Long senderId;

    /**
     * 接受者ID
     */
    private Long receiverId;

    /**
     * 物品ID
     */
    private Integer propId;

    /**
     * 背包ID
     */
    private Long backpackId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;
}
