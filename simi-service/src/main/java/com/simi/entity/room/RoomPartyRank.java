package com.simi.entity.room;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/01 20:11
 * 房间party 榜单表
 **/
@Data
@Builder
@TableName(value ="room_party_rank")
public class RoomPartyRank implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @TableId(type = IdType.AUTO)
    /**
     * 主键
     */
    private Long id;

    /**
     * uid
     */
    private Long uid;

    /**
     * party_id
     */
    private Long partyId;

    /**
     * 类型:1-送礼: 2-收礼
     */
    private Integer type;

    /**
     * 分数
     */
    private Long score;

    /**
     * 礼物数量
     */
    private Long giftCount;

    /**
     * 创建时间
     */
    private Date createTime;
}
