package com.simi.entity.room;

import com.simi.common.constant.room.RoomBookSongType;
import com.simi.common.constant.room.RoomMicUpType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

//房间配置更新
@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoomConfigUpdateEventModel {
    //点歌限制
    private RoomBookSongType bookSongType;
    //上麦限制
    private RoomMicUpType micUpType;

    private String quickWelcomeStr;

}
