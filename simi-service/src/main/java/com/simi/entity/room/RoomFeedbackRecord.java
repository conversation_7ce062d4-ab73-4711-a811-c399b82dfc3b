package com.simi.entity.room;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 房间反馈记录
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "room_feedback_record", autoResultMap = true)
public class RoomFeedbackRecord {

    @TableId(type= IdType.AUTO)
    private Long id;

    /**
     * 房间ID
     */
    private String roomId;

    /**
     * 反馈类别
     */
    @TableField(typeHandler = JacksonTypeHandler.class, value = "`categories`")
    private List<Integer> categories;

    /**
     * 描述
     */
    private String description;

    /**
     * 图片列表
     */
    @TableField(typeHandler = JacksonTypeHandler.class, value = "`photos`")
    private List<String> photos;

    /**
     * 反馈时间
     */
    private Date createTime;

    /**
     * 反馈用户
     */
    private Long uid;

    private String showId;

    private Integer status;

    private String remark;
}
