package com.simi.entity.room;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/07/29 15:56
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value ="room_party_tag")
public class RoomPartyTag implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 标签阿语名
     */
    private String arName;

    /**
     * 标签英文名
     */
    private String enName;

    /**
     * 图片
     */
    private String tagPic;

    /**
     * 排序
     */
    private Integer seqNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    @TableLogic
    private Integer deleted;

}
