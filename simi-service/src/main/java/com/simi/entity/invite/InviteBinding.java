package com.simi.entity.invite;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/05/08 19:37
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("invite_binding")
public class InviteBinding implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 被邀请用户uid
     */
    @TableId
    private Long invitedUid;

    /**
     * 邀请用户uid
     */
    private Long inviteUid;

    /**
     * 状态: 1-未绑定; 2-已绑定; 3-已解绑; 4-手动绑定
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 活跃时间
     */
    private Date activeTime;
}
