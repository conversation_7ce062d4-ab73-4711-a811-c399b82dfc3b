package com.simi.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-15 16:33
 **/
@TableName(value ="rank_score_detail")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RankScoreDetail {
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 榜单key
     */
    private String rankKey;
    private Long userId;
    /**
     * 得分
     */
    private Integer score;
    private LocalDateTime createTime;
    private LocalDateTime modifyTime;
}
