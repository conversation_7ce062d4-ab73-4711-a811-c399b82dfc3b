package com.simi.entity.agent;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("agent_invite_log")
public class AgentInviteLog {

    private Long id;
    private Long inviterUid;  // 邀请人UID
    private Long inviteeUid;  // 被邀请人UID
    private Integer inviteType;  // 邀请身份类型：1-代理，2-主播
    private Integer inviteChannel;//邀请渠道：1官方邀请、2上级代理邀请、3 自己申请
    private Integer inviteStatus;//0-已发送，1-已接受，2-已拒绝，3-超时失效',
    private LocalDateTime confirmTime;  // 被邀请人确认时间（接受或拒绝）
    private LocalDateTime createTime;

}
