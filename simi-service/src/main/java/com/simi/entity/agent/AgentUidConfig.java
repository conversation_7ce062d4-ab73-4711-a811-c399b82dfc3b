package com.simi.entity.agent;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 代理UID配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("agent_uid_config")
public class AgentUidConfig {

    /**
     * 代理UID（主键）
     */
    @TableId
    private Long uid;

    /**
     * 关联的配置数据ID（多个ID用逗号分隔）
     */
    private String configIds;

    /**
     * 记录创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
