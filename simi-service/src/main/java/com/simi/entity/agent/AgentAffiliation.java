package com.simi.entity.agent;


import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName("agent_affiliation")
public class AgentAffiliation {
    private Long id;
    private Long uid; // 当前用户UID（主播或代理）
    private Integer roleType; // 身份类型：1-代理，2-主播
    private Long parentUid; // 直属上级UID（邀请者/绑定者）
    private Integer layer; // 层级默认1
    private Integer level; // 在代理链条中的层级默认1，从1级代理开始向下递增
    private LocalDateTime createTime; // 记录创建时间
}
