package com.simi.entity.agent;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理分佣配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("agent_commission_config")
public class AgentCommissionConfig {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 代理UID
     */
    private Long agentUid;

    /**
     * 最小收益金额（含）
     */
    private BigDecimal minAmount;

    /**
     * 最大收益金额（含）
     */
    private BigDecimal maxAmount;

    /**
     * 分佣比例（如0.20表示20%）
     */
    private BigDecimal commissionRate;

    /**
     * 记录创建时间
     */
    private Date createTime;
}
