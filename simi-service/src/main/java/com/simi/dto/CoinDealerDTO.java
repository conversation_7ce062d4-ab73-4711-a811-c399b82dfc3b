package com.simi.dto;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.constant.CoinDealerStatus;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Schema(description = "币商信息")
public class CoinDealerDTO {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    @Schema(description = "uid")
    private Long uid;
    @EnumValue
    private CoinDealerStatus status;
    @Schema(description = "币商等级")
    private Integer level;
    @Schema(description = "上级币商")
    private Long superiorsUid;
    @Schema(description = "金票")
    private Long goldenTicket;
    private String remark;
    @Schema(description = "用户信息")
    private UserBaseInfoDTO userBaseInfoDTO;

    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
