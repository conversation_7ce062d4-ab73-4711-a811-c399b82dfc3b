package com.simi.dto;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-16 19:05
 **/
@Data
public class RankScoreHistoryDTO {
    private Integer id; // 主键

    private String rankKey; // 排行榜Key

    private String rankInfo; // 数据历史记录

    private LocalDateTime startTime; // 开始时间

    private LocalDateTime endTime; // 结束时间

    private LocalDateTime createTime; // 创建时间

    private LocalDateTime modifyTime; // 修改时间
}
