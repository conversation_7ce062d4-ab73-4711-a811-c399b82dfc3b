package com.simi.dto.game;

import com.simi.common.constant.GamePhase;
import com.simi.common.dto.revenue.PurseDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "游戏首页数据")
public class GameHomeDTO {

    @Schema(description = "用户钱包")
    private PurseDTO purse;
    @Schema(description = "当前阶段枚举类型")
    private Integer currentPhase;
    @Schema(description = "倒计时")
    private Integer countdown;
    @Schema(description = "下注总额")
    private List<BetAmountDTO> betAmounts;
    @Schema(description = "是否自动开始下一回合")
    private Boolean autoNextRound;
    @Schema(description = "当前回合数")
    private Long currentRound;
}
