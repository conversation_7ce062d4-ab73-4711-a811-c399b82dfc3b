package com.simi.dto.event;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendGiftEventDTO {

    @SerializedName("roomid")
    private String roomId;
    @SerializedName("send_type")
    private Integer sendType;
    @SerializedName("touid")
    private String toUid;
    @SerializedName("giftid")
    private Integer giftId;
    @SerializedName("value")
    private Integer value;
    @SerializedName("gift_num")
    private Integer giftNum;
    @SerializedName("gift_type")
    private Integer giftType;
}
