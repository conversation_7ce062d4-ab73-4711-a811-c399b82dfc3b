package com.simi.dto.event;

import com.google.gson.annotations.SerializedName;
import com.simi.common.dto.DeviceDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class KickOutEventDTO extends DeviceDTO {

    @SerializedName("roomid")
    private String roomId;
    @SerializedName("touid")
    private Long toUid;
}
