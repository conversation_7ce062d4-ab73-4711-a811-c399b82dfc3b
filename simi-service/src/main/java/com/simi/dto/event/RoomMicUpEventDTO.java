package com.simi.dto.event;

import com.google.gson.annotations.SerializedName;
import com.simi.common.dto.DeviceDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoomMicUpEventDTO extends DeviceDTO {

    @SerializedName("roomid")
    private String roomId;
    @SerializedName("num")
    private Integer num;
    @SerializedName("uid")
    private Long uid;
    @SerializedName("seat_type")
    private Boolean seatType;
    @SerializedName("fromuid")
    private Long fromUid;
}

