package com.simi.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMax;
import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 代理分佣配置请求DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "代理分佣配置请求")
public class AgentCommissionConfigReq {

    @Schema(description = "配置ID（更新时需要）")
    private Long id;

    @Schema(description = "代理UID", required = true)
    @NotNull(message = "代理UID不能为空")
    private Long agentUid;

    @Schema(description = "最小收益金额（含）", required = true)
    @NotNull(message = "最小收益金额不能为空")
    @DecimalMin(value = "0", message = "最小收益金额不能小于0")
    private BigDecimal minAmount;

    @Schema(description = "最大收益金额（含）")
    @DecimalMin(value = "0", message = "最大收益金额不能小于0")
    private BigDecimal maxAmount;

    @Schema(description = "分佣比例（如0.20表示20%）", required = true)
    @NotNull(message = "分佣比例不能为空")
    @DecimalMin(value = "0.01", message = "分佣比例不能小于0.01")
    @DecimalMax(value = "1.00", message = "分佣比例不能大于1.00")
    private BigDecimal commissionRate;
}
