package com.simi.dto.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 代理分佣配置DTO
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "代理分佣配置")
public class AgentCommissionConfigDTO {

    @Schema(description = "配置ID")
    private Long id;

    @Schema(description = "代理UID")
    private Long agentUid;

    @Schema(description = "最小收益金额（含）")
    private BigDecimal minAmount;

    @Schema(description = "最大收益金额（含）")
    private BigDecimal maxAmount;

    @Schema(description = "分佣比例（如0.20表示20%）")
    private BigDecimal commissionRate;

    @Schema(description = "分佣比例百分比显示（如20%）")
    private String commissionRatePercent;

    @Schema(description = "记录创建时间")
    private Date createTime;

    /**
     * 获取分佣比例的百分比显示
     */
    public String getCommissionRatePercent() {
        if (commissionRate == null) {
            return "0%";
        }
        return commissionRate.multiply(new BigDecimal("100")).setScale(2, BigDecimal.ROUND_HALF_UP) + "%";
    }
}
