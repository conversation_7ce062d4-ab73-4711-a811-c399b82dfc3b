package com.simi.dto.push;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.Map;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-05 14:44
 **/
@Getter
@Setter
public class VipMsgDTO {

    /**
     * 标题映射
     */
    @JsonProperty("titleMap")
    private Map<String, String> titleMap;

    /**
     * 文本映射
     */
    @JsonProperty("textMap")
    private Map<String, String> textMap;

    /**
     * 消息类型
     */
    @JsonProperty("type")
    private int type;

    /**
     * 链接地址
     */
    @JsonProperty("linkUrl")
    private String linkUrl;

    /**
     * vip信息
     */
    @JsonProperty("vipInfo")
    private VipInfo vipInfo;

    /**
     * 发送用户编号
     */
    @JsonProperty("sendUserNo")
    private Long sendUserNo;

    /**
     * 备注
     */
    @JsonProperty("remarkMap")
    private Map<String, String> remarkMap;

    /**
     * 装扮ICON
     */
    @JsonProperty("propIcon")
    private String propIcon;

    @Getter
    @Setter
    public static class VipInfo {

        /**
         * 贵族ID
         */
        @JsonProperty("vipLevel")
        private int vipLevel;

        /**
         * 图标链接
         */
        @JsonProperty("iconUrl")
        private String iconUrl;

        /**
         * 天数
         */
        @JsonProperty("days")
        private Integer days;


        /**
         * 文本映射
         */
        @JsonProperty("nameMap")
        private Map<String, String> nameMap;

    }
}
