package com.simi.dto.reward;

import com.simi.common.constant.CodeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RewardResult {
    /**
     * 状态码
     */
    private int code;

    /**
     * 错误提示
     */
    private String msg;

    /**
     * 数据
     */
    private Object data = "";

    public RewardResult(int code, String msg) {
        this.code = code;
        this.msg = msg;
    }


    public static RewardResult success() {
        return new RewardResult(CodeEnum.SUCCESS_ZERO.getNumber(), CodeEnum.SUCCESS_ZERO.name());
    }


    public static RewardResult fail(int code, String msg) {
        return new RewardResult(code, msg);
    }

    public static RewardResult fail() {
        return new RewardResult(1, "");
    }


    public boolean isSuc() {
        return CodeEnum.SUCCESS_ZERO.getNumber() == this.code;
    }
}
