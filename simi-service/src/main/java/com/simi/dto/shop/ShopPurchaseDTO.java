package com.simi.dto.shop;

import com.simi.entity.PropInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ShopPurchaseDTO {
    /**
     * 道具id
     */
    private Long id;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 货币类型
     */
    private Integer currencyType;

    /**
     * 价格
     */
    private Integer propDayPrice;

    /**
     * 赠送用户id
     */
    private Long targetUserId;

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 道具信息
     */
    private PropInfo propInfo;

    /**
     * 天数
     */
    private Integer day;
}
