package com.simi.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CallbackCommandDTO {


    @JsonProperty("CallbackCommand")
    private String callbackCommand;

    @JsonProperty("EventTime")
    private Long eventTime;

    @JsonProperty("FromAccount")
    private String fromAccount;

    @JsonProperty("To_Account")
    private String toAccount;

    @JsonProperty("MsgSeq")
    private int msgSeq;

    @JsonProperty("MsgRandom")
    private int msgRandom;

    @JsonProperty("MsgTime")
    private int msgTime;

    @JsonProperty("MsgKey")
    private String msgKey;

    @JsonProperty("OnlineOnlyFlag")
    private int onlineOnlyFlag;

    @JsonProperty("MsgBody")
    private List<TimMsgBody> msgBodyList;

    @JsonProperty("CloudCustomData")
    private String cloudCustomData;

    @JsonProperty("Info")
    private CallbackInfo info;
}
