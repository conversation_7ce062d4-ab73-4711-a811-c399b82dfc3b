package com.simi.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "版本更新")
public class AppVersionGetResp {

    @Schema(description = "是否需要更新")
    private Boolean needUpdate;
    @Schema(description = "更新类型 0、强制更新 1、建议更新")
    private Integer updateType;
    @Schema(description = "title")
    private String title;
    @Schema(description = "版本更新说明")
    private String explain;
    @Schema(description = "跳转地址")
    private String skipUrl;
}
