//package com.simi.mapper.search;
//
//import cn.hutool.core.util.StrUtil;
//import com.simi.common.annotation.meilisearch.AdminMSIndex;
//import com.simi.common.annotation.meilisearch.MSFiled;
//import com.simi.common.dto.user.search.SearchAdminUserInfoDTO;
//import com.simi.manager.MeiliSearchRepository;
//import com.meilisearch.sdk.Index;
//import com.meilisearch.sdk.model.Faceting;
//import com.meilisearch.sdk.model.Pagination;
//import com.meilisearch.sdk.model.Results;
//import com.meilisearch.sdk.model.Settings;
//import org.springframework.stereotype.Repository;
//
//import java.lang.reflect.Field;
//import java.lang.reflect.ParameterizedType;
//import java.util.ArrayList;
//import java.util.List;
//
//
//@Repository
//public class MeiliSearchAdminUserInfoMapper extends MeiliSearchRepository<SearchAdminUserInfoDTO> {
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        initAdminIndex();
//    }
//
//    /**
//     * 初始化索引信息
//     *
//     * @throws Exception
//     */
//    private void initAdminIndex() {
//        Class<? extends MeiliSearchRepository> clazz = getClass();
//        tClass = (Class<SearchAdminUserInfoDTO>) ((ParameterizedType) clazz.getGenericSuperclass()).getActualTypeArguments()[0];
//        AdminMSIndex annoIndex = tClass.getAnnotation(AdminMSIndex.class);
//        String uid = annoIndex.uid();
//        String primaryKey = annoIndex.primaryKey();
//        if (StrUtil.isEmpty(uid)) {
//            uid = tClass.getSimpleName().toLowerCase();
//        }
//        if (StrUtil.isEmpty(primaryKey)) {
//            primaryKey = "id";
//        }
//        int maxTotalHit = annoIndex.maxTotalHits();
//        int maxValuesPerFacet = annoIndex.maxValuesPerFacet();
//
//        List<String> filterKey = new ArrayList<>();
//        List<String> sortKey = new ArrayList<>();
//        List<String> noDisPlay = new ArrayList<>();
//        List<String> searchableKeys = new ArrayList<>();
//        //获取类所有属性
//        for (Field field : tClass.getDeclaredFields()) {
//            //判断是否存在这个注解
//            if (field.isAnnotationPresent(MSFiled.class)) {
//                MSFiled annotation = field.getAnnotation(MSFiled.class);
//                if (annotation.openFilter()) {
//                    filterKey.add(annotation.key());
//                }
//
//                if (annotation.openSort()) {
//                    sortKey.add(annotation.key());
//                }
//                if (annotation.noDisplayed()) {
//                    noDisPlay.add(annotation.key());
//                }
//                if (annotation.searchable()) {
//                    searchableKeys.add(annotation.key());
//                }
//            }
//        }
//        Results<Index> indexes = client.getIndexes();
//        Index[] results = indexes.getResults();
//        boolean isHaveIndex = false;
//        for (Index result : results) {
//            if (uid.equals(result.getUid())) {
//                isHaveIndex = true;
//                break;
//            }
//        }
//
//        if (isHaveIndex) {
//            client.updateIndex(uid, primaryKey);
//        } else {
//            client.createIndex(uid, primaryKey);
//        }
//        this.index = client.getIndex(uid);
//
//        Faceting faceting = new Faceting();
//        faceting.setMaxValuesPerFacet(maxValuesPerFacet);
//        Pagination pagination = new Pagination();
//        pagination.setMaxTotalHits(maxTotalHit);
//
//        Settings settings = new Settings();
//        settings.setDisplayedAttributes(!noDisPlay.isEmpty() ? noDisPlay.toArray(new String[0]) : new String[]{"*"});
//        settings.setFilterableAttributes(filterKey.toArray(new String[0]));
//        settings.setSortableAttributes(sortKey.toArray(new String[0]));
//        settings.setSearchableAttributes(searchableKeys.toArray(new String[0]));
//        settings.setPagination(pagination);
//        settings.setFaceting(faceting);
//
//        index.updateSettings(settings);
//    }
//}
