package com.simi.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

@Mapper
public interface PurserMapperExpand {

    /**
     * 钻石兑换金币
     *
     * @param uid
     * @param diamond
     * @param coin
     * @return
     */
    @Update("update purse set diamond=diamond - #{diamond}, coin=coin + #{coin}, update_time=now() where uid=#{uid} and diamond >= #{diamond} and role = #{role}")
    int diamond2Coin(@Param("uid") Long uid, @Param("diamond") Long diamond, @Param("coin") Long coin, @Param("role") Integer role);

    /**
     * 扣除金币
     * @param uid
     * @param coin
     * @return
     */
    @Update("update purse set coin=coin -#{coin}, update_time=now() where uid=#{uid} and coin >=#{coin} and role = #{role}")
    int deductCoin(@Param("uid") Long uid, @Param("coin") Long coin, @Param("role") Integer role);

    /**
     * 扣除金币(允许负数)
     * @param uid
     * @param coin
     * @return
     */
    @Update("update purse set coin=coin -#{coin}, update_time=now() where uid=#{uid} and role = #{role}")
    int deductCoinAllowNegative(@Param("uid") Long uid, @Param("coin") Long coin, @Param("role") Integer role);

    /**
     * 增加金币
     * @param uid
     * @param coin
     * @return
     */
    @Update("update purse set coin=coin + #{coin}, update_time=now() where uid = #{uid} and role = #{role}")
    int addCoin(@Param("uid") Long uid, @Param("coin") Long coin,@Param("role") Integer role);

    /**
     * 扣除钻石
     * @param uid
     * @param diamond
     * @return
     */
    @Update("update purse set diamond=diamond -#{diamond}, update_time=now() where uid=#{uid} and diamond >=#{diamond} and role = #{role}")
    int deductDiamond(@Param("uid") Long uid, @Param("diamond") Long diamond,@Param("role") Integer role);

    /**
     * 增加钻石
     * @param uid
     * @param diamond
     * @return
     */
    @Update("update purse set diamond=diamond + #{diamond}, update_time=now() where uid = #{uid} and role = #{role}")
    int addDiamond(@Param("uid") Long uid, @Param("diamond") Long diamond, @Param("role") Integer role);

    /**
     * 扣除美元
     * @param uid
     * @param usd
     * @return
     */
    @Update("update purse set usd=usd -#{usd}, update_time=now() where uid=#{uid} and usd >=#{usd} and role = #{role}")
    int deductUsd(@Param("uid") Long uid, @Param("usd") Long usd, @Param("role") Integer role);

    /**
     * 增加美元
     * @param uid
     * @param usd
     * @return
     */
    @Update("update purse set usd=usd + #{usd}, update_time=now() where uid = #{uid} and role = #{role}")
    int addUsd(@Param("uid") Long uid, @Param("usd") Long usd, @Param("role") Integer role);

}
