package com.simi.mapper.crazyTriple;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.dto.crazyTriple.CrazyTripleJoinInfoDTO;
import com.simi.entity.crazyTriple.ActivityCrazyTripleJoinRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【activity_crazy_triple_join_record(疯狂triple参与记录表)】的数据库操作Mapper
 * @createDate 2024-05-28 10:55:29
 * @Entity com.tsea.ksing.activity.core.entity.crazyTriple.ActivityCrazyTripleJoinRecord
 */
public interface ActivityCrazyTripleJoinRecordMapper extends BaseMapper<ActivityCrazyTripleJoinRecord> {
    @Select("<script>" +
            " select product_type productType,sum(currency_amount) joinAmount," +
            " COUNT(*) AS num " +
            " from activity_crazy_triple_join_record where 1 = 1 " +
            " and game_id = #{gameId} " +
            " and user_id = #{userId} " +
            " and status = 2 " +
            " GROUP BY product_type" +
            "</script>")
    List<CrazyTripleJoinInfoDTO> getJoinInfoList(@Param("gameId") long gameId,
                                                 @Param("userId") long userId);

    @Select("<script>" +
            " select product_type productType,sum(currency_amount) joinAmount" +
            " from activity_crazy_triple_join_record where 1 = 1 " +
            " and game_id = #{gameId} " +
            " and status = 2 " +
            " GROUP BY product_type" +
            "</script>")
    List<CrazyTripleJoinInfoDTO> getAllUserJoinInfoList(@Param("gameId") long gameId);

}




