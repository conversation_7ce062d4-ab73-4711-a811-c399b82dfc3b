package com.simi.mapper.rewardpack;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.common.dto.pures.RewardSendUseDTO;
import com.simi.entity.rewardpack.RewardPackPrizeRecord;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
* <AUTHOR>
* @description 针对表【reward_pack_prize_record(奖励包奖品发放记录)】的数据库操作Mapper
* @createDate 2024-01-11 16:49:18
* @Entity com.tsea.ksing.admin.entity.rewardpack.RewardPackPrizeRecord
*/
public interface RewardPackPrizeRecordMapper extends BaseMapper<RewardPackPrizeRecord> {

    /**
     * 查询优质歌房每天发放的奖励（根据发放原始记录giveRecordId）
     * @param prizeType
     * @param giveRecordIdList
     * @return
     */
    @Select("<script>" +
            " select user_id as userId,prize_id as goodsId,sum(amount) as sendAmount from reward_pack_prize_record" +
            "  where 1 =1 " +
            " and source_type = 3 " +
            " and state = 2 " +
            " and prize_type = #{prizeType} " +
            " and give_record_id in  " +
            "  <foreach collection='giveRecordIdList' item='item' separator=',' open='(' close=')'>" +
            "#{item}" +
            "  </foreach>" +
            " GROUP BY user_id,prize_id" +
            "</script>")
    List<RewardSendUseDTO> getRewardSendList(@Param("prizeType") int prizeType,
                                             @Param("giveRecordIdList") List<Long> giveRecordIdList);

}




