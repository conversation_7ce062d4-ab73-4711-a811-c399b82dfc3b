package com.simi.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.entity.agent.AgentUidConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 代理UID配置Mapper接口
 */
@Mapper
public interface AgentUidConfigMapper extends BaseMapper<AgentUidConfig> {

    /**
     * 根据配置ID查询关联的代理UID列表
     * 
     * @param configId 配置ID
     * @return 代理UID列表
     */
    @Select("SELECT uid FROM agent_uid_config " +
            "WHERE FIND_IN_SET(#{configId}, config_ids) > 0")
    List<Long> findUidsByConfigId(@Param("configId") Long configId);

    /**
     * 批量更新代理的配置ID
     * 
     * @param uid 代理UID
     * @param configIds 配置ID字符串
     * @return 更新行数
     */
    @Update("UPDATE agent_uid_config SET config_ids = #{configIds}, update_time = NOW() " +
            "WHERE uid = #{uid}")
    int updateConfigIds(@Param("uid") Long uid, @Param("configIds") String configIds);

    /**
     * 查询包含指定配置ID的所有记录
     * 
     * @param configId 配置ID
     * @return 配置记录列表
     */
    @Select("SELECT * FROM agent_uid_config " +
            "WHERE FIND_IN_SET(#{configId}, config_ids) > 0")
    List<AgentUidConfig> findByConfigId(@Param("configId") Long configId);
}
