package com.simi.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.entity.agent.AgentCommissionConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代理分佣配置Mapper接口
 */
@Mapper
public interface AgentCommissionConfigMapper extends BaseMapper<AgentCommissionConfig> {


}
