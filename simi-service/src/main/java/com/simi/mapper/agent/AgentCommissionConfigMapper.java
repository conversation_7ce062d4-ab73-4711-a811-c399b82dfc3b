package com.simi.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.entity.agent.AgentCommissionConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代理分佣配置Mapper接口
 */
@Mapper
public interface AgentCommissionConfigMapper extends BaseMapper<AgentCommissionConfig> {

    /**
     * 查询指定代理和金额范围内的最匹配配置
     *
     * @param agentUid 代理UID
     * @param amount 金额
     * @return 最匹配的配置
     */
    @Select("SELECT * FROM agent_commission_config " +
            "WHERE agent_uid = #{agentUid} " +
            "AND (min_amount IS NULL OR #{amount} >= min_amount) " +
            "AND (max_amount IS NULL OR #{amount} <= max_amount) " +
            "ORDER BY min_amount ASC " +
            "LIMIT 1")
    AgentCommissionConfig findMatchingConfig(@Param("agentUid") Long agentUid, @Param("amount") BigDecimal amount);

    /**
     * 查询指定代理的最高佣金比例配置
     *
     * @param agentUid 代理UID
     * @return 最高佣金比例的配置
     */
    @Select("SELECT * FROM agent_commission_config " +
            "WHERE agent_uid = #{agentUid} " +
            "AND commission_rate IS NOT NULL " +
            "ORDER BY commission_rate DESC " +
            "LIMIT 1")
    AgentCommissionConfig findHighestRateConfig(@Param("agentUid") Long agentUid);

    /**
     * 查询指定代理的所有配置（按最小金额排序）
     *
     * @param agentUid 代理UID
     * @return 配置列表
     */
    @Select("SELECT * FROM agent_commission_config " +
            "WHERE agent_uid = #{agentUid} " +
            "ORDER BY min_amount ASC")
    List<AgentCommissionConfig> findAgentConfigs(@Param("agentUid") Long agentUid);

}
