package com.simi.mapper.agent;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.entity.agent.AgentCommissionConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.math.BigDecimal;
import java.util.List;

/**
 * 代理分佣配置Mapper接口
 */
@Mapper
public interface AgentCommissionConfigMapper extends BaseMapper<AgentCommissionConfig> {

    /**
     * 根据代理UID查询分佣配置
     * @param agentUid 代理UID
     * @return 分佣配置列表
     */
    @Select("SELECT * FROM agent_commission_config WHERE agent_uid = #{agentUid} ORDER BY min_amount ASC")
    List<AgentCommissionConfig> selectByAgentUid(@Param("agentUid") Long agentUid);

    /**
     * 根据代理UID和收益金额查询匹配的分佣配置
     * @param agentUid 代理UID
     * @param amount 收益金额
     * @return 匹配的分佣配置
     */
    @Select("SELECT * FROM agent_commission_config " +
            "WHERE agent_uid = #{agentUid} " +
            "AND #{amount} >= min_amount " +
            "AND (max_amount IS NULL OR #{amount} <= max_amount) " +
            "ORDER BY min_amount DESC " +
            "LIMIT 1")
    AgentCommissionConfig selectByAgentUidAndAmount(@Param("agentUid") Long agentUid, 
                                                   @Param("amount") BigDecimal amount);

    /**
     * 检查金额区间是否重叠
     * @param agentUid 代理UID
     * @param minAmount 最小金额
     * @param maxAmount 最大金额
     * @param excludeId 排除的配置ID（用于更新时排除自己）
     * @return 重叠的配置数量
     */
    @Select("<script>" +
            "SELECT COUNT(*) FROM agent_commission_config " +
            "WHERE agent_uid = #{agentUid} " +
            "<if test='excludeId != null'>" +
            "AND id != #{excludeId} " +
            "</if>" +
            "AND (" +
            "  (#{minAmount} >= min_amount AND (max_amount IS NULL OR #{minAmount} <= max_amount)) " +
            "  OR " +
            "  (#{maxAmount} IS NOT NULL AND #{maxAmount} >= min_amount AND (max_amount IS NULL OR #{maxAmount} <= max_amount)) " +
            "  OR " +
            "  (#{minAmount} <= min_amount AND (#{maxAmount} IS NULL OR #{maxAmount} >= max_amount)) " +
            ")" +
            "</script>")
    int countOverlappingRanges(@Param("agentUid") Long agentUid,
                              @Param("minAmount") BigDecimal minAmount,
                              @Param("maxAmount") BigDecimal maxAmount,
                              @Param("excludeId") Long excludeId);
}
