package com.simi.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.simi.entity.MapInvitation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Date;
import java.util.List;

@Mapper
public interface MapInvitationMapper extends BaseMapper<MapInvitation> {


    @Select({"<script>" ,
            "SELECT * from map_invitation " ,
            "<where>" ,
            "<if test='uid != targetUid '>" ,
            "(`targets` like  CONCAT('%', #{uid}, '%') or `status` = 1 )  " ,
            "</if>",
            "<if test='uid == targetUid'>" ,
            " and `uid` = #{uid} " ,
            "</if>",
            "<if test='status != 0 and status == 1'>" ,
            " and `status` = 1 " ,
            "</if>",
            "<if test='status != 0 and status == 2'>" ,
            " and `targets` is not null " ,
            "</if>",
            "<if test='startTime != null and endTime != null'>",
            " AND create_time BETWEEN #{startTime} AND #{endTime}",
            "</if>",
            "<if test='blockStr != null '>",
            " AND uid not in (${blockStr}) " ,
            "</if>",
            "<if test='idStr != null '>",
            " AND id not in (${idStr}) " ,
            "</if>",
            "</where>",
            " and is_del = 0 ",
            " order by create_time desc ",
            "</script>"
    })
    List<MapInvitation> getMapInvitationList(@Param("uid") Long uid,
                                             @Param("targetUid") Long targetUid,
                                             @Param("startTime") Date startTime,
                                             @Param("endTime") Date endTime,
                                             @Param("status") Integer status,
                                             @Param("blockStr") String blockStr,
                                             @Param("idStr") String idStr);

    @Select({"<script>" ,
            "SELECT * from map_invitation where `id` = #{id} " ,
            "<if test='uid != null '>" ,
            " and (`targets` like  CONCAT('%', #{uid}, '%') or `uid` = #{uid} or `status` = 1)  " ,
            "</if>",
            " and is_del = 0",
            "</script>"
    })
    MapInvitation getInvitationById(@Param("id") Long id,@Param("uid") Long uid);


    @Select({"<script>"+
            "SELECT " +
            "    `id`, " +
            "    `uid`, context, material, create_time, " +
            "    `address`, " +
            "    `type`, " +
            "    `longitude`, " +
            "    `latitude`, " +
            "    6371 * ACOS(" +
            "        COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * " +
            "        COS(RADIANS(longitude) - RADIANS(#{longitude})) + " +
            "        SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))" +
            "    ) AS calculated_distance " +
            "FROM " +
            "    map_invitation " +
            "WHERE " +
            "    is_del = 0 AND `status` = 1 " +
            "<if test='uidStr != null '>",
            " AND uid not in (${uidStr}) " ,
            "</if>",
            "<if test='idStr != null '>",
            " AND id not in (${idStr}) " ,
            "</if>",
            "ORDER BY " +
            "    calculated_distance ASC" +
                    "</script>"
    })
    List<MapInvitation> getMapList(@Param("longitude") Double longitude,
                                   @Param("latitude") Double latitude,
                                   @Param("uidStr") String uidStr,
                                   @Param("idStr") String idStr);

    @Select({
            "<script>",
            "SELECT ",
            "    `id`, `uid`, context, material, create_time, ",
            "    `address`, `type`, `longitude`, `latitude`, ",
            "    6371 * ACOS(",
            "        COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * ",
            "        COS(RADIANS(longitude) - RADIANS(#{longitude})) + ",
            "        SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))",
            "    ) AS calculated_distance ",
            "FROM map_invitation ",
            "WHERE is_del = 0 AND `status` = 1 ",
            "  <if test='uidList != null and uidList.size > 0'>",
            "    AND uid IN ",
            "    <foreach collection='uidList' item='uid' open='(' separator=',' close=')'>",
            "        #{uid}",
            "    </foreach>",
            "  </if>",
            "ORDER BY calculated_distance ASC",
            "</script>"
    })
    List<MapInvitation> getMapList(@Param("longitude") Double longitude,
                                   @Param("latitude") Double latitude,
                                   @Param("uidList") List<Long> uidList);


    @Select("<script>" +
            "SELECT " +
            " `id`, " +
            " `uid`, " +
            " `context`, " +
            " `type`, " +
            " `material`, " +
            " `create_time`, " +
            " `address`, " +
            " `longitude`, " +
            " `latitude`, " +
            " 6371 * ACOS(" +
            " COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * " +
            " COS(RADIANS(longitude) - RADIANS(#{longitude})) + " +
            " SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))" +
            " ) AS cd " +
            "FROM " +
            " map_invitation " +
            "WHERE 1 = 1 " +
            "<if test='uids != null '>" +
            " AND `uid` IN (${uids})" +
            " and (targets LIKE CONCAT('%', #{uid}, '%') OR `status` = 1 )"+
            "</if>" +
            "<if test='uids == null'>" +
            " AND  `uid` = #{uid} " +
            "</if>" +
            "<if test='range != null'>" +
            " AND 6371 * ACOS(" +
            " COS(RADIANS(#{latitude})) * COS(RADIANS(latitude)) * " +
            " COS(RADIANS(longitude) - RADIANS(#{longitude})) + " +
            " SIN(RADIANS(#{latitude})) * SIN(RADIANS(latitude))" +
            " ) &lt;= #{range} " +
            "</if>" +
            " AND is_del = 0 " +
            "ORDER BY cd ASC" +
            "</script>")
    List<MapInvitation> getMeOfFriend(@Param("longitude") Double longitude,
                                      @Param("latitude") Double latitude,
                                      @Param("uids")String uids,
                                      @Param("uid")Long uid,
                                      @Param("range")Integer range);
}
