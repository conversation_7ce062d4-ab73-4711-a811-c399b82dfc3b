package com.simi.manager;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.*;
import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.constant.resource.ResourceTypeEnum;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.constant.vip.VIPConstant;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.aristocracy.PrivilegeDescribeDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.vip.GradeInfo;
import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.dto.vip.config.VipConfigDTO;
import com.simi.common.dto.vip.config.VipPrivilegeConfigDTO;
import com.simi.common.dto.vip.config.VipProConfigDTO;
import com.simi.common.dto.vip.req.VipBackgroundSettingReq;
import com.simi.common.dto.vip.req.VipBackgroundUseReq;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.exception.ApiException;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.*;
import com.simi.common.vo.UserSimpleVO;
import com.simi.common.vo.req.RoomUpdateReq;
import com.simi.common.vo.req.UnblockReq;
import com.simi.common.vo.vip.GradeVO;
import com.simi.common.vo.vip.PrivilegeInfoVO;
import com.simi.common.vo.vip.VipBackgroundVO;
import com.simi.common.vo.vip.VipCenterInfo;
import com.simi.constant.*;
import com.simi.dto.push.VipMsgDTO;
import com.simi.entity.BlockRecord;
import com.simi.entity.account.Account;
import com.simi.entity.room.Room;
import com.simi.entity.vip.UserVipRecord;
import com.simi.entity.vip.VipGrantExperienceCardRecord;
import com.simi.service.backpack.BackpackServerService;
import com.simi.service.cache.BackPackCache;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.oauth2.AccountService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomHighService;
import com.simi.service.room.RoomService;
import com.simi.service.user.BlockRecordService;
import com.simi.service.user.BlockServerService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import com.simi.service.vip.VipConfigService;
import com.simi.service.vip.VipGrantExperienceCardRecordService;
import com.simi.service.vip.VipRecordService;
import com.simi.util.PushMsgUtil;
import jodd.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-20 11:26
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class VipManager {
    private final VipConfigService vipConfigService;
    private final VipRecordService vipRecordService;
    private final UserServerService userServerService;
    private final UserVipService userVipService;
    private final RedissonDistributionLocker distributionLocker;
    private final PurseManageService purseManageService;
    private final RoomService roomService;
    private final RoomHighService roomHighService;
    private final RedissonManager redissonManager;
    private final BackPackCache backPackCache;
    private final BackpackServerService backpackServerService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final TaskExecutor taskExecutor;
    private final SystemConfigService systemConfigService;
    private final BlockServerService blockServerService;
    private final BlockRecordService blockRecordService;
    private final VipGrantExperienceCardRecordService vipGrantExperienceCardRecordService;
    private final AccountService accountService;

    // 一天的毫秒数
    private final static long DAY_MILLIS = 24 * 60 * 60 * 1000;
    // 延迟线程池
    ScheduledThreadPoolExecutor executor;

    @PostConstruct
    public void  init() {
        // 创建一个具有3个线程的延迟线程池
        executor = new ScheduledThreadPoolExecutor(1);
    }

    public VipCenterInfo vipCenterInfo(Long userId, String os, String appVersion) {
        VipCenterInfo vipCenterInfo = new VipCenterInfo();
        LanguageEnum lang = MessageSourceUtil.getLang();
        // 是否审核版本
        Boolean version = isVersion(os, appVersion);

        // 获取用户基础信息
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(userId);
        UserSimpleVO userSimpleVO = BeanUtil.copyProperties(userBaseInfo, UserSimpleVO.class);
        vipCenterInfo.setUserInfo(userSimpleVO);
        Map<Integer, VipProConfigDTO> vipProConfigDTOMap = vipConfigService.getVipProConfigDTOMap();
        List<VipPrivilegeConfigDTO> vipPrivilegeConfigDTOS;
        // 审核版本过滤
        if (version) {
            vipPrivilegeConfigDTOS = vipConfigService.getVipPrivilegeConfigDTOS().stream().filter(k -> k.getId() != 5).toList();
        }else {
            vipPrivilegeConfigDTOS = vipConfigService.getVipPrivilegeConfigDTOS();
        }
        Map<Integer, VipConfigDTO> vipConfigDTOS = vipConfigService.getVipConfigDTOS();
        Map<Integer, List<PrivilegeDescribeDTO>> privilegeDescribe = vipConfigService.getPrivilegeDescribe();
        List<GradeInfo> grades = vipConfigService.getGrades();
        // 用户VIP 信息
        Optional<UserCurVipInfo> userVipOptional = userVipService.getUserVipCacheWithExpire(userId);
        if (userVipOptional.isPresent()) {
            UserCurVipInfo k = userVipOptional.get();
            VipProConfigDTO vipProConfigDTO = vipProConfigDTOMap.get(k.getVipLevel());
            VipCenterInfo.UserVipInfo userVipInfo = new VipCenterInfo.UserVipInfo();
            userVipInfo.setLevel(k.getVipLevel());
            userVipInfo.setExperience(k.getVipExperience());
            userVipInfo.setMaxExperience(k.getNextVipExperience());
            // 这里需要减去本月剩余的天数时间
            if (k.getVipLevel() >= 2) {
                Date recordTime = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3).getRecordTime();
                Date monthEnd = DateTimeUtil.getMonthEnd(recordTime);
                long duration = k.getVipEndTime() - (monthEnd.getTime() - System.currentTimeMillis());
                userVipInfo.setEndTime((duration < 0) ? 0 : duration);
            } else {
                userVipInfo.setEndTime(k.getVipEndTime());
            }
            // 判断状态
            userVipInfo.setStatus(userVipInfo.getEndTime() > System.currentTimeMillis() ? 1 : 2);

            userVipInfo.setTimeProtection(k.getVipLevel() >= 2);
            userVipInfo.setVipIcon(vipProConfigDTO.getIcon());
            vipCenterInfo.setUserVipInfo(userVipInfo);
        } else {
            VipProConfigDTO vipProConfigDTO = vipProConfigDTOMap.get(2);
            // 用户未拥有vip
            VipCenterInfo.UserVipInfo userVipInfo = new VipCenterInfo.UserVipInfo();
            userVipInfo.setLevel(0);
            userVipInfo.setExperience(0);
            userVipInfo.setMaxExperience(vipProConfigDTO.getExperience());
            userVipInfo.setEndTime(0L);
            userVipInfo.setStatus(0);
            userVipInfo.setVipIcon("");
            userVipInfo.setTimeProtection(false);
            vipCenterInfo.setUserVipInfo(userVipInfo);
        }


        // 档位信息
        vipCenterInfo.setGrades(grades.stream().map(k -> {
            GradeVO gradeVO = new GradeVO();
            gradeVO.setId(k.getId());
            gradeVO.setDay(k.getDay());
            gradeVO.setPrice(k.getPrice());
            return gradeVO;
        }).collect(Collectors.toList()));

        // VIP 信息
        List<VipCenterInfo.VipInfo> vipInfos = new ArrayList<>();
        vipCenterInfo.setVipInfos(vipInfos);
        vipConfigDTOS.forEach((k, v) -> {
            VipCenterInfo.VipInfo vipInfo = new VipCenterInfo.VipInfo();
            vipInfos.add(vipInfo);
            vipInfo.setLevel(k);
            vipInfo.setIconUrl(v.getIconUrl());
            vipInfo.setShowIconUrl(v.getShowIconUrl());
            vipInfo.setAnimationUrl(v.getAnimationUrl());
            vipInfo.setEnName(v.getEnName());
            vipInfo.setArName(v.getArName());

            // 特权列表
            List<Integer> privileges = v.getPrivileges();
            if (version) {
                vipInfo.setNumber(privileges.size()-1);
                vipInfo.setTotal(vipPrivilegeConfigDTOS.size());
            }else {
                vipInfo.setNumber(privileges.size());
                vipInfo.setTotal(vipPrivilegeConfigDTOS.size());
            }

            List<PrivilegeInfoVO> privilegeInfoVOS = new ArrayList<>();
            vipInfo.setPrivilegeInfoVOS(privilegeInfoVOS);
            List<PrivilegeDescribeDTO> privilegeDescribeDTOs = privilegeDescribe.get(k);
            Map<Integer, PrivilegeDescribeDTO> privilegeDescribeDTOMap = privilegeDescribeDTOs.stream().collect(Collectors.toMap(PrivilegeDescribeDTO::getId, j -> j));

            vipPrivilegeConfigDTOS.forEach(item -> {
                int id = item.getId();
                PrivilegeInfoVO privilegeInfoVO = new PrivilegeInfoVO();
                privilegeInfoVOS.add(privilegeInfoVO);
                privilegeInfoVO.setId(item.getId());
                privilegeInfoVO.setName(lang == LanguageEnum.ar ?
                        item.getArName() : item.getEnName());
                privilegeInfoVO.setDescribe(lang == LanguageEnum.ar ?
                        item.getArDescribe() : item.getEnDescribe());
                if (privileges.contains(privilegeInfoVO.getId())) {
                    privilegeInfoVO.setStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());
                } else {
                    privilegeInfoVO.setStatus(AristocracyConstant.Status.INVALID.getStatus());
                }
                privilegeInfoVO.setIconUrl(item.getIconUrl());
                PrivilegeDescribeDTO privilegeDescribeDTO = privilegeDescribeDTOMap.get(id);
                PrivilegeInfoVO.Detail detail = new PrivilegeInfoVO.Detail();
                detail.setId(privilegeDescribeDTO.getId());
                detail.setDescribe(lang == LanguageEnum.ar ? privilegeDescribeDTO.getArDescribe() : privilegeDescribeDTO.getEnDescribe());
                detail.setTitle(lang == LanguageEnum.ar ? privilegeDescribeDTO.getArTitle() : privilegeDescribeDTO.getEnTitle());
                detail.setImage(lang == LanguageEnum.ar ? privilegeDescribeDTO.getArImage() : privilegeDescribeDTO.getEnImage());
                detail.setIconUrl(privilegeDescribeDTO.getIconUrl());
                // 特权描述
                if (privileges.contains(privilegeInfoVO.getId())) {
                    privilegeInfoVO.setDetail(detail);
                }
            });
        });
        vipInfos.sort(Comparator.comparing(VipCenterInfo.VipInfo::getLevel));
        return vipCenterInfo;
    }

    /**
     * 购买贵族
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void buyVip(long userId, int gradeId, Long giveUserId) {
        /**
         * 上锁
         * 扣款
         * 插入记录
         * 购买成功后的行为
         * 解锁
         */
        try (Locker locker = distributionLocker.lock(
                Oauth2RedisKey.vip_lock.getKey(userId))) {
            List<GradeInfo> grades = vipConfigService.getGrades();

            Optional<GradeInfo> gradeInfoOptional = grades.stream().filter(item -> item.getId() == gradeId).findAny();
            gradeInfoOptional.orElseThrow(() -> new ApiException(CodeEnum.VIP_DUTY_NOT_EXIST));
            GradeInfo gradeInfo = gradeInfoOptional.get();
            if (gradeInfo.getPrice() <= 0) {
                // 该贵族价格档位有异常
                log.info("vip this grade is error.{},userID:{}", gradeInfo, userId);
                throw new ApiException(CodeEnum.VIP_BUY_FAIL);
            }
            long deductUserId = giveUserId == null ? userId : giveUserId;
            PurseDTO purseDTO = purseManageService.getPurse(deductUserId);
            if (purseDTO.getCoin() < gradeInfo.getPrice()) {
                log.error("vip buy user[{}] current coin is {}, spend coin:{}", deductUserId, purseDTO.getCoin(), gradeInfo.getPrice());
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }
            int day = gradeInfo.getDay();
            int price = gradeInfo.getPrice();

            // 查询用户当前生效的记录
            LambdaQueryWrapper<UserVipRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(UserVipRecord::getUserId, userId);
            wrapper.eq(UserVipRecord::getRecordStatus, VIPConstant.Status.EFFECTIVE.getStatus());
            wrapper.ge(UserVipRecord::getEndTime, System.currentTimeMillis());

            List<UserVipRecord> records = vipRecordService.list(wrapper);
            records.sort(Comparator.comparing(UserVipRecord::getEndTime).reversed());
            // 获取第一条
            long startTime = System.currentTimeMillis();
            if (CollectionUtil.isNotEmpty(records)) {
                UserVipRecord userVipRecord = records.get(0);
                Long recordEndTime = userVipRecord.getEndTime();
                startTime = recordEndTime + 1001;
            }
            long endTime = startTime + day * DAY_MILLIS;
            VIPConstant.Source source = Objects.isNull(giveUserId)
                    ? VIPConstant.Source.BUY : VIPConstant.Source.GIFT;

            // 扣钱
            purseManageService
                    .deductCoin(deductUserId, (long) price, BillEnum.BUY_VIP,
                            CommonUtil.genId(), "", Collections.emptyMap(), 0L, PurseRoleTypeEnum.USER.getType());

            // 插入记录
            UserVipRecord userVipRecord = new UserVipRecord();
            userVipRecord.setGetSource(source.getSource());
            userVipRecord.setUserId(userId);
            userVipRecord.setDurationDays(day);
            userVipRecord.setGiftUserId(giveUserId);
            userVipRecord.setGradeId(gradeId);
            userVipRecord.setStartTime(startTime);
            userVipRecord.setEndTime(endTime);
            userVipRecord.setCreateTime(new Date());
            userVipRecord.setModifyTime(new Date());
            userVipRecord.setRecordStatus(AristocracyConstant.Status.EFFECTIVE.getStatus());
            vipRecordService.save(userVipRecord);
            log.info("vip 购买成功 uid:{},aristocracyId:{}", userId, userVipRecord);
            // 刷新缓存
            vipRecordService.flushCache(userId, endTime);
            // 发送系统消息
            if (VIPConstant.Source.GIFT == source) {
                taskExecutor.execute(()->{
                    sendMsg(userId, giveUserId, day);
                });
            }
        } catch (ApiException e) {
            log.error("vip buy error.userId:{},e:{}", userId, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("vip buy error.userId:{},e:{}", userId, ExceptionUtil.message(e), e);
            throw new ApiException(CodeEnum.VIP_BUY_FAIL);
        }

    }


    public VipBackgroundVO getBackground(Long userId) {
        VipBackgroundVO vipBackgroundVO = new VipBackgroundVO();
        vipBackgroundVO.setVipLevel(0);
        vipBackgroundVO.setBackgroundUrl("");
        Optional<UserCurVipInfo> optional = userVipService.getUserVipCache(userId);
        if (optional.isEmpty()) {
            return vipBackgroundVO;
        }
        UserCurVipInfo userCurVipInfo = optional.get();
        Integer vipLevel = userCurVipInfo.getVipLevel();
        vipBackgroundVO.setVipLevel(vipLevel);
        if (vipLevel < 3) {
            return vipBackgroundVO;
        }

        String userGoods = backPackCache.getUserGoods(userId, ResourceTypeEnum.RESOURCE_ROOM_BACKGROUND.getNumber());
        if (StringUtils.isNotBlank(userGoods)) {
            vipBackgroundVO.setStatus(false);
        } else {
            // 如果大于3级，并且开启 用房间背景
            String value = redissonManager.hGet(RoomRedisKey.room_custom_background_status.getKey(), userId + "");
            if (userCurVipInfo.getVipLevel() >= 3 && StringUtils.isNotBlank(value) && "1".equals(value)) {
                vipBackgroundVO.setStatus(true);
            }
        }
        // 获取房间背景图
        Room roomByUid = roomService.getRoomByUid(userId);
        String backgroundUrl = roomByUid.getBackgroundUrl();
        vipBackgroundVO.setBackgroundUrl(OssUrlUtil.jointUrl(backgroundUrl));
        return vipBackgroundVO;
    }

    public void updateBackground(Long operateUserId, VipBackgroundSettingReq req) {
        long uid = req.getUid();
        String backgroundUrl = req.getBackgroundUrl();
        log.info("vip 更新房间背景 operateUserId:{},req:{}", operateUserId, req);

        Room room = roomService.getRoomByUid(uid);
        if (Objects.isNull(room)) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        String roomId = room.getId();
        Long roomUid = room.getUid();
        if (operateUserId != uid) {
            // 判断是否管理员
            Set<String> members = redissonManager.sMembers(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId)));
            if (!members.contains(String.valueOf(operateUserId))) {
                throw new ApiException(CodeEnum.ROOM_NO_AUTHORITY);
            }
        }

        // 更新房间信息
        RoomUpdateReq roomUpdateReq = new RoomUpdateReq();
        roomUpdateReq.setRoomBg(backgroundUrl);
        roomUpdateReq.setRoomId(roomId);
        roomHighService.updateRoomInfo(roomUid, roomUpdateReq);
        // 延迟推送
        final long pushRoomUid = roomUid;
        executor.schedule(()->{
            backpackServerService.doPushRoomMsg(pushRoomUid, roomId);
        }, 5, TimeUnit.SECONDS);
    }

    /**
     *  获取VIP赠送的体验卡剩余次数
     */
    public int getSendRemainCount(long userId) {
        Optional<UserCurVipInfo> userVipCache = userVipService.getUserVipCache(userId);
        if (userVipCache.isPresent()) {
            UserCurVipInfo userCurVipInfo = userVipCache.get();
            Map<Integer, Integer> sendCount = vipConfigService.getSendCount();
            Integer count = sendCount.get(userCurVipInfo.getVipLevel());
            if (Objects.isNull(count) || count < 1) {
                return 0;
            }
            // 获取这个月用户赠送次数
            Date date = new Date();
            TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(date, TimeZoneEnum.GMT3);
            String dayKey = DateUtil.format(DateUtil.beginOfDay(timeZoneDateDTO.getRecordTime()), DateTimeUtil.CACHE_MONTH_PATTERN);
            int hadGiveCount = redissonManager.lSize(UserRedisKey.user_vip_send.getKey(dayKey, userId));
           return count - hadGiveCount;
        }
        return 0;
    }

    /**
     * 高级VIP 赠送体验卡
     */
    public void giveExperienceCard(long userId, long sendUserId) {
        // 查询赠送者是否VIP
        Optional<UserCurVipInfo> userVipCache = userVipService.getUserVipCache(sendUserId);
        if (userVipCache.isEmpty()) {
            log.error("vip sendUserId 没有赠送体验卡资格 id:{}", sendUserId);
            throw new ApiException(CodeEnum.VIP_HAS_NO_PERMISSION);
        }
        UserCurVipInfo userCurVipInfo = userVipCache.get();
        Map<Integer, Integer> sendCount = vipConfigService.getSendCount();
        Integer count = sendCount.get(userCurVipInfo.getVipLevel());
        if (Objects.isNull(count) || count < 1) {
            log.error("vip sendUserId 没有赠送体验卡资格 id:{}", sendUserId);
            throw new ApiException(CodeEnum.VIP_HAS_NO_PERMISSION);
        }
        // 获取这个月用户赠送次数
        Date date = new Date();
        TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(date, TimeZoneEnum.GMT3);
        String monthKey = DateUtil.format(DateUtil.beginOfDay(timeZoneDateDTO.getRecordTime()), DateTimeUtil.CACHE_MONTH_PATTERN);
        int hadGiveCount = redissonManager.lSize(UserRedisKey.user_vip_send.getKey(monthKey, sendUserId));
        if (hadGiveCount >= count) {
            log.info("vip sendUserId 每日赠送体验卡次数已达上限 id:{}", sendUserId);
            throw new ApiException(CodeEnum.VIP_SEND_COUNT_LIMIT);
        }
        // 查看接受者是否VIP
        Optional<UserCurVipInfo> receiveOptional = userVipService.getUserVipCache(userId);
        if (receiveOptional.isPresent()) {
            log.info("vip 每日赠送体验卡 接受者是VIP，无法赠送 sendUserId:{}，userId：{}", sendUserId, userId);
            throw new ApiException(CodeEnum.VIP_RECEIVER_NOT_EXIST);
        }
        // 发送体验卡 固定赠送一天
        vipRecordService.giveVip(userId, DAY_MILLIS, VIPConstant.Source.VIP_SENIOR_GIFT, sendUserId);
        // 添加次数
        redissonManager.leftPush(UserRedisKey.user_vip_send.getKey(monthKey, sendUserId), userId + "");
        redissonManager.expire(UserRedisKey.user_vip_send.getKey(monthKey, sendUserId), 100, TimeUnit.DAYS);
        // 记录到数据库
        VipGrantExperienceCardRecord record = new VipGrantExperienceCardRecord();
        record.setGrantorId(sendUserId);
        record.setReceiverId(userId);
        record.setCreateTime(new Date());
        record.setModifyTime(new Date());
        vipGrantExperienceCardRecordService.save(record);
        log.info("vip 赠送体验卡成功 sendUserId:{},receive:{}, 赠送次数：{}", sendUserId, userId, hadGiveCount + 1);
        // 推送系统消息
        sendMsg(userId, sendUserId, 1);
    }

    public void effectBackground(Long operateUserId, VipBackgroundUseReq req) {
        try {
            long roomUid = req.getUid();
            boolean use = req.isUse();
            log.info("vip 使用房间背景图 operateUserId:{},uid:{}", operateUserId, roomUid);
            Optional<UserCurVipInfo> optional = userVipService.getUserVipCache(roomUid);
            if (optional.isEmpty()) {
                log.info("vip 使用房间背景图 房主不是会员：{}", roomUid);
                return ;
            }
            UserCurVipInfo userCurVipInfo = optional.get();
            Integer vipLevel = userCurVipInfo.getVipLevel();
            if (vipLevel < 3) {
                log.info("vip 使用房间背景图 房主会员少于3级：{}", roomUid);
                return ;
            }

            Room room = roomService.getRoomByUid(roomUid);
            if (Objects.isNull(room)) {
                throw new ApiException(CodeEnum.PARAM_ILLEGAL);
            }
            String roomId = room.getId();
            roomUid = room.getUid();
            if (operateUserId != roomUid) {
                // 判断是否管理员
                Set<String> members = redissonManager.sMembers(RoomRedisKey.room_manager.getKey(StrUtil.format("{{}}", roomId)));
                if (!members.contains(String.valueOf(operateUserId))) {
                    throw new ApiException(CodeEnum.ROOM_NO_AUTHORITY);
                }
            }
            redissonManager.hSet(RoomRedisKey.room_custom_background_status.getKey(), roomUid + "", use ? "1" : "0");
            // 延迟推送
            final long pushRoomUid = roomUid;
            executor.schedule(()->{
                backpackServerService.doPushRoomMsg(pushRoomUid, roomId);
            }, 5, TimeUnit.SECONDS);

        } catch (Exception e) {

            log.error("vip effectBackground error. operateUserId:{},uid:{},error:{}", operateUserId, req.getUid(), ExceptionUtil.message(e), e);
            throw new ApiException(CodeEnum.SERVER_ERROR);
        }
    }


    public int getBlockUsers(long uid) {
        // 获取用户信息
        List<BlockRecord> blockRecords = blockRecordService.blockByUid(uid);
        Optional<BlockRecord> optional = blockRecords.stream().filter(item -> item.getType() == 1).findAny();
        if (optional.isEmpty()) {
            return 0;
        }
        BlockRecord blockRecord = optional.get();
        Date endTime = blockRecord.getEndTime();
        long duration = endTime.getTime() - new Date().getTime();
        // 计算天数 向上取整
        int days = (int) (duration / DAY_MILLIS) + 1;
        double coefficient = vipConfigService.getCoefficient();
        int unblockAmount = vipConfigService.getUnblockAmount();
        // 计算金额 天数*1000*系数向上取整
        int amount = (int) Math.ceil(days * unblockAmount * coefficient);
        log.info("vip 获取封禁用户金额，uid:{},days:{},coefficient:{},amount:{}", uid, days, coefficient, amount);
        return amount;
    }

    public void unblock(Long uid, long targetUid) {
        try (Locker locker = distributionLocker.lock(
                Oauth2RedisKey.vip_lock.getKey(uid))) {

            // 查询用户是否VIP6
            Optional<UserCurVipInfo> userVipCache = userVipService.getUserVipCache(uid);
            UserCurVipInfo userCurVipInfo = userVipCache.orElseThrow(() -> new ApiException(CodeEnum.VIP_NOT_EXIST));
            if (userCurVipInfo.getVipLevel() < 6) {
                throw new ApiException(CodeEnum.VIP_HAS_NO_PERMISSION);
            }

            // 查询被封禁用户金额
            int amount = getBlockUsers(targetUid);
            if (amount <= 0) {
                log.info("vip unblock success .用户解封金额为0.uid:{},targetUid:{}.", uid, targetUid);
                return;
            }
            PurseDTO purseDTO = purseManageService.getPurse(uid);
            if (purseDTO.getCoin() < amount) {
                log.error("vip unblock user[{}] current coin is {}, spend coin:{}", uid, purseDTO.getCoin(), amount);
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }

            // 扣钱
            purseManageService
                    .deductCoin(uid, (long) amount, BillEnum.VIP_RECOVER_COIN,
                            CommonUtil.genId(), "", Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());

            // 获取用户的账号信息
            Account account = accountService.getByUid(targetUid);
            // 获取设备和IP
            String deviceId = account.getDeviceId();
            String registerIp = account.getRegisterIp();

            log.info("vip 解封用户，uid:{},deviceId:{},registerIp:{},targetUid:{}", uid, deviceId, registerIp, targetUid);

            // 解封用户
            UnblockReq unblockReq = new UnblockReq();
            unblockReq.setUid(targetUid);
            unblockReq.setContent(targetUid + "");
            unblockReq.setType(1);
            blockServerService.unblockByUid(unblockReq, 0);


            unblockReq = new UnblockReq();
            unblockReq.setContent(deviceId + "");
            unblockReq.setType(2);
            blockServerService.unblockByUid(unblockReq, 0);

            unblockReq = new UnblockReq();
            unblockReq.setContent(registerIp + "");
            unblockReq.setType(3);
            blockServerService.unblockByUid(unblockReq, 0);

        } catch (ApiException e) {
            log.error("vip unblock error.userId:{},e:{}", uid, e.getMessage(), e);
            throw e;
        } catch (Exception e) {
            log.error("vip unblock error.userId:{},e:{}", uid, com.simi.common.util.ExceptionUtil.formatEx(e), e);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    private void sendMsg(long targetUid, Long sendUserId, int days) {
        try {
            String vipIcon = vipConfigService.getVipIcon();
            Map<String, String> titleMap = new HashMap<>();
            Map<String, String> textMap = new HashMap<>();
            Map<String, String> nameMap = new HashMap<>();
            //发送消息
            String enMsgTitle = MessageSourceUtil.i18nByCode("vip_give_msg_title", LanguageEnum.en);
            String enMsg = MessageSourceUtil.i18nByCode("vip_give_msg", LanguageEnum.en);
            String arMsgTitle = MessageSourceUtil.i18nByCode("vip_give_msg_title", LanguageEnum.ar);
            String arMsg = MessageSourceUtil.i18nByCode("vip_give_msg", LanguageEnum.ar);
            titleMap.put("ar", arMsgTitle);
            titleMap.put("en", enMsgTitle);
            textMap.put("ar", String.format(enMsg, days));
            textMap.put("en", String.format(arMsg, days));

            VipMsgDTO textMsgModel = new VipMsgDTO();
            VipMsgDTO.VipInfo vipInfo = new VipMsgDTO.VipInfo();
            textMsgModel.setVipInfo(vipInfo);
            vipInfo.setDays(days);
            vipInfo.setIconUrl(vipIcon);

            textMsgModel.setTitleMap(titleMap);
            textMsgModel.setTextMap(textMap);
            textMsgModel.setType(VIPConstant.MsgType.GIFT.getType());
            textMsgModel.setLinkUrl("nady:///main/me-vip");

            UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(sendUserId);
            Long userNo = userBaseInfo.getUserNo();
            textMsgModel.setSendUserNo(userNo);

            String textMsgModelStr = JSONUtil.toJsonStr(textMsgModel);
            CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                    IMMsgType.Vip,
                    System.currentTimeMillis(), textMsgModelStr);

            OfflinePushInfo offlinePushInfo = null;
            try {
                LanguageEnum languageEnum = userServerService.userAppLanguage(targetUid);
                String offlineTitle = textMsgModel.getTitleMap().get(languageEnum.name());
                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                        offlineTitle,
                        offlineTitle,
                        vipInfo.getIconUrl(),
                        textMsgModel.getLinkUrl());
            } catch (Exception e) {
                // ignore
                log.info("sendAristocracyInfo build tim message offlinePushInfo error:[{}]", com.simi.common.util.ExceptionUtil.formatEx(e));
            }

            notifyMessageComponent.publishSystemDefineMessage(imMessage, targetUid + "",
                    offlinePushInfo);
            log.info("vip 发送赠送消息: [{}]  targetUid: [{}]", textMsgModelStr, targetUid);
        } catch (Exception e) {
            log.error("vip send message error.targetUid:{},{}", targetUid, com.simi.common.util.ExceptionUtil.formatEx(e));
        }

    }

    private Boolean isVersion(String os,String appVersionCode) {
        int iend = appVersionCode.indexOf("+");
        String appVersion = appVersionCode.substring(0, iend);
        String userOs = os.substring(0,os.indexOf(" "));
        String appVersionAuditingAndroid = systemConfigService.getSysConfValueById(SystemConfigConstant.APP_VERSION_AUDITING_ANDROID);
        if (StrUtil.isNotBlank(appVersionAuditingAndroid)) {
            if (PlatformEnum.ANDROID.getName().equalsIgnoreCase(userOs) && appVersionAuditingAndroid.equals(appVersion)) {
                return true;
            }
        }
        String appVersionAuditingIOS = systemConfigService.getSysConfValueById(SystemConfigConstant.APP_VERSION_AUDITING_IOS);
        if (StrUtil.isNotBlank(appVersionAuditingIOS)) {
            if (PlatformEnum.IOS.getName().equalsIgnoreCase(userOs) && appVersionAuditingIOS.equals(appVersion)) {
                return true;
            }
        }
        return false;
    }
}
