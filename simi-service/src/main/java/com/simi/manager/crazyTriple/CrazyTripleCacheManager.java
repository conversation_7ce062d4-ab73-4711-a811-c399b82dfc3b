package com.simi.manager.crazyTriple;

import cn.hutool.core.collection.CollectionUtil;
import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;
import com.google.common.collect.Lists;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.rank.RankFrequencyEnum;
import com.simi.common.dto.rank.RankKeyParam;
import com.simi.common.util.GsonUtil;
import com.simi.dto.crazyTriple.CrazyTripleGetGameHistoryDataDTO;
import com.simi.config.CrazyTripleConfig;
import com.simi.constant.ActivityRedisKey;
import com.simi.dto.crazyTriple.RankInfoDTO;
import com.simi.entity.crazyTriple.ActivityCrazyTripleGame;
import com.simi.entity.crazyTriple.ActivityCrazyTripleJoinRecord;
//import com.simi.service.activity.rank.RankComponent;
import com.simi.service.activity.rank.RankComponent;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.*;
import org.redisson.client.codec.StringCodec;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description: 疯狂Triple
 * @Author: Yibo Liu
 * @Date: 2024-05-18
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class CrazyTripleCacheManager {

    private final RedissonClient redissonClient;

    private final RankComponent rankComponent;

    public static final String SPLITTER = "|";

    private LoadingCache<String, List<RankInfoDTO>> rankJoinCache;

    private LoadingCache<String, List<RankInfoDTO>> rankRewardCache;

    @PostConstruct
    public void refreshRank() {
        ScheduledExecutorService newScheduledThreadPool = Executors.newScheduledThreadPool(1);
        newScheduledThreadPool.scheduleWithFixedDelay(() -> {
            try {
                long timeMillis = System.currentTimeMillis();
                // 日榜top3
                String rankKey = getRankJoinKey(timeMillis);
                getRankJoinCache(rankKey, 0, 3);

                // 周榜
                rankKey = getRankRewardKey(timeMillis);
                getRankRewardCache(rankKey, 0, 3);
                getRankRewardCache(rankKey, 0, 99);
               // log.info("refreshRank end time:{}", System.currentTimeMillis() - timeMillis);
            } catch (Exception e) {
                log.error("refreshRank error", e);
            }
        }, 20, 8, TimeUnit.SECONDS);
    }

    @PostConstruct
    private void cacheRank() {
        rankJoinCache = CacheBuilder.newBuilder()
                .maximumSize(50).
                expireAfterWrite(30, TimeUnit.MINUTES)
                .refreshAfterWrite(7, TimeUnit.SECONDS)
                .build(new CacheLoader<String, List<RankInfoDTO>>() {
                    @Override
                    public List<RankInfoDTO> load(String key) throws Exception {
                        try {
                            List<String> stringList = Splitter.on(SPLITTER).splitToList(key);
                            String rankKey = stringList.get(0);
                            int startIndex = Integer.parseInt(stringList.get(1));
                            int count = Integer.parseInt(stringList.get(2));
                            return rankComponent.getUserRankInfoList(rankKey, startIndex, count);
                        } catch (Exception e) {
                            log.error("getRankJoin error key:{}", key, e);
                        }
                        return Lists.newArrayList();
                    }
                });

        rankRewardCache = CacheBuilder.newBuilder()
                .maximumSize(50).
                expireAfterWrite(30, TimeUnit.MINUTES)
                .refreshAfterWrite(7, TimeUnit.SECONDS)
                .build(new CacheLoader<String, List<RankInfoDTO>>() {
                    @Override
                    public List<RankInfoDTO> load(String key) throws Exception {
                        try {
                            List<String> stringList = Splitter.on(SPLITTER).splitToList(key);
                            String rankKey = stringList.get(0);
                            int startIndex = Integer.parseInt(stringList.get(1));
                            int count = Integer.parseInt(stringList.get(2));
                            return rankComponent.getUserRankInfoList(rankKey, startIndex, count);
                        } catch (Exception e) {
                            log.error("getRankReward error key:{}", key, e);
                        }
                        return Lists.newArrayList();
                    }
                });
    }

    public void clearRankJoinCache(String rankKey, int startIndex, int count) {
        try {
            String key = Joiner.on(SPLITTER).join(rankKey, startIndex, count);
            rankJoinCache.invalidate(key);
        } catch (Exception e) {
            log.error("clearRankJoinCache error rankKey{},startIndex{},count:{}", rankKey, startIndex, count, e);
        }
    }

    public void clearRankRewardCache(String rankKey, int startIndex, int count) {
        try {
            String key = Joiner.on(SPLITTER).join(rankKey, startIndex, count);
            rankRewardCache.invalidate(key);
        } catch (Exception e) {
            log.error("clearRankRewardCache error rankKey{},startIndex{},count:{}", rankKey, startIndex, count, e);
        }
    }

    public List<RankInfoDTO> getRankJoinCache(String rankKey, int startIndex, int count) {
        try {
            String key = Joiner.on(SPLITTER).join(rankKey, startIndex, count);
            return rankJoinCache.getUnchecked(key);
        } catch (Exception e) {
            log.error("getRankJoinCache error rankKey{},startIndex{},count:{}", rankKey, startIndex, count, e);
        }
        return Collections.emptyList();
    }

    public List<RankInfoDTO> getRankRewardCache(String rankKey, int startIndex, int count) {
        try {
            String key = Joiner.on(SPLITTER).join(rankKey, startIndex, count);
            return rankRewardCache.getUnchecked(key);
        } catch (Exception e) {
            log.error("getRankRewardCache error rankKey{},startIndex{},count:{}", rankKey, startIndex, count, e);
        }
        return Collections.emptyList();
    }

    /**
     * 设置当前游戏id
     *
     * @return
     */
    public void setCurGameId(long gameId) {
        redissonClient.getBucket(ActivityRedisKey.crazyTripleCurGameId(), StringCodec.INSTANCE)
                .set(String.valueOf(gameId), 1, TimeUnit.DAYS);
    }

    /**
     * 获取当前游戏id
     *
     * @return
     */
    public Long getCurGameId() {
        String gameId = redissonClient.<String>getBucket(ActivityRedisKey.crazyTripleCurGameId(), StringCodec.INSTANCE).get();
        if (StringUtils.isBlank(gameId)) {
            return null;
        }
        return Long.parseLong(gameId);
    }

    /**
     * 设置当前游戏信息
     *
     * @return
     */
    public void setGameInfo(ActivityCrazyTripleGame gameInfo) {
        redissonClient.getBucket(ActivityRedisKey.crazy_triple_game_info.getKey(gameInfo.getId()), StringCodec.INSTANCE)
                .set(GsonUtil.getGson().toJson(gameInfo), 30, TimeUnit.MINUTES);
    }


    /**
     * 获取游戏信息
     *
     * @return
     */
    public ActivityCrazyTripleGame getGameInfo(long gameId) {
        String gameInfo = redissonClient.<String>getBucket(ActivityRedisKey.crazy_triple_game_info.getKey(gameId), StringCodec.INSTANCE).get();
        if (StringUtils.isBlank(gameInfo)) {
            return null;
        }
        return GsonUtil.getGson().fromJson(gameInfo, ActivityCrazyTripleGame.class);
    }

    /**
     * 获取当前游戏信息
     *
     * @return
     */
    public ActivityCrazyTripleGame getCurGameInfo() {
        Long gameId = getCurGameId();
        if (gameId == null || gameId <= 0) {
            return null;
        }
        return getGameInfo(gameId);
    }

    /**
     * @param prizeList
     */
    public void lPushPrize(List<String> prizeList, int drawType) {
        if (CollectionUtil.isEmpty(prizeList)) {
            return;
        }
        log.info("lPushPrize prizeList:{}", prizeList);
        RList<String> list = redissonClient.getList(ActivityRedisKey.crazyTripleGamePrizePool(drawType), StringCodec.INSTANCE);
        list.addAll(0, prizeList);
    }

    /**
     *
     */
    public int rPopPrize(int drawType) {
        RList<String> list = redissonClient.getList(ActivityRedisKey.crazyTripleGamePrizePool(drawType), StringCodec.INSTANCE);
        if (CollectionUtil.isEmpty(list)) {
            return -1;
        }
        String lastElement = list.remove(list.size() - 1);
        return StringUtils.isBlank(lastElement) ? -1 : Integer.parseInt(lastElement);
    }

    /**
     * @param gameId
     */
    public void lockStartNextGame(long gameId) {
        long oneGameDurationMillis = getOneGameDurationMillis();
        redissonClient.getBucket(ActivityRedisKey.crazyTripleStartNextGameLock(), StringCodec.INSTANCE)
                .set(String.valueOf(gameId), oneGameDurationMillis + 2000, TimeUnit.MILLISECONDS);
    }


    /**
     * @return
     */
    public boolean existLockStartNextGame() {
        String gameId = redissonClient.<String>getBucket(ActivityRedisKey.crazyTripleStartNextGameLock(), StringCodec.INSTANCE).get();
        return StringUtils.isNotBlank(gameId);
    }

    /**
     * 缓存信息
     */
    public CrazyTripleGetGameHistoryDataDTO getHistoryData(String lang) {
        String json = redissonClient.<String>getBucket(ActivityRedisKey.crazyTripleGameHistoryData(LanguageEnum.ar.name()), StringCodec.INSTANCE).get();
        if (StringUtils.isBlank(json)) {
            return null;
        }
        CrazyTripleGetGameHistoryDataDTO crazyTripleGetGameHistoryDataDTO = GsonUtil.getGson().fromJson(json, CrazyTripleGetGameHistoryDataDTO.class);
        return crazyTripleGetGameHistoryDataDTO;
    }

    public void setHistoryData(String lang, CrazyTripleGetGameHistoryDataDTO resultHistory) {
        String json = null;
        try {
            if (resultHistory == null) {
                return;
            }
            json = GsonUtil.getGson().toJson(resultHistory);
            log.debug("crazyTriple setHistoryData lang:{},json:{}", lang, json);
            redissonClient.<String>getBucket(ActivityRedisKey.crazyTripleGameHistoryData(LanguageEnum.ar.name()), StringCodec.INSTANCE).set(json, 60, TimeUnit.MINUTES);

            Set<String> top3UidList = Optional.ofNullable(resultHistory.getDayRankInfo())
                    .orElse(new ArrayList<>()).stream()
                    .map(x -> String.valueOf(x.getUser().getUid())).collect(Collectors.toSet());
            setDayTop3UidList(top3UidList);
        } catch (Exception e) {
            log.error("crazyTriple setHistoryData error lang:{},json:{}", lang, json, e);
            throw new RuntimeException(e);
        }
    }

    public void setDayTop3UidList(Set<String> top3UidList) {
        if (CollectionUtil.isEmpty(top3UidList)) {
            return;
        }
        RBatch batch = redissonClient.createBatch();
        RSetAsync<String> rSetAsync = batch.getSet(ActivityRedisKey.crazyTripleRankJoinTop3(), StringCodec.INSTANCE);
        rSetAsync.deleteAsync();
        rSetAsync.addAllAsync(top3UidList);
        rSetAsync.expireAsync(60, TimeUnit.SECONDS);
        batch.execute();

    }

    public List<Long> getDayTop3UidList() {
        Set<String> topUids = redissonClient.<String>getSet(ActivityRedisKey.crazyTripleRankJoinTop3(), StringCodec.INSTANCE).readAll();
        if (CollectionUtil.isNotEmpty(topUids)) {
            return topUids.stream().map(Long::parseLong).collect(Collectors.toList());
        }
        List<Long> topUidList = null;
        if (CollectionUtil.isEmpty(topUids)) {
            String rankKey = this.getRankJoinKey(System.currentTimeMillis());
            topUidList = rankComponent.getRankTopMemberLongList(rankKey, 3);
        }
        return topUidList;
    }

    public boolean isDayTop3User(long uid) {
        List<Long> top3UidList = getDayTop3UidList();
        if (CollectionUtil.isEmpty(top3UidList)) {
            return false;
        }
        return top3UidList.contains(uid);
    }



    /**
     *
     */
    public long getJoinAmount(long gameId, int productType, long userId) {
        return redissonClient.getAtomicLong(ActivityRedisKey.crazyTripleJoinAmount(gameId, productType, userId)).get();
    }

    public long addJoinAmount(long gameId, int productType, long userId, int amount) {
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(ActivityRedisKey.crazyTripleJoinAmount(gameId, productType, userId));
        long totalAmount = rAtomicLong.addAndGet(amount);
        rAtomicLong.expire(60, TimeUnit.SECONDS);
        return totalAmount;
    }

    public long getJoinAmountTotal(long gameId, int productType) {
        return redissonClient.getAtomicLong(ActivityRedisKey.crazyTripleJoinAmountTotal(gameId, productType)).get();
    }

    public long addJoinAmountTotal(long gameId, int productType, int amount) {
        RAtomicLong rAtomicLong = redissonClient.getAtomicLong(ActivityRedisKey.crazyTripleJoinAmountTotal(gameId, productType));
        long totalAmount = rAtomicLong.addAndGet(amount);
        rAtomicLong.expire(60, TimeUnit.SECONDS);
        return totalAmount;
    }


    private RankKeyParam getRankRewardKeyParam(long timeMillis) {
        RankFrequencyEnum rankFrequencyEnum = RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK;
        return RankKeyParam.builder()
                .keyPrefix(ActivityRedisKey.crazyTripleRankReward())
                .keyParam("")
                .timeMillis(timeMillis)
                .rankFrequencyEnum(rankFrequencyEnum)
                .build();
    }

    public String getRankRewardKey(long timeMillis) {
        RankKeyParam rankKeyParam = getRankRewardKeyParam(timeMillis);
        return rankComponent.getRankKey(rankKeyParam);
    }

    public void addRankReward(long userId, long amount, long timeMillis) {
        RankKeyParam rankKeyParam = getRankRewardKeyParam(timeMillis);
        rankComponent.addRankScore(rankKeyParam, String.valueOf(userId), amount, true);
    }

    private RankKeyParam getRankJoinKeyParam(long timeMillis) {
        RankFrequencyEnum rankFrequencyEnum = RankFrequencyEnum.RANK_FREQUENCY_TYPE_DAY;
        return RankKeyParam.builder()
                .keyPrefix(ActivityRedisKey.crazyTripleRankJoin())
                .keyParam("")
                .timeMillis(timeMillis)
                .rankFrequencyEnum(rankFrequencyEnum)
                .build();
    }

    public String getRankJoinKey(long timeMillis) {
        RankKeyParam rankKeyParam = getRankJoinKeyParam(timeMillis);
        return rankComponent.getRankKey(rankKeyParam);
    }

    public void addRankJoin(long userId, long amount, long timeMillis) {
        RankKeyParam rankKeyParam = getRankJoinKeyParam(timeMillis);

        rankComponent.addRankScore(rankKeyParam, String.valueOf(userId), amount, true);
    }


    public void addBetInfo(long gameId, ActivityCrazyTripleJoinRecord joinRecord, boolean isAddTop3) {
        RBatch batch = redissonClient.createBatch();
        String key = Joiner.on(SPLITTER).join(joinRecord.getUserId(), joinRecord.getProductType(), joinRecord.getPriceId(), joinRecord.getCurrencyAmount(), joinRecord.getId());

        RScoredSortedSetAsync<String> allZset = batch.getScoredSortedSet(ActivityRedisKey.crazyTripleJoinList(gameId), StringCodec.INSTANCE);
        allZset.addAsync(joinRecord.getCreateTime().getTime(), key);
        allZset.expireAsync(10, TimeUnit.SECONDS);

        if (isAddTop3) {
            RScoredSortedSetAsync<String> top3Zset = batch.getScoredSortedSet(ActivityRedisKey.crazyTripleJoinListTop3(gameId), StringCodec.INSTANCE);
            top3Zset.addAsync(joinRecord.getCreateTime().getTime(), key);
            top3Zset.expireAsync(10, TimeUnit.SECONDS);
        }

        RListAsync<String> rList = batch.getList(ActivityRedisKey.crazyTripleBetList(gameId), StringCodec.INSTANCE);
        rList.addAsync(key);
        rList.expireAsync(60, TimeUnit.SECONDS);
        batch.execute();
    }

    public Collection<String> getJoinList(long gameId, double maxScore) {
        return redissonClient.<String>getScoredSortedSet(ActivityRedisKey.crazyTripleJoinList(gameId), StringCodec.INSTANCE)
                .valueRange(Double.NEGATIVE_INFINITY, false, maxScore, true);
    }

    public void removeJoinList(long gameId, double maxScore) {
        redissonClient.<String>getScoredSortedSet(ActivityRedisKey.crazyTripleJoinList(gameId), StringCodec.INSTANCE)
                .removeRangeByScore(Double.NEGATIVE_INFINITY, false, maxScore, true);
    }

    public void removeJoinList(long gameId, Collection<String> memberList) {
        if (CollectionUtil.isEmpty(memberList)) {
            return;
        }
        redissonClient.<String>getScoredSortedSet(ActivityRedisKey.crazyTripleJoinList(gameId), StringCodec.INSTANCE)
                .removeAll(memberList);
    }

    public Collection<String> getBetList(long gameId) {
        return redissonClient.<String>getList(ActivityRedisKey.crazyTripleBetList(gameId), StringCodec.INSTANCE)
                .readAll();
    }

    public void lockPushAllUserBet() {
        redissonClient.getBucket(ActivityRedisKey.crazyTriplePushAllUserBetLock()).set(0, CrazyTripleConfig.pushAllUserBetLockMillis, TimeUnit.MILLISECONDS);
    }

    public boolean existLockPushAllUserBet() {
        return redissonClient.getBucket(ActivityRedisKey.crazyTriplePushAllUserBetLock()).isExists();
    }

    public void lockPushTop3UserBet() {
        redissonClient.getBucket(ActivityRedisKey.crazyTriplePushTop3UserBetLock()).set(0, CrazyTripleConfig.pushTop3UserBetLockMillis, TimeUnit.MILLISECONDS);
    }

    public boolean existLockPushTop3UserBet() {
        return redissonClient.getBucket(ActivityRedisKey.crazyTriplePushTop3UserBetLock()).isExists();
    }

    public long getOneGameDurationMillis() {
        return CrazyTripleConfig.betTimeMillis +
                CrazyTripleConfig.stopBetTimeMillis +
                CrazyTripleConfig.playAnimationTimeMillis +
                CrazyTripleConfig.showResultTimeMillis +
                CrazyTripleConfig.flyingCurrencyTimeMillis +
                CrazyTripleConfig.breakTimeMillis;
    }

    public Collection<String> getTop3JoinList(long gameId, double maxScore) {
        return redissonClient.<String>getScoredSortedSet(ActivityRedisKey.crazyTripleJoinListTop3(gameId), StringCodec.INSTANCE)
                .valueRange(Double.NEGATIVE_INFINITY, false, maxScore, true);
    }


    public void removeTop3JoinList(long gameId, Collection<String> memberList) {
        if (CollectionUtil.isEmpty(memberList)) {
            return;
        }
        redissonClient.<String>getScoredSortedSet(ActivityRedisKey.crazyTripleJoinListTop3(gameId), StringCodec.INSTANCE)
                .removeAll(memberList);
    }

    public void setHistoryDataPB(String lang, CrazyTripleGetGameHistoryDataDTO resultHistoryPB) {
        String json = null;
        try {
            if (resultHistoryPB == null) {
                return;
            }
            json = GsonUtil.getGson().toJson(resultHistoryPB);
            log.debug("crazyTriple setHistoryData lang:{},json:{}", lang, json);
            redissonClient.<String>getBucket(ActivityRedisKey.crazyTripleGameHistoryData(LanguageEnum.ar.name()), StringCodec.INSTANCE).set(json, 60, TimeUnit.MINUTES);

            Set<String> top3UidList = resultHistoryPB.getDayRankInfo().stream().map(x -> String.valueOf(x.getUser().getUid())).collect(Collectors.toSet());
            setDayTop3UidList(top3UidList);
        } catch (Exception e) {
            log.error("crazyTriple setHistoryData error lang:{},json:{}", lang, json, e);
            throw new RuntimeException(e);
        }
    }


}
