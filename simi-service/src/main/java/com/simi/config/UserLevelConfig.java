package com.simi.config;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.entity.LevelInfo;
import jakarta.annotation.PostConstruct;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Configuration
@ConfigurationProperties(prefix = "level")
public class UserLevelConfig {

    public static String wealth;
    public static String charm;

    public static String active;

    public static Long MAX_EXP = 999999999L;

    public static Integer MAX_LV = 201;

    public static Integer ACTIVE_MAX_LV = 101;

    public static List<LevelInfo> WEALTH_LEVEL_INFO;

    public static List<LevelInfo> CHARM_LEVEL_INFO;

    public static List<LevelInfo> ACTIVE_LEVEL_INFO;

    public void setWealth(String wealth) {
        UserLevelConfig.wealth = wealth;
    }

    public void setCharm(String charm) {
        UserLevelConfig.charm = charm;
    }

    public void setActive(String active) {
        UserLevelConfig.active = active;
    }

    /**
     * 等级对应勋章
     */
    public static int getUserWealthLevelMedal(Long exp) {
        LevelInfo levelInfo = currentLevel(UserLevelTypeEnum.LEVEL_WEALTH, exp);
        Integer maxLevel = levelInfo.getLevel();
        //当前等级
        int level = maxLevel / 10;
        if (maxLevel % 10 != 0) {
            level = level + 1;
        }
        if (level >= 10) {
            return 10;
        }
        if (level == 0) {
            return 1;
        }
        return level;
    }

    /**
     * 等级对应勋章
     */
    public static int getUserCharmLevelMedal(Long exp) {
        LevelInfo levelInfo = currentLevel(UserLevelTypeEnum.LEVEL_CHARM, exp);
        Integer maxLevel = levelInfo.getLevel();
        //当前等级
        int level = maxLevel / 10;
        if (maxLevel % 10 != 0) {
            level = level + 1;
        }
        if (level >= 10) {
            return 10;
        }
        if (level == 0) {
            return 1;
        }
        return level;
    }

    public static int getUserActiveLevelMedal(Long exp) {
        LevelInfo levelInfo = activeLevel(UserLevelTypeEnum.LEVEL_ACTIVE, exp);
        Integer maxLevel = levelInfo.getLevel();
        //当前等级
        int level = maxLevel / 10;
        if (maxLevel % 10 != 0) {
            level = level + 1;
        }
        if (level >= 10) {
            return 10;
        }
        if (level == 0) {
            return 1;
        }
        return level;
    }


    public static LevelInfo currentLevel(UserLevelTypeEnum levelTypeEnum, Long exp) {
        Optional<LevelInfo> current = Optional.empty();
        if (Objects.equals(levelTypeEnum, UserLevelTypeEnum.LEVEL_CHARM)) {

            current = CHARM_LEVEL_INFO.stream()
                    .filter(record -> record.getGold() <= exp)
                    .max(Comparator.comparing(LevelInfo::getLevel));
        }
        if (Objects.equals(levelTypeEnum, UserLevelTypeEnum.LEVEL_WEALTH)) {

            current = WEALTH_LEVEL_INFO.stream()
                    .filter(record -> record.getGold() <= exp)
                    .max(Comparator.comparing(LevelInfo::getLevel));
        }
        return current.orElseGet(() -> new LevelInfo(MAX_LV, MAX_EXP, MAX_EXP));
    }

    public static LevelInfo activeLevel(UserLevelTypeEnum levelTypeEnum, Long exp) {
        Optional<LevelInfo> current = Optional.empty();
        if (Objects.equals(levelTypeEnum, UserLevelTypeEnum.LEVEL_ACTIVE)) {
            current = ACTIVE_LEVEL_INFO.stream()
                    .filter(record -> record.getGold() <= exp)
                    .max(Comparator.comparing(LevelInfo::getLevel));
        }
        //当前等级
        return current.orElseGet(() -> new LevelInfo(ACTIVE_MAX_LV, MAX_EXP, MAX_EXP));
    }

    public static LevelInfo nextLevel(UserLevelTypeEnum levelTypeEnum, Long exp) {
        //用户的下一等级
        Optional<LevelInfo> next = Optional.empty();
        if (Objects.equals(levelTypeEnum, UserLevelTypeEnum.LEVEL_CHARM)) {

            next = CHARM_LEVEL_INFO.stream()
                    .filter(record -> record.getGold() > exp)
                    .min(Comparator.comparing(LevelInfo::getLevel));
        }
        if (Objects.equals(levelTypeEnum, UserLevelTypeEnum.LEVEL_WEALTH)) {

            next = WEALTH_LEVEL_INFO.stream()
                    .filter(record -> record.getGold() > exp)
                    .min(Comparator.comparing(LevelInfo::getLevel));
        }
        //当前等级
        return next.orElseGet(() -> new LevelInfo(MAX_LV, MAX_EXP, MAX_EXP));
    }

    public static LevelInfo activeNextLevel(UserLevelTypeEnum levelTypeEnum, Long exp) {
        Optional<LevelInfo> next = Optional.empty();
        if (Objects.equals(levelTypeEnum, UserLevelTypeEnum.LEVEL_ACTIVE)) {
            next = ACTIVE_LEVEL_INFO.stream()
                    .filter(record -> record.getGold() > exp)
                    .min(Comparator.comparing(LevelInfo::getLevel));
        }
        //当前等级
        return next.orElseGet(() -> new LevelInfo(ACTIVE_MAX_LV, MAX_EXP, MAX_EXP));
    }

    @PostConstruct
    public void init() {
        CHARM_LEVEL_INFO = JSONUtil.toList(UserLevelConfig.charm, LevelInfo.class);
        WEALTH_LEVEL_INFO = JSONUtil.toList(UserLevelConfig.wealth, LevelInfo.class);
        ACTIVE_LEVEL_INFO = JSONUtil.toList(UserLevelConfig.active, LevelInfo.class);
    }

}
