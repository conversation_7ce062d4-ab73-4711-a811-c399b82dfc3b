package com.simi.config;

import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/08/20 14:32
 **/
@Configuration
@Setter
@ConfigurationProperties(prefix = "pandapay")
@Getter
public class PandaPayConfig {
    private String domain;
    private String appId;
    private String apiKey;
    private String merchantId;
    private String redirectUrl;
}
