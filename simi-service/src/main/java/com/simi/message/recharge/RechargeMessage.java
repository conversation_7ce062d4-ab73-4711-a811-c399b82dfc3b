package com.simi.message.recharge;

import com.simi.message.recharge.OneTimeProductNotification;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
public class RechargeMessage {

    private String version;

    private String packageName;

    private Long eventTimeMillis;
    //订阅支付成功结果
    private SubscriptionNotification subscriptionNotification;
    //支付成功结果
    private OneTimeProductNotification oneTimeProductNotification;
    //失败结果
    private VoidedPurchaseNotification voidedPurchaseNotification;
}
