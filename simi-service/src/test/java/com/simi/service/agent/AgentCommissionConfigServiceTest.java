package com.simi.service.agent;

import com.simi.entity.agent.AgentCommissionConfig;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;

/**
 * 代理佣金配置服务测试类
 */
@Slf4j
@SpringBootTest
public class AgentCommissionConfigServiceTest {

    @Autowired
    private AgentCommissionConfigService agentCommissionConfigService;

    @Test
    public void testFindBestMatchConfig() {
        Long agentUid = 100001L;
        
        // 测试不同金额的匹配情况
        BigDecimal[] testAmounts = {
            new BigDecimal("50"),    // 小金额
            new BigDecimal("500"),   // 中等金额
            new BigDecimal("5000"),  // 大金额
            new BigDecimal("50000")  // 超大金额
        };
        
        for (BigDecimal amount : testAmounts) {
            AgentCommissionConfig config = agentCommissionConfigService.findBestMatchConfig(agentUid, amount);
            if (config != null) {
                log.info("代理UID: {}, 金额: {}, 匹配配置: 最小金额={}, 最大金额={}, 佣金比例={}%", 
                    agentUid, amount, config.getMinAmount(), config.getMaxAmount(), 
                    config.getCommissionRate().multiply(BigDecimal.valueOf(100)));
                
                // 计算佣金
                BigDecimal commission = agentCommissionConfigService.calculateCommission(agentUid, amount);
                log.info("计算佣金: {}", commission);
            } else {
                log.info("代理UID: {}, 金额: {}, 没有找到匹配的配置", agentUid, amount);
            }
        }
    }
    
    @Test
    public void testGetHighestRateConfig() {
        Long agentUid = 100001L;
        AgentCommissionConfig config = agentCommissionConfigService.getHighestRateConfig(agentUid);
        if (config != null) {
            log.info("代理UID: {} 的最高佣金比例配置: {}%", 
                agentUid, config.getCommissionRate().multiply(BigDecimal.valueOf(100)));
        } else {
            log.info("代理UID: {} 没有配置", agentUid);
        }
    }
    
    @Test
    public void testGetBestMatchConfig() {
        // 测试根据配置ID列表和金额查询最匹配的配置
        List<Long> configIds = Arrays.asList(1L, 2L, 3L, 4L);
        BigDecimal[] testAmounts = {
            new BigDecimal("100"),    // 小金额
            new BigDecimal("1000"),   // 中等金额
            new BigDecimal("10000"),  // 大金额
            new BigDecimal("100000")  // 超大金额
        };
        
        for (BigDecimal amount : testAmounts) {
            AgentCommissionConfig config = agentCommissionConfigService.getBestMatchConfig(configIds, amount);
            if (config != null) {
                log.info("配置ID列表: {}, 金额: {}, 最匹配配置: ID={}, 最小金额={}, 最大金额={}, 佣金比例={}%", 
                    configIds, amount, config.getId(), config.getMinAmount(), config.getMaxAmount(), 
                    config.getCommissionRate().multiply(BigDecimal.valueOf(100)));
            } else {
                log.info("配置ID列表: {}, 金额: {}, 没有找到匹配的配置", configIds, amount);
            }
        }
    }
    
    @Test
    public void testGetBestMatchConfigWithEmptyIds() {
        // 测试空配置ID列表的情况
        List<Long> emptyIds = Arrays.asList();
        BigDecimal amount = new BigDecimal("1000");
        
        AgentCommissionConfig config = agentCommissionConfigService.getBestMatchConfig(emptyIds, amount);
        log.info("空配置ID列表测试结果: {}", config);
    }
    
    @Test
    public void testGetBestMatchConfigWithNonExistentIds() {
        // 测试不存在的配置ID列表
        List<Long> nonExistentIds = Arrays.asList(99999L, 99998L);
        BigDecimal amount = new BigDecimal("1000");
        
        AgentCommissionConfig config = agentCommissionConfigService.getBestMatchConfig(nonExistentIds, amount);
        if (config != null) {
            log.info("不存在配置ID测试，使用默认配置: 佣金比例={}%", 
                config.getCommissionRate().multiply(BigDecimal.valueOf(100)));
        } else {
            log.info("不存在配置ID测试，没有找到默认配置");
        }
    }
}
