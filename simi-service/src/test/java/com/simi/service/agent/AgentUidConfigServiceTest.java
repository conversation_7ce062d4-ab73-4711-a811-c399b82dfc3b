package com.simi.service.agent;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代理UID配置服务测试类
 */
@Slf4j
@SpringBootTest
public class AgentUidConfigServiceTest {

    @Autowired
    private AgentUidConfigService agentUidConfigService;

    @Test
    public void testAddAndGetConfigIds() {
        Long uid = 100001L;
        Long configId1 = 1001L;
        Long configId2 = 1002L;

        // 添加配置ID
        boolean result1 = agentUidConfigService.addConfigId(uid, configId1);
        log.info("添加配置ID {} 到代理 {}: {}", configId1, uid, result1);

        boolean result2 = agentUidConfigService.addConfigId(uid, configId2);
        log.info("添加配置ID {} 到代理 {}: {}", configId2, uid, result2);

        // 获取配置ID列表
        List<Long> configIds = agentUidConfigService.getConfigIdsByUid(uid);
        log.info("代理 {} 的配置ID列表: {}", uid, configIds);

        // 检查是否包含配置ID
        boolean hasConfig1 = agentUidConfigService.hasConfigId(uid, configId1);
        boolean hasConfig2 = agentUidConfigService.hasConfigId(uid, configId2);
        log.info("代理 {} 是否包含配置 {}: {}", uid, configId1, hasConfig1);
        log.info("代理 {} 是否包含配置 {}: {}", uid, configId2, hasConfig2);
    }

    @Test
    public void testGetUidsByConfigId() {
        Long configId = 1001L;
        
        // 根据配置ID获取关联的代理UID列表
        List<Long> uids = agentUidConfigService.getUidsByConfigId(configId);
        log.info("配置ID {} 关联的代理UID列表: {}", configId, uids);
    }

    @Test
    public void testSetConfigIds() {
        Long uid = 100002L;
        List<Long> configIds = Arrays.asList(2001L, 2002L, 2003L);

        // 设置配置ID列表
        boolean result = agentUidConfigService.setConfigIds(uid, configIds);
        log.info("为代理 {} 设置配置ID列表 {}: {}", uid, configIds, result);

        // 验证设置结果
        List<Long> actualConfigIds = agentUidConfigService.getConfigIdsByUid(uid);
        log.info("代理 {} 实际的配置ID列表: {}", uid, actualConfigIds);
    }

    @Test
    public void testRemoveConfigId() {
        Long uid = 100001L;
        Long configId = 1001L;

        // 移除配置ID
        boolean result = agentUidConfigService.removeConfigId(uid, configId);
        log.info("从代理 {} 移除配置ID {}: {}", uid, configId, result);

        // 验证移除结果
        List<Long> configIds = agentUidConfigService.getConfigIdsByUid(uid);
        log.info("代理 {} 移除后的配置ID列表: {}", uid, configIds);
    }

    @Test
    public void testBatchSetConfigIds() {
        Map<Long, List<Long>> uidConfigMap = new HashMap<>();
        uidConfigMap.put(100003L, Arrays.asList(3001L, 3002L));
        uidConfigMap.put(100004L, Arrays.asList(3003L, 3004L, 3005L));
        uidConfigMap.put(100005L, Arrays.asList(3001L, 3003L));

        // 批量设置配置ID
        int successCount = agentUidConfigService.batchSetConfigIds(uidConfigMap);
        log.info("批量设置配置ID，成功数量: {}", successCount);

        // 验证批量设置结果
        for (Long uid : uidConfigMap.keySet()) {
            List<Long> configIds = agentUidConfigService.getConfigIdsByUid(uid);
            log.info("代理 {} 的配置ID列表: {}", uid, configIds);
        }
    }

    @Test
    public void testRemoveByConfigId() {
        Long configId = 3001L;

        // 根据配置ID删除所有关联的代理配置
        int removeCount = agentUidConfigService.removeByConfigId(configId);
        log.info("根据配置ID {} 删除关联配置，删除数量: {}", configId, removeCount);

        // 验证删除结果
        List<Long> uids = agentUidConfigService.getUidsByConfigId(configId);
        log.info("配置ID {} 删除后关联的代理UID列表: {}", configId, uids);
    }
}
