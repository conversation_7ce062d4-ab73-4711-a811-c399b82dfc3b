<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE configuration>
<configuration debug="false">
    <property name="LOG_HOME" value="p6spy.log" />
    <property name="CONSOLE_LOG_PATTERN"
              value="%X{traceId} %X{userId} %d{yyyy-MM-dd HH:mm:ss} %-5level [%thread] %logger{50} %msg%n"/>
    <!-- 控制台输出 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- 日志输出级别 -->
    <root level="INFO">
        <appender-ref ref="STDOUT" />
    </root>
</configuration>
