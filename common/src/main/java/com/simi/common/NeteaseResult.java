package com.simi.common;

/**
 * Created by PaperCut on 2018/7/17.
 */
public class NeteaseResult<T> {
    private int errCode;

    protected static final int PASS_CODE = 0;
    protected static final int FAIL_CODE = 1;

    public NeteaseResult(){}


    public NeteaseResult(int errCode){
        this.errCode = errCode;
    }


    public boolean success(){
        return this.errCode == PASS_CODE;
    }

    public int getErrCode() {
        return errCode;
    }


    public void setErrCode(int errCode) {
        this.errCode = errCode;
    }


    public static <T> NeteaseResultBuilder<T> builder(){
        return new NeteaseResultBuilder<T>();
    }


    public static final class NeteaseResultBuilder<T>{
        private int errCode;

        private NeteaseResultBuilder(){}


        public NeteaseResultBuilder<T> withCode(int errCode){
            this.errCode = errCode;
            return this;
        }

        public NeteaseResult<T> build(){
            NeteaseResult<T> result = new NeteaseResult<>();
            result.setErrCode(errCode);
            return result;
        }
    }

}
