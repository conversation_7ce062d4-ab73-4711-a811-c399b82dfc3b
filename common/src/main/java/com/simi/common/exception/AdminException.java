package com.simi.common.exception;

import com.simi.common.constant.CodeEnum;
import lombok.Getter;

@Getter
public class AdminException extends RuntimeException {

    private int responseCode;

    public AdminException(CodeEnum codeEnum) {
        this(codeEnum.getNumber(), codeEnum.getDesc(), null, false, false);
    }

    public AdminException(int responseCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.responseCode = responseCode;
    }

    public AdminException(int responseCode, String message) {
        this(responseCode, message, null, false, false);
        this.responseCode = responseCode;
    }
}
