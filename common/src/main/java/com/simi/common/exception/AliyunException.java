package com.simi.common.exception;

/**
 * 阿里云异常
 *
 * <AUTHOR>
 * @date 2022-03-18 14:04:55
 */
public class AliyunException extends RuntimeException {

    private int code;

    public AliyunException(int code, String message) {
        this(code, message, null, false, false);
        this.code = code;
    }

    public AliyunException(int code, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}