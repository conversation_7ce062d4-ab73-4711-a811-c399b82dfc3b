package com.simi.common.exception;

/**
 * Created by PaperCut on 2018/3/15.
 */
public class NetEaseException extends RuntimeException {

    private int code;


    public NetEaseException(int code, String message) {
        this(code, message, null, false, false);
        this.code = code;
    }

    public NetEaseException(int code, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = code;
    }

    public int getCode() {
        return code;
    }
}
