package com.simi.common.exception;


public class TencentException extends RuntimeException {
    private int code;

    public TencentException(int code, String message) {
        this(code, message, null, false, false);
        this.code = code;
    }

    public TencentException(int responseCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = responseCode;
    }

    public int getCode() {
        return code;
    }

}
