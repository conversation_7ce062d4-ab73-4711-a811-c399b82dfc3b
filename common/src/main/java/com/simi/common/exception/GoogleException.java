package com.simi.common.exception;


import com.simi.common.constant.CodeEnum;

public class GoogleException extends RuntimeException {
    private int code;

    public GoogleException(String message) {
        this(CodeEnum.SERVER_ERROR.getNumber(), message, null, false, false);
        this.code = CodeEnum.SERVER_ERROR.getNumber();
    }

    public GoogleException(int code, String message) {
        this(code, message, null, false, false);
        this.code = code;
    }

    public GoogleException(int responseCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = responseCode;
    }

    public int getCode() {
        return code;
    }

}
