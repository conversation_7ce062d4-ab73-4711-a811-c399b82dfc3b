package com.simi.common.exception;


import cn.hutool.core.util.StrUtil;
import com.simi.common.BusiResult;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.handler.MessageSourceUtil;
import lombok.Getter;

;

/**
 * Created by PaperCut on 2018/3/27.
 */
@Getter
public class ApiException extends RuntimeException {

    private int responseCode;
    private BusiResult<?> busiResult;
    private LanguageEnum language;


    public ApiException(int responseCode, String message) {
        this(responseCode, message, null, false, false);
        this.responseCode = responseCode;
    }

    public ApiException(CodeEnum codeEnum) {
        this(codeEnum.getNumber(), codeEnum.getDesc(), null, false, false);
        this.language = MessageSourceUtil.getLang();
        String messaget = MessageSourceUtil.i18nByCode(String.valueOf(codeEnum.getNumber()), language);
        this.busiResult = new BusiResult<>(codeEnum.getNumber(), messaget, null);
    }

    public ApiException(CodeEnum codeEnum, Object... params) {
        this(codeEnum.getNumber(), codeEnum.getDesc(), null, false, false);
        this.language = MessageSourceUtil.getLang();
        String messaget = MessageSourceUtil.i18nByCode(String.valueOf(codeEnum.getNumber()), language);
        messaget = StrUtil.format(messaget, params);
        this.busiResult = new BusiResult<>(codeEnum.getNumber(), messaget, null);
    }

    public ApiException(int responseCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.responseCode = responseCode;
    }

}
