package com.simi.common.exception;


import com.simi.common.constant.CodeEnum;

public class ZegoException extends RuntimeException {
    private int code;

    public ZegoException(String message) {
        this(CodeEnum.SERVER_ERROR.getNumber(), message, null, false, false);
        this.code = CodeEnum.SERVER_ERROR.getNumber();
    }

    public ZegoException(int code, String message) {
        this(code, message, null, false, false);
        this.code = code;
    }

    public ZegoException(int responseCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = responseCode;
    }

    public int getCode() {
        return code;
    }

}
