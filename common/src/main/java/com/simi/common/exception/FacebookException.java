package com.simi.common.exception;


import com.simi.common.constant.CodeEnum;

public class FacebookException extends RuntimeException {
    private int code;

    public FacebookException(String message) {
        this(CodeEnum.SERVER_ERROR.getNumber(), message, null, false, false);
        this.code = CodeEnum.SERVER_ERROR.getNumber();
    }

    public FacebookException(int code, String message) {
        this(code, message, null, false, false);
        this.code = code;
    }

    public FacebookException(int responseCode, String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
        this.code = responseCode;
    }

    public int getCode() {
        return code;
    }

}
