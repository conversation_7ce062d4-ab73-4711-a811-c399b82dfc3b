package com.simi.common;


import cn.hutool.core.util.StrUtil;
import com.simi.common.constant.CodeEnum;

/**
 * <AUTHOR>
 */
public class BusiResultUtil {

    public static <T> BusiResult<T> success() {
        return BusiResult.<T>builder().withStatus(CodeEnum.SUCCESS).build();
    }

    public static <T> BusiResult<T> success(T data) {
        return BusiResult.<T>builder().withStatus(CodeEnum.SUCCESS).withData(data).build();
    }

    public static <T> BusiResult<T> success(CodeEnum codeEnum, T data, Object... params) {
        return BusiResult.<T>builder().withStatus(codeEnum, params).withData(data).build();
    }

    public static <T> BusiResult<T> failed() {
        return BusiResult.<T>builder().withStatus(CodeEnum.SERVERERROR).build();
    }

    public static <T> BusiResult<T> failedWithMessage(int code, String message) {
        return BusiResult.<T>builder().withCode(code).withMessage(message).build();
    }

    public static <T> BusiResult<T> failed(CodeEnum codeEnum, Object... params) {
        String message = params.length > 0 ? StrUtil.format(codeEnum.getDesc(), params) : codeEnum.getDesc();
        return failedWithMessage(codeEnum.getNumber(), message);
    }

    public static <T> BusiResult<T> build(CodeEnum codeEnum, Object... params) {
        return BusiResult.<T>builder().withStatus(codeEnum, params).build();
    }

    public static <T> BusiResult<T> buildWithData(CodeEnum codeEnum, T data, Object... params) {
        return BusiResult.<T>builder().withStatus(codeEnum, params).withData(data).build();
    }

}
