package com.simi.common.annotation.room;

import com.google.protobuf.GeneratedMessageV3;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 用户不在房间异常检测
 * @Author: Andy
 * @Date: 2023/11/24
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RoomUserNotInComplete {
    Class<? extends GeneratedMessageV3> clazz();
}
