package com.simi.common.annotation.room;

import com.simi.common.constant.room.RoomOperateEnum;
import com.google.protobuf.GeneratedMessageV3;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 房间管理员注解
 * 包括房主和管理员
 * @Author: Andy
 * @Date: 2023/11/13
 */
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface RoomManagerComplete {

    Class<? extends GeneratedMessageV3> clazz();
    RoomOperateEnum value();
}
