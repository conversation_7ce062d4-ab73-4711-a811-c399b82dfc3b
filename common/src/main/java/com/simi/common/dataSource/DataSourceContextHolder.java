package com.simi.common.dataSource;

public class DataSourceContextHolder {

    public static final ThreadLocal<String> DATASOURCE_CONTEXT_HOLDER = new ThreadLocal<>();

    // 放入 DataSource 的 key
    public static void setDataSourceContext(String dataSource) {
        DATASOURCE_CONTEXT_HOLDER.set(dataSource);
    }

    // 获取 DataSource 的 key
    public static String getDataSource() {
        return DATASOURCE_CONTEXT_HOLDER.get();
    }

    // 清除 DataSource 的 key
    public static void clear() {
        DATASOURCE_CONTEXT_HOLDER.remove();
    }
}
