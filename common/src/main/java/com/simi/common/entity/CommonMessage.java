package com.simi.common.entity;


import com.simi.common.constant.longLink.CommonMessageEvent;
import com.simi.common.constant.longLink.CommonMessageToUserType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "通用消息实体")
public class CommonMessage {

    @Schema(description = "事件")
    private CommonMessageEvent event;
    @Schema(description = "具体事件的数据载荷，根据event不同有不一样的字段定义")
    private String payload;
    @Schema(description = "服务器毫秒级时间")
    private Long timestamp;
    @Schema(description = "消息发送给什么用户")
    private CommonMessageToUserType toType;
    @Schema(description = "接收的用户id")
    private List<String> uids;

}
