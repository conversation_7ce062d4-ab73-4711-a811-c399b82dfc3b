package com.simi.common.entity.user;



import com.simi.common.entity.CommonMultipleMessage;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class NextEventData {

    @Schema(description = "房间id")
    private String roomId;
    @Schema(description = "发送这个event的uid")
    private Long uid;
    @Schema(description = "组合通用消息实体")
    private List<CommonMultipleMessage> messageList;



}
