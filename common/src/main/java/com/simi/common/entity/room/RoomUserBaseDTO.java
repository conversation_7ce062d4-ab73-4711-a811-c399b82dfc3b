package com.simi.common.entity.room;

import com.simi.common.constant.room.RoomIdentity;
import com.simi.common.dto.user.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RoomUserBaseDTO {
    @Schema(description = "用户信息")
    private UserBaseInfoDTO userBase;
    @Schema(description = "用户身份标识")
    private RoomIdentity roomIdentity;
    @Schema(description = "贵族等级")
    private Integer curAristocracy = 0;
    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "公屏禁言结束时间")
    private Long silenceEndTime;
}
