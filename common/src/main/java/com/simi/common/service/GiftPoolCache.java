package com.simi.common.service;

import com.simi.common.redis.GiftRedisKey;
import com.simi.common.util.RedissonManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class GiftPoolCache {

    @Autowired
    private RedissonManager redissonManager;

    private String getGiftPoolKey(){
        return GiftRedisKey.gift_pool.getKey() ;
    }

    private String getGiftPoolWhiteKey(){
        return GiftRedisKey.gift_pool_white.getKey() ;
    }

    public String getGiftPool(Integer random){
        return redissonManager.hGet(getGiftPoolKey(),random.toString());
    }

    public String setGiftPool(Integer random, Integer multiply){
        return redissonManager.hSet(getGiftPoolKey(),random.toString(), multiply.toString());
    }

    public void delGiftPool(){
        redissonManager.del(getGiftPoolKey());
    }

    public Boolean giftPoolExists(){
        return redissonManager.hExists(getGiftPoolKey());
    }

    public String getGiftPoolWhite(Integer random){
        return redissonManager.hGet(getGiftPoolWhiteKey(),random.toString());
    }

    public String setGiftPoolWhite(Integer random, Integer multiply){
        return redissonManager.hSet(getGiftPoolWhiteKey(),random.toString(), multiply.toString());
    }

    public Boolean giftPoolWhiteExists(){
        return redissonManager.hExists(getGiftPoolWhiteKey());
    }


    public void delGiftPoolWhite(){
        redissonManager.del(getGiftPoolWhiteKey());
    }
}
