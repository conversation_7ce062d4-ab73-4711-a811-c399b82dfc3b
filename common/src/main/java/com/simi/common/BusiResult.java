package com.simi.common;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonGetter;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.AdminException;
import com.simi.common.exception.ApiException;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;

import java.util.Date;
import java.util.Objects;

/**
 * Created by PaperCut on 2018/7/17.
 */
@Getter
@Setter
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
public class BusiResult<T> {

    private int code;
    private String message;
    private T data;
    private Date timestamp = new Date();

    public BusiResult() {
    }

    public BusiResult(CodeEnum status) {
        this(status, status.getDesc(), null);
    }

    public BusiResult(CodeEnum status, T data) {
        this.code = status.getNumber();
        this.message = status.getDesc();
        this.data = data;
    }

    public BusiResult(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public BusiResult(CodeEnum status, String message, T data) {
        this.code = status.getNumber();
        this.message = message;
        this.data = data;
    }

    public BusiResult(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    public static BusiResult<?> failure(ApiException e) {
        if (Objects.nonNull(e.getBusiResult())) {
            BusiResult<?> busiResult = e.getBusiResult();
            if (StrUtil.isBlank(busiResult.getMessage())) {
                busiResult.setMessage(CodeEnum.SERVER_ERROR.getDesc());
            }
            return new BusiResult<>(busiResult.getCode(), busiResult.getMessage(), busiResult.getData());
        }

        return new BusiResult<>(500, CodeEnum.SERVER_ERROR.getDesc());
    }

    public static BusiResult<?> adminFailure(AdminException e) {
        return new BusiResult<>(e.getResponseCode(), e.getMessage());
    }

    public static <T> BusiResultBuilder<T> builder() {
        return new BusiResultBuilder<>();
    }

    @JsonGetter("message")
    public String getMessage() {
        return message;
    }

    @JsonGetter("msg")
    public String getMsg() {
        return message;
    }

    public boolean success() {
        return this.code == CodeEnum.SUCCESS.getNumber();
    }

    public static final class BusiResultBuilder<T> {
        private int code;
        private String message;
        private T data;

        private BusiResultBuilder() {
        }

        @JsonGetter("message")
        public String getMessage() {
            return message;
        }

        @JsonGetter("msg")
        public String getMsg() {
            return message;
        }

        public BusiResultBuilder<T> withStatus(CodeEnum codeEnum, Object... params) {
            this.code = codeEnum.getNumber();
            if (params.length > 0) {
                this.message = StrUtil.format(codeEnum.getDesc(), params);
            } else {
                this.message = codeEnum.getDesc();
            }
            return this;
        }

        public BusiResultBuilder<T> withCode(int code) {
            this.code = code;
            return this;
        }

        public BusiResultBuilder<T> withMessage(String message) {
            this.message = message;
            return this;
        }

        public BusiResultBuilder<T> withData(T data) {
            this.data = data;
            return this;
        }

        public BusiResult<T> build() {
            BusiResult<T> busiResult = new BusiResult<>();
            busiResult.setCode(code);
            busiResult.setMessage(message);
            busiResult.setData(data);
            return busiResult;
        }
    }

}
