package com.simi.common.sensitive;

import cn.hutool.core.util.StrUtil;
import com.alibaba.nacos.common.utils.CollectionUtils;
import com.google.common.collect.Lists;
import com.hankcs.hanlp.dictionary.CustomDictionary;
import com.simi.common.util.GsonUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Properties;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
@RequiredArgsConstructor
public class SensitiveService implements InitializingBean {


    private Sensitive sensitive = new StandardTokenizerSensitive();

    public static final String SENSITIVE_DATA_ID = "sensitive_data.yml";
    public static final String GROUP = "DEFAULT_GROUP";

    /**
     * 将字符串中的敏感词用指定符号替代
     *
     * @param text
     * @return
     */
    public String getStringSensitiveFilter(String text) {
        List<String> list = sensitive.getSensitiveStr(text.toLowerCase());
        log.info("recognize sensitive words:{}", StrUtil.join(",", list));
        for (String str : list) {
            text = doReplaceText(text, str);
        }
        log.info("replace sensitive:{}", text);
        return text;
    }

    /**
     * 判断字符串是否含有敏感词
     *
     * @param text
     * @return
     */
    public boolean getBooleanSensitiveFilter(String text) {
        List<String> list = sensitive.getSensitiveStr(text.toLowerCase());
        return CollectionUtils.isNotEmpty(list);
    }

    /**
     * 获取字符串中所有敏感词
     */
    public List<String> getSensitiveStr(String text) {
        return sensitive.getSensitiveStr(text);
    }

    /**
     * 对结果内容进行替换
     *
     * @param text
     * @param word
     * @return
     */
    private String doReplaceText(String text, String word) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < word.length(); i++) {
            sb.append(SensitiveConstant.REPLACE_CHAR);
        }
        Pattern pattern = Pattern.compile(word, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(text);
        String newText = matcher.replaceAll(sb.toString());
        log.info("replace sensitive word from {} to {}", text, newText);
        return newText;
    }

    /**
     * 对结果内容转换(可包含空格)
     *
     * @param text
     * @param chars
     * @return
     */
    private String doReplaceText(String text, char[] chars) {
        for (char c : chars) {
            if (' ' != c) {
                text = text.replaceAll(String.valueOf(c), SensitiveConstant.REPLACE_CHAR.toString());
            }
        }
        return text;
    }

    /**
     * 初始化加载
     *
     * @throws Exception
     */
    @Override
    public void afterPropertiesSet() {
       /* try {
            String config = nacosConfigManager.getConfigService().getConfig(SENSITIVE_DATA_ID, GROUP, 5000);
            receiveConfig(config);
            // 新增敏感词变更监听
            nacosConfigManager.getConfigService().addListener(SENSITIVE_DATA_ID, GROUP, new Listener() {
                @Override
                public Executor getExecutor() {
                    return Executors.newSingleThreadExecutor();
                }

                @Override
                public void receiveConfigInfo(String data) {
                    receiveConfig(data);
                }
            });
        } catch (Exception e) {
            log.error("Failed to init sensitive.", e);
        }*/
    }

    private void receiveConfig(String data) {
        YamlPropertiesFactoryBean yamlFactory = new YamlPropertiesFactoryBean();
        yamlFactory.setResources(new ByteArrayResource(data.getBytes()));
        Properties properties = yamlFactory.getObject();
        List<String> dataList = Lists.newArrayListWithCapacity(properties.size());
        properties.forEach((k, v) -> {
            if (String.valueOf(k).endsWith("sensitive")) {
                dataList.add(String.valueOf(v).trim());
            }
        });
        log.debug("receive sensitive words:{}", GsonUtil.getGson().toJson(dataList));
        reload(dataList);
        log.info("Successful init load sensitive to index writer");
    }

    /**
     * 将数据重新写入字典
     *
     * @param words
     */
    private synchronized void reload(List<String> words) {
        // 文艺青年写法
        if (CustomDictionary.getTrie() != null && CustomDictionary.getTrie().size() > 0) {
            Set<String> keys = CustomDictionary.getTrie().keySet();
            keys.forEach(CustomDictionary::remove);
        }
        boolean reload = CustomDictionary.reload();
        log.info("reload custom dictionary:{}", reload);
        // 写入数据
        words.forEach((word) -> CustomDictionary.insert(word.toLowerCase(), SensitiveConstant.SENSITIVE_NATURE_WITH_FREQUENCY));

    }

    public void setSensitive(Sensitive sensitive) {
        this.sensitive = sensitive;
    }
}
