package com.simi.common.sensitive;

import org.springframework.util.Assert;

import java.util.List;

public class SensitiveFilterUtil {
    private static SensitiveService sensitiveService;

    public static String getStringSensitiveFilter(String text) {
        Assert.notNull(sensitiveService, "The sensitiveService can't be null");
        return sensitiveService.getStringSensitiveFilter(text);
    }

    public static List<String> getSensitiveStr(String text){
        Assert.notNull(sensitiveService, "The sensitiveService can't be null");
        return sensitiveService.getSensitiveStr(text);
    }

    public static boolean getBooleanSensitiveFilter(String text) {
        return sensitiveService.getBooleanSensitiveFilter(text);
    }

    public static void setSensitiveService(SensitiveService sensitiveService) {
        SensitiveFilterUtil.sensitiveService = sensitiveService;
    }
}
