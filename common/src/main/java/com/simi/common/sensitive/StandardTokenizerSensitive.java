package com.simi.common.sensitive;

import com.google.common.collect.Lists;
import com.hankcs.hanlp.seg.common.Term;
import com.hankcs.hanlp.tokenizer.StandardTokenizer;
import com.simi.common.util.GsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

@Slf4j
public class StandardTokenizerSensitive implements Sensitive {

    static {
        StandardTokenizer.SEGMENT.enableCustomDictionaryForcing(true);
    }

    @Override
    public List<String> getSensitiveStr(String text) {
        List<Term> terms = StandardTokenizer.segment(text);
        log.debug("terms:{}", GsonUtil.getGson().toJson(terms));
        List<String> sensitiveStrs = Lists.newArrayList();
        for (int i = 0; i < terms.size(); i++) {
            Term term = terms.get(i);
            if (term.nature.toString().equals(SensitiveConstant.CUSTOM_NATURE)) {
                sensitiveStrs.add(term.word);
            }
        }
        return sensitiveStrs;
    }
}
