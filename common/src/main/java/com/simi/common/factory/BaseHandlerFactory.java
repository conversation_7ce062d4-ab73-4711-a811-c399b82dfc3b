package com.simi.common.factory;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 基础工厂
 */
@Slf4j
@Component
public abstract class BaseHandlerFactory<T, H> implements HandlerFactory<T, H> {

    protected final Map<T, H> HANDLERS = new LinkedHashMap<>();

    /**
     * 注册所有handler
     */
    @PostConstruct
    public abstract void registerAllHandlers();

    /**
     * 注册handler
     *
     * @param type
     * @param handler
     */
    protected void registerHandler(T type, H handler) {
        HANDLERS.put(type, handler);
    }

    @Override
    public H getHandler(T type) {
        H handler = HANDLERS.get(type);
        if (handler == null) {
            log.info("handler not found. type: {}, handlerClass: {}", type, this.getClass().getSimpleName());
        }
        return handler;
    }
}
