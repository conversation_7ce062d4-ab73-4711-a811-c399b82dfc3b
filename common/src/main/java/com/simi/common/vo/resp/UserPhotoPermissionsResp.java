package com.simi.common.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class UserPhotoPermissionsResp {

    @Schema(description = "是否vip")
    private boolean vip;

    @Schema(description = "是否可以编辑头像")
    private boolean editGIFAvatar;

    @Schema(description = "是否可以编辑相册")
    private boolean editGIFAlbum;

    public static UserPhotoPermissionsResp notVip(){
        return UserPhotoPermissionsResp.builder()
                .vip(false)
                .editGIFAlbum(false)
                .editGIFAvatar(false)
                .build();
    }
}
