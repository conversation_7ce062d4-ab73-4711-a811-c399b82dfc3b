package com.simi.common.vo.resp;

import lombok.Data;

import java.io.Serializable;
import java.util.Map;

@Data
public class HttpPostReq implements Serializable {
    private String url;

    private Map<String,String> param;
    private Map<String,Object> objParam;
    private Integer sendType; // 定义见 HttpPostSendTypeEnums;
    private String paramStr; // 相比于param，更优先于用paramStr
    private String objToJson;

    private Map<String,String> header;

    private String valueEnc; // value的编码方式，默认为UTF-8
}
