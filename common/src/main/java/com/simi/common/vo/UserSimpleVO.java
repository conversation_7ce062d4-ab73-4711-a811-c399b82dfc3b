package com.simi.common.vo;

import com.simi.common.vo.resp.UserLevelBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "简单的用户信息")
public class UserSimpleVO {

    @Schema(description = "用户ID")
    private Long uid ;
    @Schema(description = "用户号")
    private Long userNo;
    @Schema(description = "")
    private String nick;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "性别")
    private int gender;
    @Schema(description = "国家代码")
    private String countryCode;
    @Schema(description = "认证标签列表VOs")
    private List<AttestationTagInfoVO> tagPicInfos;
    @Schema(description = "用户等级信息")
    private UserLevelBaseVO userLevel;
}
