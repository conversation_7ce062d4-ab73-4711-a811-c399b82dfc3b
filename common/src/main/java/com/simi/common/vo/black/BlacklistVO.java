package com.simi.common.vo.black;

import com.simi.common.dto.user.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

@Data
@Schema(description = "拉黑vo")
public class BlacklistVO {

    @Schema(description = "id")
    private Long id;

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "被拉黑uid")
    private Long targetUid;

    @Schema(description = "拉黑vo")
    private Date createTime;

    @Schema(description = "被封禁用户信息")
    private UserBaseInfoDTO targetUserInfo;
}