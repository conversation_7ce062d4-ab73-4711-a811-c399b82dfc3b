package com.simi.common.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-07 16:26
 **/
@Data
@Schema(description = "活动资源")
@AllArgsConstructor
@NoArgsConstructor
public class ActivityResourcesResp {
    private Integer id;
    private String activityNameEn; // 活动名称（英文）
    private String activityNameAr; // 活动名称（阿拉伯语）
    private String activityImageUrl; // 活动图片URL
    private Integer isShowButton; // 是否显示按钮（0: 不显示, 1: 显示）
    private Integer buttonDirection; // 按钮方向
    private String buttonImageUrl; // 按钮图片URL
    private String buttonTextEn; // 按钮文案（英文）
    private String buttonTextAr; // 按钮文案（阿拉伯语）
    private String buttonLink; // 按钮挑战链接
    private String buttonColor;
}
