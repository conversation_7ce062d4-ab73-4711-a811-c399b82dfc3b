package com.simi.common.vo.resp;

import com.simi.common.constant.FollowRelation;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.room.RoomInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * 个人主页vo
 *
 * <AUTHOR>
 * @date 2024/03/27 11:51
 **/
@Data
@Builder
@Schema(description = "个人主页vo信息")
public class HomepageVO {

    @Schema(description = "用户信息")
    private UserBaseInfoDTO userBaseInfo;

    @Schema(description = "用户在房基础信息")
    private RoomInfoDTO userInRoomInfo;

    @Schema(description = "关注数量")
    private Integer followingNum;

    @Schema(description = "被关注数量")
    private Integer followerNum;

    @Schema(description = "用户房间id")
    private String roomId;

    @Schema(description = "用户收礼值")
    private Long receiveGiftValue;

    @Schema(description = "访客数量")
    private Integer visitorNum;

    @Schema(description = "关注关系: 0-没有关系; 1-关注; 2-被关注,粉丝; 3-互关")
    private FollowRelation userRelation;

    @Schema(description = "个人照片")
    private List<PrivatePhotoVO> privatePhotoVOS;

    @Schema(description = "黑名单消息")
    private String blackMagJson;

    @Schema(description = "用户所在房间id")
    private String inRoomId;

    @Schema(description = "用户房间在麦人数")
    private Integer roomInMicNum;
}
