package com.simi.common.vo.agent;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AgentDataResp {

    /**
     * 钻石
     */
    private Integer diamond = 0;

    /**
     * 邀请人数
     */
    private Integer userNum = 0;

    /**
     * 主播人数
     */
    private Integer hostNum = 0;

    /**
     * 代理人数
     */
    private Integer agentNum = 0;

    /**
     * 邀请数据列表
     */
    private List<InviteDataResp> inviteDataRespList = new ArrayList<>();

    /**
     * 主播数据列表
     */
    private List<HostDataResp> hostDataRespList = new ArrayList<>();

    /**
     * 代理数据列表
     */
    private List<AgentData2Resp> AgentDataRespList = new ArrayList<>();


    /**
     * 申请数据列表
     */
    private List<ApplyDataResp> applyDataRespList = new ArrayList<>();


    @Data
    public static class InviteDataResp {

        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 激活奖励
         */
        private Integer diamond = 0;

        /**
         * 充值奖励
         */
        private Integer diamond2 = 0;

    }

    @Data
    public static class HostDataResp {
        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 收益分佣
         */
        private Integer diamond = 0;

        /**
         * 收益
         */
        private Integer diamond2 = 0;
    }

    @Data
    public static class AgentData2Resp {
        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 收益分佣
         */
        private Integer diamond = 0;

        /**
         * 收益
         */
        private Integer diamond2 = 0;
    }

    @Data
    public static class ApplyDataResp {
        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 日期-精准到毫秒
         */
        private Long time;

        /**
         * 0-等待，1-接受，2-拒绝，3-超时
         */
        private Integer status;

        /**
         *  身份类型 1代理 2主播 0默认   【客户端根据type+channel=组合完成需求写的状态】
         */
        private Integer type;

        /**
         * 申请或邀请渠道：1官方邀请、2上级代理邀请、3 自己申请  【客户端根据type+channel=组合完成需求写的状态】
         */
        private Integer channel;
    }
}
