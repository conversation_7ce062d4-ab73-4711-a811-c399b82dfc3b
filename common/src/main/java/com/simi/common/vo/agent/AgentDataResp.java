package com.simi.common.vo.agent;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class AgentDataResp {

    /**
     * 钻石
     */
    private Integer diamond = 0;

    /**
     * 邀请人数
     */
    private Integer userNum = 0;

    /**
     * 主播人数
     */
    private Integer hostNum = 0;

    /**
     * 代理人数
     */
    private Integer agentNum = 0;

    /**
     * 邀请数据列表
     */
    private List<InviteDataResp> inviteDataRespList = new ArrayList<>();

    /**
     * 主播数据列表
     */
    private List<HostDataResp> hostDataRespList = new ArrayList<>();

    /**
     * 代理数据列表
     */
    private List<AgentData2Resp> AgentDataRespList = new ArrayList<>();


    /**
     * 申请数据列表
     */
    private List<ApplyDataResp> applyDataRespList = new ArrayList<>();


    @Data
    public static class InviteDataResp {

        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 激活奖励
         */
        private Integer diamond = 0;

        /**
         * 充值奖励
         */
        private Integer diamond2 = 0;

    }

    @Data
    public static class HostDataResp {
        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 收益分佣
         */
        private Integer diamond = 0;

        /**
         * 收益
         */
        private Integer diamond2 = 0;
    }

    @Data
    public static class AgentData2Resp {
        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 收益分佣
         */
        private Integer diamond = 0;

        /**
         * 收益
         */
        private Integer diamond2 = 0;
    }

    @Data
    public static class ApplyDataResp {
        /**
         * 用户id
         **/
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         **/
        private String avatar;

        /**
         * 性别:1-男，2-女,0-未知
         **/
        private int gender;

        /**
         * 身份 1代理 2主播 0默认
         */
        private Integer type;

        /**
         * 日期-精准到毫秒
         */
        private Long time;

        /**
         * 0-已发送，1-已接受，2-已拒绝，3-超时失效'
         */
        private Integer status;
    }
}
