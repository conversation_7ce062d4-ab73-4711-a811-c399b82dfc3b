package com.simi.common.vo.vip;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-29 13:54
 **/
@Data
@Schema(description = "档位信息")
public class GradeVO {
    /**
     * 主键ID
     */
    @Schema(description = "主键ID")
    private Integer id;

    /**
     * 价格
     */
    @Schema(description = "价格")
    private int price;

    /**
     * 天数
     */
    @Schema(description = "天数")
    private int day;
}
