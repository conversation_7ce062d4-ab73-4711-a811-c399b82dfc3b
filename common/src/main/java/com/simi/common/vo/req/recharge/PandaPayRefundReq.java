package com.simi.common.vo.req.recharge;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/09/03 20:24
 **/
@Data
@Schema(description = "充值req")
public class PandaPayRefundReq {
    @NotBlank
    private String externalId;
    @NotBlank
    private String name;
    @NotBlank
    private String phoneNumber;
    private boolean block = false;
    private Long blockUid;
}
