package com.simi.common.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "基础分页req")
public class BasePageReq {
    @Schema(description = "页码")
    @NotNull(message = "页码不能为空")
    private Integer pageNum = 1;
    @Schema(description = "页大小")
    @NotNull(message = "页大小不能为空")
    private Integer pageSize = 10;

}

