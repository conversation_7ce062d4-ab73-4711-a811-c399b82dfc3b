package com.simi.common.vo.medal;

import cn.hutool.core.util.StrUtil;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * 用户勋章
 *
 * <AUTHOR>
 * @date 2024/08/19 18:00
 **/
@Data
@Builder
@Schema(description = "用户勋章VO")
public class UserHaveMedalVO {

    @JsonProperty("id")
    private String id;

    @Schema(description = "勋章类型: 1-Achievement; 2-Gift; 3-Special")
    private Integer medalType;

    @Schema(description = "勋章名称（英）")
    private String medalNameEn;

    @Schema(description = "勋章名称")
    private String medalName;

    @Schema(description = "描述（英）")
    private String descEn;

    @Schema(description = "描述（阿）")
    private String desc;

    @Schema(description = "获得时间")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSSZ")
    private Date obtainTime;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "动画")
    private String animation;

    @Schema(description = "过期时间")
    private Date expireTime;

    @Schema(description = "礼物Icon")
    @Builder.Default
    private String giftIcon = StrUtil.EMPTY;

    @Schema(description = "排序[冗余]")
    private Integer sortingOrder;
}
