package com.simi.common.vo.req.privatePhote;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
@Schema(description = "新增相册请求参数")
public class PrivatePhotoItem {

    @Schema(description = "排序号")
    @NotNull(message = "排序号不能为空")
    private Integer sort;

    @Schema(description = "图片id，有则需要填")
    private Long id;

    @Schema(description = "图片url")
    @NotNull(message = "图片url不能为空")
    private String photoUrl;

}
