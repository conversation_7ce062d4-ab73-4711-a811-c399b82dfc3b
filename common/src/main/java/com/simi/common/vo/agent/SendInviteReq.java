package com.simi.common.vo.agent;

import lombok.Data;

@Data
public class SendInviteReq {
    /**
     * 邀请的用户id
     */
    private Long userId;

    /**
     * 验证码 - inviteType==2 不能为空  inviteType=1 为空
     */
    private Integer code;

    /**
     * 邀请类型 1代理 2主播
     */
    private Integer type;

    /**
     * 邀请渠道：1官方邀请、2上级代理邀请、3申请申请
     */
    private Integer channel;

    /**
     * 1 APP  2端外
     */
    private Integer inviteType;

}
