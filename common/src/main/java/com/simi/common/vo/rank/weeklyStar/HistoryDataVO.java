package com.simi.common.vo.rank.weeklyStar;

import com.simi.common.vo.UserSimpleVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-15 19:09
 **/
@Data
@Schema(description = "历史数据")
public class HistoryDataVO {

        @Schema(description = "开始时间")
        private String startTime;
        @Schema(description = "结束时间")
        private String endTime;
        @Schema(description = "TOP列表")
        private List<TopInfo> topList;

        @Data
        @Schema(description = "TOP")
        public static class TopInfo{
                @Schema(description = "奖励图")
                private UserSimpleVO user;
                @Schema(description = "奖励图")
                private String imageUrl;
                @Schema(description = "数量")
                private Integer count;
        }
}
