package com.simi.common.vo.resp;

import lombok.Data;

import java.util.Date;

/**
 * @description:
 * @author: lrq
 * @create: 2024-10-14 16:08
 **/
@Data
public class BackpackGiftRecordVO {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 赠送者ID
     */
    private Long grantorId;

    private String grantorName;

    /**
     * 接受者ID
     */
    private Long receiverId;

    private String receiverName;
    /**
     * 物品ID
     */
    private Integer propId;

    /**
     * 物品名称
     */
    private String propName;
    /**
     * 图片
     */
    private String icon;
    /*
     * 物品类型
     */
    private Integer propType;

    /**
     * 背包ID
     */
    private Long backpackId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 装扮类型名称
     */
    private String propTypeName;
}
