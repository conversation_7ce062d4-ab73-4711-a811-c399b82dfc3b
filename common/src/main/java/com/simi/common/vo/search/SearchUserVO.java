package com.simi.common.vo.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.simi.common.util.OssUrlUtil;
import com.simi.common.vo.resp.UserLevelBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/04/10 15:55
 **/
@Data
@Builder
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "检索用户vo")
public class SearchUserVO {

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "用户ID")
    private Long userNo;

    @Schema(description = "昵称")
    private String nick;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "性别:1-男; 2-女")
    private Byte gender;

    private Integer seqNo;

    @Schema(description = "用户等级信息对象VO")
    private UserLevelBaseVO userLevel;

    @Schema(description = "是否靓号，true为是，false为否，默认否flase")
    private Boolean hasPrettyNo;

    public void setAvatar(String avatar) {
        this.avatar = OssUrlUtil.jointUrl(avatar);
    }
}
