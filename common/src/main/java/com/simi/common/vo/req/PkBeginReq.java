package com.simi.common.vo.req;

import com.simi.common.dto.pk.PkUserIdentity;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/13 17:01
 **/
@Data
@Schema(description = "发起pk-req")
public class PkBeginReq {

    @Schema(description = "房间id")
    private String roomId;
    @Schema(description = "pk时间(分钟)")
    @Min(3)
    @Max(60)
    private Integer duration;
    @Schema(description = "pk用户uid列表")
    private List<PkUserIdentity> pkUserList;
}
