package com.simi.common.vo.aristocracy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-29 13:52
 **/
@Data
@Schema(description = "贵族等级")
public class SendAristocracyVO {
    private List<Info> infos;

    /**
     * 用户信息，为空则不拥有
     */
    @Data
    public static class Info {
        @Schema(description = "贵族Id")
        private Integer aristocracyId;
        @Schema(description = "ICON")
        private String icon;
        @Schema(description = "enName")
        private String enName;
        @Schema(description = "arName")
        private String arName;
        @Schema(description = "天数")
        private int day;
        @Schema(description = "限制数量")
        private int limitCount;
        @Schema(description = "已经赠送次数")
        private int sendCount;
    }
}
