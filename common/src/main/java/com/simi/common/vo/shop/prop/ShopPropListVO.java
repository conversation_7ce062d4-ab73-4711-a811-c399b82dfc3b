package com.simi.common.vo.shop.prop;

import com.simi.common.vo.PageResultVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Collections;
import java.util.List;

@Data
@Schema(description = "商城装扮商品集合")
public class ShopPropListVO extends PageResultVO {
    @Schema(description = "装扮商品列表")
    private List<ShopPropItem>  shopPropList;

    public static ShopPropListVO getNullResultVO(){
        ShopPropListVO shopPropListVO =  new ShopPropListVO();
        shopPropListVO.setShopPropList(Collections.EMPTY_LIST);
        shopPropListVO.setTotalPage(0);
        shopPropListVO.setPageSize(0);
        shopPropListVO.setTotalSize(0);
        shopPropListVO.setCurrentPage(0);
        return shopPropListVO;
    }
}
