package com.simi.common.vo.bd;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AnchorPerformanceExportModel {
    @ExcelProperty(value = "时间",index = 0)
    private String performanceMonth;

    @ExcelProperty(value = "主播ID",index = 1)
    private String anchorUid;

    @ExcelProperty(value = "主播昵称",index = 2)
    private String anchorNick;

    @ExcelProperty(value = "所属BD",index = 3)
    private String bdUid;

    @ExcelProperty(value = "所属公会长",index = 4)
    private String guildUid;

    @ExcelProperty(value = "绑定时间",index = 5)
    private String bindingTimeDesc;

    @ExcelProperty(value = "业绩",index = 6)
    private String performance;
}