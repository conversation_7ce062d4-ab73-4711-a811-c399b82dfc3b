package com.simi.common.vo.room;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.simi.common.dto.room.RoomPartyTagDTO;
import com.simi.common.util.OssUrlUtil;
import com.simi.common.vo.UserSimpleVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/30 15:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "房间partyVO")
public class RoomPartyVO {

    @JsonProperty("partyId")
    @Schema(description = "partyId")
    private Long id;

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "roomId")
    private String roomId;

    @Schema(description = "图片url")
    private String picUrl;

    @Schema(description = "活动主题")
    private String topic;

    @Schema(description = "活动描述")
    private String description;

    @Schema(description = "活动时长-分钟")
    private Integer duration;

    @Schema(description = "开始时间")
    private Date beginTime;

    @Schema(description = "结束时间")
    private Date endTime;

    @Schema(description = "订阅人数")
    private Long subscribeNum;

    @Schema(description = "是否上架: 1-是 0-否")
    private Integer enable;

    @Schema(description = "标签，逗号分割")
    private String tagIds;

    @Schema(description = "修改版本")
    private Integer version;

    @Schema(description = "创建时间")
    private Date createTime;

    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "创建人详情")
    private UserSimpleVO createUserInfo;

    @Schema(description = "在房人数")
    private Long onlineNum;

    @Schema(description = "是否取消")
    private Boolean cancle;

    @Schema(description = "状态: 1-进行中; 2-未开始; 3-已结束; 4-已取消")
    private Integer status;

    @Schema(description = "是否订阅")
    private Boolean isSubscribe;

    @Schema(description = "标签信息列表")
    private List<RoomPartyTagDTO> partyTags;

    @Schema(description = "订阅前N")
    private List<UserSimpleVO> subscribeUserList;

    public void setPicUrl(String picUrl) {
        this.picUrl = OssUrlUtil.jointUrl(picUrl);
    }

}
