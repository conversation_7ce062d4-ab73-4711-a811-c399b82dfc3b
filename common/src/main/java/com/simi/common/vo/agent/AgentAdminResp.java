package com.simi.common.vo.agent;

import lombok.Data;

import java.util.Date;

@Data
public class AgentAdminResp {

    /**
     * 当前代理信息
     */
    public AgentUserResp agentUserResp1;

    /**
     * 上一级代理信息
     */
    public AgentUserResp agentUserResp12;


    @Data
    public static class AgentUserResp {
        /**
         * uid
         */
        private Long uid;

        /**
         * 昵称
         **/
        private String nick;

        /**
         * 头像
         */
        private String avatar;

        /**
         * 性别
         */
        private Byte gender;

        /**
         * 国家
         */
        private String country;

        /**
         * 成为代理时间
         */
        private Date becomeAgentTime;
    }
}
