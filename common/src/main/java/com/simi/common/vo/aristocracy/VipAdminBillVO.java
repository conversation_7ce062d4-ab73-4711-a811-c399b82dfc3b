package com.simi.common.vo.aristocracy;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-30 17:01
 **/
@Data
public class VipAdminBillVO {
    private long userNo;
    // 剩余时间
    @Schema(description = "天数")
    private Integer day;
    @Schema(description = "类型 0 经验值 1 账单")
    private Integer type;
    @Schema(description = "source")
    private Integer source;
    @Schema(description = "经验值")
    private Integer exp;
    @Schema(description = "level")
    private Integer level;
    @Schema(description = "赠送人对象ID")
    private Long giveUserNo;
    @Schema(description = "创造时间")
    private Long createTime;
}
