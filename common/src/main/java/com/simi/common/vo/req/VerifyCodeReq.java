package com.simi.common.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "校验验证码")
public class VerifyCodeReq {

    @Schema(description = "手机号码")
    private String code;
    @Schema(description = "验证码")
    private String smsCode;
    @Schema(description = "国家编码")
    private String areaCode;
}
