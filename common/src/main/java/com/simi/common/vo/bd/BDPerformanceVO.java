package com.simi.common.vo.bd;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BDPerformanceVO {
    @Schema(description = "列表")
    private List<BDPerformanceItem>  bdPerformanceItems;

    @Schema(description = "总业绩")
    private BigDecimal totalPerformance;

    @Schema(description = "总收入")
    private BigDecimal totalIncome;

    @Schema(description = "总数量")
    private Long totalSize;

    public static BDPerformanceVO empty(){
        return BDPerformanceVO.builder()
                .bdPerformanceItems(Collections.EMPTY_LIST)
                .totalSize(0L)
                .totalPerformance(BigDecimal.ZERO)
                .totalIncome(BigDecimal.ZERO)
                .build();
    }
}
