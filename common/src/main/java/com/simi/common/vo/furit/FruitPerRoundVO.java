package com.simi.common.vo.furit;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-30 11:41
 **/
@Data
public class FruitPerRoundVO {
    private long id; // 局id
    private String fruit; // 当局开奖的水果id
    private List<Winner> winner; // 赢的用户组
    private List<Long> plays; // 当局下注的用户组

    @Data
    public static class Winner {
        private long userId; // 用户ID
        private int coin; // 赢得的金币
    }
}
