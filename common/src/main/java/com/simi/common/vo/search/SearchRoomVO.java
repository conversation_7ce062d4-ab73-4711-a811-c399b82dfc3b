package com.simi.common.vo.search;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.simi.common.util.OssUrlUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/04/10 15:55
 **/
@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
@Schema(description = "检索房间vo")
public class SearchRoomVO {

    @Schema(description = "roomId")
    private String roomId;

    @Schema(description = "房主uid")
    private Long roomUid;

    @Schema(description = "房间no")
    private Long roomNo;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "房间类型")
    private Integer roomTypeValue;

    @Schema(description = "国家简称")
    private String countryCode;

    @Schema(description = "是否靓号，true为是，false为否，默认否flase")
    private Boolean hasPrettyNo;

    public void setAvatar(String avatar) {
        this.avatar = OssUrlUtil.jointUrl(avatar);
    }
}
