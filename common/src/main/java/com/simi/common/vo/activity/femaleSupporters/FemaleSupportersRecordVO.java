package com.simi.common.vo.activity.femaleSupporters;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * 女性支持者领取记录
 *
 * <AUTHOR>
 * @date 2024/06/06 11:46
 **/
@Data
@Builder
@Schema(description = "女性支持者活动VO")
public class FemaleSupportersRecordVO {
    @Schema(description = "领取状态: true-已领取; false-未领取")
    private boolean status;
    @Schema(description = "展示开始时间")
    private String beginTime;
    @Schema(description = "冗余, 领奖时回传")
    private String beginTimeBak;
    @Schema(description = "展示结束时间")
    private String endTime;
    @Schema(description = "奖励金币")
    private Long goldCoin;
    @Schema(description = "冗余")
    private Integer rewardpackId;
}
