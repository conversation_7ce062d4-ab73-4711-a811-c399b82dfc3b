package com.simi.common.vo.req;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 获取token参数
 *
 * <AUTHOR>
 * @date 2023/11/1 16:19
 */
@Data
@Builder
public class TokenParam extends BaseReq {

    private String code;
    @JsonProperty("client_id")
    private String clientId;
    @JsonProperty("client_secret")
    private String clientSecret;
    @JsonProperty("grant_type")
    @Builder.Default
    private String grantType = "authorization_code";
    @JsonProperty("redirect_uri")
    private String redirectUri;

    private String scope = "";

}
