package com.simi.common.vo.redpacket;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


/**¨Ø
 * <AUTHOR>
 * @date 2024/07/30 15:34
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "红包信息")
public class RedPacketVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "结束时间戳")
    private Long deadline;

    @Schema(description = "秒数")
    private Long remainingTime;

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "头像")
    private String avatar;

    @Schema(description = "昵称")
    private String nickName;

    private Byte gender;

    private String waveEffectUrl;

    private String staticAvatarFrameUrl;
}
