package com.simi.common.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PropInfoReq {

    private Long id;

    @Schema(description = "道具类型")
    private Integer type;

    @Schema(description = "名称(英语)")
    private String nameEn;

    @Schema(description = "名称(阿语)")
    private String nameAr;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "特效地址")
    private String animationUrl;

    @Schema(description = "特效类型")
    private Integer animationType;

    @Schema(description = "商场排序")
    @NotNull(message = "商场排序不能为空")
    private Integer weight;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "操作人id")
    private Integer operatorId;

    @Schema(description = "方向 1、上 2、下 3、左 4、右")
    private Integer direction;

    @Schema(description = "记录创建用户名")
    private String operator;

    @Schema(description = "循环url")
    private String circulationUrl;

    @Schema(description = "价格明细")
    @NotNull(message = "价格明细不能为空")
    private List<PropPriceDetailInfo> priceDetailList;

    @Schema(description = "是否标记为新品，0-否，1-是")
    private Integer markNewProduct;

    @Schema(description = "国家组id")
    private String groupIds;
}
