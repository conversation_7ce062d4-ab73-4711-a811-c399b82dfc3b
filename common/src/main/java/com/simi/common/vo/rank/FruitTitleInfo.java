package com.simi.common.vo.rank;

import com.simi.common.vo.UserSimpleVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 2024宰牲节活动页信息
 * <AUTHOR>
 * @date 2024/06/11 19:55
 **/
@Data
@Schema(description = "水果的冠名信息")
public class FruitTitleInfo {
    // 各种水果的冠名信息
    @Schema(description = "各种水果的冠名信息")
    private List<FruitDay> fruitDays;
    // 总榜用户
    @Schema(description = "总榜用户")
    private UserSimpleVO winner;

    @Data
    public static class FruitDay{
        private String fruit;
        private UserSimpleVO userSimpleVO;
    }
}
