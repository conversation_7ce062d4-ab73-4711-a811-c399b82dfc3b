package com.simi.common.vo.login;


import com.simi.common.constant.user.LoginType;
import com.simi.common.constant.user.UserStatusEnum;
import com.simi.common.dto.user.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "登录实体类")
public class LoginResp implements Serializable {

    @Schema(description = "平台token")
    private String token;
    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "用户状态【1：正常；2：注销；3：未完善资料；】")
    private UserStatusEnum status;
    @Schema(description = "公共频道")
    private String pubChannel;
    @Schema(description = "登录方式")
    private LoginType loginType;
    @Schema(description = "用户编号")
    private Long userNo;
    @Schema(description = "用户相关")
    private UserBaseInfoDTO userBaseInfo;
}
