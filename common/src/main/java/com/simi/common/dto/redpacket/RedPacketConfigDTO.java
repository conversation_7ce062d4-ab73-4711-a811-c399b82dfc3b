package com.simi.common.dto.redpacket;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/09/12 15:45
 **/
@Data
@Builder
public class RedPacketConfigDTO {

    @Builder.Default
    private boolean visible = true;
    private String rulesUrlEn;
    private String rulesUrlAr;
    private Config room;
    private Config world;

    @Data
    public static class Config {
        private List<Configuration> configuration;
        private List<Integer> countdown;
    }

    @Data
    public static class Configuration {
        private int goldQuantiry;
        private List<Integer> numberOfRecipients;
    }

}
