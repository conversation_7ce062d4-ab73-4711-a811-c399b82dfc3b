package com.simi.common.dto;

import com.simi.common.dto.user.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class MineResp {

    @Schema(description = "用户信息")
    private UserBaseInfoDTO userBaseInfo;
    @Schema(description = "关注数")
    private Integer followingNum;
    @Schema(description = "粉丝数")
    private Integer followerNum;
    @Schema(description = "访客数")
    private Integer visitorNum;
    @Schema(description = "收礼总价值")
    private Long receiveGiftValue;


}
