package com.simi.common.dto.room;

import lombok.Data;

/**
 * 退出房间回调
 * https://doc-zh.zego.im/article/13767
 * @Author: Andy
 * @Date: 2023-12-23
 */
@Data
public class ZegoRoomLogoutParam {
    private String event;
    private String appid;
    private String timestamp;
    private String nonce;
    private String signature;

    private String room_id;
    private String room_seq;
    private String user_account;
    private String user_nickname;
    private String session_id;
    private String login_time;
    private String logout_time;
    private String reason;
    private String user_role;
    private String user_update_seq;
}
