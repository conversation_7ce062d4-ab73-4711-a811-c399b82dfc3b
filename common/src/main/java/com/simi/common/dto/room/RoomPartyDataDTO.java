package com.simi.common.dto.room;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/07/31 10:30
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "房间party数据统计信息")
public class RoomPartyDataDTO {
    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "roomId")
    private Long roomId;
    @Schema(description = "开始时间")
    private Date beginTime;
    @Schema(description = "活动时长-分钟")
    private Integer duration;
    @Schema(description = "最高在线")
    private Integer highestOnline;
    @Schema(description = "官方奖励金币")
    private Long rewardGold;
    @Schema(description = "官方奖励比例")
    private String rewardRatio;
    @Schema(description = "官方奖励等级")
    private String rewardLevel;
    @Schema(description = "总收礼")
    private Long receiveTotalVal;
    @Schema(description = "总送礼")
    private Long sendTotalVal;
}
