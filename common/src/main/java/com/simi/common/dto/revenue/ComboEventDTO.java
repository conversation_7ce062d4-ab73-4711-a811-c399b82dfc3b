package com.simi.common.dto.revenue;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ComboEventDTO {
    private String comboId;
    private Long uid;
    private List<Long> targetUids;
    private String roomId;
    private Integer sendType;
    private Integer giftId;
    private Integer giftNum;
    private Long timestamp;
}
