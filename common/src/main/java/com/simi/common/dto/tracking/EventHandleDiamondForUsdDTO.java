package com.simi.common.dto.tracking;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 钻石转
 */
@Data
public class EventHandleDiamondForUsdDTO extends EventAdCommonDTO{

    @SerializedName("qey_diamond")
    private Long qeyDiamond;

    @SerializedName("qty_usd")
    private Long qtyUsd;

    @SerializedName("exchange_state")
    private Boolean exchangeState;

    @SerializedName("uid")
    private String uid;

    @SerializedName("#device_id")
    private String deviceId;
}
