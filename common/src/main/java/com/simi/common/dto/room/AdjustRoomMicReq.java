package com.simi.common.dto.room;


import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "修改麦位请求")
public class AdjustRoomMicReq {

    @Schema(description = "房间id")
    private String roomId;

    @Schema(description = "麦位类型 1 - 10麦位、 2 - 12麦位、 3 - 15麦位 4 - 20麦位")
    private Integer micType;
}
