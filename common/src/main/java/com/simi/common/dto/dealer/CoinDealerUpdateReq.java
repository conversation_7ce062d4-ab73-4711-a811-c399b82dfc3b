package com.simi.common.dto.dealer;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "修改币商请求")
public class CoinDealerUpdateReq {


    @Schema(description = "币商")
    private Long uid;

    @Schema(description = "备注")
    private String remark;
}
