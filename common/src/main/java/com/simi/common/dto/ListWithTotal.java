package com.simi.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/11/14 13:41
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "分页响应")
public class ListWithTotal<T> {

    @Schema(description = "数据总条数")
    private long total = 0L;

    @Schema(description = "数据列表")
    private List<T> list;

    public static <U> ListWithTotal<U> empty(){
        return ListWithTotal.<U>builder().list(Collections.emptyList()).total(0).build();
    }
}
