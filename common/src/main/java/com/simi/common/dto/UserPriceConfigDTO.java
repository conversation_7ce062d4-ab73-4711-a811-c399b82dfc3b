package com.simi.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户钻石兑换白名单")
public class UserPriceConfigDTO {

    private Long id;

    private String configKey;

    private Long uid;

    private Long userNo;

    private String nick;

    private String remark;

    private Long currentPrice;

    private Date createTime;
}
