package com.simi.common.dto.rank;

import com.simi.common.constant.rank.RankFrequencyEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 *
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Builder
public class RankKeyParam {

    /**
     * key前缀
     */
    private String keyPrefix;

    /**
     * rankKey需要额外拼的参数，比如roomId，收礼人uid
     */
    private String keyParam;

    /**
     *
     */
    private long timeMillis;

    /**
     *
     */
    private RankFrequencyEnum rankFrequencyEnum;


    /**
     * 榜单周期偏移量：0、代表当前周期(比如当天日榜)；-1、代表前1个周期(比如昨日榜)；-2、代表前2个周期(比如前日榜)
     */
    private int rankOffset;

}
