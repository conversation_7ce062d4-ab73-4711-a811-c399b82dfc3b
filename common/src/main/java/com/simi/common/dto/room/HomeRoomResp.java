package com.simi.common.dto.room;

import com.simi.common.dto.TranslationCopyDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "首页房间")
public class HomeRoomResp {

    @Schema(description = "房间id")
    private String roomId;
    @Schema(description = "房间编号")
    private Long roomNo;
    @Schema(description = "封面")
    private String cover;
    @Schema(description = "标题")
    private String title;
    @Schema(description = "在房人数")
    private long online;
    @Schema(description = "房主国籍")
    private String country;
    @Schema(description = "房主id")
    private Long uid;
    @Schema(description = "房间类型 1、普通")
    private Byte type;
    @Schema(description = "房间类型文案")
    private String typeName;
    @Schema(description = "房间类型文案对象")
    private TranslationCopyDTO typeNameVO;
    @Schema(description = "房间观众前五")
    private RoomAudienceDTO audienceTop5;
    @Schema(description = "热度值")
    private Integer hot = 0;
}
