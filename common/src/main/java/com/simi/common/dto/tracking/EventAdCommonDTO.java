package com.simi.common.dto.tracking;

import com.google.gson.annotations.SerializedName;
import com.simi.common.dto.DeviceDTO;
import lombok.Data;

/**
 * 事件广告公共属性
 */
@Data
public class EventAdCommonDTO extends DeviceDTO {

  /**
   * 广告id
   */
  @SerializedName("adid")
  private String adId;
  /**
   * 广告组名称
   */
  @SerializedName("adgroup")
  private String adGroup;
  /**
   * 活动名称
   */
  @SerializedName("campaign")
  private String campaign;
  /**
   * 安装标记标签安装成本
   */
  @SerializedName("click_label")
  private String clickLabel;
  /**
   * 安装成本
   */
  @SerializedName("cost_amount")
  private Double costAmount;
  /**
   * 安装成本货币
   */
  @SerializedName("cost_currency")
  private String costCurrency;
  /**
   * 活动定价模型
   */
  @SerializedName("cost_type")
  private String costType;
  /**
   * 创意名称
   */
  @SerializedName("creative")
  private String creative;
  /**
   * 网络名称
   */
  @SerializedName("ad_network")
  private String adNetwork;
  /**
   * 跟踪器名称
   */
  @SerializedName("tracker_name")
  private String trackerName;
  /**
   * 跟踪器令牌
   */
  @SerializedName("tracker_token")
  private String trackerToken;
  /**
   * Facebook安装引荐人
   */
  @SerializedName("fbInstall_referrer")
  private String fbInstallReferrer;
  /**
   * 是否新设备
   */
  @SerializedName("is_new")
  private Boolean isNew;
}
