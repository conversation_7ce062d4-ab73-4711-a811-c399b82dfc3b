package com.simi.common.dto.recharge;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/09/03 22:22
 **/
@Data
public class PaydaPayOrderDetailDTO {

    private Integer code;
    private String description;
    private String businessCode;
    private ContentData data;

    @Data
    public static class ContentData {
        private String externalId;
        private String transactionId;
        private String phoneNumber;
        private String paymentPhoneNumber;
        private String amount;
        private String fee;
        private String status;
        private Date repayTime;
    }
}
