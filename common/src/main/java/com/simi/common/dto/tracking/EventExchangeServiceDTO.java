package com.simi.common.dto.tracking;

import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * 钻石兑换金币_后端
 * 兑换时上报
 * @Date: 2024-03-06
 */
@Data
public class EventExchangeServiceDTO extends EventAdCommonDTO {
    @SerializedName("qty_coin")
    private Long qtyCoin;

    @SerializedName("qty_usd")
    private Long qtyUsd;

    @SerializedName("exchange_state")
    private Boolean exchangeState;

    @SerializedName("uid")
    private String uid;

    @SerializedName("#device_id")
    private String deviceId;
}
