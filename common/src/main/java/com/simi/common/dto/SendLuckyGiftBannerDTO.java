package com.simi.common.dto;

import com.simi.common.dto.gift.GiftInfoDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SendLuckyGiftBannerDTO {

    private UserBaseInfoDTO userBaseInfo;

    private GiftInfoDTO giftInfo;

    private Integer multiply;
    /**
     * icon链接
     */
    private String icon;
    /**
     * 下层
     */
    private String effectUrl;

    /**
     * 上层
     */
    private String upperEffect;

    private String roomId;
}
