package com.simi.common.dto.tencent.callback;

import com.simi.common.dto.tencent.MsgBody;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 单聊前消息回调
 * <AUTHOR>
 * @date 2024/2/23 16:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BeforeSendMsgCallbackBody extends AbstractCallbackBody {

  @JsonProperty("From_Account")
  private String fromAccount;
  @JsonProperty("To_Account")
  private String toAccount;
  @JsonProperty("MsgSeq")
  private Integer msgSeq;
  @JsonProperty("MsgRandom")
  private Long msgRandom;
  @JsonProperty("MsgTime")
  private Integer msgTime;
  @JsonProperty("MsgKey")
  private String msgKey;
  @JsonProperty("OnlineOnlyFlag")
  private Integer onlineOnlyFlag;
  @JsonProperty("MsgBody")
  private List<MsgBody> msgBody;
  @JsonProperty("CloudCustomData")
  private String cloudCustomData;
  @JsonProperty("EventTime")
  private Long eventTime;


}
