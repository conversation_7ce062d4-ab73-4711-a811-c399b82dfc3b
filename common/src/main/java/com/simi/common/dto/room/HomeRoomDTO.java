package com.simi.common.dto.room;

import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.UserPropInfoDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;

/**
 * 首页房间
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "首页房间")
public class HomeRoomDTO {
    @Schema(description = "房间id")
    private String roomId;
    @Schema(description = "房间编号")
    private Long roomNo;
    @Schema(description = "封面")
    private String cover;
    @Schema(description = "标题")
    private String title;
    @Schema(description = "在房人数")
    private long online;
    @Schema(description = "排序使用")
    private BigDecimal score;
    @Schema(description = "房主国籍")
    private String country;
    @Schema(description = "房主id")
    private Long uid;
    @Schema(description = "房间类型 0未开播 1、普通 2、大厅")
    private Byte type;
    @Schema(description = "房间类型文案")
    private String typeName;
    @Schema(description = "房间类型文案对象")
    private TranslationCopyDTO typeNameVO;
    @Schema(description = "房间观众前五")
    private RoomAudienceDTO audienceTop5;
    @Schema(description = "热度值")
    private Integer hot = 0;
    @Schema(description = "是否pk中")
    private Boolean isPk;
    @Schema(description = "房间边框")
    private UserPropInfoDTO roomFrame;
    @Schema(description = "gid")
    private Long gid;
    @Schema(description = "recType")
    private Integer recType;
    @Schema(description = "nameAr")
    private String nameAr;
    @Schema(description = "gid")
    private String nameEn;
    @Schema(description = "是否有红包")
    private boolean thereOneLuckyBox;

    @Schema(description = "热度值(字符串区分)")
    private String hotStr;
    @Schema(description = "用户信息")
    private UserBaseInfoDTO userBaseInfo;

    @Schema(description = "在麦人数")
    private Integer inMicNum = 0;

    private Long createTime;

    public String getHotStr() {
        if (StringUtils.isBlank(hotStr)) {
            return "0";
        }
        return hotStr;
    }
}
