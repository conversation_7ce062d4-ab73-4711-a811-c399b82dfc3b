package com.simi.common.dto.expression;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@TableName(value ="expression_info")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "表情包")
public class ExpressionInfoDTO {


    private Long id;
    @Schema(description = "类型 1、普通 2、随机")
    private Integer type;
    @Schema(description = "分类  表情（暂时不用）")
    private Integer classify;
    @Schema(description = "名称(英文)")
    private String enName;
    @Schema(description = "名称(阿拉伯)")
    private String arName;
    @Schema(description = "静态图片")
    private String staticPic;
    @Schema(description = "动态图片")
    private String dynamicPic;
    @Schema(description = "标签（英语）")
    private String tagEn;
    @Schema(description = "标签（阿语）")
    private String tagAr;
    @Schema(description = "结束静态图片")
    private String endPic;
    @Schema(description = "排序 -1 客户端隐藏")
    private Integer sort;
    @Schema(description = "rsp")
    private String ext;
    @Schema(description = "方向 1、上 2、下 3、左 4、右")
    private Integer direction;
    @Schema(description = "贵族信息 当为空时，则没有贵族限制")
    private AristocracyInfo aristocracyInfo;

    @Data
    public static class AristocracyInfo{
        @Schema(description = "贵族等级")
        private int aristocracyLevel;
        @Schema(description = "图标")
        private String iconUrl;
        @Schema(description = "当前用户等级")
        private int userCurAristocracyLevel;
        @Schema(description = "名称")
        private Map<String, String> nameMap = new HashMap<>();
    }

}
