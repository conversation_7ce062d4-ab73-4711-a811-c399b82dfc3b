package com.simi.common.dto.wheel;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "转盘超时处理参数")
public class LuckyWheelDelayMsgDTO {
    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 类型，0-正常结束幸运转盘游戏，1-超时未开始结束游戏，2每局淘汰
     */
    private int type;

    /**
     * 用户id
     */
    private Long uid;
}
