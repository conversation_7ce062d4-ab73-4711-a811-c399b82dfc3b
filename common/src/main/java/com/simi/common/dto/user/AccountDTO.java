package com.simi.common.dto.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "账号信息")
public class AccountDTO {

    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "0、google 1、facebook 2、mobile 3、google_web 4、facebook_web 5、phone 6、apple")
    private Integer loginType;
    @Schema(description = "是否绑定")
    private Boolean isbanding;

}
