package com.simi.common.dto.tag;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "认证标签")
public class AttestationTagInfoDto {

    @Schema(description = "id")
    private Long id;
    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "userNo")
    private Long userNo;
    //昵称
    @Schema(description = "昵称")
    private String nick;
    //标签（英语）
    @Schema(description = "标签图片（英语）")
    private String tagEn;
    //标签（阿语）
    @Schema(description = "标签图片（阿语）")
    private String tagAr;
    @Schema(description = "adminId")
    private Integer adminId;
    @Schema(description = "createTime")
    private Date createTime;
    @Schema(description = "updateTime")
    private Date updateTime;

    /**
     * 英文描述
     */
    @Schema(description = "英文描述")
    private String descEn;
    /**
     * 阿语描述
     */
    @Schema(description = "阿语描述")
    private String descAr;
    /**
     * 开始时间戳
     */
    @Schema(description = "开始时间戳，毫秒，未设置为空")
    private Long startTime;
    /**
     * 结束时间戳
     */
    @Schema(description = "开始时间戳，毫秒，未设置为空")
    private Long endTime;

    @Schema(description = "状态，-1-全部，1-待生效，2-生效中，3-已失效")
    private Integer tagStatus;

    @Schema(description = "国家组id")
    private String groupIds;
}
