package com.simi.common.dto.infrastructure;

import com.simi.common.dto.user.UserBaseInfoDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.Map;

/**
 * 触发的基础数据
 *
 * @param <T>
 */
@Data
public class TriggerDto<T> implements Serializable {

    /**
     * 例如礼物等等触发条件的数据
     */
    private T data;

    /**
     * data的class
     */
    private Class<T> dataClazz;

    /**
     * 所在直播间
     */
    private String roomId;

    /**
     * 送礼人uid
     */
    private long fromUid;

    /**
     * 收礼人uid
     */
    private long toUid;


    /**
     * 消息产生时间
     */
    private long msgTimeMillis;

    /**
     * 要加的分数
     */
    private long score;

    private String timeDiff;


    private Map<Long, UserBaseInfoDTO> userInfoMap;

    private Long roomUid;
}
