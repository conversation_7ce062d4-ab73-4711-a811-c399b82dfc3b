package com.simi.common.dto;

import lombok.Data;

/***
 *  'gameId' 游戏类型
 *   'uid'  参与玩家id
 *   'avatar'  头像
 *   ’nick'   昵称
 *   ’win'   中奖金额
 *   'multiple' 倍数
 * */
@Data
public class GameAllWinListDTO {

    /**
     * 游戏类型 1水果机
     **/
    private int gameId;

    /**
     * 用户id
     **/
    private long uid;

    /**
     * 头像
     **/
    private String avatar;

    /**
     * 昵称
     **/
    private String nick;

    /**
     * 获取中奖金额
     **/
    private long win;

    /**
     * 倍数
     **/
    private int multiple;

    /**
     * 游戏icon
     **/
    private String icon ;
}
