package com.simi.common.dto.shumei;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: Andy
 * @Date: 2024-01-09
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ShumeiAuditImgReq {
    /**
     * 接口认证密钥
     */
    private String accessKey;

    /**
     * 应用标识
     */
    private String appId;

    /**
     * 事件标识
     */
    private String eventId;

    /**
     * 检测的风险类型
     * 监管一级标签 可选值:
     * POLITY :涉政识别
     * EROTIC :色情&性感违规识别
     * VIOLENT :暴恐&违禁识别
     * QRCODE :二维码识别
     * ADVERT :广告识别
     * IMGTEXTRISK :图片文字违规识别
     * 如果需要识别多个功能，通过下划线连接，如 POLITY_QRCODE_ADVERT 用于涉政、二维码和广告组合识别
     * （该字段与businessType字段必须选择一个传入）
     * 涉政、色情、暴恐只包含了图片本身的违规检测，如需要识别图片里文字的违规内容，务必传入图片文字违规识别功能
     */
    private String type;

    /**
     * 请求的数据内容
     * data字段长度最长10MB
     */
    private ShumeiAuditImgData data;

    /**
     * 回调请求url，传callback表示走异步回调逻辑，否则走同步逻辑，回调http地址字段，当该字段非空时，服务将根据该字段回调通知用户审核结果，地址必须为http或https的规范的url
     */
    private String callback;
}
