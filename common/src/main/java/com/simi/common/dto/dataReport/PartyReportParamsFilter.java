package com.simi.common.dto.dataReport;

import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

public class PartyReportParamsFilter {
    private List<String> filterList;

    public static class Builder{
        private List<String> filterList = new ArrayList<>();

        public Builder(){
        }

        public Builder reportDate(String reportDate){
            if (StringUtils.isNotBlank(reportDate)){
                filterList.add("reportDate = " + reportDate);
            }

            return this;
        }

        public Builder partyId(Long partyId){
            if (Objects.nonNull(partyId)){
                filterList.add("partyId = " + partyId);
            }

            return this;
        }

        public Builder uid(Long uid){
            if (Objects.nonNull(uid)){
                filterList.add("uid = " + uid);
            }

            return this;
        }

        public Builder roomNo(String roomNo){
            if (StringUtils.isNotBlank(roomNo)){
                filterList.add("roomNo = " + roomNo);
            }

            return this;
        }

        public Builder longestBroadcastUid(Long longestBroadcastUid){
            if (Objects.nonNull(longestBroadcastUid)){
                filterList.add("longestBroadcastUid = " + longestBroadcastUid);
            }

            return this;
        }


        public String[] build(){
            return filterList.toArray(new String[filterList.size()]);
        }
    }
}
