package com.simi.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "首充sku")
public class FirstRechargeDTO {

    @Schema(description = "skuId")
    private List<String> skuIdList;
    @Schema(description = "档位顺序")
    private Integer gears;
    @Schema(description = "奖励包id")
    private Integer rewardPackId;
    @Schema(description = "原价")
    private Integer originalPrice;
}
