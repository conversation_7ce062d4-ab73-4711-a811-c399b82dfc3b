package com.simi.common.dto.tracking;

import com.google.gson.annotations.SerializedName;
import com.simi.common.dto.DeviceDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventRoomViewerPeakValueDTO  extends DeviceDTO {

  /**
   * 房间id
   */
  @SerializedName("roomid")
  private Long roomId;

  /**
   * 用户数量
   */
  @SerializedName("viwer_num")
  private Integer viewerNum;
}
