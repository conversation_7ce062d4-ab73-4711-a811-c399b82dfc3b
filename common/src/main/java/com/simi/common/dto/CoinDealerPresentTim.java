package com.simi.common.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class CoinDealerPresentTim {

    private String titleEn;

    private String titleAr;

    private Long uid;

    private Integer digitalCurrency;

    private Long num;

    private String remark;

    private Date operationTimestamp;
}
