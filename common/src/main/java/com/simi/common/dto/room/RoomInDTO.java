package com.simi.common.dto.room;

import com.simi.common.constant.room.RoomIdentity;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "进房参数")
public class RoomInDTO {
    @Schema(description = "房间id")
    private String roomId;
    @Schema(description = "角色 0、ROOM_AUDIENCE 普通观众   1000、管理员 ROOM_MANAGER   2000、房主 ROOM_OWNER ")
    private RoomIdentity identity;
    @Schema(description = "agoraToken")
    private String agoraToken;
    @Schema(description = "longLinkToken")
    private String longLinkToken;
    @Schema(description = "公屏禁言结束时间")
    private Long silenceEndTime;
    @Schema(description = " 0 是关闭， 1 才是打开")
    private int status;
}
