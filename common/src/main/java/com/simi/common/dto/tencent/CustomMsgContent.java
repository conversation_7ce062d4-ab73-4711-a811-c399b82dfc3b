package com.simi.common.dto.tencent;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 自定义消息元素
 * @Author: Andy
 * @Date: 2023/11/22
 */
@Data
@NoArgsConstructor
public class CustomMsgContent {
    /**
     * 自定义消息数据
     */
    @JsonProperty("Data")
    private String Data;
    /**
     * 自定义消息描述信息。当接收方为 iOS 或 Android 后台在线时，做离线推送文本展示。
     */
    @JsonProperty("Desc")
    private String Desc;
    /**
     * 扩展字段。当接收方为 iOS 系统且应用处在后台时，此字段作为 APNs 请求包 Payloads 中的 Ext 键值下发
     */
    @JsonProperty("Ext")
    private String Ext;
    /**
     * 自定义 APNs 推送铃音
     */
    @JsonProperty("Sound")
    private String Sound;

    public CustomMsgContent(String data) {
        Data = data;
    }
}
