package com.simi.common.dto.user.search;

import lombok.Data;


@Data
public class SearchAdminUserInfoAdapterDTO {

    /**
     * 用户UID
     */
    private Long uid;

    /**
     * 用户id,靓号
     */
    private Long userNo;

    /**
     * 状态: 0-全部; 1-正常; 2-注销; 3-注册待完善
     */
    private Integer status;

    /**
     * 昵称
     */
    private String nick;

    /**
     * 性别:0-未明确;1-男; 2-女
     */
    private Integer gender;

    /**
     *  手机系统
     */
    private String os;

    /**
     * 注册方式 0-手机; 1-facebook; 2-google; 3-mobile_value
     */
    private Integer signType;

    /**
     * 是否封禁，0-否，1-是
     */
    private Integer blocked;


    /**
     * uid封禁，0-否，1-是
     */
    private Integer uidBlocked;

    /**
     * did封禁，0-否，1-是
     */
    private Integer didBlocked;

    /**
     * ip封禁，0-否，1-是
     */
    private Integer ipBlocked;

    /**
     * 地区编码
     */
    private String countryCode;

    /**
     * 支付金币
     */
    private Long payGoldAmount;

    /**
     * 支付金额
     */
    private Double payAmount;


    /**
     * 注册完成时间戳，秒级
     */
    private Integer signTimestamp;


    /**
     * 活跃用户，0-否，1-是
     */
    private Integer activeUser;


    /**
     * 最后一次活跃时间戳，秒级
     */
    private Integer activeTimestamp;

    /**
     * 币商，0-否，1-是
     */
    private Integer coinDealer;

    /**
     * 工会长，0-否，1-是
     */
    private Integer guildMaster;

    /**
     * 主播，0-否，1-是
     */
    private Integer anchor;

    /**
     * 贵族等级
     */
    private Integer aristocracyLevel;

    /**
     * 贵族等级有效期
     */
    private Integer aristocracyLevelEndTimestamp;

    /**
     * 财富等级
     */
    private Integer wealthLevel;

    /**
     * 财富等级值
     */
    private Long wealthLevelAmount;

    /**
     * 魅力等级
     */
    private Integer charmLevel;

    /**
     * 魅力等级值
     */
    private Long charmLevelAmount;


    /**
     * 财富等级
     */
    private Integer activeLevel;

    /**
     * 魅力等级值
     */
    private Long activeLevelAmount;


    /**
     * 来源
     */
    private String source;
}
