package com.simi.common.dto.wheel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "超时取消转盘参数")
public class LuckyWheelCancelContentDTO {
    @Schema(description = "取消公平英文")
    private String cancelContentEn;

    @Schema(description = "取消公平中文")
    private String cancelContentAr;


    @Schema(description = "房间id")
    private String roomId;

    @Schema(description = "转盘游戏id")
    private Long wheelId;
}
