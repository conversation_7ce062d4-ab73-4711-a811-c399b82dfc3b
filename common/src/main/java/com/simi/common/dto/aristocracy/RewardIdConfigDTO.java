package com.simi.common.dto.aristocracy;

import lombok.Data;

import java.util.List;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-30 17:43
 **/
@Data
public class RewardIdConfigDTO {
    /**
     * The ID of the aristocracy.
     */
    private int aristocracyId;

    /**
     * The list of grades for the aristocracy.
     */
    private List<GradeVO> grades;

    /**
     * Value Object for the grade configuration.
     */
    @Data
    public static class GradeVO {
        /**
         * The ID of the grade.
         */
        private Integer id;

        /**
         * The ID of the package (backage_id typo fixed).
         */
        private Integer packageId;
    }
}
