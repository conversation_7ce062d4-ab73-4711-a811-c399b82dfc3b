package com.simi.common.dto.dataReport;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;


@Data
@ExcelIgnoreUnannotated
public class GameReportEasyExcelModel {
    @Schema(description = "日期")
    @ExcelProperty(value = "时间",index = 0)
    private String reportDate;

    @Schema(description = "日期时间戳")
    private Long reportTimestamp;

    @Schema(description = "用户id")
    @ExcelProperty(value = "用户id",index = 1)
    private Long userNo;

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "gameId")
    private Long gameId;

    @Schema(description = "用户昵称")
    @ExcelProperty(value = "用户昵称",index = 2)
    private String nick;

    @Schema(description = "游戏类型，-1:全部，1-Fruit Party,2:RB,3:Lucky777")
    private Integer gameType;

    @Schema(description = "游戏名称")
    @ExcelProperty(value = "游戏",index = 3)
    private String gameName;

    @Schema(description = "局数")
    @ExcelProperty(value = "局数",index = 4)
    private Integer gameNumber;

    @Schema(description = "投注项")
    @ExcelProperty(value = "投注项",index = 5)
    private String bets;


    @Schema(description = "游戏结果,-1:全部，0:N,1:Y")
    @ExcelProperty(value = "结果",index = 6)
    private String gameResultDesc;


    @Schema(description = "游戏结果,-1:全部，0:N,1:Y")
    private Integer gameResult;

    @Schema(description = "投入金币")
    @ExcelProperty(value = "投入金币",index = 7)
    private Integer inCoin;

    @Schema(description = "产出金币")
    @ExcelProperty(value = "产出金币",index = 8)
    private Integer outCoin;

    @Schema(description = "实际盈利")
    @ExcelProperty(value = "实际盈利",index = 9)
    private Integer profitCoin;
}
