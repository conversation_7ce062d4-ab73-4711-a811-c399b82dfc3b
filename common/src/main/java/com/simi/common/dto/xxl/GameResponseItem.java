package com.simi.common.dto.xxl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GameResponseItem {
    /**
     * id
     */
    private Long ID;

    /**
     * 用户id
     */
    private Long UID;

    /**
     * 下注金额
     */
    private Integer Amount;

    /**
     * 赢的金额
     */
    private Integer Win;

    /**
     * 轮次id
     */
    private String RoundID;

    /**
     * 轮次
     */
    private Integer Round;

    /**
     * 投注项，fruit party 跟Lucky 777投注项
     */
    private String Fruit;

    /**
     * Rb投注项
     */
    private String Place;

    /**
     * 开始时间
     */
    private Date CreatedAt;
}
