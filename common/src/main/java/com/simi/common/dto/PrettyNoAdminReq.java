package com.simi.common.dto;

import com.simi.common.vo.req.BasePageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "请求")
public class PrettyNoAdminReq extends BasePageReq {

    private Long uid;
    private Long userNo;
    private Date stime;
    private Date etime;

}
