package com.simi.common.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "支付请求")
public class PayerMaxDTO {

    @Schema(description = "skuId")
    private String productId;
    @Schema(description = "支付渠道")
    private String channel;
    @Schema(description = "国家")
    private String country;
    @Schema(description = "支付类型")
    private String PaymentMethodType;
    @Schema(description = "给充值的userNo")
    private Long userNo;
}
