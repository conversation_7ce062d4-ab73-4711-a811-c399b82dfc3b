package com.simi.common.dto.tracking;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * room_peak_value
 * 1. 当天同时在线的房间数最大值（目标房间：有效房间）
 * 2. 后端每分钟上报
 * @Author: Andy
 * @Date: 2024-03-05
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class EventRoomPeakValueDTO {
    @SerializedName("room_num")
    private Integer roomNum;
}
