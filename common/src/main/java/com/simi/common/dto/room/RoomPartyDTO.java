package com.simi.common.dto.room;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/07/29 19:53
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RoomPartyDTO {

    /**
     * 主键
     */
    private Long id;

    /**
     * uid
     */
    private Long uid;

    /**
     * roomId
     */
    private String roomId;

    /**
     * 图片url
     */
    private String picUrl;

    /**
     * 活动名称
     */
    private String topic;

    /**
     * 活动描述
     */
    private String description;

    /**
     * 活动时长-分钟
     */
    private Integer duration;

    /**
     * 开始时间
     */
    private Date beginTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 订阅人数
     */
    private Long subscribeNum;

    /**
     * 是否上架: 1-是 0-否
     */
    private Integer enable;

    /**
     * 标签，逗号分割
     */
    private String tagIds;

    /**
     * 操作人id
     */
    //private Long adminId;

    /**
     * 修改版本
     */
    private Integer version;

    private Boolean cancle;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

}
