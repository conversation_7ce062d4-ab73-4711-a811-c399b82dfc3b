package com.simi.common.dto.user;

import com.simi.common.constant.FollowRelation;
import com.simi.common.constant.user.UserStatusEnum;
import com.simi.common.dto.UserPropInfoDTO;
import com.simi.common.entity.user.UserPropInUse;
import com.simi.common.vo.AttestationTagInfoVO;
import com.simi.common.vo.medal.UserWearMedalVO;
import com.simi.common.vo.resp.UserLevelBaseVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "用户信息")
public class UserBaseInfoDTO {

    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "靓号")
    private Long userNo;
    @Schema(description = "昵称")
    private String nick;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "性别")
    private Byte gender;
    @Schema(description = "是否靓号，true为是，false为否，默认否flase")
    private Boolean hasPrettyNo;
    @Schema(description = "生日")
    private Long birth;
    @Schema(description = "用户类型")
    private Integer defUserValue;
    @Schema(description = "区域")
    private String region;
    @Schema(description = "个性签名")
    private String userDesc;

    private Long createTime;

    @Schema(description = "用户状态")
    private UserStatusEnum userStatus;

    private Long lastLoginTime;

    private String lastLoginIp;
    @Schema(description = "国家编码")
    private String areaCode;

    @Schema(description = "经度")
    private Double longitude;
    @Schema(description = "纬度")
    private Double latitude;

    private String countryCode;
    @Schema(description = "用户app语言")
    private String appLanguage;
    @Schema(description = "用户在用的装扮信息")
    private List<UserPropInUse> userPropInUse;
    @Schema(description = "是否新用户")
    private Boolean newBie;
    @Schema(description = "用户等级信息")
    private UserLevelBaseVO userLevel;
    @Schema(description = "用户关系 0、FOLLOW_NONE 没有关系  1、FOLLOW_FOLLOWING  关注 2、FOLLOW_FOLLOWERS  被关注，粉丝  3、FOLLOW_MUTUAL 互关")
    private FollowRelation followRelation;

    @Schema(description = "用户房间id")
    private String roomId;
    @Schema(description = "认证标签")
    private List<String> tagPic;
    @Schema(description = "认证标签列表VOs")
    private List<AttestationTagInfoVO> tagPicInfos;
    //装扮
    @Schema(description = "头像框")
    private UserPropInfoDTO avatarWidget;
    @Schema(description = "气泡")
    private UserPropInfoDTO bubble;
    @Schema(description = "进房特效")
    private UserPropInfoDTO specialEffects;
    @Schema(description = "光圈")
    private UserPropInfoDTO ripple;
    @Schema(description = "房间背景")
    private UserPropInfoDTO background;
    @Schema(description = "主页动效")
    private UserPropInfoDTO dynamicEffect;
    @Schema(description = "座驾")
    private UserPropInfoDTO vehicle;
    @Schema(description = "资料卡")
    private UserPropInfoDTO card;


    @Schema(description = "是否币商")
    private Boolean isCoinDealer;
    @Schema(description = "币商等级")
    private Integer coinDealerLv;

    @Schema(description = "是否被封禁")
    private Boolean blocked;

    @Schema(description = "用户穿戴勋章列表")
    private List<UserWearMedalVO> userWearMedalVOS;

    @Schema(description = "身份牌认证信息")
    private List<UserIdentityAuthenticationDTO> userIdentityAuthenticationList;

    @Schema(description = "是否BD")
    private boolean bdFlag;
    @Schema(description = "心情")
    private String mood;
    @Schema(description = "星座")
    private String constellation;
    @Schema(description = "星座图标")
    private String constellationIcon;
    @Schema(description = "朋友在麦")
    private String friendInMic;

    private Boolean isFollow = false;
}
