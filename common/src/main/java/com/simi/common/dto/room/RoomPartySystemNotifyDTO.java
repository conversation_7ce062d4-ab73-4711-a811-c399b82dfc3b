package com.simi.common.dto.room;

import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/01 21:57
 * 房间party系统消息payload
 **/
@Data
@Builder
public class RoomPartySystemNotifyDTO {
    /**
     * partyId
     */
    private Long partyId;

    /**
     * party名称
     */
    private String topic;

    /**
     * roomId
     */
    private String roomId;

    /**
     * roomNo
     */
    private Long roomNo;

    /**
     * 事件
     */
    private Integer notifyEvent;
    /**
     * uid
     */
    private Long uid;
    /**
     * 结算金币
     */
    private Long settleGold;

    /**
     * 结算百分比
     */
    private BigDecimal settlePercent;

    /**
     * 活动时长-分钟
     */
    private Integer duration;

    /**
     * 结算评级
     */
    private String settleLv;

    /**
     * 总收礼值
     */
    private Long receiveTotal;

    /**
     * 最高在线
     */
    private Integer onlineCount;
    /**
     * 开始时间
     */
    private Date beginTime;

}
