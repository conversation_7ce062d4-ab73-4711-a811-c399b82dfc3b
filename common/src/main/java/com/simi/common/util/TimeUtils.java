package com.simi.common.util;

import com.github.pagehelper.util.StringUtil;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2020/4/8.
 */
public class TimeUtils {

    public static String YEAR2MONTH4SQL = "yyyy_MM";
    public static String PATTERN_FORMAT_DATE = "yyyy-MM-dd";
    public static String PATTERN_FORMAT_TIME = "yyyy-MM-dd HH:mm:ss";
    public static String YEAR2SECOND12 = "yyyy-MM-dd hh:mm:ss";
    public static String YEAR2SECOND24 = "yyyy-MM-dd HH:mm:ss";
    public static String YEAR2DAY_NOLINE = "yyyyMMdd";
    public static String YEAR2SECOND24_NOLINE = "yyyyMMddHHmmss";
    public static String YEAR2MILLION_NOLINE = "yyyyMMddHHmmssSSS";
    public static String YEAR2SECOND24ONLYHOUR = "yyyy-MM-dd HH:00:00";
    public static String YEAR2SECOND24FirSecond = "yyyy-MM-dd 00:00:00";
    public static String YEAR2SECOND24LastSecond = "yyyy-MM-dd 23:59:59";
    public static String YEAR2MINUTE24 = "yyyy-MM-dd HH:mm:00";
    public static String YEAR2HOUR_NOLINE = "yyyyMMddHH";
    public static String YEAR2MINUTE24_NOLINE = "yyyyMMddHHmm";
    public static String HHMM = "HH:mm";
    public static String HHMM_NOLINE = "HHmm";
    public static String HHMMSS_NOLINE = "hhmmss";
    public static String MMdd = "MMdd";

    public static String TXT_NOW = "刚刚活跃";
    public static String TXT_MINUTE = "分钟前";
    public static String TXT_HOUR = "小时前";

    public static Date fromStrDate(String str) throws ParseException {
        return fromStr(str, PATTERN_FORMAT_DATE);
    }

    public static Date fromStrTime(String str) throws ParseException {
        return fromStr(str, PATTERN_FORMAT_TIME);
    }

    public static Date fromStr(String str, String pattern) throws ParseException {
        DateFormat format = new SimpleDateFormat(pattern);
        return format.parse(str);
    }

    public static String toStr(Date date) {
        return toStr(date, PATTERN_FORMAT_TIME);
    }

    public static String toStr(Date date, String pattern) {
        DateFormat format = new SimpleDateFormat(pattern);
        return format.format(date);
    }

    public static int getCurHour() {
        Calendar today = Calendar.getInstance();
        return today.get(Calendar.HOUR_OF_DAY);
    }

    public static int getPreHour() {
        Calendar now = Calendar.getInstance();
        //获取上一小时
        now.set(Calendar.HOUR_OF_DAY, now.get(Calendar.HOUR_OF_DAY) - 1);
        int preHour = now.get(Calendar.HOUR_OF_DAY);
        return preHour;
    }

    /**
     * 时间偏移(年数)，负数:往前，正数:往后
     */
    public static Date offsetYeah(Date date, int offset) {
        return offset(date, Calendar.YEAR, offset);
    }

    /**
     * 时间偏移(天数)，负数:往前，正数:往后
     */
    public static Date offsetDay(Date date, int offset) {
        return offset(date, Calendar.DAY_OF_YEAR, offset);
    }

    /**
     * 时间偏移(小时)，负数:往前，正数:往后
     */
    public static Date offsetHour(Date date, int offset) {
        return offset(date, Calendar.HOUR_OF_DAY, offset);
    }

    /**
     * 时间偏移(分钟)，负数:往前，正数:往后
     */
    public static Date offsetMinute(Date date, int offset) {
        return offset(date, Calendar.MINUTE, offset);
    }

    /**
     * 时间偏移(秒)，负数:往前，正数:往后
     */
    public static Date offsetSecond(Date date, int offset) {
        return offset(date, Calendar.SECOND, offset);
    }

    /**
     * 时间偏移
     *
     * @param date   参数date,如果为空，默认为当前
     * @param field  如Calendar.HOUR_OF_DAY
     * @param offset 偏移量，正数:往后，负数:往前
     * @return 偏移后的date
     */
    public static Date offset(Date date, int field, int offset) {
        if (date == null) {
            date = new Date();
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(field, offset);
        return calendar.getTime();
    }

    //获取当天（按当前传入的时区）00:00:00所对应时刻的long型值
    public static long getStartTimeOfDay(long now, String timeZone) {
        String tz = StringUtil.isEmpty(timeZone) ? "GMT+8" : timeZone;
        TimeZone curTimeZone = TimeZone.getTimeZone(tz);
        Calendar calendar = Calendar.getInstance(curTimeZone);
        calendar.setTimeInMillis(now);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTimeInMillis();
    }

    /**
     * 获取周一的日期
     *
     * @param date 传入当前日期
     * @return
     */
    public static Date getThisWeekMonday(Date date) {
        return getThisWeekDay(date, Calendar.MONDAY);
    }

    /**
     * 获取指定日期所在周的第一天（周一）
     *
     * @param date 指定日期
     * @return 该周的周一日期（时间设置为00:00:00）
     */
    public static Date getWeekFirstDay(Date date) {
        if (date == null) {
            date = new Date();
        }

        Calendar cal = Calendar.getInstance();
        cal.setTime(date);

        // 设置一周的第一天为周一
        cal.setFirstDayOfWeek(Calendar.MONDAY);

        // 获取当前是星期几
        int dayOfWeek = cal.get(Calendar.DAY_OF_WEEK);

        // 计算到周一的偏移量
        int offset;
        if (dayOfWeek == Calendar.SUNDAY) {
            // 如果是周日，需要回到6天前的周一
            offset = -6;
        } else {
            // 其他情况，计算到周一的天数
            offset = Calendar.MONDAY - dayOfWeek;
        }

        // 调整到周一
        cal.add(Calendar.DAY_OF_MONTH, offset);

        // 设置时间为当天的00:00:00
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);

        return cal.getTime();
    }

    /**
     * 获取当前周的第一天（周一）
     *
     * @return 当前周的周一日期（时间设置为00:00:00）
     */
    public static Date getWeekFirstDay() {
        return getWeekFirstDay(new Date());
    }

    /**
     * 获取指定日期所在周的第一天，返回字符串格式
     *
     * @param date 指定日期
     * @param pattern 日期格式，如 "yyyy-MM-dd"
     * @return 格式化后的周一日期字符串
     */
    public static String getWeekFirstDayStr(Date date, String pattern) {
        Date firstDay = getWeekFirstDay(date);
        return toStr(firstDay, pattern);
    }

    /**
     * 获取当前周的第一天，返回字符串格式
     *
     * @param pattern 日期格式，如 "yyyy-MM-dd"
     * @return 格式化后的当前周一日期字符串
     */
    public static String getWeekFirstDayStr(String pattern) {
        return getWeekFirstDayStr(new Date(), pattern);
    }

    /**
     * 获取这周的指定星期几
     *
     * @param date
     * @param weekDay Calendar.SUNDAY 会返回上周的周日 。记得处理。 Calendar.MONDAY , Calendar.TUESDAY........
     * @return
     */
    public static Date getThisWeekDay(Date date, int weekDay) {
        date = date == null ? new Date() : date;
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        // 获得当前日期是一个星期的第几天
        int dayWeek = cal.get(Calendar.DAY_OF_WEEK);
        if (1 == dayWeek) {
            cal.add(Calendar.DAY_OF_MONTH, -1);
        }
        // 设置一个星期的第一天，按中国的习惯一个星期的第一天是星期一
        cal.setFirstDayOfWeek(weekDay);
        // 获得当前日期是一个星期的第几天
        int day = cal.get(Calendar.DAY_OF_WEEK);
        cal.add(Calendar.DATE, cal.getFirstDayOfWeek() - day);
        return cal.getTime();
    }

    /**
     * 自定义时分秒
     *
     * @param date
     * @return
     */
    public static Date buildDate(Date date, int hourOfDay, int minute, int sencond) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                hourOfDay, minute, sencond);
        return calendar.getTime();
    }


    /**
     * 时间推移到 23:59:59
     *
     * @param date
     * @return
     */
    public static Date getLastSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

    /**
     * 时间推移到 00:00:00
     *
     * @param date
     * @return
     */
    public static Date getFirstSecond(Date date) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH),
                0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    public static int getDayOfMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_MONTH);
    }

    public static int getDayOfWeekInMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_WEEK_IN_MONTH);
    }

    public static int getDayOfYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.DAY_OF_YEAR);
    }

    public static int getCurMonth() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.MONTH) + 1;
    }

    public static int getCurYear() {
        Calendar calendar = Calendar.getInstance();
        return calendar.get(Calendar.YEAR);
    }

    /**
     * 时间戳转日期
     *
     * @param timestamp
     * @return
     */
    public static Date timestamp2Date(Long timestamp) {
        if (timestamp.toString().length() < 11) {
            timestamp = timestamp * 1000;
        }
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp);
        return calendar.getTime();
    }

    /**
     * 两个日期相差多少天
     *
     * @param one
     * @param two
     * @return
     */
    public static long daysBetween(Date one, Date two) {
        long difference = (one.getTime() - two.getTime()) / 86400000;
        return Math.abs(difference);
    }

    /**
     * 两个日期相差多少秒
     *
     * @param one
     * @param two
     * @return
     */
    public static long secondsBetween(Date one, Date two) {
        long difference = (one.getTime() - two.getTime()) / 1000;
        return Math.abs(difference);
    }

    /**
     * 是否两个时间之间
     *
     * @param sDate
     * @param eDate
     * @return
     */
    public static boolean isBetween(Date sDate, Date eDate) {
        long now = new Date().getTime();
        long stime = sDate.getTime();
        long etime = eDate.getTime();
        if (now >= stime && now <= etime) {
            return true;
        }
        return false;
    }

    /**
     * @param startStr   "06:00"
     * @param endStr     "10:00"
     * @param isInterDay 是否跨天
     * @throws ParseException
     */
    public static boolean isBelong(String startStr, String endStr, boolean isInterDay) throws ParseException {

        SimpleDateFormat df = new SimpleDateFormat("HH:mm");//设置日期格式
        Date now = null;
        Date beginTime = null;
        Date endTime = null;

        now = df.parse(df.format(new Date()));
        beginTime = df.parse(startStr);
        endTime = df.parse(endStr);


        Boolean flag = belongCalendar(now, beginTime, endTime, isInterDay);
        return flag;
    }

    /**
     * 判断时间是否在时间段内
     *
     * @param nowTime
     * @param beginTime
     * @param endTime
     * @return
     */
    public static boolean belongCalendar(Date nowTime, Date beginTime, Date endTime, boolean isInterDay) {
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(beginTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);
        if (isInterDay) {
            end.add(Calendar.DATE, 1);
        }
        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 向前取整，30分钟
     *
     * @param time "01:25"
     * @return
     */
    public static String getPreRoundTime(String time) {
        String hour = "00";//小时
        String minutes = "00";//分钟
        String outTime = "0000";
        StringTokenizer st = new StringTokenizer(time, ":");
        List<String> inTime = new ArrayList<String>();
        while (st.hasMoreElements()) {
            inTime.add(st.nextToken());
        }
        hour = inTime.get(0).toString();
        minutes = inTime.get(1).toString();
        if (Integer.parseInt(minutes) > 30) {
            outTime = hour + "30";
        } else {
            outTime = hour + "00";
        }
        return outTime;
    }

    /**
     * 由出生日期获得年龄  精确到天
     *
     * @param birthDay
     * @return
     */
    public static int calcAge(Date birthDay) {
        Calendar cal = Calendar.getInstance();
        if (cal.before(birthDay)) {
            throw new RuntimeException("出生日期不能晚于当前日期");
        }
        int yearNow = cal.get(Calendar.YEAR);
        int monthNow = cal.get(Calendar.MONTH);
        int dayOfMonthNow = cal.get(Calendar.DAY_OF_MONTH);
        cal.setTime(birthDay);

        int yearBirth = cal.get(Calendar.YEAR);
        int monthBirth = cal.get(Calendar.MONTH);
        int dayOfMonthBirth = cal.get(Calendar.DAY_OF_MONTH);

        int age = yearNow - yearBirth;

        if (monthNow <= monthBirth) {
            if (monthNow == monthBirth) {
                if (dayOfMonthNow < dayOfMonthBirth) age--;
            } else {
                age--;
            }
        }
        return age;
    }

    /**
     * 由出生日期获得年龄
     *
     * @param birthDayTimestamp 出生时间戳，毫秒
     * @return
     */
    public static int calcAge(long birthDayTimestamp) {
        return calcAge(new Date(birthDayTimestamp));
    }

    /**
     * 获取每月第一天
     *
     * @param pattern
     * @return
     */
    public static String getMonthFirstDay(String pattern) {
        // 获取当前年份、月份、日期
        Calendar cale = Calendar.getInstance();
        // 获取当前月的第一天
        cale.add(Calendar.MONTH, 0);
        cale.set(Calendar.DAY_OF_MONTH, 1);
        return TimeUtils.toStr(cale.getTime(), pattern);
    }


    /**
     * 获取每月第x天
     *
     * @return
     */
    public static Date getMonthDay(int day) {
        Calendar cale = Calendar.getInstance();
        // 获取当前月的第x天
        cale.set(Calendar.DAY_OF_MONTH, day);
        return cale.getTime();
    }


    /**
     * 今天是这个月的第几天
     *
     * @return
     */
    public static int getDateMonthDay(Date date) {
        // 获取当前年份、月份、日期
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        return cale.get(Calendar.DAY_OF_MONTH);
    }


    /**
     * 获取每月第一小时
     *
     * @param pattern
     * @return
     */
    public static String getMonthFirstHour(String pattern) {
        // 获取当前年份、月份、日期
        Calendar cale = Calendar.getInstance();
        // 获取当前月的第一天
        cale.add(Calendar.MONTH, 0);
        cale.set(Calendar.DAY_OF_MONTH, 1);
        cale.set(Calendar.HOUR_OF_DAY, 0);
        return TimeUtils.toStr(cale.getTime(), pattern);
    }

    /**
     * 获取每月最后一天
     *
     * @return
     */
    public static Date getMonthLastDay(Date date) {
        // 获取当前年份、月份、日期
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        // 获取当前月的最后一天
        cale.add(Calendar.MONTH, 1);
        cale.set(Calendar.DAY_OF_MONTH, 0);
        return cale.getTime();
    }

    /**
     * 获取每月最后一天
     *
     * @param pattern
     * @return
     */
    public static String getMonthLastDay(Date date, String pattern) {
        // 获取当前年份、月份、日期
        Calendar cale = Calendar.getInstance();
        cale.setTime(date);
        // 获取当前月的最后一天
        cale.add(Calendar.MONTH, 1);
        cale.set(Calendar.DAY_OF_MONTH, 0);
        return TimeUtils.toStr(cale.getTime(), pattern);
    }

    /**
     * 是否每月第一天
     *
     * @return
     */
    public static boolean isMonthFirstDay() {
        String now = TimeUtils.toStr(new Date(), TimeUtils.PATTERN_FORMAT_DATE);
        String firstDay = TimeUtils.getMonthFirstDay(TimeUtils.PATTERN_FORMAT_DATE);
        return now.equals(firstDay);
    }

    /**
     * 是否每月最后一天
     *
     * @return
     */
    public static boolean isMonthLastDay(Date date) {
        String dateStr = TimeUtils.toStr(date, TimeUtils.PATTERN_FORMAT_DATE);
        String lastDay = TimeUtils.getMonthLastDay(date, TimeUtils.PATTERN_FORMAT_DATE);
        return dateStr.equals(lastDay);
    }

    /**
     * 是否每月第一小时
     *
     * @return
     */
    public static boolean isMonthFirstHour() {
        String now = TimeUtils.toStr(new Date(), TimeUtils.YEAR2SECOND24ONLYHOUR);
        String firstHour = TimeUtils.getMonthFirstHour(TimeUtils.YEAR2SECOND24ONLYHOUR);
        return now.equals(firstHour);
    }

    /**
     * 转文本显示
     *
     * @param time
     * @return
     */
    public static String toBeatifulTxt(long time) {
        long now = System.currentTimeMillis() / 1000;
        long diff = now - time;
        if (diff <= 180) {
            return TXT_NOW;
        } else if (diff <= 3600) {
            return diff / 60 + TXT_MINUTE;
        } else if (diff <= 10800) {
            return diff / 3600 + TXT_HOUR;
        } else {
            return "";
        }
    }

    /**
     * 转文本显示
     *
     * @param time
     * @return
     */
    public static String toTxt(long time) {
        long now = System.currentTimeMillis() / 1000;
        long diff = now - time;
        if (diff <= 60) {
            return diff + "秒";
        } else if (diff <= 3600) {
            return diff / 60 + "分钟";
        } else if (diff <= 86400) {
            return diff / 3600 + "小时";
        } else {
            return diff / 86400 + "天";
        }
    }

    public static String toBeatifulTimeTxt(long time) {
        if (time < 60) {
            return "00:" + (time < 10 ? "0" + time : time);
        } else if (time < 3600) {
            long minute = time / 60;
            long second = time % 60;
            return (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second);
        } else {
            long hour = time / 3600;
            long minute = (time % 3600) / 60;
            long second = time % 60;
            return (hour < 10 ? "0" + hour : hour) + ":" + (minute < 10 ? "0" + minute : minute) + ":" + (second < 10 ? "0" + second : second);
        }
    }

    /**
     * 比较是否同一天
     *
     * @return
     */
    public static boolean isSameDay(Date date1, Date date2) {
        return TimeUtils.toStr(date1, TimeUtils.PATTERN_FORMAT_DATE).equals(TimeUtils.toStr(date2, TimeUtils.PATTERN_FORMAT_DATE));
    }

    /**
     * 比较是否同一个月
     *
     * @return
     */
    public static boolean isSameMonth(Date date1, Date date2) {
        return TimeUtils.toStr(date1, TimeUtils.YEAR2MONTH4SQL).equals(TimeUtils.toStr(date2, TimeUtils.YEAR2MONTH4SQL));
    }


    public static void main(String[] args) throws ParseException {
        System.out.println(TimeUtils.getDateMonthDay(new Date()));
        System.out.println(TimeUtils.getMonthDay(32));
    }

}
