package com.simi.common.util;

import com.alibaba.excel.EasyExcel;
import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;
import java.util.List;

@Slf4j
public class ExportUtils {
    /**
     * 导出接口
     * @param fileName
     * @param list
     * @param response
     * @throws Exception
     */
    public static <T> void exportFile(String fileName, List<T> list,HttpServletResponse response,Class clz)  {
        // 设置响应头，告诉浏览器这是一个 Excel 文件
        response.setContentType("application/vnd.ms-excel");
        response.setCharacterEncoding("utf-8");
        try {
            String finalFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-disposition", "attachment;filename=" + finalFileName + ".xlsx");
            // 调用 EasyExcel 导出
            EasyExcel.write(response.getOutputStream(),clz)
                    .sheet("用户信息")
                    .doWrite(list);
        }catch (Exception e){
            log.error("exportFile error,msg:{}",ExceptionUtil.formatEx(e));
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }
}
