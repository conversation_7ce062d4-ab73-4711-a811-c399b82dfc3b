package com.simi.common.util;

import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class StringSplitUtils {
    public static List<Long> convertStrToLongList(String source){
        if (StringUtils.isBlank(source)){
            return Collections.EMPTY_LIST;
        }

        source = source.trim();
        String[] array = source.split("[,\n]");
        return Arrays.stream(array).filter(e-> NumberUtil.isNumber(e)).map(x->Long.valueOf(x)).collect(Collectors.toList());
    }
}
