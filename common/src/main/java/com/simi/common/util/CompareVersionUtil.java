package com.simi.common.util;

import lombok.extern.log4j.Log4j2;

@Log4j2
public class CompareVersionUtil {

    // 支持形如 "1.3.0" 与 "1.2.9" 的比较
    public static int compareVersion(String v1, String v2) {
        try {
            String[] arr1 = v1.split("\\.");
            String[] arr2 = v2.split("\\.");
            int len = Math.max(arr1.length, arr2.length);
            for (int i = 0; i < len; i++) {
                int num1 = i < arr1.length ? Integer.parseInt(arr1[i]) : 0;
                int num2 = i < arr2.length ? Integer.parseInt(arr2[i]) : 0;
                if (num1 != num2) {
                    return num1 - num2;
                }
            }
        } catch (Exception e) {
            log.error("版本比较异常 ", e);

        }
        return 0;
    }

    public static void main(String[] args) {
        int s = compareVersion("1.4.1", "1.4.0");
        System.out.println(s);
    }
}
