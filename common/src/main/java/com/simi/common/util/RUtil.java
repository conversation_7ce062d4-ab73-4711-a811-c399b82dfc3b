package com.simi.common.util;


import com.simi.common.base.R;
import com.simi.common.constant.CodeEnum;

/**
 * 响应工具类
 *
 * <AUTHOR>
 * @date 2023/10/26 15:55
 */
public class RUtil {
    public static <T> R<T> ok() {
        return R.<T>builder().withStatus(CodeEnum.SUCCESS_ZERO).build();
    }

    public static <T> R<T> ok(T data) {
        return R.<T>builder().withStatus(CodeEnum.SUCCESS_ZERO).withData(data).build();
    }

    public static <T> R<T> ok(CodeEnum baseCode, T data, Object... params) {
        return R.<T>builder().withStatus(baseCode, params).withData(data).build();
    }

    public static <T> R<T> error() {
        return R.<T>builder().withStatus(CodeEnum.SERVER_ERROR).build();
    }

    public static <T> R<T> error(int code, String message) {
        return R.<T>builder().withCode(code).withMessage(message).build();
    }

    public static <T> R<T> error(CodeEnum baseCode, Object... params) {
        return error(baseCode.getNumber(), baseCode.name());
    }

    public static <T> R<T> build(CodeEnum baseCode, Object... params) {
        return R.<T>builder().withStatus(baseCode, params).build();
    }

    public static <T> R<T> buildWithData(CodeEnum baseCode, T data, Object... params) {
        return R.<T>builder().withStatus(baseCode, params).withData(data).build();
    }
}
