package com.simi.common.util;


import com.simi.common.constant.LanguageEnum;
import io.micrometer.common.util.StringUtils;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

/**
 *
 */
@Slf4j
public class LangUtil {

    /**
     * {
     * "zh_hans": "简中",
     * "zh_hant": "繁中",
     * "en": "英文"
     * "id": "印尼"
     * }
     * 根据语言获取配置
     *
     * @param contentConf 配置
     * @param lang        语言
     * @return 语言配置
     */
    public static String getContentByLang(String contentConf, String lang) {
        return getContentByLang(contentConf, lang, LanguageEnum.en.name());
    }

    /**
     * {
     * "zh_hans": "简中",
     * "zh_hant": "繁中",
     * "en": "英文"
     * "id": "印尼"
     * }
     * 根据语言获取配置
     *
     * @param contentConf 配置
     * @param lang        语言
     * @param defaultLang 默认语言
     * @return 语言配置
     */
    public static String getContentByLang(String contentConf, String lang, String defaultLang) {
        if (StringUtils.isBlank(lang)) {
            lang = defaultLang;
        }
        if (StringUtils.isBlank(contentConf)) {
            return "";
        }
        if (!contentConf.startsWith("{")) {
            return contentConf;
        }
        if (!contentConf.endsWith("}")) {
            return contentConf;
        }
        try {
            Map<String, Object> map = GsonUtil.getGson().fromJson(contentConf, Map.class);
            Object content = map.get(lang.toLowerCase());
            if (content != null) {
                return content.toString();
            }
            content = map.get(defaultLang);
            return content == null ? "" : content.toString();
        } catch (Exception e) {
            log.error("getContentByLang error, contentConf={},lang={},defaultLang={}", contentConf, lang, defaultLang, e);
        }
        return "";
    }
}
