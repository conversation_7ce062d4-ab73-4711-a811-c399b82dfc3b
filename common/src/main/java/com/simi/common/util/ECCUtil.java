package com.simi.common.util;

import org.bouncycastle.jce.provider.BouncyCastleProvider;

import javax.crypto.KeyAgreement;
import java.security.*;
import java.security.spec.InvalidKeySpecException;
import java.security.spec.PKCS8EncodedKeySpec;
import java.security.spec.X509EncodedKeySpec;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-30 22:19
 **/
public class ECCUtil {
    private static final BouncyCastleProvider bouncyCastleProvider = new BouncyCastleProvider();

    public static KeyPair genKeyPair(int keySize, String algorithm) throws NoSuchAlgorithmException {
        KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance(algorithm);
        keyPairGenerator.initialize(keySize);

        return keyPairGenerator.generateKeyPair();
    }

    public static byte[] getSecretKey(PublicKey publicKey, PrivateKey privateKey, String algorithm) throws NoSuchAlgorithmException, InvalidKeyException {
        KeyAgreement keyAgreement = KeyAgreement.getInstance(algorithm);
        keyAgreement.init(privateKey);
        keyAgreement.doPhase(publicKey, true);

        return keyAgreement.generateSecret();
    }

    public static PublicKey getPublicKey(String algorithm, byte[] publicKey) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException {
        Security.addProvider(bouncyCastleProvider);

        KeyFactory factory = KeyFactory.getInstance(algorithm, "BC");

        return factory.generatePublic(new X509EncodedKeySpec(publicKey));
    }

    public static PrivateKey getPrivateKey(String algorithm, byte[] privateKey) throws NoSuchAlgorithmException, NoSuchProviderException, InvalidKeySpecException {
        Security.addProvider(bouncyCastleProvider);

        KeyFactory factory = KeyFactory.getInstance(algorithm, "BC");

        return factory.generatePrivate(new PKCS8EncodedKeySpec(privateKey));
    }
}
