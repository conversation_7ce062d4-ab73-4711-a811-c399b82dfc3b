package com.simi.common.util;

import cn.hutool.core.date.DateUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.rank.RankFrequencyEnum;
import com.simi.common.exception.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

/**
 *
 */
@Slf4j
public class FrequencyUtil {

    /**
     * @param timeZoneStr
     * @param rankFrequencyEnum
     * @param rankOffset        榜单周期偏移量：0、代表当前周期(比如当天日榜)；-1、代表前1个周期(比如昨日榜)；-2、代表前2个周期(比如前日榜)
     * @param timeMillis
     * @return
     */
    public static Date getRankTargetDate(String timeZoneStr, RankFrequencyEnum rankFrequencyEnum, int rankOffset, long timeMillis) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneStr);
        Calendar calendar = Calendar.getInstance(timeZone);
        Date curDate = new Date(timeMillis);
        Date targetDate = null;
        long timeMillisOffset = timeMillis;
        if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_ALL) {
        } else if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_MONTH) {
            if (rankOffset < 0) {
                timeMillisOffset = DateUtil.offsetMonth(curDate, rankOffset).getTime();
            }
            calendar.setTimeInMillis(timeMillisOffset);
            targetDate = DateUtil.beginOfMonth(calendar).getTime();
        } else if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK) {
            if (rankOffset < 0) {
                timeMillisOffset = DateUtil.offsetWeek(curDate, rankOffset).getTime();
            }
            calendar.setTimeInMillis(timeMillisOffset);
            targetDate = DateUtil.beginOfWeek(calendar).getTime();
        } else if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_DAY) {
            if (rankOffset < 0) {
                timeMillisOffset = DateUtil.offsetDay(curDate, rankOffset).getTime();
            }
            calendar.setTimeInMillis(timeMillisOffset);
            targetDate = DateUtil.beginOfDay(calendar).getTime();
        }
        return targetDate;
    }

    /**
     * @param timeZoneStr
     * @param rankFrequencyEnum
     * @param rankOffset        榜单周期偏移量：0、代表当前周期(比如当天日榜)；-1、代表前1个周期(比如昨日榜)；-2、代表前2个周期(比如前日榜)
     * @param timeMillis
     * @return
     */
    public static String getRankTargetDateStr(String timeZoneStr, RankFrequencyEnum rankFrequencyEnum, int rankOffset, long timeMillis) {
        if (timeMillis <= 0L) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        Date targetDate = getRankTargetDate(timeZoneStr, rankFrequencyEnum, rankOffset, timeMillis);
        String targetDateStr = targetDate == null ? "" : DateTimeUtil.getTargetDateStr(timeZoneStr, rankFrequencyEnum.getDateFormat(), targetDate.getTime());
        return StringUtils.isBlank(targetDateStr) ? rankFrequencyEnum.getFrequencyKey() : rankFrequencyEnum.getFrequencyKey() + "_" + targetDateStr;
    }


    /**
     * @param timeZoneStr
     * @param rankFrequencyEnum
     * @param rankOffset        榜单周期偏移量：0、代表当前周期(比如当天日榜)；-1、代表前1个周期(比如昨日榜)；-2、代表前2个周期(比如前日榜)
     * @param timeMillis
     * @return
     */
    public static Map<String, Date> getRankTargetTimeRange(String timeZoneStr, RankFrequencyEnum rankFrequencyEnum, int rankOffset, long timeMillis) {
        TimeZone timeZone = TimeZone.getTimeZone(timeZoneStr);
        Calendar calendar = Calendar.getInstance(timeZone);
        Date curDate = new Date(timeMillis);
        Date startTime = null;
        Date endTime = null;
        long timeMillisOffset = timeMillis;
        if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_ALL) {
            startTime = new Date(0);
            endTime = DateUtil.endOfYear(new Date());
        } else if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_MONTH) {
            if (rankOffset < 0) {
                timeMillisOffset = DateUtil.offsetMonth(curDate, rankOffset).getTime();
            }
            calendar.setTimeInMillis(timeMillisOffset);
            startTime = DateUtil.beginOfMonth(calendar).getTime();
            endTime = DateUtil.endOfMonth(calendar).getTime();
        } else if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_WEEK) {
            if (rankOffset < 0) {
                timeMillisOffset = DateUtil.offsetWeek(curDate, rankOffset).getTime();
            }
            calendar.setTimeInMillis(timeMillisOffset);
            startTime = DateUtil.beginOfWeek(calendar).getTime();
            endTime = DateUtil.endOfWeek(calendar).getTime();
        } else if (rankFrequencyEnum == RankFrequencyEnum.RANK_FREQUENCY_TYPE_DAY) {
            if (rankOffset < 0) {
                timeMillisOffset = DateUtil.offsetDay(curDate, rankOffset).getTime();
            }
            calendar.setTimeInMillis(timeMillisOffset);
            startTime = DateUtil.beginOfDay(calendar).getTime();
            endTime = DateUtil.endOfDay(calendar).getTime();
        }
        Map<String, Date> timeRangeMap = new HashMap<>();
        timeRangeMap.put("startTime", startTime);
        timeRangeMap.put("endTime", endTime);
        return timeRangeMap;
    }


}
