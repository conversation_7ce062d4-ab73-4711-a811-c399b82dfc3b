package com.simi.common.util;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 客户端路由工具
 *
 * <AUTHOR>
 * @date 2024/2/23 11:44
 */
public class ClientRouteUtil {

    /**
     * 路由模板
     */
    private static final String PREFIX = "nady:///main";

    /**
     * 去用户个人主页
     *
     * @param uid
     * @return
     */
    public static String toHomePage(long uid, boolean isPopChat) {
        return StrUtil.format("{}/me-homepage?user-uid={}&is-pop-chat={}", PREFIX, uid, isPopChat);
    }

    /**
     * 用户跟随跳转主页
     *
     * @param
     * @return
     */
    public static String toHome(Integer action, Long uid) {
        return StrUtil.format("nady://?action={}&uid={}", action, uid);
    }

    /**
     * 去房间
     *
     * @param roomId
     * @return
     */
    public static String toRoom(final String roomId) {
        return StrUtil.format("{}/voice-room?roomId={}", PREFIX, roomId);
    }

    /**
     * 游戏页
     *
     * @param type
     * @return
     */
    public static String toGame(final String type) {
        return StrUtil.format("{}/game?type={}", PREFIX, type);
    }

    /**
     * party
     *
     * @return
     */
    public static String toParty(final String param) {
        return StrUtil.format("{}/party?type={}", PREFIX, param);
    }

    /**
     * 去主页
     *
     * @param tab
     * @return
     */
    public static String toMain(MainTab tab) {
        return StrUtil.format("nady://?page=main&tab={}", PREFIX, tab.getType());
    }

    /**
     * 用户信息编辑页
     * @return
     */
    public static String toUserEdit() {
        return StrUtil.format("{}/me-edit", PREFIX);
    }

    /**
     * 去每日任务
     *
     * @return
     */
    public static String toDailyReward() {
        return StrUtil.format("{}/daily-reward", PREFIX);
    }

    /**
     * 去IM聊天页
     *
     * @return
     */
    public static String toIM(String targetUid, boolean isOnRoom) {
        return StrUtil.format("{}/p2p-chat?target-u-i-d={}&is-on-room={}", PREFIX, targetUid, isOnRoom);
    }

    /**
     * 去等级页面
     *
     * @return
     */
    public static String toLevel() {
        return StrUtil.format("{}/level", PREFIX);
    }

    /**
     * 去动态详情
     *
     * @return
     */
    public static String toPostDetail(long postId) {
        return StrUtil.format("{}moment_detail&type={}&momentId={}", PREFIX, MomentType.detail.name(), postId);
    }

    /**
     * 去钱包页
     *
     * @return
     */
    public static String toPurse(long purseType) {
        return StrUtil.format("nady:///main/me-wallet?initial-index={}", purseType);
    }
    /**
     * 去动态评论
     *
     * @return
     */
    public static String toPostComment(long postId, long commentId) {
        return StrUtil.format("{}moment_detail&type={}&momentId={}&commentId={}", PREFIX, MomentType.commentId.name(), postId, commentId);
    }

    /**
     * 去动态回复
     *
     * @return
     */
    public static String toPostReply(long postId, long replyId) {
        return StrUtil.format("{}moment_detail&type={}&momentId={}&replyId={}", PREFIX, MomentType.replyId.name(), postId, replyId);
    }

    /**
     * 去动态送礼
     *
     * @return
     */
    public static String toPostSendGift(long postId, long sendGiftUid) {
        return StrUtil.format("{}moment_detail&type={}&momentId={}&giftUid={}", PREFIX, MomentType.giftId.name(), postId, sendGiftUid);
    }

    /**
     * 去动态点赞
     *
     * @return
     */
    public static String toPostLike(long postId, long likeUid) {
        return StrUtil.format("{}moment_detail&type={}&momentId={}&likeUid={}", PREFIX, MomentType.likeId.name(), postId, likeUid);
    }

    /**
     * 去背包：
     * @return
     */
    public static String toBackpack() {
        return StrUtil.format("{}/me-backpack", PREFIX);
    }

    /**
     * 去勋章页：
     * @return
     */
    public static String toMedal() {
        return StrUtil.format("{}/me-badge", PREFIX);
    }

    /**
     * 跳转到应用内web浏览器
     * @param url
     * @return
     */
    public static String toInAppBrowser(String url) {
        return StrUtil.format("{}/app-web?url={}", PREFIX, url);
    }

    @Getter
    @AllArgsConstructor
    public enum MainTab {
        /**
         * 房间
         */
        room(1),
        /**
         * im列表
         */
        im(2),
        /**
         * 我的
         */
        mine(3),
        ;
        final Integer type;
    }

    public enum MainSubTab {
        /**
         * 推荐
         */
        recommend,
        /**
         * 历史
         */
        history
    }

    public enum Level {
        /**
         * 活跃等级
         */
        active,
        /**
         * 财富等级
         */
        wealth,
        /**
         * 魅力等级
         */
        charm
    }

    public enum MomentType {
        /**
         * 动态详情
         */
        detail,
        /**
         * 动态评论
         */
        comment,
        /**
         * 动态
         */
        momentId,
        /**
         * 评论
         */
        commentId,
        /**
         * 回复
         */
        replyId,
        /**
         * 送礼
         */
        giftId,
        /**
         * 点赞
         */
        likeId,
    }

}
