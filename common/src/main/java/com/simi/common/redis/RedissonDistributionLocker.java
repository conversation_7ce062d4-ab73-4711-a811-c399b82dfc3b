package com.simi.common.redis;

import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;

import java.util.concurrent.TimeUnit;

/**
 * redisson实现分布式锁接口
 *
 * <AUTHOR>
 * @date 2023/10/27 20:07
 */
@Slf4j
public class RedissonDistributionLocker implements IDistributionLocker {

    @Resource
    private RedissonClient redissonClient;

    public RedissonDistributionLocker(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 加锁
     *
     * @param lockKey
     */
    @Override
    public Locker lock(String lockKey) {
        return lock(lockKey, 0, TimeUnit.SECONDS, Boolean.FALSE);
    }

    /**
     * 释放锁
     *
     * @param locker
     */
    @Override
    public void unlock(Object locker) {
        if (!(locker instanceof RLock)) {
            throw new IllegalArgumentException("Invalid locker object");
        }
        RLock lock = (RLock) locker;
        if (lock.isLocked()) {
            try {
                lock.unlock();
            } catch (IllegalMonitorStateException e) {
                log.error("e", e);
            }
        }
    }

    /**
     * 加锁，设置有效时间并指定时间单位
     *
     * @param lockKey
     * @param leaseTime
     * @param unit
     */
    @Override
    public Locker lock(String lockKey, long leaseTime, TimeUnit unit, boolean fair) {
        RLock lock = getLock(lockKey, fair);
        if (leaseTime > 0) {
            lock.lock(leaseTime, unit);
        } else {
            lock.lock();
        }
        return new Locker(lock, this);
    }

    /**
     * 尝试获取锁，获取到则持有该锁返回true,未获取到立即返回false
     *
     * @param lockKey
     * @return
     */
    @Override
    public Locker tryLock(String lockKey, long waitTime) throws InterruptedException {
        return this.tryLock(lockKey, waitTime, 0, TimeUnit.SECONDS, false);
    }

    /**
     * 尝试获取锁，获取到则持有该锁leaseTime时间.
     * 若未获取到，在waitTime时间内一直尝试获取，超过waitTime还未获取到则返回false
     *
     * @param lockKey
     * @param waitTime
     * @param leaseTime
     * @param unit
     * @return
     * @throws InterruptedException
     */
    @Override
    public Locker tryLock(String lockKey, long waitTime, long leaseTime, TimeUnit unit, boolean fair) throws InterruptedException {
        RLock lock = getLock(lockKey, fair);
        boolean lockAcquired = waitTime > 0 ? lock.tryLock(waitTime, leaseTime, unit) : lock.tryLock(waitTime, unit);
        return lockAcquired ? new Locker(lock, this) : null;
    }

    private RLock getLock(String lockKey, boolean fair) {
        return fair ? redissonClient.getFairLock(lockKey) : redissonClient.getLock(lockKey);
    }
}
