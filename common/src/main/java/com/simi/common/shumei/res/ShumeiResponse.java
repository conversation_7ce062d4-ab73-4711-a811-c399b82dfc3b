package com.simi.common.shumei.res;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 数美天网API响应
 *
 * <AUTHOR>
 * @date 2021-12-14 11:16:45
 */
@Data
@Builder
public class ShumeiResponse {

    private String requestId;
    private Integer code;
    private String message;
    private String riskLevel;
    private ResDetail detail;

    @Data
    public static class ResDetail{
        private Integer riskType;
        private String description;
        private String model;
        // 业务码")
        public String businessCode;
        private List<Hit> hits;
        private MachineAccountRisk machineAccountRisk;
        private FakePhoneRisk fakePhoneRisk;
        private FakeDeviceRisk fakeDeviceRisk;
        @JsonProperty("ip_country")
        private String ipCountry;
        @JsonProperty("ip_province")
        private String ipProvince;
        @JsonProperty("ip_city")
        private String ipCity;
        private String verifyType;
    }

    @Data
    public static class MachineAccountRisk{
        private Date tokenSampleLastTs;
        private String tokenSampleDesc;
    }

    @Data
    public static class FakePhoneRisk{
        private Date phoneSampleLastTs;
        private String phoneSampleDesc;
    }

    @Data
    public static class FakeDeviceRisk{
        private Date smidSampleLastTs;
        private String smidSampleDesc;
    }

    @Data
    public static class Hit{
        private String description;
        private String model;
        private String riskLevel;
        private String verifyType;
    }
}


