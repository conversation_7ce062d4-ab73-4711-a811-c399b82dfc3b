package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.time.LocalDate;

@Getter
public enum ZodiacSign {
    ARIES("Aries", "icons/aries.png", LocalDate.of(0, 3, 21), LocalDate.of(0, 4, 19)),
    TAURUS("Taurus", "icons/taurus.png", LocalDate.of(0, 4, 20), LocalDate.of(0, 5, 20)),
    GEMINI("Gemini", "icons/gemini.png", LocalDate.of(0, 5, 21), LocalDate.of(0, 6, 20)),
    CANCER("Cancer", "icons/cancer.png", LocalDate.of(0, 6, 21), LocalDate.of(0, 7, 22)),
    LEO("Leo", "icons/leo.png", LocalDate.of(0, 7, 23), LocalDate.of(0, 8, 22)),
    VIRGO("Virgo", "icons/virgo.png", LocalDate.of(0, 8, 23), LocalDate.of(0, 9, 22)),
    LIBRA("Libra", "icons/libra.png", LocalDate.of(0, 9, 23), LocalDate.of(0, 10, 22)),
    SCORPIO("Scorpio", "icons/scorpio.png", LocalDate.of(0, 10, 23), LocalDate.of(0, 11, 21)),
    SAGITTARIUS("Sagittarius", "icons/sagittarius.png", LocalDate.of(0, 11, 22), LocalDate.of(0, 12, 21)),
    CAPRICORN("Capricorn", "icons/capricorn.png", LocalDate.of(0, 12, 22), LocalDate.of(0, 1, 19)),
    AQUARIUS("Aquarius", "icons/aquarius.png", LocalDate.of(0, 1, 20), LocalDate.of(0, 2, 18)),
    PISCES("Pisces", "icons/pisces.png", LocalDate.of(0, 2, 19), LocalDate.of(0, 3, 20));

    private final String name;
    private final String icon;
    private final LocalDate startDate;
    private final LocalDate endDate;

    ZodiacSign(String name, String icon, LocalDate startDate, LocalDate endDate) {
        this.name = name;
        this.icon = icon;
        this.startDate = startDate;
        this.endDate = endDate;
    }

    /**
     * 判断指定日期是否在星座日期范围内。
     */
    public boolean isInRange(LocalDate date) {
        int month = date.getMonthValue();
        int day = date.getDayOfMonth();

        LocalDate dateOfYear = LocalDate.of(0, month, day);

        if (startDate.getMonthValue() <= endDate.getMonthValue()) {
            // 日期范围在同一年
            return (dateOfYear.isEqual(startDate) || dateOfYear.isEqual(endDate) ||
                    (dateOfYear.isAfter(startDate) && dateOfYear.isBefore(endDate)));
        } else {
            // 日期范围跨年（如摩羯座：12.22 - 1.19）
            return (dateOfYear.isEqual(startDate) || dateOfYear.isEqual(endDate) ||
                    dateOfYear.isAfter(startDate) || dateOfYear.isBefore(endDate));
        }
    }

    public static ZodiacSign getZodiacSign(LocalDate birthDate) {
        for (ZodiacSign sign : ZodiacSign.values()) {
            if (sign.isInRange(birthDate)) {
                return sign;
            }
        }
        return null; // 如果没有匹配到星座
    }
}
