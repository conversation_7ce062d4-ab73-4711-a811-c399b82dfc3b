package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FollowRelation {

    FOLLOW_NONE(0,"没有关系"),
    FOLLOW_FOLLOWING(1,"关注"),
    FOLLOW_FOLLOWERS(2,"被关注，粉丝"),
    FOLLOW_MUTUAL(3,"互关");

    private Integer type;

    private String desc;

    public static FollowRelation getFrequencyType(Integer type){
        if (type != null) {
            for (FollowRelation value : values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
        }
        return null;
    }

}
