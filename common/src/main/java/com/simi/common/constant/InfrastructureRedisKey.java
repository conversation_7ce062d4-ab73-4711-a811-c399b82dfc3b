package com.simi.common.constant;
import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * Oauth2 Redis Key
 *
 * <AUTHOR>
 * @date 2023/11/2 10:58
 */
@Getter
@AllArgsConstructor
public enum InfrastructureRedisKey implements BaseRedisKey {

    app_version_valid,  //有效的版本控制

    sms_send_record,
    sms_ip_limit,
    sms_phone_limit,
    sms_device_limit,

    // todo 临时加的短信次数免校验flag
    sms_count_limit_flag,
    /**
     * 客户端设备
     */
    client_device,
    client_device_lock,

    /**
     * 签到
     */
    checkin,
    /**
     * 签到锁
     */
    checkin_lock,
    /**
     * 签到配置
     */
    checkin_config,
    ;

    @Override
    public String getName() {
        return this.name();
    }

}

