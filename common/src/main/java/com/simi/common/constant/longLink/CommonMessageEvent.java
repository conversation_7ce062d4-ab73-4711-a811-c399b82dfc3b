package com.simi.common.constant.longLink;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CommonMessageEvent {
    //房间
    RoomScreenMessageEvent(0,"公屏消息"),
    RoomToastNotifyEvent(1,"房间toast提示"),
    RoomPopupNotifyEvent(2,"房间弹窗提示"),
    RoomMicUpdateEvent(3,"麦位变动"),
    RoomMicInviteUpEvent(4,"邀请上麦"),
    RoomKickOutEvent(5,"踢出房间"),
    RoomAudienceUpdateEvent(6,"观众列表更新"),
    RoomIdentityUpdateEvent(7,"房间身份更新"),
    RoomUserConfUpdateEvent(8,"房间内用户配置信息更新"),
    RoomMicAllMuteEvent(9,"房间一键禁麦"),
    RoomConfigUpdateEvent(10,"房间配置更新"),
    RoomMicApplyListUpdateEvent(11,"房间麦位申请列表更新"),
    RoomUserEnterVehicleEvent(12,"进房特效"),
    RoomSendGiftNumChangeEvent(13,"房间送礼产生的金币价值总和变更"),

    //演唱
    BookingSongActionEvent(100,"点歌操作信令"),
    BookingSongInviteEvent(101,"邀请演唱"),
    BookingSongApplyListUpdateEvent(102,"点歌申请列表更新"),
    StageProgressEvent(110,"演唱舞台流程信令"),
    StagePauseEvent(111,"舞台暂停信令"),

    //用户
    UserFollowUpdateEvent(200,"关注关系变动，uid操作结果抄送给target_uid"),
    UserBlacklistUpdateEvent(210,"黑名单变动，uid操作结果抄送给target_uid"),
    UserLevelEvent(220,"用户等级"),

    //礼物
    PushSendGiftMsgEvent(230,"送礼消息"),


    // 通用全服飘屏
    GeneralBroadcastEvent(300,"通用全服飘屏"),

    //客户端
    ClientLogNotifyEvent(1000,"客户端日志上报"),

    ;

    private Integer type;

    private String desc;
}
