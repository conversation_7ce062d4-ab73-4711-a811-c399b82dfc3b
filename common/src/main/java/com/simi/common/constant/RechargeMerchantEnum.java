package com.simi.common.constant;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

@Getter
@AllArgsConstructor
public enum RechargeMerchantEnum {

    PANDA_PAY("Pandapay"),

    ;
    final String merchant;

    public static RechargeMerchantEnum getByMerchant(String merchant) {
        return Arrays.stream(RechargeMerchantEnum.values()).filter(e -> StrUtil.equalsIgnoreCase(merchant, e.getMerchant())).findAny().orElse(null);
    }
}
