package com.simi.common.constant.crazyTriple;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 */
@Getter
@AllArgsConstructor
public enum CrazyTripleProductTypeEnum {


    SMALL(CrazyTripleGameProductTypeEnum.CRAZY_TRIPLE_GAME_PRODUCT_TYPE_SMALL, "SMALL"),
    BIG(CrazyTripleGameProductTypeEnum.CRAZY_TRIPLE_GAME_PRODUCT_TYPE_BIG, "BIG"),
    TRIPLE(CrazyTripleGameProductTypeEnum.CRAZY_TRIPLE_GAME_PRODUCT_TYPE_TRIPLE, "TRIPLE"),
    ;

    private final CrazyTripleGameProductTypeEnum productType;
    private final String name;

    public static String getNameByProductType(CrazyTripleGameProductTypeEnum productType) {
        for (CrazyTripleProductTypeEnum productTypeEnum : CrazyTripleProductTypeEnum.values()) {
            if (productTypeEnum.getProductType().equals(productType)) {
                return productTypeEnum.getName();
            }
        }
        return null;
    }
}

