package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum VersionUpdateConditionEnum {

    LT(0, "小于"),
    EQ(1, "等于"),
    LQ(2, "小于等于");

    private Integer type;

    private String desc;

    public static VersionUpdateConditionEnum getByType(Integer type){
        for (VersionUpdateConditionEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
