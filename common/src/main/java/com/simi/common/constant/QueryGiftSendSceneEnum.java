package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum QueryGiftSendSceneEnum {

    /**
     *房间；
     */
    GIFT_SEND_SCENE_ROOM(1),
    /**
     *私信；
     */
    GIFT_SEND_SCENE_PRIVATE_CHAT(2),
    /**
     *动态；
     */
    GIFT_SEND_SCENE_POST(3),

    ;
    final Integer number;

    public static QueryGiftSendSceneEnum forNumber(Integer scene) {
        return Stream.of(QueryGiftSendSceneEnum.values()).filter(e -> Objects.equals(e.getNumber(), scene))
                .findFirst().orElse(null);
    }
}
