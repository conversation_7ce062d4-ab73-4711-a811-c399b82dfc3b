package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExpressionType {

    GENERAL(0,"普通"),
    RANDOM(1,"随机");

    private Integer type;

    private String desc;

    public static ExpressionType getFrequencyType(Integer type){
        if (type != null) {
            for (ExpressionType value : values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
        }
        return null;
    }
}
