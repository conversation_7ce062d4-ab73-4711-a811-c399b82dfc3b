package com.simi.common.constant;

import cn.hutool.core.util.StrUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Locale;
import java.util.Optional;
import java.util.stream.Stream;

/**
 * 语言
 *
 * <AUTHOR>
 * @date 2023/11/17 20:30
 */
@Getter
@AllArgsConstructor
public enum LanguageEnum {
    /**
     * 英语
     */
    en(new Locale("en")),

    /**
     * 阿根廷
     */
    //id(new Locale("id")),

    /**
     * 阿语
     */
    ar(new Locale("ar")),

    ;
    final Locale language;

    public static LanguageEnum getLang(String name) {
        return getLang(name, false);
    }

    public static LanguageEnum getLang(String name, boolean throwEx) {
        Optional<LanguageEnum> option = Stream.of(LanguageEnum.values()).filter(e -> e.name().equals(name)).findAny();
        if (option.isEmpty() && throwEx) {
            throw new IllegalArgumentException(StrUtil.format("language[{}] not support", name));
        }
        return option.orElse(LanguageEnum.en);
    }

    public static LanguageEnum langWithoutDefault(String name) {
        return Stream.of(LanguageEnum.values()).filter(e -> e.name().equals(name)).findAny().orElse(null);
    }
}
