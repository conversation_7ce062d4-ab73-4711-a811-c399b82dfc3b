package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 *
 */
@AllArgsConstructor
@Getter
public enum RewardPackCopywritingEnum {

    SEND_REWARD_SUCCESS(1, "cp.reward.pack.sendReward.success", "奖励发放成功"),

    SEND_ACT_REWARD_SUCCESS(2, "cp.reward.pack.sendReward.success", "奖励发放成功"),

    SEND_NEW_USER_REWARD_SUCCESS(3, "cp.new.reward.pack.sendReward.success", "新手礼包发放成功"),

    SEND_FEMALE_SUPPORTERS_REWARD_SUCCESS(4, "cp.female.supporters.reward.pack", "女性支持者活动发放奖励包"),

    EID_AL_ADHA_REWARD_DAY_SUCCESS(5, "eid_al_adha_act_reward_day", "宰牲节活动奖励包按日"),

    EID_AL_ADHA_REWARD_GIFT_SUCCESS(6, "eid_al_adha_act_reward_gift", "宰牲节活动奖励包按礼包"),

    MISSION_DAILY_SIGN(7, "mission_daily_sign", "每日任务签到"),

    FIRST_RECHARGE_GIFT_BAG(9, "first_recharge_gift_bag", "首充礼包"),

    TAKE_THE_MIC_FOR_15MINS(10, "take_the_mic_for_15mins", "当天在麦位上15 分钟"),

    STAY_IN_ROOM_45_MINS(11, "stay_in_room_45_mins", "当天在房间内45 分钟"),

    WIN_1000_COINS_IN_FRUIT_PARTY(12, "win_1000_coins_in_fruit_party", "当天在游戏水果派对中win1000 金币"),

    SEND_GIFTS_IN_THE_ROOM(13, "send_gifts_in_the_room", "当天在房间内送礼1 次"),

    FOLLOW_A_NEW_FRIEND(14, "follow_a_new_friend", "当天关注一个用户"),

    /**
     * 周星榜
     */
    WEEKLY_STAR(15, "weekly_star", "周星榜"),
    /**
     * 贵族
     */
    ARISTOCRACY(16, "aristocracy_pack", "贵族"),
    ARISTOCRACY_CHECK_IN(17, "aristocracy_check_in", "贵族-每日任务签到"),
    /**
     * PK周星榜-送礼榜
     */
    WEEKLY_STAR_GIFT_RANKING(18, "weekly_star_gift_ranking", "周星榜-送礼榜"),
    /**
     * PK周星榜-收礼榜
     */
    WEEKLY_STAR_RECEIVE_RANKING(19, "weekly_star_receive_ranking", "周星榜-收礼榜"),

    /**
     * 金币数单位
     */
    COINS(20, "Coins", "金币数单位"),

    /**
     * 水果机，赢钱奖励
     */
    FRUIT_LOTTERY(21, "fruit_winner", "水果机，赢钱奖励"),
    /**
     * 水果机，总局数
     */
    FRUIT_LOTTERY_TOTAL(22, "fruit_lottery_total", "水果机，总局数"),


    PARTY_ACTIVITY_REWARD(25, "cp.reward.pack.sendReward.success", "奖励发放成功"),


    INVITATION_PARTY_ACTIVITY_REWARD(26, "invitation_party_activity_reward", "邀请奖励发放成功"),

    ;

    private final Integer source;
    private final String key;
    private final String desc;


    /**
     * 根据 source 获取枚举
     */
    public static RewardPackCopywritingEnum getBySource(Integer source) {
        return Arrays.stream(RewardPackCopywritingEnum.values())
                .filter(e -> Objects.equals(source, e.source))
                .findFirst().orElse(null);
    }

}
