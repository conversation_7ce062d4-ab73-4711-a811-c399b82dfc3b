package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PurchasesState {

    succeed(0,"成功"),
    cancel(1,"取消"),
    wait(1,"等待"),
    ;

    final Integer number;

    final String desc;

    public static PurchasesState forNumber(Integer platform) {
        for (PurchasesState value : values()) {
            if (value.getNumber().equals(platform)) {
                return value;
            }
        }
        return null;
    }
}
