package com.simi.common.constant.rocketmq;

/**
 * RocketMQ 消费者组（按环境前缀统一管理，静态常量形式）
 */
public interface RocketMQGroup {

    /**
     * 环境前缀，开发环境使用 "dev_"，正式环境使用 ""
     */
    String PREFIX = "dev_"; // 切正式服时改成 ""

    String GROUP_SEND_GIFT = PREFIX + "simi_send_gift";
    String GROUP_RECHARGE_CALL_BACK = PREFIX + "simi_recharge_call_back";
    String GROUP_CONSTRAINT_DROP_OUT = PREFIX + "simi_constraint_drop_out";

    String TIM_LOGIN_CALLBACK = PREFIX + "simi_tim_login_callback";
    String TIM_LOGOUT_CALLBACK = PREFIX + "simi_tim_logout_callback";
    String TIM_VERIFY_IMAGE = PREFIX + "tim_verify_image";
    String ROOM_PUBLIC_SCREEN_SILENCE = PREFIX + "simi_room_public_screen_silence";
    String BLACK_KICK_OUT_ALL = PREFIX + "black_kick_out_all";

    String MISSION_COMPLETE = PREFIX + "simi_mission_complete";
    String STREAM_EVENT_TASK_STATISTICS = PREFIX + "simi_stream_event_task_statistics";
    String PK_OVER_DELAY = PREFIX + "simi_pk_over_delay";

    String ARISTOCRACY_UNSUBSCRIBE = PREFIX + "aristocracy_unsubscribe_consumer";
    String ROOM_PARTY_NOTIFY_CONSUMER = PREFIX + "simi_room_party_notify_consumer";
    String CANAL_MSG = PREFIX + "canal_msg";
    String ARISTOCRACY_CONSUMER = PREFIX + "aristocracy_consumer";

    String RED_PACKET_SETTLE_CONSUMER = PREFIX + "simi_red_packet_settle_consumer";
    String GRAB_RED_PACKET_RECORD_CONSUMER = PREFIX + "simi_grab_red_packet_record_consumer";

    String LUCKY_WHEEL_DELAY_CONSUMER = PREFIX + "lucky_wheel_delay_consumer";
}
