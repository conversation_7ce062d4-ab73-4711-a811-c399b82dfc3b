package com.simi.common.constant.room;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum RoomMicUpType {

    ANYONE_MIC_UP(0,"任意"),
    NEED_PERMISSION_MIC_UP(1,"需要申请");

    private Integer type;

    private String desc;

    public static RoomMicUpType getByType(Integer type){
        for (RoomMicUpType value : values()) {
            if (value.getType().equals(type)){
                return value;
            }
        }
        return null;
    }
}
