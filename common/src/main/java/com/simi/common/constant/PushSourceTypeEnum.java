package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * push消息来源类型
 * <AUTHOR>
 * @date 2024/04/28 20:47
 **/
@Getter
@AllArgsConstructor
public enum PushSourceTypeEnum {

    /**
     * fcm直接推送
     */
    PUSH_SOURCE_TYPE_FCM(1),

    /**
     * 通过腾讯IM 私信推送
     */
    PUSH_SOURCE_TYPE_TENCENT_IM(2),

    /**
     * 通过腾讯IM 系统推送
     */
    PUSH_SOURCE_TYPE_TENCENT_IM_SYSTEM(3),

    /**
     * 通过腾讯IM 动态推送
     */
    PUSH_SOURCE_TYPE_TENCENT_IM_MOMENT(4),

    /**
     * 通过FCM 动态推送
     */
    PUSH_SOURCE_TYPE_FCM_MOMENT(5),
    ;

    final Integer type;

}
