package com.simi.common.constant;

/**
 * 系统配置文件常量
 *
 * <AUTHOR>
 * @date 2023/11/13 20:16
 */
public interface SystemConfigConstant {

    /**
     * 注册设备限制
     */
    String REGISTER_DEVICE_LIMIT = "register_device_limit";

    /**
     * 注册设备限制白名单
     */
    String REGISTER_DEVICE_WHITE_LIST = "register_device_white_list";

    /**
     * 注册ip限制
     */
    String REGISTER_IP_LIMIT = "register_ip_limit";

    /**
     * 注册ip限制白名单
     */
    String REGISTER_IP_WHITE_LIST = "register_ip_white_list";

    /**
     * 当天注册ip限制
     */
    String REGISTER_TODAY_IP_LIMIT = "register_today_ip_limit";

    /**
     * 当天注册ip白名单限制
     */
    String REGISTER_TODAY_IP_LIMIT_WHITE_LIST = "register_today_ip_limit_white_list";

    /**
     * 默认头像
     */
    String DEFAULT_AVATAR = "https://simi-cos.oss-eu-central-1.aliyuncs.com/20250320-154907.webp";
    String DEFAULT_MALE_AVATAR = "https://simi-cos.oss-eu-central-1.aliyuncs.com/prod/20250121-114203.png";
    String DEFAULT_FEMALE_AVATAR = "https://simi-cos.oss-eu-central-1.aliyuncs.com/prod/20250121-114150.png";


    /**
     * 短信运营商
     */
    String SMS_CARRIER = "sms_carrier";


    /**
     * 是否送礼给自己 1、是 2 、否（全平台生效）
     */
    String SEND_GIFT_BY_ONESELF = "send_gift_by_oneself";

    /**
     * 进房系统公告 en
     */
    String IN_ROOM_SYSTEM_NOTICE_EN = "in_room_system_notice_en";

    /**
     * 进房系统公告 ar
     */
    String IN_ROOM_SYSTEM_NOTICE_AR = "in_room_system_notice_ar";


    /**
     * 房间黑名单
     */
    String BLOCK_ROOM_UID_LIST = "block_room_uid_list";

    /**
     * 登录限制uid白名单
     */
    String LOGIN_RESTRICTION_UIDS_WHITE = "login_restriction_uids_white";
    /**
     * 登录限制app版本白名单
     */
    String LOGIN_RESTRICTION_APP_VERSION_WHITE = "login_restriction_app_version_white";

    /**
     * 登录限制ip黑名单
     */
    String LOGIN_RESTRICTION_NOT_ALLOWED_IP_LIST = "login_restriction_not_allowed_ip_list";
    /**
     * 登录限制ip国家列表黑名单
     */
    String LOGIN_RESTRICTION_NOT_ALLOWED_IP_COUNTRY_LIST = "login_restriction_not_allowed_ip_country_list";
    /**
     * 登录限制系统语言黑名单
     */
    String LOGIN_RESTRICTION_NOT_ALLOWED_SYS_LANGUAGE_LIST = "login_restriction_not_allowed_sys_language_list";

    /**
     * 登录限制sim国家黑名单
     */
    String LOGIN_RESTRICTION_NOT_ALLOWED_SIM_LIST = "login_restriction_not_allowed_sim_list";

    /**
     * 登录限制时区黑名单
     */
    String LOGIN_RESTRICTION_NOT_ALLOWED_TIMEZONE_LIST = "login_restriction_not_allowed_timezone_list";

    /**
     * 登录限制商店code黑名单
     */
    String LOGIN_RESTRICTION_NOT_ALLOWED_STORE_CODE_LIST = "login_restriction_not_allowed_store_code_list";

    /**
     * 审核中的安卓APP版本
     */
    String APP_VERSION_AUDITING_ANDROID = "app_version_auditing_android";

    /**
     * 审核中的IOS-APP版本
     */
    String APP_VERSION_AUDITING_IOS = "app_version_auditing_ios";

    /**
     * 审核中的UID
     */
    String APP_VERSION_AUDITING_UID = "app_version_auditing_uid";

    /**
     * 审核中需过滤的bannerID列表(逗号分割)
     */
    String AUDITING_FILTER_BANNER_IDS = "auditing_filter_banner_ids";

    /**
     * 邀请好友h5链接域名
     */
    String INVITE_H5_LINK_URL = "invite_h5_link_url";

    /**
     * 邀请美金分成比例
     */
    String INVITE_USD_SCALE = "invite_usd_scale";

    /**
     * 主播代理达标活动时间配置
     */
    String ACT_ANCHOR_INVITE_TIME = "act_anchor_invite_time";
    /**
     * 主播达标活动配置
     */
    String ACT_ANCHOR_INVITE_ANCHOR_CONFIG = "act_anchor_invite_anchor_config";
    /**
     * 代理达标活动配置
     */
    String ACT_ANCHOR_INVITE_AGENT_CONFIG = "act_anchor_invite_agent_config";

    /**
     * 邀请好友成功关注 各语言打招呼文本消息(json格式)
     */
    String INVITE_SUCCESS_GREETING_TEXT = "invite_success_greeting_text";

    /**
     * 用户失活通知邀请者消息文本配置(json格式)
     */
    String USER_INACTIVATION_NOTIFY_TEXT = "user_inactivation_notify_text";

    /**
     * 用户未活跃通知邀请者消息文本配置(json格式)
     */
    String USER_NOT_ACTIVE_NOTIFY_TEXT = "user_not_active_notify_text";
    /**
     * 提现渠道：1 BackCard 2、payoneer 3、payPal 4、USDT
     */
    String WITHDRAW_CHANNEL_TYPE = "withdraw_channel_type";

    /**
     * 收礼获得奖励 100%钻石+20%金币(英语)
     */
    String USER_RECEIVE_GIFTS_EN = "user_receive_gifts_en";

    /**
     * 收礼收益 100% 钻石+ 20% 金币(阿语)
     */
    String USER_RECEIVE_GIFTS_AR = "user_receive_gifts_ar";

    /**
     * 资源道具奖励icon配置(1001-金币; 1002-钻石; 1004-美金; 1005金票)
     */
    String RESOURCE_REWARD_ICON_CONFIG = "resource_reward_icon_config";

    /**
     * 发送弹幕扣除钻石
     */
    String BULLET_CHAT_DEDUCT_GOLD = "bullet_chat_deduct_gold";

    /**
     * 房间公屏一分钟内可重复发送次数
     */
    String ROOM_SEND_MSG_TIME = "room_send_msg_time";

    /**
     * 房间公屏消息发送间隔（秒）
     */
    String ROOM_SEND_INTERVAL = "room_send_interval";

    /**
     * 热门国家列表(大写逗号分割)
     */
    String HOT_COUNTRY_CODE_LIST = "hot_country_code_list";

    /**
     * app支持的国家列表code(大写逗号分割)
     */
    String SUPPORTED_COUNTRY_LIST = "supported_country_list";

    /**
     * app默认的国家code(大写)
     */
    String DEFAULT_COUNTRY_CODE = "default_country_code";
    /**
     * 收礼钻石分成比例
     */
    String DIAMOND_EARNINGS_SCALE = "diamond_earnings_scale";
    /**
     * 送礼金币分成比例
     */
    String GOLD_EARNINGS_SCALE = "gold_earnings_scale";
    /**
     * 币商白名单
     */
    String COIN_DEALER_WHITE_LIST = "coin_dealer_white_list";

    /**
     * 客服房间号（房间id）
     */
    String CUSTOMER_SERVICE_ROOM = "customer_service_room";

    /**
     * 钱包美金标题（英语）
     */
    String WALLET_USD_TITLE_EN = "wallet_usd_title_en";

    /**
     * 钱包美金标题（阿语）
     */
    String WALLET_USD_TITLE_AR = "wallet_usd_title_ar";

    /**
     * 提现美金提示文案（阿语）
     */
    String WALLET_USD_REMINDER_AR = "wallet_usd_reminder_ar";

    /**
     * 提现美金提示文案（英语）
     */
    String WALLET_USD_REMINDER_EN = "wallet_usd_reminder_en";


    /**
     * 发放新用户礼包
     */
    String SEND_REWARD_PACK_BY_NEW_USER = "send_reward_pack_by_new_user";


    String COUNTRY_CODE_SCOPE_SKIP_ROOMS = "country_code_scope_skip_rooms";

    String DEFAULT_SCOPE_SKIP_ROOMS = "default_scope_skip_rooms";


    /**
     * 短信校验开关(1-需校验; 0-免校验)
     */
    String SMS_VERIFICATION_SWITCH = "sms_verification_switch";
    /**
     * 女性支持者活动时间
     */
    String FEMALE_SUPPORTERS_ACT_TIME = "female_supporters_act_time";

    /**
     * 女性支持者活动小时范围
     */
    String FEMALE_SUPPORTERS_HOUR_RANGE = "female_supporters_hour_range";

    /**
     * 女性支持者活动用户名单(用户id逗号分割)
     */
    String FEMALE_SUPPORTERS_USER_LIST = "female_supporters_user_list";

    /**
     * 女性支持者活动配置
     */
    String FEMALE_SUPPORTERS_CONFIG = "female_supporters_config";

    /**
     * 女性支持者活动跳转id
     */
    String FEMALE_SUPPORTERS_JUMP_ROOM_ID = "female_supporters_jump_room_id";


    /**
     * 邀请链接ip触发白名单, 英文逗号分割
     */
    String INVITE_LINK_TRIGGER_IP_WHITELIST = "invite_link_trigger_ip_whitelist";

    /**
     * 2024宰牲节活动时间
     */
    String ACT_CORBAN_2024_TIME = "act_corban_2024_time";
    /**
     * 2024宰牲节活动任务配置
     */
    String ACT_CORBAN_2024_TASK_CONFIG = "act_corban_2024_task_config";
    /**
     * 2024宰牲节活动任务奖励配置
     */
    String ACT_CORBAN_2024_REWARD = "act_corban_2024_reward";
    /**
     * 2024宰牲节活动充值附带奖励包配置
     */
    String ACT_CORBAN_2024_RECHARGE_REWARD = "act_corban_2024_recharge_reward";

    /**
     * 官网apk下载入口
     */
    String H5_SKIP_APK_URL = "h5_skip_apk_url";

    /**
     * 官网跳转谷歌商店下载入口
     */
    String H5_SKIP_GOOGLE_PLAY_URL = "h5_skip_google_play_url";

    /**
     * 官网跳转苹果商城下载入口
     */
    String H5_SKIP_APPLE_STORE_URL = "h5_skip_apple_store_url";

    /**
     * 游戏入口展示最低财富等级
     */
    String GAME_ENTRANCE_WEALTH = "game_entrance_wealth";

    /**
     * 游戏入口展示最低魅力等级
     */
    String GAME_ENTRANCE_CHARM = "game_entrance_charm";

    /**
     * 用户信息banner过滤, 7#3
     */
    String CUSTOMIZATION_USER_BANNER_FILTER = "customization_user_banner_filter";

    /**
     * 水果派对特效配置
     */
    String FRUIT_PARTY_EFFECT_CONFIG = "fruit_party_effect_config";

    /**
     * kb特效配置
     */
    String KING_BATTLE_EFFECT_CONFIG = "king_battle_effect_config";

    /**
     * 播放器可播放音频最小字节数(单位KB)
     */
    String ROOM_PLAYER_AUDIO_MIN_BYTES = "room_player_audio_min_bytes";

    /**
     * H5 banner 币商列表过滤指定用户uid(英文逗号分割)
     */
    String H5_COINDEALER_LIST_BANNER_FILTER = "h5_coindealer_list_banner_filter";

    /**
     * 弹幕列表显示最大数量
     */
    String BULLET_CHAT_LIST_MAX = "bullet_chat_list_max";

    /**
     * 任务-每日签到配置
     */
    String MISSION_DAILY_SIGN_CONFIG = "mission_daily_sign_config";
    /**
     * 任务
     */
    String MISSION_CONFIG = "mission_config";

    /**
     * 首充礼包
     */
    String FIRST_RECHARGE_REWARD = "first_recharge_reward";

    /**
     * 首充礼包（安卓）
     */
    String FIRST_RECHARGE_SKU_ANDROID = "first_recharge_sku_android";

    /**
     * 首充礼包（ios）
     */
    String FIRST_RECHARGE_SKU_IOS = "first_recharge_sku_ios";

    /**
     * crazy-triple 特效配置
     */
    String CRAZY_TRIPLE_EFFECT_CONFIG = "crazy_triple_effect_config";

    /**
     * 周星榜礼物Icon
     */
    String WEEK_STAR_GIFT_ICON = "weekly_star_gift_icon";

    /**
     * 指定奖励包id列表
     */
    String APPOINT_REWARDPACK_IDS = "appoint_rewardpack_ids";

    /**
     * 消费热度倍率
     */
    String CONSUME_HEAT_RATE = "consume_heat_rate";

    /**
     * 默认礼物tag
     */
    String DEFAULT_GIFT_TAB = "default_gift_tab";

    /**
     * 房间party等级限制配置
     */
    String ROOM_PARTY_LEVEL_CONFIG = "room_party_level_config";

    /**
     * 房间party评级奖励
     */
    String ROOM_PARTY_REWARD_CONFIG = "room_party_reward_config";

    /**
     * 房间party默认图片
     */
    String ROOM_PARTY_DEFAULT_PIC = "room_party_default_pic";


    /**
     * 自动推送任务文案配置
     */
    String AUTO_PUSH_MESSAGE_CONFIG = "auto_push_message_config";

    /**
     * 游戏Id+name
     */
    String ROOM_GAME_ID_AND_NAME = "room_game_id_and_name";

    /**
     * 幸运礼物配置（白名单）
     */
    String LUCKY_GIFT_CONFIG_WHITE = "lucky_gift_config_white";

    /**
     * 幸运礼物配置
     */
    String LUCKY_GIFT_CONFIG = "lucky_gift_config";

    /**
     * 幸运礼物白名单用户
     */
    String LUCKY_GIFT_WHITE_UID = "lucky_gift_white_uid";

    /**
     * 勋章佩戴数量与贵族等级的映射配置(贵族等级:数量, 例如5级佩戴6个-5:6)
     */
    String MEDALS_WEAR_NUM_ARISTOCRACY_LEVEL_CONFIG = "medals_wear_num_aristocracy_level_config";

    /**
     * 幸运礼物触发横幅
     */
    String LUCKY_GIFT_BANNER_CONFIG = "lucky_gift_banner_config";

    /**
     * 幸运礼物钻石比例
     */
    String LUCK_DIAMOND_EARNINGS_SCALE = "luck_diamond_earnings_scale";

    /**
     * 幸运礼物金币分成
     */
    String LUCK_GOLD_EARNINGS_SCALE = "luck_gold_earnings_scale";

    /**
     * 幸运礼物中奖金币比例
     */
    String LUCK_GIFT_GOLD_EARNINGS_SCALE = "luck_gift_gold_earnings_scale";


    /**
     * 水果榜单开始日期
     */
    String FRUIT_RANK_START = "fruit_rank_start";

    /**
     * 水果榜单-活动房间id
     */
    String FRUIT_RANK_ROOM_ID = "fruit_rank_room_id";
    /**
     * 发送进房公告频率（分钟）
     */
    String SEND_ENTER_ROOM_NOTICE_RATE = "send_enter_room_notice_rate";

    /**
     * 房间数据校准处理开关
     */
    String ROOM_DATA_CORRECTION_SWITCH = "room_data_correction_switch";


    /**
     * pandaPay默认用户信息, #号分割(nick#phone)
     */
    String PANDAPAY_DEFAULT_INFO = "pandapay_default_info";

    /**
     * 对应sku支付汇率
     */
    String PAYMENT_EXCHANGE_RATE = "payment_exchange_rate";
    /**
     * 红包配置
     */
    String RED_PACKET_VISIBLE_CONFIG = "red_packet_visible_config";
    String RED_PACKET_CONFIG = "red_packet_config";
    String RED_PACKET_EFFECT_CONFIG = "red_packet_effect_config";

    /**
     * 幸运转盘赢的比例
     */
    String LUCKY_WHEEL_COMMISSION_RATIO = "lucky_wheel_commission_ratio";

    /**
     * 幸运转盘发起者比例
     */
    String LUCKY_WHEEL_GAME_STARTER_RATIO = "lucky_wheel_game_starter_ratio";


    /**
     * 幸运转盘最大参与人数
     */
    String LUCKY_WHEEL_MAX_PARTICIPANTS_SIZE = "lucky_wheel_max_participants_size";
    /**
     * 幸运转盘每局展示时间
     */
    String LUCKY_WHEEL_RESULT_SHOW_TIME = "lucky_wheel_result_show_time";

    /**
     * 幸运转盘每局转动时间
     */
    String LUCKY_WHEEL_TURN_TIME = "lucky_wheel_turn_time";
    /**
     * 幸运游戏开始前时间
     */
    String LUCKY_WHEEL_BEFORE_STARTING_TIME = "lucky_wheel_before_starting_time";

    /**
     * 幸运转盘超时时间
     */
    String LUCKY_WHEEL_DELAY_CANCEL_TIME = "lucky_wheel_delay_cancel_time";

    /**
     * 房间魅力值显示文案
     */
    String ROOM_MIC_CHARM_RULE = "room_mic_charm_rule";

    /**
     * 首页召回配置（region - 配置名称；country - 用户组；priorityRoom - 内容组；priority - 优先级）
     */
    String HOME_PAGE_SORTING_RULES = "home_page_sorting_rules";

    /**
     * 开播冷启动分比例（秒数）
     */
    String BROADCAST_START_SCORE = "broadcast_start_score";

    /**
     * 刷新开播冷启动分时间（秒数）
     */
    String REFRESH_ROOM_BROADCAST_TIME = "refresh_room_broadcast_time";


    /**
     * 豁免白名单：登录、注册、部分功能限制访问
     */
    String WHITELIST_EXEMPT_FUNCTIONS = "whitelist_exempt_functions";

    /**
     * 邀请收益金额配置（chargeNum -- 充值打到的金币数，award -- 奖励的钻石数）
     */
    String INVITATION_INCOME_AMOUNT = "invitation_income_amount";

    /**
     * 邀请发放奖励包id
     */
    String INVITATION_INCOME_REWARD_PACKAGE = "invitation_income_reward_package";
}
