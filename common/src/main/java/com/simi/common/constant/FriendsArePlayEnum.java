package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum FriendsArePlayEnum {

    FRIEND_ROOM(1,"好友的房间"),
    FRIEND(2,"关注的房间"),
    MY_ROOM(3,"我的房间"),
    COLLECT_ROOM(4,"收藏的房间");

    private Integer type;

    private String desc;

    public static FriendsArePlayEnum getFrequencyType(Integer type){
        if (type != null) {
            for (FriendsArePlayEnum value : values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
        }
        return null;
    }
}
