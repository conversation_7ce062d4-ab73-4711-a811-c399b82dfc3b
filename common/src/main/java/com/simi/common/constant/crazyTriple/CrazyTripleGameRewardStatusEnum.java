package com.simi.common.constant.crazyTriple;


public enum CrazyTripleGameRewardStatusEnum {
    CRAZY_TRIPLE_GAME_REWARD_STATUS_NONE(0, "占位"),
    CRAZY_TRIPLE_GAME_REWARD_STATUS_INIT(1, "初始"),
    CRAZY_TRIPLE_GAME_REWARD_STATUS_SUC(2, "成功"),
    CRAZY_TRIPLE_GAME_REWARD_STATUS_FAIL(3, "失败");

    private final int value;
    private final String description;

    CrazyTripleGameRewardStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
