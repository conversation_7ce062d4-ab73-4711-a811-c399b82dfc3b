package com.simi.common.constant;

import com.simi.common.config.ExpressionConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 游戏推送长链接类型
 */
@Getter
@AllArgsConstructor
public enum GamePushType {

    phase(1, "游戏阶段"),
    bet(2, "下注"),
    result(3,"开奖结果"),
    meWin(4,"中奖")

    ;
    private Integer type;

    private String desc;

    public static String getByType(Integer type){
        if (type != null) {
            for (GamePushType value : values()) {
                if (value.getType().equals(type)) {
                    return value.getDesc();
                }
            }
        }
        return null;
    }
}
