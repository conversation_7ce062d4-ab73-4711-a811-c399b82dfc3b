package com.simi.common.constant.tim;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum IMMsgType {

    UnknownIMMsg(0, "未知"),
    TextIMMsg(1, "文本"),
    SystemTextIMMsg(99, "系统文本消息"),
    SystemComplexIMMsg(100, "系统复杂消息"),
    GiftPrivateChatIMMsg(101, "私聊送礼消息 结构体见"),
    RewardPackSendIMMsg(102, "奖励包发放成功系统消息"),
    purseTransferAccountsImMsg(103, "钱包转账消息"),
    InviteActiveMsg(104, "好友助手-用户活跃消息"),
    transferAccounts(105, "币商金票转赠金币消息"),
    // 贵族消息
    Aristocracy(106, "贵族消息"),
    PrivatePhoto(107, "个人相册"),
    // VIP
    Vip(108, "VIP消息"),
    InvitationIMMsg(109, "动态消息"),
    InvitationNoticeMsg(110, "互动通知"),

    //代理
    AgentSendsVerificationCodeIMMsg(210, "验证码"),
    AgentSendsInviteIMMsg(211, "发送邀请"),
    ;

    private Integer number;

    private String desc;
}
