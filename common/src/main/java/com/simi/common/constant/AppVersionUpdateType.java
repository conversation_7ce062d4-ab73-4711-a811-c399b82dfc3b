package com.simi.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AppVersionUpdateType {

    FORCE(0,"强制更新"),
    RECOMMEND(1,"建议更新");

    private Integer type;

    private String desc;

    public static AppVersionUpdateType getByType(Integer type){
        for (AppVersionUpdateType value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
