package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PositionEnum {

    POSITION_NONE(0,"占位"),
    HOME(1,"首页"),
    ROOM(2,"房间"),
    FLASH_SCREEN(3,"启动闪屏"),
    PURSE(4,"钱包"),

    ;


    final Integer number;

    final String desc;

    public static PositionEnum  forNumber(Integer platform) {
        for (PositionEnum value : values()) {
            if (value.getNumber().equals(platform)) {
                return value;
            }
        }
        return null;
    }
}
