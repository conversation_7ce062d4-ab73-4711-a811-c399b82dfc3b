package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum SignTypeEnum {

    PHONE(0,"phone"),
    FACEBOOK(1,"facebook"),
    GOOGLE(2,"google"),
    MOBILE_VALUE(3,"mobile_value"),
    APPLE(4,"apple"),
    HUAWEI(5,"huawei"),

    ;


    final Integer code;

    final String value;

    public static SignTypeEnum getSignByType(Integer type) {
        return Arrays.stream(SignTypeEnum.values())
                .filter(e -> Objects.equals(e.getCode(), type)).findFirst().orElse(null);
    }
}
