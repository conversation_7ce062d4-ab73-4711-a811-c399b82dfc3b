package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum RechargeRecordStatusEnum {

    RECHARGE_CREATE(1,"创建"),
    RECHARGE_SUCCESS(2,"SUCCESS"),
    RECHARGE_REFUND(3,"退款"),
    RECHARGE_FAILED(4,"FAILED"),
    RECHARGE_CLOSED(5,"CLOSED");

    private final Integer type;

    private final String desc;

    public static RechargeRecordStatusEnum getByDesc(String desc){
        for (RechargeRecordStatusEnum value : values()) {
            if (value.getDesc().equals(desc)){
                return value;
            }
        }
        return null;
    }

    public static RechargeRecordStatusEnum getByType(Integer type) {
        return Arrays.stream(RechargeRecordStatusEnum.values()).filter(e -> Objects.equals(type, e.getType())).findAny().orElse(null);
    }
}
