package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *
 */
@Getter
@AllArgsConstructor
public enum I18nKeyEnum {

    GIFT("gift", "礼物", ""),
    COIN("coin", "礼物", "{\"en\":\"coin\",\"ar\":\"عملة معدنية\"}"),
    DIAMOND("diamond", "钻石", ""),
    USD("usd", "美金", "{\"en\":\"USD\",\"ar\":\"USD\"}"),
    GOLDEN_TICKET("golden_ticket", "金票", "{\"en\":\"golden ticket\",\"ar\":\"تذكرة ذهبية\"}"),
    ACTIVE_EXP("active_exp", "活跃值", ""),
    WEALTH_EXP("wealth_exp", "活跃值", ""),
    CHARM_EXP("charm_exp", "活跃值", ""),
    DAY("day", "天", "天"),
    DAYS("days", "天", "天（复数）"),
    FOLLOW("follow", "关注", ""),
    // 贵族
    ARISTOCRACY("aristocracy", "贵族", ""),
    // VIP 经验
    VIP_EXP("vip_exp", "VIP经验", ""),
    // VIP 等级
    VIP_TIME("vip_time", "VIP时长", ""),
    VIP_RECHARGE("vip_recharge", "VIP账单-充值", ""),
    VIP_REWARD("vip_reward", "VIP账单-后台下发", ""),
    VIP_MONTH_DEDUCE("vip_month_deduce", "VIP账单-月度扣减", ""),
    ;

    final String key;
    final String name;
    final String desc;
}
