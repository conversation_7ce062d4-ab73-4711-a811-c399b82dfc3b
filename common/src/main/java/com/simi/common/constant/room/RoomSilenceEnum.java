package com.simi.common.constant.room;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum RoomSilenceEnum {

    TEN_MINUTES(1, "十分钟", 10, "10min", "10 دقائق"),
    SIXTY_MINUTES(2, "六十分钟", 60, "60min", "ساعة "),
    ONE_DAY(3, "一天", 1440, "1 Day", "يوم"),
    SEVEN_DAYS(4, "七天", 10080, "7 Day", "7 يوما"),
    ;
    final Integer type;
    final String desc;
    final Integer minutes;
    final String durationEnStr;
    final String durationArStr;

    public static RoomSilenceEnum getByType(Integer type) {
        return Arrays.stream(RoomSilenceEnum.values()).filter(e -> Objects.equals(e.getType(), type))
                .findAny().orElse(null);
    }
}
