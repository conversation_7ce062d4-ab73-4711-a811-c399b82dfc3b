package com.simi.common.constant.resource;

import com.simi.common.redis.BaseRedisKey;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 资源redis健
 *
 * <AUTHOR>
 * @date 2023/11/11 11:02
 */
@Getter
@AllArgsConstructor
public enum ResourceRedisKey implements BaseRedisKey {

    banner,

    /**
     * 奖励包下的奖品资源
     */
    reward_pack_resource,

    /**
     * 装扮
     */
    prop_info,

    /**
     * 用户认证标签(数据来源后台管理)
     */
    user_attestation_tag,
    ;

    @Override
    public String getName() {
        return this.name();
    }
}
