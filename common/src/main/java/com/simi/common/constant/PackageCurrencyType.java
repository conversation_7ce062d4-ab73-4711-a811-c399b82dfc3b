package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PackageCurrencyType {

    COIN(1,"金币"),
    GOLDEN_TICKET(2,"金票")
    ;

    private final Integer number;

    private final String desc;

    public static PackageCurrencyType getByType(Integer number){
        for (PackageCurrencyType value : values()) {
            if (value.getNumber().equals(number)){
                return value;
            }
        }
        return null;
    }

}
