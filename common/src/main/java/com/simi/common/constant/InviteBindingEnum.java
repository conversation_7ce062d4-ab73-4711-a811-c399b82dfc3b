package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 *
 */
@Getter
@AllArgsConstructor
public enum InviteBindingEnum {
    /**
     * 未绑定
     */
    UNBOUND(1, "未绑定"),

    /**
     * 已绑定
     */
    ALREADY_BOUND(2, "已绑定"),

    /**
     * 已解绑
     */
    UNBUNDLING(3, "已解绑"),

    /**
     * 手动绑定
     */
    MANUAL_BINDING(4, "手动绑定"),

    /**
     * 二次手动绑定
     */
    REBIND(5, "二次手动绑定"),
    ;

    final Integer type;
    final String desc;

    public static InviteBindingEnum getByType(Integer type) {
        return Stream.of(InviteBindingEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findAny().orElse(null);
    }
}
