package com.simi.common.constant.resource;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 审核资源类型枚举
 */
@Getter
@AllArgsConstructor
public enum ResourceAuditObjTypeEnum {

    /**
     * 个人相册
     */
    RESOURCE_AUDIT_TYPE_PRIVATE_PHOTO(1),

    ;
    final Integer type;

    public static ResourceAuditObjTypeEnum getByType(Integer type) {
        return Stream.of(ResourceAuditObjTypeEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findAny().orElse(null);
    }
}
