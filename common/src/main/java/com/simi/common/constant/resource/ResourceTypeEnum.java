package com.simi.common.constant.resource;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum ResourceTypeEnum {

    RESOURCE_NONE(0, "空"),
    RESOURCE_GIFT(1, "礼物"),
    RESOURCE_BUBBLE(2, "气泡"),
    RESOURCE_AVATAR_WIDGET(3, "头像框"),
    RESOURCE_SPECIAL_EFFECTS(4, "进房特效"),
    RESOURCE_RIPPLE(5, "光圈"),
    RESOURCE_ROOM_BACKGROUND(6, "房间背景"),
    RESOURCE_ROOM_DYNAMIC_EFFECT(7, "主页动效"),
    RESOURCE_VEHICLE(8, "坐骑"),

    RESOURCE_COIN(1001, "金币"),
    RESOURCE_DIAMOND(1002, "钻石"),
    RESOURCE_ACTIVE_EXP(1003, "活跃值"),
    RESOURCE_USD(1004, "美金"),
    RESOURCE_GOLDEN_TICKET(1005, "金票"),
    RESOURCE_WEALTH_EXP(1006, "财富值"),
    RESOURCE_CHARM_EXP(1007, "魅力值"),
    ARISTOCRACY(1008,"贵族"),
    // VIP 经验
    VIP_EXP(1009, "VIP经验"),
    // VIP 等级
    VIP_TIME(1010, "VIP时长"),
    // 特殊勋章
    SPECIAL_MEDAL(1011, "特殊勋章"),

    ;


    private Integer number;

    private String desc;

    public static ResourceTypeEnum getFrequencyType(Integer number){
        if (number != null) {
            for (ResourceTypeEnum value : values()) {
                if (value.getNumber().equals(number)) {
                    return value;
                }
            }
        }
        return null;
    }


    public static String getDescByType(Integer type) {
        if (type != null) {
            for (ResourceTypeEnum value : values()) {
                if (value.getNumber().equals(type)) {
                    return value.getDesc();
                }
            }
        }
        return null;
    }

}
