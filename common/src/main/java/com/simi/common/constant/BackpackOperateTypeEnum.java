package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BackpackOperateTypeEnum {


    BACKPACK_OPERATE_NONE( 0, "占位"),
    BACKPACK_OPERATE_SEND( 1, "发放"),
    BACKPACK_OPERATE_USE( 2, "使用"),
    BACKPACK_OPERATE_CANCEL_USE( 3, "取消使用（针对装扮）"),
    BACKPACK_OPERATE_SHOP_BUY_FOR_ONESELF( 4, "商城自购"),
    BACKPACK_OPERATE_SHOP_BUY_FOR_OTHERS( 5, "商城馈赠");

    private Integer type;

    private String desc;

    public static BackpackOperateTypeEnum getByType(Integer type){
        for (BackpackOperateTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
