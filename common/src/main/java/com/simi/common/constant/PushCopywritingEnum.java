package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * push标题文案
 */
@AllArgsConstructor
@Getter
public enum PushCopywritingEnum {

    CP_PUSH_TITLE_1("cp.push.title.set_as_admin", "管理员任命", "房间内被添加为管理员"),
    CP_PUSH_TITLE_2("cp.push.title.admin_removed", "管理员罢免", "房间内被取消管理员"),
    CP_PUSH_TITLE_3("cp.push.title.receive_new_items", "获得新物品", "奖励包发放"),
    CP_PUSH_TITLE_4("cp.push.title.violation_notice", "违规通知", "头像审核违规"),
    CP_PUSH_TITLE_5("cp.push.title.level_up", "升级", "各种等级升级"),
    CP_PUSH_TITLE_6("cp.push.title.receive_reward", "获得奖励", "房间政策奖励领取"),

    ;

    private final String key;
    private final String copywriting;
    private final String desc;
}
