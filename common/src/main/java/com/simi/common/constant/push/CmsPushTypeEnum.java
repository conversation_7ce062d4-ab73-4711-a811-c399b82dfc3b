package com.simi.common.constant.push;

import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.ApiException;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 后管推送发放类型
 */
@Getter
@AllArgsConstructor
public enum CmsPushTypeEnum {
    /**
     * 定时发放
     */
    CMS_PUSH_TYPE_TIMED(1),
    /**
     * 不定时发放
     */
    CMS_PUSH_TYPE_UN_TIMED(2),
    ;
    final Integer type;

    public static CmsPushTypeEnum getByType(Integer type) {
        return Stream.of(CmsPushTypeEnum.values()).filter(e -> Objects.equals(e.getType(), type))
                .findAny().orElseThrow(() -> new ApiException(CodeEnum.PARAM_ILLEGAL));
    }
}
