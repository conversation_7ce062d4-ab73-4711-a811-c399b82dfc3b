package com.simi.common.constant;


import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum MissionTypeEnum {

    /**
     * 每日任务
     */
    DAILY_MISSION("daily_tasks", 1),

    /**
     * 成长任务
     */
    GROW_UP_MISSION("", 2),
    ;

    /**
     * 翻译key
     */
    final String translateKey;
    final Integer type;

    public static MissionTypeEnum getByType(Integer type) {
        return Arrays.stream(MissionTypeEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findAny().orElse(null);
    }

}
