package com.simi.common.constant.leven;

import com.simi.common.dto.level.WealthLeveBase;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 财富等级
 * <AUTHOR>
 * @date 2024/1/23 14:38
 */
@Getter
@AllArgsConstructor
public enum WealthLevelEnum {
    LV0(0,0),
    LV1(1,200),
    LV2(2,600),
    <PERSON>V3(3,1200),
    LV4(4,2000),
    <PERSON>V5(5,3000),
    <PERSON>V6(6,5400),
    <PERSON>V7(7,8200),
    <PERSON>V8(8,11400),
    <PERSON>V9(9,15000),
    <PERSON>V10(10,19000),
    <PERSON>V11(11,27800),
    <PERSON>V12(12,37400),
    <PERSON>V13(13,47800),
    <PERSON>V14(14,59000),
    <PERSON>V15(15,71000),
    <PERSON>V16(16,83800),
    <PERSON>V17(17,97400),
    <PERSON>V18(18,111800),
    <PERSON>V19(19,127000),
    <PERSON>V20(20,143000),
    <PERSON>V21(21,227000),
    <PERSON>V22(22,315000),
    <PERSON><PERSON>23(23,407000),
    <PERSON><PERSON>24(24,503000),
    <PERSON><PERSON>25(25,603000),
    <PERSON><PERSON>26(26,707000),
    LV27(27,815000),
    LV28(28,927000),
    LV29(29,1043000),
    LV30(30,1163000),
    LV31(31,2403000),
    LV32(32,3683000),
    LV33(33,5003000),
    LV34(34,6363000),
    LV35(35,7763000),
    LV36(36,9203000),
    LV37(37,10683000),
    LV38(38,12203000),
    LV39(39,13763000),
    LV40(40,15363000),
    LV41(41,39963000),
    LV42(42,65163000),
    LV43(43,90963000),
    LV44(44,117363000),
    LV45(45,144363000),
    LV46(46,171963000),
    LV47(47,200163000),
    LV48(48,228963000),
    LV49(49,258363000),
    LV50(50,288363000),
    LV51(51,318963000),
    LV52(52,357963000),
    LV53(53,397713000),
    LV54(54,438213000),
    LV55(55,479463000),
    LV56(56,521463000),
    LV57(57,564213000),
    LV58(58,607713000),
    LV59(59,651963000),
    LV60(60,696963000),
    LV61(61,742713000),
    LV62(62,800838000),
    LV63(63,859900500),
    LV64(64,919900500),
    LV65(65,980838000),
    LV66(66,1042713000),
    LV67(67,1105525500),
    LV68(68,1169275500),
    LV69(69,1233963000),
    LV70(70,1299588000),
    LV71(71,1366150500),
    LV72(72,1440400500),
    LV73(73,1515681750),
    LV74(74,1591994250),
    LV75(75,1669338000),
    LV76(76,1747713000),
    LV77(77,1827119250),
    LV78(78,1907556750),
    LV79(79,1989025500),
    LV80(80,2071525500),
    LV81(81,2155056750L),
    LV82(82,2248075500L),
    LV83(83,2342228625L),
    LV84(84,2437516125L),
    LV85(85,2533938000L),
    LV86(86,2631494250L),
    LV87(87,2730184875L),
    LV88(88,2830009875L),
    LV89(89,2930969250L),
    LV90(90,3033063000L),
    LV91(91,3146613938L),
    LV92(92,3261412688L),
    LV93(93,3377459250L),
    LV94(94,3494753625L),
    LV95(95,3613295813L),
    LV96(96,3733085813L),
    LV97(97,3854123625L),
    LV98(98,3976409250L),
    LV99(99,4099942688L),
    LV100(100,4224723938L),
    ;

    private final int level;
    private final long amount;

    public static WealthLevelEnum maxLevel(){
        return WealthLevelEnum.LV100;
    }

    public WealthLeveBase toBase(){
        WealthLeveBase base = new WealthLeveBase();
        base.setLevel(this.getLevel());
        base.setAmount(this.getAmount());
        return base;
    }
}
