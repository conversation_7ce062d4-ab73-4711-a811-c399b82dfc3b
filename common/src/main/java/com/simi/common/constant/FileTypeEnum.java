package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 文件类型
 *
 * <AUTHOR>
 * @date 2023/11/3 11:04
 */

@Getter
@AllArgsConstructor
public enum FileTypeEnum {

    /**
     * 头像
     */
    AVATAR(0, "avatar/"),
    /**
     * 房间封面
     */
    ROOM_COVER(1, "room_cover/"),
    /**
     * 客户端日志
     */
    CLIENT_LOG(2, "client_log/"),
    /**
     * 举报
     */
    REPORT(3, "report/"),
    /**
     * 反馈
     */
    FEEDBACK(4, "feedback/"),
    /**
     * banner
     */
    BANNER(5, "banner/"),
    /**
     * 礼物
     */
    GIFT(6, "gift/"),
    /**
     * 资源道具
     */
    PROP(7, "prop/"),
    /**
     * 私人图片
     */
    PRIVATE_PHOTO(8, "private_photo/"),
    /**
     * 表情包
     */
    EMOJI_PACK(9, "emoji_pack/"),

    /**
     * 送礼横幅
     */
    GIFT_BANNER(10, "gift_banner/"),

    /**
     * 提现回执
     */
    WITHDRAW_RECEIPT(11, "withdraw_receipt/"),
    /**
     * 认证标签
     */
    ATTESTATION_TAG(12, "attestation_tag/"),
    /**
     * 下载
     */
    DOWNLOAD(13, "download/"),

    /**
     * 其他
     */
    OTHER(99, "other/")
    ;

    private final int type;
    private final String dir;


    public static FileTypeEnum getByType(Integer type){
        return Stream.of(FileTypeEnum.values())
                .filter(e -> Objects.equals(e.type, type)).findAny()
                .orElseThrow(() -> new IllegalArgumentException("file type illegal."));
    }
}
