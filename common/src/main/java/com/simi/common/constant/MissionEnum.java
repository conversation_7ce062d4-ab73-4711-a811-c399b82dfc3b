package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;


/**
 * 任务枚举
 *
 * <AUTHOR>
 * @date 2024/1/25 11:20
 */
@Getter
@AllArgsConstructor
public enum MissionEnum {

    /**
     * 当天在麦位上15 分钟（必须连续）
     */
    TAKE_THE_MIC_FOR_15_MINS(1, MissionTypeEnum.DAILY_MISSION, RewardPackCopywritingEnum.TAKE_THE_MIC_FOR_15MINS),

    /**
     * 当天在房间内45 分钟(非连续，当天累计)
     */
    STAY_IN_ROOM_45_MINS(2, MissionTypeEnum.DAILY_MISSION, RewardPackCopywritingEnum.STAY_IN_ROOM_45_MINS),

    /**
     * 当天在游戏水果派对中win1000 金币
     */
    WIN_1000_COINS_IN_FRUIT_PARTY(3, MissionTypeEnum.DAILY_MISSION, RewardPackCopywritingEnum.WIN_1000_COINS_IN_FRUIT_PARTY),

    /**
     * 当天在房间内送礼1 次
     */
    SEND_GIFTS_IN_THE_ROOM(4, MissionTypeEnum.DAILY_MISSION, RewardPackCopywritingEnum.SEND_GIFTS_IN_THE_ROOM),

    /**
     * 当天关注一个用户
     */
    FOLLOW_A_NEW_FRIEND(5, MissionTypeEnum.DAILY_MISSION, RewardPackCopywritingEnum.FOLLOW_A_NEW_FRIEND),

    ;

    final int id;
    final MissionTypeEnum missionType;
    final RewardPackCopywritingEnum rewardCopywriting;

    public static MissionEnum getById(Integer id) {
        return Arrays.stream(MissionEnum.values()).filter(e -> Objects.equals(e.getId(), id)).findAny().orElse(null);
    }


}
