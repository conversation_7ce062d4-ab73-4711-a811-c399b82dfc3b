package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 常用文案
 * @Author: Andy
 * @Date: 2023/12/1
 */
@Getter
@AllArgsConstructor
public enum CommonlyWritingEnum {

    SEND_SINGLE_GIFT("gift.send.banner.single", "送单人礼物"),
    SEND_ALL_MIC_GIFT("gift.send.banner.all.mic", "送全麦礼物"),
    SEND_ALL_ROOM_GIFT("gift.send.banner.all.room", "送全房间人礼物"),

    ;

    final String key;
    final String copywriting;
}
