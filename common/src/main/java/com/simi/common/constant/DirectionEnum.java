package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum DirectionEnum {

    up(1, "上"),
    down(2,"下"),
    left(3,"左"),
    right(4,"右"),

    ;
    private Integer type;

    private String desc;

    public static String getByType(Integer type){
        if (type != null) {
            for (DirectionEnum value : values()) {
                if (value.getType().equals(type)) {
                    return value.getDesc();
                }
            }
        }
        return null;
    }
}
