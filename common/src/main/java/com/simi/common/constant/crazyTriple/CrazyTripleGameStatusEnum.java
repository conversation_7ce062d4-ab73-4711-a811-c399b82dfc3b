package com.simi.common.constant.crazyTriple;

/**
 * @description: 游戏状态
 * @author: lrq
 * @create: 2024-07-10 16:33
 **/
public enum CrazyTripleGameStatusEnum {
    CRAZY_TRIPLE_GAME_STATUS_NONE(0, "占位"),
    CRAZY_TRIPLE_GAME_STATUS_IN_PROGRESS(1, "进行中"),
    CRAZY_TRIPLE_GAME_STATUS_TO_LOTTERY(2, "结束待开奖"),
    CRAZY_TRIPLE_GAME_STATUS_END(3, "结束");

    private final int value;
    private final String description;

    CrazyTripleGameStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    public int getValue() {
        return value;
    }

    public String getDescription() {
        return description;
    }
}
