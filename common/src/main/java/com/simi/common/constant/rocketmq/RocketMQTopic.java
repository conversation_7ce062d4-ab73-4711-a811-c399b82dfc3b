package com.simi.common.constant.rocketmq;

public interface RocketMQTopic {

    String PREFIX = "dev_"; //测试使用打开这个
    // String PREFIX = "";  //正式服打开这个

    String SEND_GIFT = PREFIX + "send_gift";
    String RECHARGE_CALL_BACK = PREFIX + "recharge_call_back";
    String CONSTRAINT_DROP_OUT = PREFIX + "constraint_drop_out";
    String TIM_LOGIN_CALLBACK_TOPIC = PREFIX + "tim_login_callback_topic";
    String TIM_LOGOUT_CALLBACK_TOPIC = PREFIX + "tim_logout_callback_topic";
    String TIM_VERIFY_IMAGE_TOPIC = PREFIX + "tim_verify_image_topic";
    String ROOM_PUBLIC_SCREEN_SILENCE_EXPIRE_TOPIC = PREFIX + "room_public_screen_silence_expire_topic";
    String BLACK_KICK_OUT_ALL_TOPIC = PREFIX + "black_kick_out_all_topic";
    String MISSION_COMPLETE_TOPIC = PREFIX + "mission_complete_topic";
    String STREAM_EVENT_TASK_STATISTICS_TOPIC = PREFIX + "stream_event_task_statistics_topic";
    String PK_OVER_DELAY_TOPIC = PREFIX + "pk_over_delay_topic";
    String ARISTOCRACY_BUY_SUCCESS = PREFIX + "aristocracy_buy_success";
    String ARISTOCRACY_UNSUBSCRIBE = PREFIX + "aristocracy_unsubscribe";
    String ROOM_PARTY_DELAY_TOPIC = PREFIX + "room_party_delay_topic";
    String ROOM_PARTY_ACTIVITY_TOPIC = PREFIX + "room_party_activity_topic";
    String CANAL_USER_INFO_MESSAGE_TOPIC = PREFIX + "canal_user_info_message_topic";
    String RED_PACKET_SETTLE_TOPIC = PREFIX + "red_packet_settle_topic";
    String RED_PACKET_GRAB_RECORD_TOPIC = PREFIX + "red_packet_grab_record_topic";
    String LUCKY_WHEEL_DELAY_TOPIC = PREFIX + "lucky_wheel_delay_topic";
}
