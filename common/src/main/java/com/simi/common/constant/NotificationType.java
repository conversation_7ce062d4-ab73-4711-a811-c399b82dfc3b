package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum NotificationType {

    ONE_TIME_PRODUCT_PURCHASED(1,"成功购买"),
    ONE_TIME_PRODUCT_CANCELED(2,"用户已取消")

    ;


    final Integer number;

    final String desc;

    public static NotificationType forNumber(Integer platform) {
        for (NotificationType value : values()) {
            if (value.getNumber().equals(platform)) {
                return value;
            }
        }
        return null;
    }
}
