package com.simi.common.constant.medal;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum MedalTypeEnum {

    /**
     * 成就勋章
     */
    ACHIEVEMENT(1),

    /**
     * 礼物勋章
     */
    GIFT(2),

    /**
     * 特殊勋章
     */
    SPECIAL(3),

    ;
    final Integer type;

    public static MedalTypeEnum getByType(Integer type) {
        return Arrays.stream(MedalTypeEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findAny().orElse(null);
    }
}
