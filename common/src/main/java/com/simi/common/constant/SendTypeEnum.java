package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum SendTypeEnum {
    SEND_TYPE_NONE(0,"占位"),
    ROOM_SINGLE(1,"房间内指定单用户送礼"),
    ROOM_MIC_ALL(2,"房间内麦上所有用户送礼"),
    ROOM_ALL(3,"房间内所有用户送礼"),
    PRIVATE_CHAT(4,"私聊送礼"),
    SEND_TYPE_POST(5,"私聊送礼");

    private Integer type;

    private String desc;


    public static SendTypeEnum getByType(Integer type){
        for (SendTypeEnum value : values()) {
            if (value.getType().equals(type)){
                return value;
            }
        }
        return null;
    }
}
