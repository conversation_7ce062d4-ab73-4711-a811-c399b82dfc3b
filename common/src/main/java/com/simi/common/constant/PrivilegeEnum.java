package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum PrivilegeEnum {

    LEVEL_PRIVILEGES_WEALTH("level_privileges_wealth","level_privileges_wealth"),
    LEVEL_PRIVILEGES_CHARM("level_privileges_charm","level_privileges_charm"),
    LEVEL_PRIVILEGES_ACTIVE("level_privileges_active","level_privileges_active"),
    ;


    final String code;

    final String desc;

}
