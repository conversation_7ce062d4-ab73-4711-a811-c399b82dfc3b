package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@AllArgsConstructor
@NoArgsConstructor
public enum AnimationTypeEnum {

    ANIMATION_NONE(0,"无"),
    ANIMATION_SVGA(1,"SVGA"),
    ANIMATION_LOTTIE(2,"PAG"),
    ANIMATION_VAP(3,"VAP"),
    ANIMATION_MP4(4,"mp4");

    private Integer type;

    private String desc;

    public static AnimationTypeEnum getByType(Integer type){
        for (AnimationTypeEnum value : values()) {
            if (value.getType().equals(type)) {
                return value;
            }
        }
        return null;
    }
}
