package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum AppJumpTypeEnum {

    H5(1),
    ROOM(2),
    HOMEPAGE(3),
    GAME(4),
    PARTY(5),
    IM_MESSAGE(6),
    ;
    final Integer type;

    public static AppJumpTypeEnum getByType(Integer type) {
        return Stream.of(AppJumpTypeEnum.values()).filter(e -> Objects.equals(e.getType(), type)).findAny().orElse(null);
    }

    /*public static String getJumpLinkUrl(Integer type, String jumpParam) {
        AppJumpTypeEnum jumpTypeEnum = getByType(type);
        if (Objects.isNull(jumpTypeEnum)) {
            return StrUtil.EMPTY;
        }
        if (Objects.equals(jumpTypeEnum, AppJumpTypeEnum.H5)) {
            return jumpParam;
        } else if (Objects.equals(jumpTypeEnum, AppJumpTypeEnum.ROOM)) {
            return ClientRouteUtil.toRoom(jumpParam);
        } else if (Objects.equals(jumpTypeEnum, AppJumpTypeEnum.HOMEPAGE)) {
            long uid;
            try {
                uid = Long.parseLong(jumpParam);
            } catch (NumberFormatException e) {
                return StrUtil.EMPTY;
            }
            return ClientRouteUtil.toHomePage(uid, Boolean.FALSE);
        } else if (Objects.equals(jumpTypeEnum, AppJumpTypeEnum.GAME)) {
            return ClientRouteUtil.toGame(jumpParam);
        } else if (Objects.equals(jumpTypeEnum, AppJumpTypeEnum.PARTY)) {
            return ClientRouteUtil.toParty(jumpParam);
        } else if (Objects.equals(jumpTypeEnum, AppJumpTypeEnum.IM_MESSAGE)) {
            return ClientRouteUtil.toIM(jumpParam, Boolean.FALSE);
        } else {
            return StrUtil.EMPTY;
        }
    }*/
}
