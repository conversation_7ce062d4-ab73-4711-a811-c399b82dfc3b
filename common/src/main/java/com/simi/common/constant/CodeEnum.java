package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum CodeEnum {

    // 通用状态码"),
    SUCCESS_ZERO(0, "success"),
    SUCCESS(200, "success"),
    SERVERERROR(500, "There was an error, please try again later~"),
    BAD_REQUEST(1000, "bad request"),
    DUPLICATE_OPERATION(1001, "重复操作"),
    PARAM_ILLEGAL(1002, "参数错误"),
    SERVER_ERROR(1003, "server error"),
    NETWORK_ERROR(1004, "网络异常"),
    SERVER_BUSY(1005, "server busy"),
    NEED_LOGIN(1006, "需要登录"),
    SIGNATURE_EMPTY(1007, "签名不能为空"),
    SIGNATURE_ERROR(1008, "签名错误"),
    REQUEST_EXPIRED(1009, "请求已失效"),
    TOKEN_ERROR(1010, "Token错误"),
    CONTAIN_ILLEGAL_CONTENT(1011, "包含敏感信息"),
    BALANCE_NOT_ENOUGH(1012, "用户余额不足"),
    INPUT_VALUE_ERROR(1013,"请输入正确的值"),
    ROOM_NO_AUTHORITY(1401, "没有房间权限"),
    ROOM_USER_NOT_IN_EXCEPTION(1402, "异常：用户不在房间内"),
    DATA_DOES_NOT_EXIST(1403, "数据不存在"),
    DATA_TYPE_DUPLICATE(1404, "数据类型不能重复添加"),
    // 登录注册模块"),
    REGISTER_DEVICE_LIMIT(2000, "设备注册账号已达上限"),
    REGISTER_ERROR(2001, "注册失败，请尝试其它注册方式"),
    LOGIN_ERROR(2002, "登录失败"),
    ACCOUNT_NOT_EXIST(2003, "账户不存在"),
    PASSWD_ERROR(2004, "密码错误"),
    BLOCKED(2005, "封禁"),
    BANDING_ERROR(2006, "绑定失败"),
    ALREADY_BANDING(2007, "已经绑定账户"),
    UPDATE_ERROR(2008, "{}"),
    FREQUENT_OPERATION(2009,"注册上限"),
    ACCOUNT_BLACK_LIST(2010,"{}"),
    REQUIRES_18_YEARS_OLD(2011,"注册18岁限制"),

    //用户模块"),
    USER_NO_EXIST(3000, "用户不存在"),
    USER_NO_MATCH(3001, "No match found"),
    NICK_CANNOT_BE_EMPTY(3002, "昵称不能为空"),
    FOLLOWING_EXIST(3010, "已关注了"),
    FOLLOWING_NOT_EXIST(3011, "还没关注"),
    USER_NICK_ILLEGAL(3020, "昵称包含非法内容，操作失败！"),
    USER_SIGNATURE_ILLEGAL(3021, "个性签名包含非法内容，操作失败！"),
    NOT_CHECKIN_BEFORE(3030, "之前未签到"),
    CHECKED_IN(3031, "已签到"),
    NOT_CONFIG_CHECKIN(3032, "未配置签到信息"),
    MISSION_INCOMPLETE(3033, "任务未完成"),
    MISSION_CLAIMED(3034, "任务奖励已领取"),
    USER_PRIVATE_PHOTO_MAX(3035, "个人相册上限了"),
    USER_WITHDRAW_CHANNEL(3036,"提现渠道最大值"),
    USER_PRETTY_NO(3037, "用户已有靓号"),
    USER_NOT_EMPTY(3038,"用户为空"),
    USER_UP_LIMIT_SIZE(3039,"编辑用户不能为多个"),

    //房间模块"),
    NO_AUTHORITY(4000, "没有权限"),
    ROOM_NO_AUTHORITY_1(4001, "没有房间权限"),
    ROOM_MIC_NO_AUTHORITY(4002, "没有麦位权限"),
    ROOM_MATCH_NOT_FOUND(4003, "No match found"),
    KICK_OUT_ROOM(4010, "你不能进入该房间"),
    ROOM_NEED_PWD(4011, "进房需要密码"),
    ROOM_PWD_ERROR(4012, "进房密码错误"),
    MIC_HAVING_USER(4020, "麦位上有人了"),
    USER_ON_MIC(4021, "用户已经在麦位上了"),
    MIC_ALL_HAVING(4022, "没有空麦位了"),
    MIC_INVITE_TIME(4023, "邀请间隔太短了"),
    MIC_UP_ERROR(4024, "上麦失败"),
    MIC_KICK_DOWN_ERROR(4025, "踢下麦失败"),
    MIC_LOCK(4026, "锁麦了"),
    USER_IN_APPLY(4027, "用户在申请列表中"),
    ROOM_MANAGER_EXIST(4030, "已经是管理员了"),
    ROOM_MANAGER_NONEXISTENT(4031, "管理员不存在"),
    ROOM_USER_IS_MANAGER(4032, "对方是管理员，不能操作"),
    ROOM_MANAGER_MAX(4033, "管理员数量上限了"),
    ROOM_USER_PUBLISH_MSG_TIME(4040, "说得太快了，请稍等"),
    ROOM_MESSAGE_TO_LONG(4041, "超出字符限制"),
    ROOM_TITLE_ILLEGAL(4050, "标题包含非法内容，操作失败！"),
    ROOM_DESC_ILLEGAL(4051, "公告包含非法内容，操作失败！"),
    NO_ROOM(4052, "房间不存在"),
    // 用户不在房间
    USER_NOT_IN_ROOM(4053, "用户不在房间内"),
    MIC_MANAGER_MUTE(4054, "你已被禁言"),
    NON_ADMIN_MUTING(4055, "非管理员禁言"),

    ROOM_SEND_MSG_INTERVAL(5022, "每次发言间隔"),

    //基础模块
    SMS_ERROR(6000, "短信发送失败"),
    SMS_VERIFY_ERROR(6001, "验证验证码失败"),
    SMS_LIMIT(6002, "短信发送限制"),
    IMAGE_VIOLATION(6003, "图片违规"),

    //营收模块"),
    EXCHANGE_AMOUNT_MUST_LARGER_THAN_ZERO(10000, "钻石兑换金币数量必须大于0"),
    EXCHANGE_AMOUNT_CANNOT_LAGER_THAN_DIAMOND_BALANCE(10001, "钻石兑换金币数量不能大于钻石余额"),
    DIAMOND_BALANCE_NOT_ENOUGH(10002, "钻石余额不足"),
    COIN_BALANCE_NOT_ENOUGH(10003, "金币余额不足"),
    ADD_DIAMOND_FAIL(10004, "增加钻石失败"),
    GIFT_DOWN_OR_NOT_EXISTS(10005, "礼物已下架"),
    NO_AUDIENCE_IN_ROOM(10006, "房间暂无观众"),
    BACKPACK_REMAIN_NOT_ENOUGH(10007, "包裹礼物不足"),
    CANNOT_SEND_GIFT_TO_YOURSELF(10008, "不能送礼给自己"),
    SEND_1_AT_A_TIME(10009, "This gift can only be sent 1 at a time"),
    ADD_COINS_FAIL(10010, "增加金币失败"),
    MANAGE_ERROR(10011, "操作用户钱包失败"),
    UNKNOWN_BILL_ITEM(10012, "未知的账单类型"),
    MINIMUM_EXCHANGE_AMOUNT(10013,"兑换的美金不小于{}"),
    MAXIMUM_EXCHANGE_VALUE(10014,"大于最多可兑换美金数"),
    INSUFFICIENT_BALANCE(10015, "美金余额不足"),
    BACKPACK_NOT_FOUND_GOODS(10100, "商品库存不存在"),
    BACKPACK_STOCK_NOT_ENOUGH(10101, "库存余额不足"),
    BACKPACK_REQ_DUPLICATE(10102, "重复请求(交易已存在)"),
    BACKPACK_CONCURRENT_MODIFY_STOCK_EXCEPTION(10103, "并发更新库存异常"),
    RECHARGE_PACKAGE_ERROR(10200, "充值套餐异常"),
    RECHARGE_PAY_ERROR(10201, "充值未成功支付"),
    RECHARGE_CHANNEL_ORDER_USED(10202, "三方订单重复了"),

    ADMIN_NEED_LOGIN(9000, "需要登录"),
    ADMIN_NOT_EXIST(9001, "账号不存在"),
    ADMIN_INVALID(9002, "账号无效"),
    PASSWORD_ERROR(9003, "密码错误"),
    ROLE_NOT_EXIST(9004, "角色不存在"),
    USERNAME_EXIST(9005, "用户名已存在"),
    USER_NOT_FOUND(9006, "用户不存在"),
    RECHARGE_PACKAGE_ID_EXIST(9010, "skuid不能重复"),

    // 闭商
    COIN_DEALER_SUPERIOR_NOT_EXIST(18001, "上级不存在"),
    COIN_DEALER_NOT_EXIST(18002, "币商不存在"),
    COIN_DEALER_GOLDEN_TICKET_NOT_ENOUGH(18003, "币商余额不足"),
    NOT_COIN_DEALER_SUBORDINATE(18004, "不是币商的下级"),
    COIN_DEALER_ALREADY_EXIST(18004, "该用户已是币商"),

    MOMENT_CONTAINS_SENSITIVE_WORD(66609, "您编辑的文本中含有敏感词"),
    QUEEN_ACTIVITY_CONTAINS_SENSITIVE_WORD(66610, "包含敏感词, 发布失败"),

    ADMIN_CONFIG_ALREADY_EXISTS(9100,"该区间已有配置，请修改其他配置"),

    LOGIN_RESTRICTION_IP(8011,"ip登录限制"),
    LOGIN_RESTRICTION_SYS_LANGUAGE(8012,"系统语言登录限制"),
    LOGIN_RESTRICTION_SIM(8013,"sim卡登录限制"),
    LOGIN_RESTRICTION_TIMEZONE(8014,"时区登录限制"),
    LOGIN_RESTRICTION_STORE_CODE(8015,"app商店code登录限制"),

    THE_RESOURCE_DOES_NOT_EXIST(8020,"资源不存在或者未上架"),
    CANNOT_BIND_ONESELF(8021,"不能绑定自己"),
    ACTIVITY_IS_OUTDATED(8031,"活动已过时"),

    ROOM_SILENCE(8040, "房间公屏禁言"),

    ACTIVITY_DISQUALIFICATION(12001, "不符合活动条件"),
    ACTIVITY_ADVENTURE_JOIN_AMOUNT_LIMIT(12002, "银河大冒险下注金额达到上限"),
    ACTIVITY_GAME_BET_NOT_START(12003, "游戏下注未开始"),
    ACTIVITY_GAME_BET_END(12004, "游戏下注已结束"),

    // 当前周不能修改
    WEEK_NOT_ALLOW_MODIFY(12005, "当前周不能修改"),

    PK_HAS_ALREADY_STARTED(12006, "已有进行中的PK"),
    // 购买贵族失败
    ARISTOCRACY_BUY_FAIL(12007, "购买贵族失败"),
    // 购买贵族成功
    ARISTOCRACY_BUY_SUCCESS(12008, "购买贵族成功"),
    // 不存在的贵族
    ARISTOCRACY_NOT_EXIST(12009, "不存在的贵族"),
    // 不存在的贵族档位
    ARISTOCRACY_DUTY_NOT_EXIST(12010, "不存在的贵族档位"),
    // 用户贵族记录不存在
    ARISTOCRACY_RECORD_NOT_EXIST(12011, "用户贵族记录不存在"),
    // 购买贵族等级不能降级
    ARISTOCRACY_CANNOT_DOWNGRADE(12012, "购买贵族等级不能降级"),
    // 赠送贵族等级不能降级
    ARISTOCRACY_GIFT_CANNOT_DOWNGRADE(12013, "赠送贵族等级不能降级"),
    // 贵族级别不够
    ARISTOCRACY_LEVEL_NOT_ENOUGH(12014, "贵族级别不够"),
    // 贵族赠送次数已超过限制
    ARISTOCRACY_GIFT_OVER_LIMIT(12015, "贵族赠送次数已超过限制"),
    // 赠送贵族礼物，贵族级别不够
    ARISTOCRACY_GIFT_LEVEL_NOT_ENOUGH(12016, "赠送贵族礼物，贵族级别不够"),
    // 贵族铭牌已被认领
    ARISTOCRACY_CARD_ALREADY_CLAIMED(12017, "贵族铭牌已被认领"),
    // 贵族铭牌不可以领取多个
    ARISTOCRACY_CARD_RECEIVE_OVER_LIMIT(12018, "贵族铭牌不可以领取多个"),

    ARISTOCRACY_LEVEL_LIMIT(12019, "赠送等级小于用户自身等级"),

    ROOM_PARTY_CANNOT_BE_MODIFIED(12110, "房间party已开始不允许修改"),
    ROOM_PARTY_TOPIC_SENSITIVE_WORDS(12102, "房间party主题包含敏感词"),
    ROOM_PARTY_DESCRIPTION_SENSITIVE_WORDS(12103, "房间party描述包含敏感词"),
    ROOM_PARTY_TIME_OVERLAP(12104, "房间party时间重叠"),
    ROOM_PARTY_TIME_NON_COMPLIANCE(12105, "房间party时间不合规"),
    ROOM_PARTY_CREATE_MAXIMUM_LIMIT(12106, "房间party创建数量限制"),
    ROOM_PARTY_CREATE_WEALTH_LACK(12107, "房间party创建财富等级不够"),
    ROOM_PARTY_CREATE_CHARM_LACK(12108, "房间party创建魅力等级不够"),
    ROOM_PARTY_CREATE_ACTIVE_LACK(12109, "房间party创建活跃等级不够"),
    ROOM_PARTY_CHANGE_LIMIT_REACHED(12111, "房间party时间不允许修改多次"),
    //商城模块
    UNABLE_TO_SEND_TO_YOURSELF(13001,"不能赠送给自己"),

    THIS_ITEM_IS_SOLD_OUT(13002,"商品下架"),

    CMS_CURRENT_STATU_CANNOT_MODIFY(13030, "当前状态不允许修改"),

    // 不存在的VIP档位
    VIP_DUTY_NOT_EXIST(13110, "不存在的VIP档位"),
    // 购买VIP失败
    VIP_BUY_FAIL(13107, "购买VIP失败"),
    // 购买VIP成功
    VIP_BUY_SUCCESS(13108, "购买VIP成功"),
    // 用户不是VIP
    VIP_NOT_EXIST(13109, "用户不是VIP"),

    // 权限不存在
    VIP_PERMISSION_NOT_EXIST(13111, "权限不存在"),
    // 赠送VIP礼物，VIP级别不够
    VIP_GIFT_LEVEL_NOT_ENOUGH(13112, "赠送VIP礼物，VIP级别不够"),
    //赠送VIP体验卡次数超限
    VIP_SEND_COUNT_LIMIT(13113, "赠送VIP体验卡次数超限"),
    // 接受者是VIP，无法赠送
    VIP_RECEIVER_NOT_EXIST(13114, "接受者是VIP，无法赠送"),
    // 禁言者是VIP用户，无法被禁言
    VIP_GAG_NOT_EXIST(13115, "禁言者是VIP用户，无法被禁言"),
    // 你没有拥有该特权
    VIP_HAS_NO_PERMISSION(13116, "你没有拥有该特权"),
    // 赠送失败
    VIP_GIFT_FAIL(13117, "赠送失败"),
    // 对方是VIP用户，无法被踢下麦
    VIP_KICK_FAIL(13118, "对方是VIP用户，无法被踢下麦"),
    // 对方是VIP用户，无法被踢出房
    VIP_KICK_OUT_ROOM_FAIL(13119, "对方是VIP用户，无法被踢出房"),


    // 勋章
    MEDAL_WEAR_LIMIT(13200, "勋章佩戴数量限制"),
    GIVE_MEDAL_FAIL(13201, "赠送勋章失败"),

    PHONE_ALREADY_BEEN_USED(13220, "该手机号已经被使用了"),


    //BD
    GUIDE_REPEAT(14000,"公会长不能在多个BD下"),
    IS_NOT_GUIDE(14111,"存在不是公会长的ID"),
    GUIDE_CAN_NOT_BE_BD(14112,"BD不能也是公会长"),
    BD_IS_EXIST(14113,"BD已存在"),

    RED_PACKET_NOT_EXISTS(14200, "红包不存在"),
    RED_PACKET_EXPIRE(14201, "红包不在可领取时间内"),
    RED_PACKET_RECEVICED_OVER(14202, "红包已领完"),
    RED_PACKET_HAS_RECEVICED(14203, "该红包已经领取过"),

    //幸运转盘
    CANNOT_RUN_REPEATEDLY(15000,"转盘重复创建"),
    CANNOT_JOIN_REPEATEDLY(15001,"重复参与转盘"),
    NOT_CAN_RUN_WHEEL(15002,"不能开始幸运转盘"),
    NOT_CAN_CANCEL_WHEEL(15003,"不能取消幸运转盘"),
    NOT_ENOUGH_PEOPLE(15004,"开局人数不足"),
    NOT_CAN_JOIN_WHEEL(15005,"不能参与幸运转盘"),
    NOT_RUNNING_WHEEL(15006,"没有正在进行中的转盘游戏"),
    GET_WHEEL_LOCK_FAIL(15007,"获取转盘锁失败"),
    NOT_AT_BETTING_TIME(15008,"不在下注时间"),
    ;
    private Integer number;

    private String desc;

    public static CodeEnum getByNumber(Integer number) {
        for (CodeEnum value : values()) {
            if (number.equals(value.getNumber())) {
                return value;
            }
        }
        return null;
    }

}
