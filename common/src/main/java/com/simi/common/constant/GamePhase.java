package com.simi.common.constant;

public enum GamePhase {
    BETTING(1,0, 20),        // 押注阶段（0~20s）
    GRAYED_OUT(2,20, 25),    // 置灰阶段（20~25s）
    DRAWING(3,25, 30),       // 开奖阶段（25~30s）
    SHOW_RESULT(4,30, 34);   // 展示阶段（30~34s）

    private final int type;
    private final int start;
    private final int end;

    GamePhase(int type, int start, int end) {
        this.type = type;
        this.start = start;
        this.end = end;
    }

    public boolean inPhase(long elapsedSeconds) {
        return elapsedSeconds >= start && elapsedSeconds < end;
    }

    public int getRemainingSeconds(long elapsedSeconds) {
        return (int) Math.max(0, end - elapsedSeconds);
    }

    public static GamePhase fromElapsedSeconds(long elapsedSeconds) {
        for (GamePhase phase : values()) {
            if (phase.inPhase(elapsedSeconds)) return phase;
        }
        return BETTING; // 默认
    }

    public int getType() {
        return type;
    }
    public int getStart() {
        return start;
    }

    public int getEnd() {
        return end;
    }
}