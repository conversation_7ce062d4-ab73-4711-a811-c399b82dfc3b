package com.simi.common.constant;

public enum GamePhase {
    BETTING(1,0, 19),        // 押注阶段（0~19s）
    GRAYED_OUT(2,19, 20),    // 置灰阶段（19~20s）
    DRAWING(3,20, 25),       // 开奖阶段（20~25s）
    SHOW_RESULT(4,25, 30);   // 展示阶段（25~29s）

    private final int type;
    private final int start;
    private final int end;

    GamePhase(int type, int start, int end) {
        this.type = type;
        this.start = start;
        this.end = end;
    }

    public boolean inPhase(long elapsedSeconds) {
        return elapsedSeconds >= start && elapsedSeconds < end;
    }

    public int getRemainingSeconds(long elapsedSeconds) {
        return (int) Math.max(0, end - elapsedSeconds);
    }

    public static GamePhase fromElapsedSeconds(long elapsedSeconds) {
        for (GamePhase phase : values()) {
            if (phase.inPhase(elapsedSeconds)) return phase;
        }
        return BETTING; // 默认
    }

    public int getType() {
        return type;
    }
    public int getStart() {
        return start;
    }

    public int getEnd() {
        return end;
    }
}