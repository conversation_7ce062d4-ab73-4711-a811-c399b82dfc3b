package com.simi.common.constant;

public enum GamePhase {
    BETTING(1,0, 40),        // 押注阶段（0~13s）
    GRAYED_OUT(2,13, 14),    // 倒计时最后1秒（13~14s）
    DRAWING(3,40, 50),       // 开奖动画 & 计算（14~16s）
    SHOW_RESULT(4,50, 60);   // 展示阶段（16~20s）

    private final int type;
    private final int start;
    private final int end;

    GamePhase(int type, int start, int end) {
        this.type = type;
        this.start = start;
        this.end = end;
    }

    public boolean inPhase(long elapsedSeconds) {
        return elapsedSeconds >= start && elapsedSeconds < end;
    }

    public int getRemainingSeconds(long elapsedSeconds) {
        return (int) Math.max(0, end - elapsedSeconds);
    }

    public static GamePhase fromElapsedSeconds(long elapsedSeconds) {
        for (GamePhase phase : values()) {
            if (phase.inPhase(elapsedSeconds)) return phase;
        }
        return BETTING; // 默认
    }

    public int getType() {
        return type;
    }
    public int getStart() {
        return start;
    }

    public int getEnd() {
        return end;
    }
}