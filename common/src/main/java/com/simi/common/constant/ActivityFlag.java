package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ActivityFlag {

    /**
     * 房间贡献
     */
    ROOM_CONTRIBUTION_RANK("room_contribution_rank"),

    /**
     * 公会/代理达标奖励-H5 活动
     */
    ACT_ANCHOR_INVITE("act_anchor_invite"),

    /**
     * 女性支持者活动
     */
    ACT_FEMALE_SUPPORTERS("act_female_supporters"),
    /**
     * 2024宰牲节
     */
    ACT_2024_CORBAN("act_2024_corban"),

    /**
     * 周星榜
     */
    WEEKLY_STAR_RANK("weekly_star_rank"),

    /**
     * PK 榜单活动
     */
    PK_RANK("pk_rank"),

    /**
     * 勋章任务
     */
    MEDAL_TASK("medal_task"),

    /**
     * 水果排行榜
     */
    FRUIT_RANK("fruit_rank"),

    ;

    final String key;
}
