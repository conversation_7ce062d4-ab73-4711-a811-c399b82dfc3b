package com.simi.common.constant;

import com.simi.common.config.ExpressionConfig;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ExpressionEnum {

    ex_01(1, ExpressionConfig.ex_01),
    ex_02(2, ExpressionConfig.ex_02),

    ;
    private Integer type;

    private String desc;

    public static String getByType(Integer type){
        if (type != null) {
            for (ExpressionEnum value : values()) {
                if (value.getType().equals(type)) {
                    return value.getDesc();
                }
            }
        }
        return null;
    }
}

