package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum PlatformPhoneEnum {

    PLATFORM_NONE(0,"占位", ""),
    ANDROID(1,"安卓", "Android"),
    IOS(2,"IOS", "ios"),;


    final Integer number;

    final String desc;

    final String name;

    public static PlatformPhoneEnum forNumber(Integer platform) {
        return Stream.of(PlatformPhoneEnum.values()).filter(e -> Objects.equals(e.getNumber(), platform)).findAny().orElse(null);
    }
}
