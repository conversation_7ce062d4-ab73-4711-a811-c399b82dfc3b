package com.simi.common.constant;

import com.simi.common.base.BaseI18Key;
import lombok.AllArgsConstructor;

/**
 * 通用文案
 * <AUTHOR>
 * @date 2024/2/29 14:06
 */
@AllArgsConstructor
public enum UniversalCopyEnum implements BaseI18Key {

  /**
   * 关注
   */
  follow("follow", "follow"),


  ;
  private final String key;
  private final String value;

  @Override
  public String getKey() {
    return this.key;
  }

  @Override
  public String getValue() {
    return this.value;
  }
}
