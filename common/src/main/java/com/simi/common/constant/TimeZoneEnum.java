package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

@Getter
@AllArgsConstructor
public enum TimeZoneEnum {

    UTC(0, "UTC"),
    // 东三区
    GMT3(3, "E3"),
    // 东五区
    GMT5(5, "E5"),
    // 东七区
    GMT7(7, "E7"),
    // 西三区
    GMT_WEST3(-3, "W3"),

    ;


    final Integer no;

    final String key;


    public static TimeZoneEnum findByNo(Integer no) {
        if (Objects.isNull(no)) {
            return TimeZoneEnum.UTC;
        }
        return Arrays.stream(TimeZoneEnum.values()).filter(item -> item.getNo().equals(no)).findFirst().orElse(TimeZoneEnum.UTC);
    }
}
