package com.simi.common.constant.aristocracy;

import lombok.Getter;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-26 16:40
 **/
public class AristocracyConstant {

    /**
     * 贵族状态
     */
    public static enum Status {
        /**
         * 生效
         */
        EFFECTIVE(0),
        /**
         * 失效
         */
        INVALID(1),
        /**
         * 已回退
         */
        REVERTED(2)
        ;

        private int status;

        Status(int status) {
            this.status = status;
        }

        public int getStatus() {
            return status;
        }
    }

    /**
     * 购买行为属性
     */
    public static enum BuyType {
        /**
         * 新增
         */
        ADD,
        /**
         * 续费
         */
        RENEW,
        /**
         * 升级
         */
        UPGRADE
        /**
         * 降级
         */
        , DOWNGRADE
        ;
    }

    /**
     * 获取来源
     */
    @Getter
    public static enum Source {
        /**
         * 主动购买
         */
        BUY(0,"购买"),
        /**
         * 他人赠送
         */
        GIFT(1,"赠送"),
        /**
         * 后台发送
         */
        ADMIN(2,"后台发放")
        /**
         * 奖励
         */
        , REWARD(3,"奖励")
        /*
        * 贵族6 赠送
         */
        , GIFT_6(4,"贵族6 赠送")
        ;

        private int source;
        private String description;

        Source(int source, String description) {
            this.source = source;
            this.description = description;
        }

        /**
         * 根据source 获取枚举
         */
        public static Source valueOfByInt(int source) {
            for (Source value : Source.values()) {
                if (value.getSource() == source) {
                    return value;
                }
            }
            return null;
        }
    }

    /**
     * 贵族等级
     */
    @Getter
    public static enum Level{
        /**
         * 1
         */
        LEVEL_1(1),
        /**
         * 2
         */
        LEVEL_2(2),
        /**
         * 3
         */
        LEVEL_3(3)
        /**
         * 4
         */
        , LEVEL_4(4)
        /**
         * 5
         */
        , LEVEL_5(5)
        /**
         * 6
         */
        , LEVEL_6(6)
        ;

        private int level;

        Level(int level) {
            this.level = level;
        }

        /**
         * 根据值获取枚举
         */
        public static Level getLevel(int level) {
            for (Level value : Level.values()) {
                if (value.getLevel() == level) {
                    return value;
                }
            }
            return null;
        }

    }

    /**
     * 推送系统消息类型
     */
    @Getter
    public static enum MsgType {
        /**
         * 续费
         */
        RENEW(1),
        /**
         * 赠送
         */
        GIFT(2)
        /**
         * 后台下发
         */
        , ADMIN(3)
        ;

        private int type;

        MsgType(int type) {
            this.type = type;
        }
    }
}
