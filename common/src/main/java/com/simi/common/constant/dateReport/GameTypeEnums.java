package com.simi.common.constant.dateReport;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * 游戏类型枚举
 */
@Getter
@AllArgsConstructor
public enum GameTypeEnums {

    FRUIT_PARTY(1, "Fruit Party"),
    RB(2, "RB"),
    LUCKY777(3, "Lucky777"),
    CRAZY_TRIPLE(4, "Crazy Triple"),
    ;
    final Integer type;
    final String desc;

    public static String getDescByType(Integer type){
        if (Objects.isNull(type)){
            return "";
        }

        for (GameTypeEnums value : GameTypeEnums.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return "";
    }
}
