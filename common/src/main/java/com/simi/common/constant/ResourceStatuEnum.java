package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 资源上架状态
 */
@Getter
@AllArgsConstructor
public enum ResourceStatuEnum {

    /**
     * 上架
     */
    LISTING(1, "上架"),

    /**
     * 下架
     */
    OFF_SHELF(2, "下架"),

    ;
    final Integer statu;
    final String desc;

    public static ResourceStatuEnum getByStatu(Integer statu) {
        return Stream.of(ResourceStatuEnum.values()).filter(e -> Objects.equals(e.getStatu(), statu)).findAny().orElse(null);
    }
}
