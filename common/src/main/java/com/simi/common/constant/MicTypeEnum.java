package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum MicTypeEnum {

    NORMAL_10_MIC(1, 10,"normal_10_mic"),
    PARTNER_12_MIC(2, 12,"partner_12_mic"),
    CHAT_15_MIC(3, 15,"chat_15_mic"),
    Chat_20_Mic(4, 20,"chat_20_mic"),
    ;
    private Integer type;

    private Integer code;

    private String desc;

    public static MicTypeEnum getByType(Integer type){
        if (type != null) {
            for (MicTypeEnum value : values()) {
                if (value.getType().equals(type)) {
                    return value;
                }
            }
        }
        return MicTypeEnum.NORMAL_10_MIC;
    }
}
