package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum FileType {

    RoomScreenMessageEvent(0,"公屏消息"),
    RoomToastNotifyEvent(1,"房间toast提示"),
    RoomPopupNotifyEvent(2,"房间弹窗提示"),
    RoomMicUpdateEvent(3,"麦位变动"),
    RoomMicInviteUpEvent(4,"邀请上麦"),
    RoomKickOutEvent(5,"踢出房间"),
    RoomAudienceUpdateEvent(6,"观众列表更新"),
    RoomIdentityUpdateEvent(7,"房间身份更新"),
    RoomUserConfUpdateEvent(8,"房间内用户配置信息更新"),
    RoomMicAllMuteEvent(9,"房间一键禁麦"),
    RoomConfigUpdateEvent(10,"房间配置更新"),
    RoomMicApplyListUpdateEvent(11,"房间麦位申请列表更新");

    private Integer number;

    private String desc;
}
