package com.simi.common.constant.resource;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.stream.Stream;

/**
 * 资源审核状态
 */
@Getter
@AllArgsConstructor
public enum ResourceAuditStateEnum {

    /**
     *审核中
     */
    RESOURCE_AUDIT_STATE_AUDIT(1),
    /**
     *通过
     */
    RESOURCE_AUDIT_STATE_PASS(2),
    /**
     *不通过
     */
    RESOURCE_AUDIT_STATE_NOT_PASS(3),

    ;
    final Integer type;

    public static ResourceAuditStateEnum getByType(Integer type) {
        return Stream.of(ResourceAuditStateEnum.values()).filter(e -> Objects.equals(e.getType(), type))
                .findAny().orElse(null);
    }
}
