package com.simi.common.constant.crazyTriple;

public enum CrazyTripleGameProductTypeEnum {
    CRAZY_TRIPLE_GAME_PRODUCT_TYPE_NONE(0),  //占位
    CRAZY_TRIPLE_GAME_PRODUCT_TYPE_SMALL(1), //small
    CRAZY_TRIPLE_GAME_PRODUCT_TYPE_BIG(2),   //big
    CRAZY_TRIPLE_GAME_PRODUCT_TYPE_TRIPLE(3);   //triple

    private final int value;

    CrazyTripleGameProductTypeEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    // Add this method to match the original code
    public static CrazyTripleGameProductTypeEnum valueOf(int value) {
        for (CrazyTripleGameProductTypeEnum e : values()) {
            if (e.value == value) {
                return e;
            }
        }
        return null;
    }
}
