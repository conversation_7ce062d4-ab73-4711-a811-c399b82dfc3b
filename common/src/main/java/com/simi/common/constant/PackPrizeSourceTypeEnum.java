package com.simi.common.constant;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 奖励包数据来源枚举
 */
@Getter
@AllArgsConstructor
public enum PackPrizeSourceTypeEnum {

    /**
     * 通过管理后台创建发放记录发放
     */
    PACK_PRIZE_SOURCE_TYPE_BACKGROUND(1),

    /**
     * 邀请钻石达标活动
     */
    ACTIVITY_REWARD(2),

    /**
     * 新用户礼包
     */
    NEW_USER_PACK_PRIZE(3),

    /**
     * 女性支持者活动
     */
    FEMALE_SUPPORTERS_PACK_PRIZE(4),

    /**
     * 宰牲节活动 按日
     */
    EID_AL_ADHA_DAY(5),

    /**
     * 宰牲节活动 按礼包
     */
    EID_AL_ADHA_GIFT(6),

    /**
     * 周星榜
     */
    WEEKLY_STAR(7),

    /**
     * 每日任务签到奖励
     */
    MISSION_DAILY_SIGN_REWARD(7),

    /**
     * 任务奖励
     */
    @Deprecated
    MISSION_REWARD(8),


    /**
     * 首充礼包
     */
    FIRST_RECHARGE_GIFT_BAG(9),

    /**
     * 当天在麦位上15 分钟任务奖励包
     */
    TAKE_THE_MIC_FOR_15MINS(10),

    /**
     * 在房间内45 分钟(奖励包标题)
     */
    STAY_IN_ROOM_45_MINS(11),

    /**
     * 在游戏水果派对中win1000 金币任务(奖励包标题)
     */
    WIN_1000_COINS_IN_FRUIT_PARTY(12),

    /**
     * 当天在房间内送礼1 次任务(奖励包标题)
     */
    SEND_GIFTS_IN_THE_ROOM(13),

    /**
     * 当天关注一个用户任务(奖励包标题)
     */
    FOLLOW_A_NEW_FRIEND(14),


    /**
     * 其他
     */
    OTHER(100),

    ;
    final Integer type;
}
