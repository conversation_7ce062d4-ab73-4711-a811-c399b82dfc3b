package com.simi.common.support;

import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import org.apache.ibatis.session.ResultContext;
import org.apache.ibatis.session.ResultHandler;
import org.springframework.util.CollectionUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * mybatis map结果集处理
 * <AUTHOR>
 */
public class MapResultHandler implements ResultHandler {

    @SuppressWarnings("rawtypes")
    private final Map<Object, List<Map>> mappedResults = new HashMap();

    @SuppressWarnings("unchecked")
    @Override
    public void handleResult(ResultContext context) {
        @SuppressWarnings("rawtypes")
        Map<String,Object> map = (Map) context.getResultObject();
        List<Map> values = mappedResults.get(map.get("key"));
        if (CollectionUtils.isEmpty(values)){
            List<Map> maps = Lists.newArrayList();
            maps.add(map);
            mappedResults.put(map.get("key"),maps);
        }else{
            values.add(map);
        }
    }

    public Map getMappedResults() {
        return mappedResults;
    }
}
