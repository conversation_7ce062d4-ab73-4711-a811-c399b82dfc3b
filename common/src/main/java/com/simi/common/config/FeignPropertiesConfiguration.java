package com.simi.common.config;

import org.springframework.cloud.openfeign.FeignClientProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/03/28 17:07
 **/
@Configuration
public class FeignPropertiesConfiguration {
    @Bean
    public FeignClientProperties feignClientProperties() {
        return new FeignClientProperties();
    }
}
