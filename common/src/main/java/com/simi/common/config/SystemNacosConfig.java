package com.simi.common.config;

import com.simi.common.dto.SystemConfigDTO;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;

/**
 * sys_conf的nacos配置
 *
 * <AUTHOR>
 * @date 2019/5/30 10:40
 */
@Configuration
@ConfigurationProperties(prefix = SystemNacosConfig.PREFIX)
@RefreshScope  // 使得配置能够实时更新
public class SystemNacosConfig {

    public static final String PREFIX = "system-config";

    @Getter
    @Setter
    //依赖于nacos特性，data的数据会实时更新
    private List<SystemConfigDTO> data = Lists.newArrayList();
}
