package com.simi.common.config;

import com.simi.common.dto.TranslationCopyDTO;
import lombok.Data;

import java.util.List;

/**
 * 自动推送文案内容配置
 * <AUTHOR>
 * @date 2024/08/16 14:31
 **/
@Data
public class AutoPushMsgConfig {
    private Integer type;
    private List<MsgCopywriting> msgCopywritings;

    @Data
    public static class MsgCopywriting {
        private TranslationCopyDTO copyTitle;
        private TranslationCopyDTO copyText;
    }

}
