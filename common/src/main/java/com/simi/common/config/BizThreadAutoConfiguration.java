package com.simi.common.config;

import com.simi.common.support.ThreadPoolExecutorMonitor;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 业务线程池配置
 */
@Configuration
@ConfigurationProperties(prefix = "api.async.threadpool")
public class BizThreadAutoConfiguration implements ThreadPoolExecutorMonitor {
    public static Integer coreSize;
    public static Integer maxSize;
    public static Integer queueCapacity;
    public static Integer keepAliveSeconds;

    /**
     * 业务线程调度器
     * @return
     */
    @Bean
    public ThreadPoolTaskExecutor bizExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(coreSize);
        executor.setMaxPoolSize(maxSize);
        executor.setQueueCapacity(queueCapacity);
        executor.setKeepAliveSeconds(keepAliveSeconds);
        executor.setThreadNamePrefix("AsyncBizThreadPool-");
        RejectedExecutionHandler handler = new ThreadPoolExecutor.CallerRunsPolicy();
        executor.setRejectedExecutionHandler(handler);
        executor.initialize();
        return executor;
    }


    /**
     * 定时任务调度器
     * @return
     */
    @Bean
    public TaskScheduler bizScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(coreSize);
        return scheduler;
    }

    public Integer getCoreSize() {
        return coreSize;
    }

    public void setCoreSize(Integer coreSize) {
        BizThreadAutoConfiguration.coreSize = coreSize;
    }

    public Integer getMaxSize() {
        return maxSize;
    }

    public void setMaxSize(Integer maxSize) {
        BizThreadAutoConfiguration.maxSize = maxSize;
    }

    public Integer getQueueCapacity() {
        return queueCapacity;
    }

    public void setQueueCapacity(Integer queueCapacity) {
        BizThreadAutoConfiguration.queueCapacity = queueCapacity;
    }

    public Integer getKeepAliveSeconds() {
        return keepAliveSeconds;
    }

    public void setKeepAliveSeconds(Integer keepAliveSeconds) {
        BizThreadAutoConfiguration.keepAliveSeconds = keepAliveSeconds;
    }

    @Override
    public ThreadPoolTaskExecutor getExecutor() {
        return bizExecutor();
    }
}
