package com.simi.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: Andy
 * @Date: 2024-01-09
 */
@Configuration
@ConfigurationProperties(prefix = "shumei")
public class ShumeiNacosConfig {

    public static String accessKey;
    public static String appId;
    public static String callback;
    public static String auditShowEventId;
    public static String auditShowType;
    public static String auditIMEventId;
    public static String auditIMType;
    public static String auditGroupMsgEventId;
    public static String auditGroupMsgType;
    public static String batchSyncUrl;


    public  void setBatchSyncUrl(String batchSyncUrl) {
        ShumeiNacosConfig.batchSyncUrl = batchSyncUrl;
    }

    public void setAccessKey(String accessKey) {
        ShumeiNacosConfig.accessKey = accessKey;
    }

    public void setAppId(String appId) {
        ShumeiNacosConfig.appId = appId;
    }

    public void setCallback(String callback) {
        ShumeiNacosConfig.callback = callback;
    }

    public void setAuditShowEventId(String auditShowEventId) {
        ShumeiNacosConfig.auditShowEventId = auditShowEventId;
    }

    public void setAuditShowType(String auditShowType) {
        ShumeiNacosConfig.auditShowType = auditShowType;
    }

    public void setAuditIMEventId(String auditIMEventId) {
        ShumeiNacosConfig.auditIMEventId = auditIMEventId;
    }

    public void setAuditIMType(String auditIMType) {
        ShumeiNacosConfig.auditIMType = auditIMType;
    }

    public void setAuditGroupMsgEventId(String auditGroupMsgEventId) {
        ShumeiNacosConfig.auditGroupMsgEventId = auditGroupMsgEventId;
    }

    public void setAuditGroupMsgType(String auditGroupMsgType) {
        ShumeiNacosConfig.auditGroupMsgType = auditGroupMsgType;
    }
}
