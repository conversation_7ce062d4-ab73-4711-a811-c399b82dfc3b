package com.simi.common;

import lombok.Getter;

@Getter
public enum ConfigGroup {

    // 运营后台通用配置分组
    OPERATIONAL_BACKEND_COMMON("1", "运营后台通用配置分组"),

    // 榜单分组
    RANKING_LIST("2", "榜单分组"),

    // 语音房分发列表
    VOICE_ROOM_DISTRIBUTION("3", "语音房分发列表");

    private final String id;
    private final String description;

    ConfigGroup(String id, String description) {
        this.id = id;
        this.description = description;
    }
}
