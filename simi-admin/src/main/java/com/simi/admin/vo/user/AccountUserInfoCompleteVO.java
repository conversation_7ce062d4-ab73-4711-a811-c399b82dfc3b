package com.simi.admin.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Andy
 * @Date: 2023/11/30
 */
@Data
@Builder
@Schema(description = "用户信息VO")
public class AccountUserInfoCompleteVO {
    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "用户id")
    private Long userNo;
    @Schema(description = "昵称")
    private String nick;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "性别:1-男; 2-女")
    private Byte gender;
    @Schema(description = "生日")
    private Date birth;
    @Schema(description = "魅力等级")
    private Integer charmLevel;
    @Schema(description = "财富等级")
    private Integer wealthLevel;
    @Schema(description = "粉丝数")
    private Integer fans;
    @Schema(description = "关注数")
    private Integer visites;
    @Schema(description = "金币数")
    private Integer coin;
    @Schema(description = "钻石数量")
    private Long diamond;
    @Schema(description = "国家代码")
    private String countryCode;

    @Schema(description = "注册方式: 0-手机; 1-facebook; 2-google; 3-mobile_value")
    private Integer signType;

    @Schema(description = "注册方式(处理好的字段)")
    private String signTypeStr;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "手机系统")
    private String os;

    @Schema(description = "客户端版本")
    private String appVersion;

    @Schema(description = "区号")
    private String areaCode;

    @Schema(description = "地区")
    private String areaStr;

    @Schema(description = "设备机型")
    private String model;

    @Schema(description = "设备号")
    private String deviceId;

    @Schema(description = "注册IP")
    private String registerIp;

    @Schema(description = "IP 地址")
    private String loginIp;

    @Schema(description = "最近活跃时间")
    private Date activeTime;

    @Schema(description = "状态: 0-全部; 1-正常; 2-注销; 3-注册待完善;")
    private Byte status;

    @Schema(description = "是否被封禁")
    private Boolean block;

    @Schema(description = "操作时间")
    private Date operationTime;
}
