package com.simi.admin.vo.user;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;

/**
 * 用户信息vo
 */
@Data
@Builder
@Schema(description = "用户信息vo")
public class UserVO implements Serializable {

    @Schema(description = "uid")
    private Long uid;
    @Schema(description = "用户 id")
    private Long userNo;
    @Schema(description = "昵称")
    private String nick;
    @Schema(description = "头像")
    private String avatar;
}