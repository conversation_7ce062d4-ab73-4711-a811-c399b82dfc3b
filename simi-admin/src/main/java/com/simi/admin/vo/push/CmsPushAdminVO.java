package com.simi.admin.vo.push;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/08/14 22:10
 **/
@Data
@Schema(description = "cms推送")
public class CmsPushAdminVO {

    /**
     * 主键
     */
    @Schema(description = "id")
    private Integer id;

    /**
     * 推送类型: 1-指定用户; 2-全部用户
     */
    @Schema(description = "推送类型: 1-指定用户; 2-全部用户")
    private Integer pushType;


    @Schema(description = "人数")
    private Integer peopleNum;

    /**
     * 状态: 1-未推送; 2-推送完成; 3-已取消
     */
    @Schema(description = "状态: 1-未推送; 2-推送完成; 3-已取消")
    private Integer status;

    /**
     * 推动时间类型: 1-定时推送; 2-不定时推送;
     */
    @Schema(description = "推动时间类型: 1-定时推送; 2-不定时推送;")
    private Integer timeType;

    /**
     * 推送时间
     */
    @Schema(description = "推送时间")
    private Date pushTime;

    /**
     * 跳转类型: 1-H5; 2-房间; 3-个人主页; 4-Game; 5-party; 6-消息页
     */
    @Schema(description = "跳转类型: 1-H5; 2-房间; 3-个人主页; 4-Game; 5-party; 6-消息页")
    private Integer jumpType;

    /**
     * 跳转参数
     */
    @Schema(description = "跳转参数")
    private String jumpParam;

    /**
     * 系统: 1-全部(默认)
     */
    @Schema(description = "系统: 1-全部(默认)")
    private Integer os;

    /**
     * 指定用户文件地址
     */
    @Schema(description = "指定用户文件地址")
    private String uidFileLink;

    /**
     * 最后操作人
     */
    @Schema(description = "adminId")
    private Integer admin;
    @Schema(description = "最后操作人")
    private String adminUser;

    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private Date createTime;

    /**
     * 英文标题
     */
    @Schema(description = "英文标题")
    private String titleEn;

    /**
     * 阿语标题
     */
    @Schema(description = "阿语标题")
    private String titleAr;

    /**
     * 英语文案内容
     */
    @Schema(description = "英语文案内容")
    private String textEn;

    /**
     * 阿语文案内容
     */
    @Schema(description = "阿语文案内容")
    private String textAr;


    /**
     * 英语图片
     */
    @Schema(description = "英语图片")
    private String picEn;

    /**
     * 阿语图片
     */
    @Schema(description = "阿语图片")
    private String picAr;

    @Schema(description = "目标用户id")
    private String targetUidJson;
}
