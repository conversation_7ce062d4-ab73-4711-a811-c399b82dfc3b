package com.simi.admin.vo.recharge;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/04/22 17:56
 **/
@Data
public class RechargeRecordVO {

    @Schema(description = "id")
    private String id;

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "用户id")
    private Long userNo;

    @Schema(description = "充值套餐id")
    private String packageId;

    @Schema(description = "渠道【1：google；2：apple】")
    private String channel;

    @Schema(description = "货币【1：USD】")
    private String currency;

    @Schema(description = "货币数量-冗余")
    private BigDecimal currencyAmount;

    @Schema(description = "货币数量")
    private String currencyAmountVal;

    @Schema(description = "金币数量")
    private Integer coinAmount;

    @Schema(description = "渠道订单号")
    private String channelOrderId;

    @Schema(description = "渠道唯一验证id")
    private String channelVerifyId;

    @Schema(description = "订单状态，1创建，2成功，3退款")
    private Integer status;

    @Schema(description = "订单创建时间")
    private Date createTime;

    @Schema(description = "订单充值成功时间")
    private Date successTime;

    @Schema(description = "订单退款时间")
    private Date refundTime;

    /**
     *
     */
    @Schema(description = "修改时间")
    private Date updateTime;

    @Schema(description = "是否沙盒充值")
    private Boolean isSandbox;

    @Schema(description = "系统")
    private String os;

    @Schema(description = "设备ID")
    private String deviceId;

    @Schema(description = "应用版本")
    private String appVersion;

    @Schema(description = "id")
    private String ip;

    @Schema(description = "订单说明")
    private String explain;

    @Schema(description = "平台货币类型: 1-金币; 2-金票")
    private Integer platformCurrencyType;
}
