package com.simi.admin.vo.dataReport;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "游戏报表")
public class GameReportVO {
    @Schema(description = "日期")
    private String reportDate;

    @Schema(description = "日期时间戳")
    private Long reportTimestamp;

    @Schema(description = "用户id")
    private Long userNo;

    @Schema(description = "uid")
    private Long uid;

    @Schema(description = "id")
    private Long id;

    @Schema(description = "gameId")
    private Long gameId;

    @Schema(description = "用户昵称")
    private String nick;

    @Schema(description = "游戏类型，-1:全部，1-Fruit Party,2:RB,3:Lucky777")
    private Integer gameType;

    @Schema(description = "游戏名称")
    private String gameName;

    @Schema(description = "局数")
    private Integer gameNumber;

    @Schema(description = "投注项")
    private String bets;

    @Schema(description = "游戏结果,-1:全部，0:N,1:Y")
    private Integer gameResult;

    @Schema(description = "投入金币")
    private Integer inCoin;

    @Schema(description = "产出金币")
    private Integer outCoin;

    @Schema(description = "实际盈利")
    private Integer profitCoin;
}
