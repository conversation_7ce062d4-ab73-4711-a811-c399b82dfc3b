package com.simi.admin.service.currency.impl;

import com.simi.admin.entity.currency.CurrencyOperationRecord;
import com.simi.admin.req.currency.CoinOperationParam;
import com.simi.admin.service.currency.BaseCoinOperationStrategy;
import com.simi.admin.service.currency.CoinOperationObserver;
import com.simi.admin.service.currency.CurrencyOperationRecordService;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.CurrencyOperationEnum;
import com.simi.entity.user.User;
//import com.simi.service.coinDealer.CoinDealerService;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

@Slf4j
@Service
@RequiredArgsConstructor
public class RewardGoldenTicketStrategy extends BaseCoinOperationStrategy {

    private final UserService userService;
    private final CurrencyOperationRecordService currencyOperationRecordService;
    private final PlatformTransactionManager platformTransactionManager;
    //private final CoinDealerService coinDealerService;
    private final MedalTaskService medalTaskService;

    @Override
    public void handle(CoinOperationParam coinOperationParam, CoinOperationObserver coinOperationObserver) {
        User user = coinOperationParam.getUser();
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            Long amount = coinOperationParam.getAmount().longValue();
            BillEnum billEnum = coinOperationParam.getBillEnum();
            CurrencyOperationRecord record = currencyOperationRecordService.createRecord(user.getUid(), coinOperationParam.getAmount(), coinOperationParam.getOperation(), billEnum, coinOperationParam.getRemark(), coinOperationParam.getIntroduce(), coinOperationParam.getAdminId());
            //coinDealerService.modifyGoldenTicketByPlatform(user.getUid(), amount);
            coinOperationObserver.operationSuccessful(user);
            log.info("add golden ticket success, user_no: {}, type: {}, num: {}, record_id: {}", user.getUserNo(), billEnum, amount, record.getId());
            platformTransactionManager.commit(status);

            try {
                // cms奖励金票算作勋章任务, "累计金币统计, 1:1"
                log.info("Cms reward golden ticket deal medal task, uid:[{}] golden_ticket:[{}]", user.getUid(), coinOperationParam.getAmount());
                medalTaskService.executeMedalTask(MedalTaskEnum.RECHARGE_GOLD, user.getUid(), coinOperationParam.getAmount().intValue());
            } catch (Exception e) {
                log.info("Cms reward golden ticket deal medal task failed, uid:[{}] gold:[{}]", user.getUid(), coinOperationParam.getAmount());
            }
        } catch (Exception ex) {
            platformTransactionManager.rollback(status);
            log.error("add golden ticket failure:{}", ExceptionUtil.formatEx(ex));
            throw ex;
        }
    }

    @Override
    public boolean isEqualWithType(int type) {
        return CurrencyOperationEnum.CMS_REWARD_GOLDEN_TICKET.getType() == type;
    }

    @Override
    protected UserService userService() {
        return this.userService;
    }

}
