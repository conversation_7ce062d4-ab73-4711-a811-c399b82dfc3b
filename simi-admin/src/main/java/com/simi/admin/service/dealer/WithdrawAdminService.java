package com.simi.admin.service.dealer;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.vo.currency.WithdrawRecordVO;
import com.simi.common.config.CopywritingEnum;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.WithdrawStatus;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.OperationWithdraw;
import com.simi.common.dto.UserIdCardDataDTO;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.OssUrlUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.DigitalCurrencyEnum;
import com.simi.entity.user.UserIdCardData;
import com.simi.entity.WithdrawRecord;
import com.simi.message.PresentImMessage;
import com.simi.service.UserIdCardDataService;
import com.simi.service.WithdrawRecordService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.purse.PurseManageService;
import com.simi.service.user.UserServerService;
import com.simi.util.PushMsgUtil;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/05/31 17:56
 **/
@Service
@RequiredArgsConstructor
public class WithdrawAdminService {

    private final PurseManageService purseManageService;
    private final WithdrawRecordService withdrawRecordService;
    private final UserServerService userServerService;
    private final UserIdCardDataService userIdCardDataService;
    private final NotifyMessageComponent notifyMessageComponent;

    public void operation(int adminId, OperationWithdraw req) {
        LambdaQueryWrapper<WithdrawRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(WithdrawRecord::getId, req.getId());
        WithdrawRecord withdrawRecord = withdrawRecordService.getOne(queryWrapper);
        LambdaUpdateWrapper<WithdrawRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.set(WithdrawRecord::getAdminId, adminId);
        wrapper.set(WithdrawRecord::getStatus, req.getStatus());
        wrapper.set(StringUtils.isNotBlank(req.getRemarkAr()), WithdrawRecord::getRemarkAr, req.getRemarkAr());
        wrapper.set(StringUtils.isNotBlank(req.getRemarkEn()), WithdrawRecord::getRemarkEn, req.getRemarkEn());
        wrapper.set(StringUtils.isNotBlank(req.getSucceedPic()), WithdrawRecord::getSucceedPic, req.getSucceedPic());
        wrapper.eq(WithdrawRecord::getId, req.getId());
        withdrawRecordService.update(wrapper);
        if (req.getStatus().equals(WithdrawStatus.refuse.getCode())) {
            //退还钻石
            purseManageService.addUSD(withdrawRecord.getUid(), new BigDecimal(withdrawRecord.getAmount()).divide(new BigDecimal(100)), BillEnum.USD_WITHDRAW_LOSE, withdrawRecord.getId(), "", Collections.emptyMap(), 0L, PurseRoleTypeEnum.USER.getType());
            CommonIMMessage imMessage = new CommonIMMessage();
            imMessage.setType(IMMsgType.purseTransferAccountsImMsg);
            PresentImMessage presentImMessage = new PresentImMessage();
            String copywriting = MessageSourceUtil.i18nByCode(CopywritingEnum.WITHDRAWAL_FAILED.getKey(), LanguageEnum.en);
            String copywritingAr = MessageSourceUtil.i18nByCode(CopywritingEnum.WITHDRAWAL_FAILED.getKey(), LanguageEnum.ar);
            presentImMessage.setTitle(copywriting);
            presentImMessage.setTitleAr(copywritingAr);
            presentImMessage.setDigitalCurrency(DigitalCurrencyEnum.USD.getNumber());
            presentImMessage.setNum(withdrawRecord.getAmount());
            presentImMessage.setSendTime(new Date().getTime());
            presentImMessage.setRemark(req.getRemarkEn());
            presentImMessage.setRemarkAr(req.getRemarkAr());
            imMessage.setPayload(JSONUtil.toJsonStr(presentImMessage));
            imMessage.setTimestamp(new Date().getTime());

            OfflinePushInfo offlinePushInfo = null;
            try {
                LanguageEnum languageEnum = userServerService.userAppLanguage(withdrawRecord.getUid());
                String offlineTitle = Objects.equals(languageEnum, LanguageEnum.en) ? copywriting : copywritingAr;
                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemPuresUid),
                        offlineTitle,
                        offlineTitle,
                        null,
                        ClientRouteUtil.toPurse(DigitalCurrencyEnum.COIN.getNumber() - 1));
            } catch (Exception e) {
                // ignore
            }

            notifyMessageComponent.publishPuresIMDefineMessage(imMessage, withdrawRecord.getUid().toString(), offlinePushInfo);
        }else if (req.getStatus().equals(WithdrawStatus.processed.getCode())) {
            CommonIMMessage imMessage = new CommonIMMessage();
            imMessage.setType(IMMsgType.purseTransferAccountsImMsg);
            PresentImMessage presentImMessage = new PresentImMessage();
            String copywriting = MessageSourceUtil.i18nByCode(CopywritingEnum.WITHDRAW_SUCCESSFULLY.getKey(), LanguageEnum.en);
            String copywritingAr = MessageSourceUtil.i18nByCode(CopywritingEnum.WITHDRAW_SUCCESSFULLY.getKey(), LanguageEnum.ar);
            presentImMessage.setTitle(copywriting);
            presentImMessage.setTitleAr(copywritingAr);
            presentImMessage.setDigitalCurrency(DigitalCurrencyEnum.USD.getNumber());
            presentImMessage.setNum(withdrawRecord.getAmount());
            presentImMessage.setSendTime(new Date().getTime());
            presentImMessage.setPic(req.getSucceedPic());
            imMessage.setPayload(JSONUtil.toJsonStr(presentImMessage));
            imMessage.setTimestamp(new Date().getTime());

            OfflinePushInfo offlinePushInfo = null;
            try {
                LanguageEnum languageEnum = userServerService.userAppLanguage(withdrawRecord.getUid());
                String offlineTitle = Objects.equals(languageEnum, LanguageEnum.en) ? copywriting : copywritingAr;
                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemPuresUid),
                        offlineTitle,
                        offlineTitle,
                        null,
                        ClientRouteUtil.toPurse(DigitalCurrencyEnum.COIN.getNumber() - 1));
            } catch (Exception e) {
                // ignore
            }
            notifyMessageComponent.publishPuresIMDefineMessage(imMessage, withdrawRecord.getUid().toString(), offlinePushInfo);
        }
    }


    public ListWithTotal<WithdrawRecordVO> queryList(Long uid, Long userNo, String channel, Integer status, Integer page, Integer size) {
        if (Objects.nonNull(userNo)) {
            Long uidByUserNo = userServerService.getUidByUserNo(userNo);
            if (Objects.isNull(uidByUserNo) || (Objects.nonNull(uid) && !Objects.equals(uid, uidByUserNo))) {
                return ListWithTotal.empty();
            }
            uid = uidByUserNo;
        }
        LambdaQueryWrapper<WithdrawRecord> wrapper = new LambdaQueryWrapper<>();
        if (Objects.nonNull(uid)) {
            wrapper.eq(WithdrawRecord::getUid, uid);
        }
        wrapper.eq(StringUtils.isNotBlank(channel), WithdrawRecord::getChannel, channel);
        wrapper.eq(status != null, WithdrawRecord::getStatus, status);
        PageHelper.startPage(page, size, "created_at DESC ");
        List<WithdrawRecord> withdrawRecords = withdrawRecordService.list(wrapper);

        if (CollUtil.isEmpty(withdrawRecords)) {
            return ListWithTotal.empty();
        }

        Map<Long, UserBaseInfoDTO> userBaseInfoDTOMap;
        List<Long> uids = withdrawRecords.stream().map(WithdrawRecord::getUserRealId).collect(Collectors.toList());
        List<Long> userId = withdrawRecords.stream().map(WithdrawRecord::getUid).collect(Collectors.toList());
        userBaseInfoDTOMap = userServerService.batchUserSummary(userId);
        List<UserIdCardData> userIdCardData = userIdCardDataService.getByUidIn(uids);
        Map<Long, UserIdCardData> dataMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(userIdCardData)) {
            dataMap = userIdCardData.stream().collect(Collectors.toMap(UserIdCardData::getId, u -> u));
        }
        PageInfo<WithdrawRecord> pageInfo = new PageInfo<>(withdrawRecords);
        Map<Long, UserIdCardData> finalDataMap = dataMap;
        List<WithdrawRecordVO> recordVOS = withdrawRecords.stream().map(withdrawRecord -> {
            WithdrawRecordVO withdrawRecordVO = BeanUtil.copyProperties(withdrawRecord, WithdrawRecordVO.class, "amount");
            withdrawRecordVO.setCreatedTime(withdrawRecord.getCreatedAt());
            UserBaseInfoDTO userBaseInfoDTO = userBaseInfoDTOMap.get(withdrawRecord.getUid());
            if (userBaseInfoDTO != null) {
                withdrawRecordVO.setUserNo(userBaseInfoDTO.getUserNo());
            }
            UserIdCardData data = finalDataMap.get(withdrawRecord.getUserRealId());
            if (data != null) {
                UserIdCardDataDTO userIdCardDataDTO = BeanUtil.copyProperties(data, UserIdCardDataDTO.class);
                userIdCardDataDTO.setIdPhoto(OssUrlUtil.jointUrl(userIdCardDataDTO.getIdPhoto()));
                userIdCardDataDTO.setCardPhoto(OssUrlUtil.jointUrl(userIdCardDataDTO.getCardPhoto()));
                withdrawRecordVO.setUserIdCardDataDTO(userIdCardDataDTO);
            }
            if (Objects.nonNull(withdrawRecord.getAmount())) {
                BigDecimal usd = new BigDecimal(withdrawRecord.getAmount()).divide(BigDecimal.TEN.multiply(BigDecimal.TEN), 2, RoundingMode.DOWN);
                withdrawRecordVO.setAmount(usd);
            }
            return withdrawRecordVO;
        }).toList();
        return ListWithTotal.<WithdrawRecordVO>builder().total(pageInfo.getTotal()).list(recordVOS).build();
    }

}
