package com.simi.admin.service.room;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.req.room.FeedbackAdminReq;
import com.simi.admin.req.room.ModifyFeedbackAdminReq;
import com.simi.admin.vo.room.RoomFeedbackRecordVO;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.ProcessStatusEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.AdminException;
import com.simi.entity.room.RoomFeedbackRecord;
import com.simi.service.room.RoomFeedbackRecordService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 房间反馈admin Service
 *
 * <AUTHOR>
 * @date 2024/04/01 14:50
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomFeedbackAdminService {

    private final RoomFeedbackRecordService feedbackRecordService;
    private final UserServerService userServerService;

    /**
     * 检索
     * @param req
     * @return
     */
    public ListWithTotal<RoomFeedbackRecordVO> search(FeedbackAdminReq req){
        if (Objects.isNull(req.getPageNum()) || Objects.isNull(req.getPageSize())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        Long uid = null;
        if (Objects.nonNull(req.getUserNo())) {
            uid = userServerService.getUidByUserNo(req.getUserNo());
            if (Objects.isNull(uid)) {
                return ListWithTotal.empty();
            }
        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<RoomFeedbackRecord> list = feedbackRecordService.lambdaQuery()
                .eq(Objects.nonNull(uid), RoomFeedbackRecord::getUid, uid)
                .eq(Objects.nonNull(req.getStatusValue()) && !Objects.equals(ProcessStatusEnum.NONE.getType(), req.getStatusValue()), RoomFeedbackRecord::getStatus, req.getStatusValue())
                .orderByDesc(RoomFeedbackRecord::getId)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return ListWithTotal.empty();
        }
        List<Long> uids = list.stream().map(RoomFeedbackRecord::getUid).collect(Collectors.toList());
        Map<Long, UserBaseInfoDTO> userMap = userServerService.batchUserSummary(uids);
        PageInfo<RoomFeedbackRecord> pageInfo = new PageInfo<>(list);

        List<RoomFeedbackRecordVO> resultVOs = BeanUtil.copyToList(list, RoomFeedbackRecordVO.class);
        resultVOs.forEach(e -> {
            UserBaseInfoDTO user = userMap.get(e.getUid());
            if (Objects.nonNull(user)) {
                e.setUserNo(user.getUserNo());
            }
        });
        return ListWithTotal.<RoomFeedbackRecordVO>builder().total(pageInfo.getTotal()).list(resultVOs).build();
    }

    public void modify(ModifyFeedbackAdminReq param){
        feedbackRecordService.lambdaUpdate()
                .set(RoomFeedbackRecord::getStatus, param.getStatusValue())
                .set(RoomFeedbackRecord::getRemark, param.getRemark())
                .eq(RoomFeedbackRecord::getId, param.getId())
                .update();
    }
}
