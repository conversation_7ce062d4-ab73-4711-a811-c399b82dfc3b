package com.simi.admin.service.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.req.resource.BannerAdminListReq;
import com.simi.admin.req.resource.RemoveBannerReq;
import com.simi.admin.req.resource.SaveBannerReq;
import com.simi.admin.vo.resource.BannerVO;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.PositionEnum;
import com.simi.common.constant.StatusEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.exception.AdminException;
import com.simi.entity.Banner;
import com.simi.service.BannerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * banner
 *
 * <AUTHOR>
 * @date 2023/12/4 14:10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class BannerAdminService {

    private final BannerService bannerService;
    private final ObjectMapper objectMapper;

    /**
     * 查询banner列表
     *
     * @return
     */
    public ListWithTotal<BannerVO> search(BannerAdminListReq req) {
        if (Objects.isNull(req.getPageNum()) || Objects.isNull(req.getPageSize())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize(), "id desc");

        LambdaQueryWrapper<Banner> queryWrapper = new LambdaQueryWrapper<>();

        // 使用groupIds匹配
        if (Objects.nonNull(req.getCountryGroupId())) {
            // 判断groupIds字段是否包含指定的countryGroupId
            queryWrapper.like(Banner::getGroupIds, req.getCountryGroupId().toString());
        }

        queryWrapper.eq(Objects.nonNull(req.getPosition()) && !Objects.equals(PositionEnum.POSITION_NONE.getNumber(), req.getPosition()),
                Banner::getPosition, req.getPosition());

        Date now = new Date();
        if (Objects.nonNull(req.getStatus())) {
            if (req.getStatus().equals(StatusEnum.invalid.getStatus())) {
                queryWrapper.and(qw -> qw.lt(Banner::getEndTime, now).or().gt(Banner::getStartTime, now));
            } else if (req.getStatus().equals(StatusEnum.normal.getStatus())) {
                queryWrapper.lt(Banner::getStartTime, now).gt(Banner::getEndTime, now);
            }
        }

        List<Banner> list = bannerService.list(queryWrapper);
        PageInfo<Banner> pageInfo = new PageInfo<>(list);
        if (CollectionUtils.isEmpty(list)) {
            return ListWithTotal.empty();
        }
        List<BannerVO> bannerVOS = BeanUtil.copyToList(list, BannerVO.class);
        bannerVOS.forEach(e -> {
            if (Objects.nonNull(e.getStartTime()) && Objects.nonNull(e.getEndTime())) {
                if (DateUtil.isIn(now, e.getStartTime(), e.getEndTime())) {
                    e.setStatus(StatusEnum.normal.getStatus());
                } else {
                    e.setStatus(StatusEnum.invalid.getStatus());
                }
            } else {
                e.setStatus(StatusEnum.invalid.getStatus());
            }
        });
        for (int i = 0; i < list.size(); i++) {
            Banner banner = list.get(i);
            BannerVO bannerVO = bannerVOS.get(i);
            String commonExtJson = banner.getCommonExt();
            if (StringUtils.isNotBlank(commonExtJson)) {
                try {
                    Object countryGroupBusinessReq = objectMapper.readValue(commonExtJson, Object.class);
                    bannerVO.setCountryGroupBusinessReq(countryGroupBusinessReq);
                } catch (JsonProcessingException ex) {
                    log.error("Failed to parse commonExt JSON for banner id {}: {}", banner.getId(), ex.getMessage());
                }
            }
        }

        return ListWithTotal.<BannerVO>builder().list(bannerVOS).total(pageInfo.getTotal()).build();
    }

    public void save(SaveBannerReq req) throws JsonProcessingException {
        Banner banner = pb2Entity(req);
        //判断客户端是否给JSON字段，如果给存储起来
        if (req.getCountryGroupBusinessReq() != null) {
            String commonExtJson = objectMapper.writeValueAsString(req.getCountryGroupBusinessReq());
            banner.setCommonExt(commonExtJson);
        }
        if (Objects.nonNull(req.getId())) {
            banner.setId(req.getId());
            bannerService.updateById(banner);
        } else {
            banner.setCreateTime(new Date());
            bannerService.save(banner);
        }
    }

    private Banner pb2Entity(SaveBannerReq bannerPB) {
        Banner build = Banner.builder()
                .name(bannerPB.getName())
                .routeType(bannerPB.getRouteTypeValue())
                .routeParam(bannerPB.getRouteParam())
                .position(bannerPB.getPositionValue())
                .seqNo(bannerPB.getSortSeq())
                .cover(bannerPB.getCover())
                .blurHash(bannerPB.getBlurHash())
                .minVersion(StringUtils.defaultIfBlank(bannerPB.getMinVersion(), ""))
                .maxVersion(StringUtils.defaultIfBlank(bannerPB.getMaxVersion(), ""))
                .groupIds(bannerPB.getGroupIds())
                .platform(bannerPB.getPlatform()).build();
        if (Objects.nonNull(bannerPB.getEndTime()) && bannerPB.getStartTime() > 0) {
            Date date = new Date(bannerPB.getStartTime());
            build.setStartTime(date);
        }
        if (Objects.nonNull(bannerPB.getEndTime()) && bannerPB.getEndTime() > 0) {
            Date date = new Date(bannerPB.getEndTime());
            build.setEndTime(date);
        }
        return build;
    }

    public void remove(RemoveBannerReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getId())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        Banner banner = bannerService.getById(req.getId());
        if (Objects.isNull(banner)) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        bannerService.removeById(req.getId());

    }
}
