package com.simi.admin.service.rewardpack;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.entity.AdminUser;
import com.simi.admin.req.rewardpack.DeleteRewardPackReq;
import com.simi.admin.req.rewardpack.ListRewardPackReq;
import com.simi.admin.req.rewardpack.SaveRewardPackReq;
import com.simi.admin.service.system.AdminUserService;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.http.Constant;
import com.simi.common.dto.RewardPackVO;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.resource.ResourceRedisKey;
import com.simi.common.constant.resource.ResourceTypeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.rewardPack.RewardPackResourceDTO;
import com.simi.common.exception.AdminException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.reward.RewardPackResourceVO;
import com.simi.constant.DeleteFlagEnum;
import com.simi.entity.rewardpack.RewardPack;
import com.simi.entity.rewardpack.RewardPackResource;
import com.simi.manager.RewardPackManager;
import com.simi.service.rewardpack.RewardPackResourceService;
import com.simi.service.rewardpack.RewardPackServerService;
import com.simi.service.rewardpack.RewardPackService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 *
 */

@Slf4j
@Service
@RequiredArgsConstructor
public class RewardPackAdminService {

    private final RewardPackManager rewardPackManager;
    private final RewardPackService rewardPackService;

    private final RewardPackResourceService rewardPackResourceService;

    private final AdminUserService adminUserService;

    private final RedissonManager redissonManager;

    private final RewardPackServerService rewardPackServerService;

    private final SystemConfigService systemConfigService;

    public ListWithTotal<RewardPackVO> listRewardPack(ListRewardPackReq req) {
        LambdaQueryWrapper<RewardPack> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RewardPack::getDeleteFlag, DeleteFlagEnum.UN_DELETED.getType());
        queryWrapper.eq(Objects.nonNull(req.getPackId()), RewardPack::getId, req.getPackId());
        queryWrapper.orderByDesc(RewardPack::getCreateTime);
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<RewardPack> list = rewardPackService.list(queryWrapper);

        if (CollUtil.isEmpty(list)) {
            return ListWithTotal.empty();
        }

        PageInfo<RewardPack> pageInfo = new PageInfo<>(list);

        List<RewardPackVO> result = BeanUtil.copyToList(list, RewardPackVO.class);
        List<Integer> packIds = result.stream().map(RewardPackVO::getId).toList();
        Map<Integer, List<RewardPackResource>> resourceMap = rewardPackManager.validRewardResourceMap(packIds);

        result.forEach(e -> {
            List<RewardPackResource> resourceList = resourceMap.get(e.getId());
            List<RewardPackResourceVO> resourceListVO = getResourceListVO(resourceList);
            e.setResourceVOList(resourceListVO);
        });
        return ListWithTotal.<RewardPackVO>builder().total(pageInfo.getTotal()).list(result).build();
    }

    public ListWithTotal<RewardPackVO> appointList() {
        String appointIdsStr = systemConfigService.getSysConfValueById(SystemConfigConstant.APPOINT_REWARDPACK_IDS);
        if (StrUtil.isBlank(appointIdsStr) || StrUtil.equals(appointIdsStr, Constant.WELL_NUMBER)) {
            return ListWithTotal.empty();
        }
        List<Integer> appointIds = StrUtil.split(appointIdsStr, StrUtil.C_COMMA).stream().map(Integer::valueOf).toList();
        LambdaQueryWrapper<RewardPack> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RewardPack::getDeleteFlag, DeleteFlagEnum.UN_DELETED.getType());
        queryWrapper.in(RewardPack::getId, appointIds);
        List<RewardPack> resultList = rewardPackService.list(queryWrapper);

        if (CollUtil.isEmpty(resultList)) {
            return ListWithTotal.empty();
        }


        List<RewardPackVO> result = BeanUtil.copyToList(resultList, RewardPackVO.class);
        List<Integer> packIds = result.stream().map(RewardPackVO::getId).toList();
        Map<Integer, List<RewardPackResource>> resourceMap = rewardPackManager.validRewardResourceMap(packIds);

        result.forEach(e -> {
            List<RewardPackResource> resourceList = resourceMap.get(e.getId());
            List<RewardPackResourceVO> resourceListVO = getResourceListVO(resourceList);
            e.setResourceVOList(resourceListVO);
        });
        return ListWithTotal.<RewardPackVO>builder().total(resultList.size()).list(result).build();
    }

    /**
     * 处理dto
     * @return
     */
    public List<RewardPackResourceDTO> getResourceListDTO(List<RewardPackResource> resourceList) {
        if (CollUtil.isEmpty(resourceList)) {
            return CollUtil.newArrayList();
        }
        return rewardPackServerService.buildResourceDTOs(resourceList);
    }

    /**
     * 处理vo
     * @return
     */
    public List<RewardPackResourceVO> getResourceListVO(List<RewardPackResource> resourceList) {
        if (CollUtil.isEmpty(resourceList)) {
            return CollUtil.newArrayList();
        }
        return rewardPackServerService.buildResourceVOs(resourceList);
    }



    public RewardPackVO save(SaveRewardPackReq req, int adminId, String jsonStr) {
        SaveRewardPackReq.RewardPackReq rewardPackReq = req.getRewardPackReq();
        Integer packId = rewardPackReq.getPackId();
        RewardPack rewardPack;
        boolean isAddPack = false;
        if (Objects.nonNull(packId)) {
            rewardPack = rewardPackService.getById(packId);
            if (Objects.isNull(rewardPack)) {
                log.error("saveRewardPack fail id param illegal adminId:[{}],param:[{}]", adminId, jsonStr);
                throw new AdminException(CodeEnum.PARAM_ILLEGAL);
            }
        } else {
            rewardPack = new RewardPack();
            isAddPack = true;
        }

        List<RewardPackResource> toAddList = new ArrayList<>();
        List<RewardPackResource> toUpdateList = new ArrayList<>();
        List<Integer> toDeleteIdList = new ArrayList<>();

        List<SaveRewardPackReq.RewardPackResourceReq> resourceReqList = req.getResourceReqList();

        boolean ret;

        ret = saveRewardPack(adminId, rewardPack, rewardPackReq);
        buildResourceList(isAddPack, adminId, rewardPack.getId(), resourceReqList, toAddList, toUpdateList, toDeleteIdList);
        if (!CollUtil.isEmpty(toDeleteIdList)) {
            rewardPackResourceService.removeBatchByIds(toDeleteIdList);
        }
        if (!CollUtil.isEmpty(toUpdateList)) {
            rewardPackResourceService.updateBatchById(toUpdateList);
        }
        if (!CollUtil.isEmpty(toAddList)) {
            rewardPackResourceService.saveBatch(toAddList);
        }

        if (!ret) {
            log.warn("saveRewardPack fail ret:{},adminId:{},param:{}", ret, adminId, jsonStr);
            throw new AdminException(CodeEnum.SERVER_ERROR);
        }
        RewardPackVO result = BeanUtil.toBean(rewardPack, RewardPackVO.class);
        packId = rewardPack.getId();
        List<RewardPackResource> resourceList = rewardPackManager.validRewardResourceListByPackId(packId);
        // 处理缓存, 使用DTO
        List<RewardPackResourceDTO> resourceListDTO = getResourceListDTO(resourceList);
        redissonManager.hSet(ResourceRedisKey.reward_pack_resource.getKey(), String.valueOf(packId), JSONUtil.toJsonStr(resourceListDTO));
        // 响应VO
        List<RewardPackResourceVO> resourceListVO = getResourceListVO(resourceList);
        result.setResourceVOList(resourceListVO);
        return result;
    }

    private void buildResourceList(boolean isAddPack, int adminId, int packId, List<SaveRewardPackReq.RewardPackResourceReq> resourceList, List<RewardPackResource> toAddList, List<RewardPackResource> toUpdateList, List<Integer> toDeleteIdList) {
        Map<Integer, RewardPackResource> rewardPackResourceMap = rewardPackManager.getAllRewardPackResourceMap(packId);
        Set<Integer> existResourceIds = rewardPackResourceMap.keySet();
        Set<Integer> updateResourceIds = new HashSet<>();
        for (SaveRewardPackReq.RewardPackResourceReq item : resourceList) {
            boolean isNew = false;
            RewardPackResource rewardPackResource = null;
            if (!isAddPack) {
                rewardPackResource = rewardPackManager.getRewardPackResource(packId, item.getResourceType(), item.getPrizeId());
            }
            if (rewardPackResource == null) {
                isNew = true;
            }
            if (isNew) {
                rewardPackResource = new RewardPackResource();
            } else {
                updateResourceIds.add(rewardPackResource.getId());
            }
            rewardPackResource.setPackId(packId);
            rewardPackResource.setName(StringUtils.defaultIfEmpty(item.getPrizeName(), ""));
            rewardPackResource.setPrizeType(item.getResourceType());
            rewardPackResource.setPrizeId(item.getPrizeId());
            rewardPackResource.setDurationType(item.getDurationType());
            rewardPackResource.setDuration(item.getDurationMillis() > 0 ? (int) (item.getDurationMillis() / (60 * 1000L)) : 0);
            if (Objects.equals(item.getResourceType(), ResourceTypeEnum.RESOURCE_USD.getNumber())) {
                // 美金转美分
                rewardPackResource.setAmount(item.getAmount().multiply(new BigDecimal(100)).longValue());
            } else {
                rewardPackResource.setAmount(Objects.isNull(item.getAmount()) ? 1L : item.getAmount().longValue());
            }
            Date now = new Date();
            AdminUser adminUser = adminUserService.getById(adminId);
            rewardPackResource.setModifyUid(adminId);
            rewardPackResource.setModifyBy(adminUser == null ? "" : adminUser.getUsername());
            rewardPackResource.setModifyTime(now);
            rewardPackResource.setDeleteFlag(DeleteFlagEnum.UN_DELETED.getType());
            if (isNew) {
                rewardPackResource.setCreateUid(adminId);
                rewardPackResource.setCreateBy(adminUser == null ? "" : adminUser.getUsername());
                rewardPackResource.setCreateTime(now);
                toAddList.add(rewardPackResource);
            } else {
                toUpdateList.add(rewardPackResource);
            }
        }
        existResourceIds.removeAll(updateResourceIds);
        toDeleteIdList.addAll(existResourceIds);
    }

    private boolean saveRewardPack(int adminId, RewardPack rewardPack, SaveRewardPackReq.RewardPackReq param) {
        rewardPack.setName(StringUtils.defaultIfEmpty(param.getPackName(), ""));
        rewardPack.setRemark("");
        Date now = new Date();
        AdminUser adminUser = adminUserService.getById(adminId);
        rewardPack.setModifyUid(adminId);
        rewardPack.setModifyBy(adminUser == null ? "" : adminUser.getUsername());
        rewardPack.setModifyTime(now);
        if (Objects.isNull(param.getPackId())) {
            rewardPack.setDeleteFlag(DeleteFlagEnum.UN_DELETED.getType());
            rewardPack.setCreateUid(adminId);
            rewardPack.setCreateBy(adminUser == null ? "" : adminUser.getUsername());
            rewardPack.setCreateTime(now);
            return rewardPackService.save(rewardPack);
        } else {
            return rewardPackService.updateById(rewardPack);
        }
    }

    public boolean delete(DeleteRewardPackReq req, int adminId) {
        if (Objects.isNull(req.getPackId())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        AdminUser adminUser = adminUserService.getById(adminId);
        LambdaUpdateWrapper<RewardPack> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(RewardPack::getId, req.getPackId());
        updateWrapper.set(RewardPack::getDeleteFlag, DeleteFlagEnum.DELETED.getType())
                .set(RewardPack::getModifyUid, adminId)
                .set(RewardPack::getModifyBy, adminUser == null ? "" : adminUser.getUsername())
                .set(RewardPack::getModifyTime, new Date());
        boolean update = rewardPackService.update(updateWrapper);
        if (update) {
            redissonManager.hDel(ResourceRedisKey.reward_pack_resource.getKey(), String.valueOf(req.getPackId()));
        }
        return update;
    }

    public RewardPackVO getRewardPack(Integer packId) {
        RewardPack rewardPack = rewardPackManager.getRewardPackById(packId);
        if (Objects.isNull(rewardPack)) {

            log.error("getRewardPack fail id param illegal packId:{}", packId);
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        RewardPackVO result = BeanUtil.toBean(rewardPack, RewardPackVO.class);
        List<RewardPackResource> resourceList = rewardPackManager.validRewardResourceListByPackId(packId);
        List<RewardPackResourceVO> resourceListVO = getResourceListVO(resourceList);
        result.setResourceVOList(resourceListVO);
        return result;
    }


}




