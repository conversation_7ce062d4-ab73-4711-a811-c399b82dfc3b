package com.simi.admin.service.currency;

import com.simi.admin.req.currency.CoinOperationParam;

/**
 * 赠送/撤回金币的策略接口
 *
 */
public interface CoinOperationStrategy {
    /**
     * 业务方法
     *
     * @param coinOperationParam
     */
    void handle(CoinOperationParam coinOperationParam, CoinOperationObserver coinOperationObserver) throws Exception;

    /**
     * 是否相同的操作类型
     *
     * @param type 操作类型
     * @return boolean
     */
    boolean isEqualWithType(int type);
}
