package com.simi.admin.service.currency.impl;

import com.simi.admin.req.currency.CoinOperationParam;
import com.simi.admin.entity.currency.CurrencyOperationRecord;
import com.simi.admin.service.currency.BaseCoinOperationStrategy;
import com.simi.admin.service.currency.CoinOperationObserver;
import com.simi.admin.service.currency.CurrencyOperationRecordService;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.BillEnum;
import com.simi.constant.CurrencyOperationEnum;
import com.simi.entity.user.User;
import com.simi.service.purse.PurseManageService;
import com.simi.service.user.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

@Slf4j
@Service
@RequiredArgsConstructor
public class RewardDiamondStrategy extends BaseCoinOperationStrategy {

    private final PurseManageService purseManageService;
    private final UserService userService;
    private final CurrencyOperationRecordService currencyOperationRecordService;
    private final PlatformTransactionManager platformTransactionManager;

    @Override
    public void handle(CoinOperationParam coinOperationParam, CoinOperationObserver coinOperationObserver) {
        User user = coinOperationParam.getUser();
        purseManageService.getPurse(user.getUid());
        TransactionStatus status = platformTransactionManager.getTransaction(new DefaultTransactionDefinition());
        try {
            Long amount = coinOperationParam.getAmount().longValue();
            BillEnum billEnum = coinOperationParam.getBillEnum();
            CurrencyOperationRecord record = currencyOperationRecordService.createRecord(user.getUid(), coinOperationParam.getAmount(), coinOperationParam.getOperation(), billEnum, coinOperationParam.getRemark(), coinOperationParam.getIntroduce(), coinOperationParam.getAdminId());
            purseManageService.addDiamond(user.getUid(), amount, billEnum, record.getId().toString(), coinOperationParam.getRemark(), coinOperationParam.getIntroduce(),0L, PurseRoleTypeEnum.USER.getType());
            coinOperationObserver.operationSuccessful(user);
            log.info("add diamond success, user_no: {}, type: {}, num: {}, record_id: {}", user.getUserNo(), billEnum, amount, record.getId());
            platformTransactionManager.commit(status);
        } catch (Exception ex) {
            platformTransactionManager.rollback(status);
            log.error("add diamond failure:{}", ExceptionUtil.formatEx(ex));
            throw ex;
        }
    }

    @Override
    public boolean isEqualWithType(int type) {
        return CurrencyOperationEnum.CMS_REWARD_DIAMOND.getType() == type;
    }

    @Override
    protected UserService userService() {
        return this.userService;
    }

}
