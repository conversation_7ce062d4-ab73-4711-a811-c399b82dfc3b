package com.simi.admin.service.system;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.nacos.NacosConfigManager;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.simi.admin.entity.enums.SystemConfigTypeEnum;
import com.simi.admin.req.system.SystemConfigQueryReq;
import com.simi.admin.req.system.SystemConfigReq;
import com.simi.common.config.SystemNacosConfig;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.LuckyGiftDTO;
import com.simi.common.dto.SystemConfigDTO;
import com.simi.common.exception.AdminException;
import com.simi.common.service.LuckyGiftService;
import com.simi.common.service.SystemConfigService;
import com.simi.service.cache.SystemConfigCache;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.core.task.TaskExecutor;
import org.springframework.stereotype.Service;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 依赖于Nacos配置中心对sys_conf进行管理，更新操作都上同步锁
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SystemConfigAdminService {

    private final SystemConfigService systemConfigService;
    @Resource
    private NacosConfigManager nacosConfigManager;

    private final TaskExecutor taskExecutor;

    private final LuckyGiftService luckyGiftService;

    private final SystemConfigCache systemConfigCache;

    private static final String SYS_CONF_DATA_ID = "system-config.yml";
    private static final String GROUP = "DEFAULT_GROUP";

    /**
     * 保存或更新配置(上锁)
     */
    public synchronized void saveOrUpdate(SystemConfigReq param, String adminName) {
        if (StrUtil.isBlank(param.getKey()) || StrUtil.isBlank(param.getValue()) || StrUtil.isBlank(param.getRemark())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        List<SystemConfigDTO> sysConfs = systemConfigService.getRealTimeSysConfs();
        Optional<SystemConfigDTO> foundConfOptional = sysConfs.stream().filter(conf -> conf.getKey().equals(param.getKey())).findAny();
        if (foundConfOptional.isPresent()) {
            SystemConfigDTO foundConf = foundConfOptional.get();
            foundConf.setRemark(param.getRemark());
            foundConf.setValue(param.getValue());
            foundConf.setUpdateTime(System.currentTimeMillis());
            foundConf.setLastEditor(adminName);
        } else {
            SystemConfigDTO systemConfigDTO = new SystemConfigDTO();
            systemConfigDTO.setKey(param.getKey());
            systemConfigDTO.setValue(param.getValue());
            systemConfigDTO.setRemark(param.getRemark());
            systemConfigDTO.setCreateTime(System.currentTimeMillis());
            systemConfigDTO.setUpdateTime(System.currentTimeMillis());
            systemConfigDTO.setLastEditor(adminName);
            sysConfs.add(systemConfigDTO);
        }
        publishConfig2Nacos(sysConfs);
    }

    /**
     * 根据配置id删除配置
     */
    public synchronized void removeByKey(SystemConfigReq param) {
        if (StrUtil.isBlank(param.getKey())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }

        List<SystemConfigDTO> sysConfs = systemConfigService.getRealTimeSysConfs();
        List<SystemConfigDTO> filteredConfs = sysConfs.stream().filter(conf -> !param.getKey().equalsIgnoreCase(conf.getKey())).collect(Collectors.toList());

        publishConfig2Nacos(filteredConfs);
    }


    public List<SystemConfigDTO> list(SystemConfigQueryReq param) {
        List<SystemConfigDTO> sysConfs = systemConfigService.getRealTimeSysConfs();
        if (StrUtil.isNotBlank(param.getKeyword())) {
            if (StrUtil.equals(param.getType(), SystemConfigTypeEnum.KEY.getType())) {
                sysConfs = sysConfs.stream().filter(conf -> conf.getKey().contains(param.getKeyword())).collect(Collectors.toList());
            } else if (StrUtil.equals(param.getType(), SystemConfigTypeEnum.VALUE.getType())) {
                sysConfs = sysConfs.stream().filter(conf -> conf.getValue().contains(param.getKeyword())).collect(Collectors.toList());
            } else if (StrUtil.equals(param.getType(), SystemConfigTypeEnum.REMAEK.getType())) {
                sysConfs = sysConfs.stream().filter(conf -> conf.getRemark().contains(param.getKeyword())).collect(Collectors.toList());
            } else {
                throw new AdminException(CodeEnum.PARAM_ILLEGAL);
            }
        }
        return sysConfs;
    }


    /**
     * 推送配置到nacos
     */
    public void publishConfig2Nacos(List<SystemConfigDTO> list) {
        List<Map<String, String>> maps = list.stream().map(dto -> {
            try {
                return toMap(dto, false);
            } catch (IOException e) {
                log.error("convert to map from system config dto error:", e);
                return null;
            }
        }).filter(Objects::nonNull).toList();
        // 构造约定的yaml格式，推送到nacos。保证SysConfNacosConfig.data能读到数据
        Yaml yaml = new Yaml();
        String content = yaml.dump(ImmutableMap.of(SystemNacosConfig.PREFIX, ImmutableMap.of("data", maps)));
        Map<String,String> map = new HashMap<>();
        for (SystemConfigDTO systemConfigDTO : list) {
            if (SystemConfigConstant.LUCKY_GIFT_CONFIG.equals(systemConfigDTO.getKey())) {
                String value = systemConfigDTO.getValue();
                List<LuckyGiftDTO> giftDTOList = JSONUtil.toList(value, LuckyGiftDTO.class);
                if (CollectionUtils.isNotEmpty(giftDTOList)) {
                    taskExecutor.execute(() -> {
                    luckyGiftService.setGiftPool(giftDTOList);
                    });
                }
            }
            if (SystemConfigConstant.LUCKY_GIFT_CONFIG_WHITE.equals(systemConfigDTO.getKey())) {
                String value = systemConfigDTO.getValue();
                List<LuckyGiftDTO> giftDTOList = JSONUtil.toList(value, LuckyGiftDTO.class);
                if (CollectionUtils.isNotEmpty(giftDTOList)) {
                    taskExecutor.execute(() -> {
                        luckyGiftService.setGiftPoolWhite(giftDTOList);
                    });
                }
            }
            map.put(systemConfigDTO.getKey(),systemConfigDTO.getValue());
        }
        systemConfigCache.setSystemConfig(map);
        try {
            nacosConfigManager.getConfigService().publishConfig(SYS_CONF_DATA_ID, GROUP, content);
        } catch (Exception e) {
            log.error("publish config 2 nacos error:", e);
            throw new AdminException(CodeEnum.SERVER_BUSY);
        }
    }

    static final ObjectMapper mapper = new ObjectMapper();

    public static Map<String, String> toMap(SystemConfigDTO systemConfigDTO, boolean skipNull) throws IOException {
        Map<String, Object> map = mapper.readValue(mapper.writeValueAsString(systemConfigDTO), Map.class);
        Map<String, String> linkedHashMap = Maps.newLinkedHashMap();
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            if (skipNull && Objects.isNull(entry.getValue())) {
                continue;
            }

            if (entry.getValue() instanceof Collection) {
                linkedHashMap.put(entry.getKey(), mapper.writeValueAsString(entry.getValue()));
            } else {
                linkedHashMap.put(entry.getKey(), Objects.isNull(entry.getValue()) ? "" : String.valueOf(entry.getValue()));
            }

        }
        return linkedHashMap;
    }

}
