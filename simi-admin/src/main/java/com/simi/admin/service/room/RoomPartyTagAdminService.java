package com.simi.admin.service.room;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.req.room.RoomPartyTagDelReq;
import com.simi.admin.req.room.RoomPartyTagSaveReq;
import com.simi.admin.vo.room.RoomPartyTagVO;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.exception.AdminException;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.req.BasePageReq;
import com.simi.constant.RoomPartyRedisKey;
import com.simi.entity.room.RoomPartyTag;
import com.simi.service.room.party.RoomPartyTagService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/29 16:03
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class RoomPartyTagAdminService {

    private final RoomPartyTagService roomPartyTagService;
    private final RedissonManager redissonManager;

    public void saveOrUpdate(RoomPartyTagSaveReq req) {
        RoomPartyTag roomPartyTag = RoomPartyTag.builder()
                .arName(req.getArName())
                .enName(req.getEnName())
                .tagPic(req.getTagPic())
                .seqNo(req.getSeqNo())
                .updateTime(new Date()).build();
        if(Objects.isNull(req.getId())){
            roomPartyTag.setCreateTime(new Date());
            roomPartyTagService.save(roomPartyTag);
        }else {
            roomPartyTag.setId(req.getId());
            roomPartyTagService.updateById(roomPartyTag);
        }
        redissonManager.hSet(RoomPartyRedisKey.room_party_tag.getKey(), roomPartyTag.getId().toString(), JSONUtil.toJsonStr(roomPartyTag));
    }


    public ListWithTotal<RoomPartyTagVO> listRoomPartyTag(BasePageReq req) {
        PageHelper.startPage(req.getPageNum(), req.getPageSize(), "seq_no DESC");
        List<RoomPartyTag> roomPartyTags = roomPartyTagService.lambdaQuery().list();
        if (CollUtil.isEmpty(roomPartyTags)) {
            return ListWithTotal.empty();
        }
        PageInfo<RoomPartyTag> pageInfo = new PageInfo<>(roomPartyTags);
        List<RoomPartyTagVO> roomPartyTagVOS = BeanUtil.copyToList(roomPartyTags, RoomPartyTagVO.class);
        return ListWithTotal.<RoomPartyTagVO>builder()
                .list(roomPartyTagVOS)
                .total(pageInfo.getTotal())
                .build();
    }

    public void removeTag(RoomPartyTagDelReq req) {
        boolean remove = roomPartyTagService.remove(new LambdaQueryWrapper<RoomPartyTag>().eq(RoomPartyTag::getId, req.getId()));
        if (remove) {
            redissonManager.hDel(RoomPartyRedisKey.room_party_tag.getKey(), req.getId().toString());
        } else {
            throw new AdminException(400, "删除失败");
        }
    }

}
