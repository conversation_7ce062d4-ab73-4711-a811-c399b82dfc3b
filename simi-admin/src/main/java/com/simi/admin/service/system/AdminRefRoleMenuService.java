package com.simi.admin.service.system;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.simi.admin.entity.AdminRefRoleMenu;
import com.simi.admin.mapper.system.AdminRefRoleMenuMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 管理员角色-菜单
 *
 * <AUTHOR>
 * @date 2023/11/13 21:23
 */
@Slf4j
@Service
public class AdminRefRoleMenuService extends ServiceImpl<AdminRefRoleMenuMapper, AdminRefRoleMenu> {

    /**
     * 根据角色删除
     * @param roleId
     * @return
     */
    public boolean removeByRoleId(final int roleId){
        return remove(new LambdaQueryWrapper<AdminRefRoleMenu>().eq(AdminRefRoleMenu::getRoleId, roleId));
    }

    public List<Integer> listMenusByRoles(List<Integer> roles){
        return lambdaQuery().in(AdminRefRoleMenu::getRoleId, roles).list().stream().map(AdminRefRoleMenu::getMenuId).distinct().collect(Collectors.toList());
    }
}
