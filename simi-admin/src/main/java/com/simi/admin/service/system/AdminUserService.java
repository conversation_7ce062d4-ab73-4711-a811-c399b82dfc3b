package com.simi.admin.service.system;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.vo.user.AdminUserVO;
import com.simi.common.dto.ListWithTotal;
import com.simi.admin.entity.AdminRefUserRole;
import com.simi.admin.entity.AdminUser;
import com.simi.admin.mapper.system.AdminUserMapper;
import com.simi.common.constant.CodeEnum;
import com.simi.common.exception.AdminException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.stream.Collectors;


@Slf4j
@Validated
@Service
@RequiredArgsConstructor
public class AdminUserService extends ServiceImpl<AdminUserMapper, AdminUser> {

    private final AdminRefUserRoleService adminRefUserRoleService;
    private final AdminRoleService adminRoleService;

    /**
     * 管理员默认密码
     */
    private final static String DEFAULT_PWD = "qwe123456";


    /**
     * 更新最后登录时间
     *
     * @param adminId
     * @return
     */
    public boolean updateLastLogin(int adminId) {
        return lambdaUpdate().set(AdminUser::getLastLogin, new Date()).eq(AdminUser::getId, adminId).update();
    }

    /**
     * 根据账号获取管理员信息
     *
     * @param username
     * @return
     */
    public AdminUser getByUsername(String username) {
        return lambdaQuery().eq(AdminUser::getUsername, username).one();
    }

    public String getNameByUserId(Integer userId){
        AdminUser user = lambdaQuery().eq(AdminUser::getId, userId).one();
        String name = Objects.isNull(user) ? "" : user.getUsername();
        return name;
    }
    /**
     * 查询
     *
     * @return
     */
    public ListWithTotal<AdminUserVO> search(Integer page, Integer size, String nick) {
        if (page < 1 || size < 1) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        PageHelper.startPage(page, size, "create_time desc");
        List<AdminUser> list = lambdaQuery().like(StrUtil.isNotBlank(nick), AdminUser::getUsername, StrUtil.format("%{}%", nick))
                .eq(AdminUser::getStatus, true)
                .list();
        if (CollUtil.isEmpty(list)) {
            return ListWithTotal.empty();
        }
        PageInfo<AdminUser> pageInfo = new PageInfo<>(list);
        List<Integer> adminIds = list.stream().map(AdminUser::getId).collect(Collectors.toList());
        Map<Integer, List<Integer>> adminRoleMap = adminRefUserRoleService.adminRoleMap(adminIds);
        List<Integer> roleIds = adminRoleMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Integer, String> roleMap = adminRoleService.roleMap(roleIds);

        List<AdminUserVO> adminUserVOS = BeanUtil.copyToList(list, AdminUserVO.class);
        adminUserVOS.forEach(e -> {
            List<Integer> rIds = adminRoleMap.getOrDefault(e.getId(), Collections.emptyList());
            List<String> rNames = rIds.stream().map(roleMap::get).filter(Objects::nonNull).toList();
            e.setRoleIds(rIds);
            e.setRoles(rNames);
        });
        return ListWithTotal.<AdminUserVO>builder().list(adminUserVOS).total(pageInfo.getTotal()).build();
    }

    /**
     * 新建或编辑管理员
     */
    @Transactional(rollbackFor = Exception.class)
    public void addOrUpdate(Integer id, String username, String nick, List<Integer> roleIds) {
        AdminUser adminUser = AdminUser.builder()
                .username(username)
                .nick(nick)
                .build();
        if (Objects.nonNull(id) && id > 0) {
            adminUser.setId(id);
            adminUser.setUpdateTime(new Date());
            updateById(adminUser);
            adminRefUserRoleService.removeByAdminId(id);
        } else {
            if(existUsername(username)){
                throw new AdminException(CodeEnum.USERNAME_EXIST);
            }
            adminUser.setPassword(SecureUtil.md5(DEFAULT_PWD));
            adminUser.setStatus(true);
            adminUser.setCreateTime(new Date());
            save(adminUser);
        }
        if (CollUtil.isNotEmpty(roleIds)) {
            List<AdminRefUserRole> list = roleIds.stream().map(r -> AdminRefUserRole.builder().adminId(adminUser.getId()).roleId(r).build()).collect(Collectors.toList());
            adminRefUserRoleService.saveBatch(list);
        }
    }

    /**
     * 删除管理员
     */
    @Transactional(rollbackFor = Exception.class)
    public void removeByAdminId(Integer id) {
        if (id < 1) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        removeById(id);
        adminRefUserRoleService.removeByAdminId(id);
    }

    /**
     * 重置密码
     */
    public void resetPwd(Integer id) {
        if (id < 1) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        lambdaUpdate().set(AdminUser::getPassword, SecureUtil.md5(DEFAULT_PWD)).eq(AdminUser::getId, id).update();
    }

    private boolean existUsername(String username){
        return exists(new LambdaQueryWrapper<AdminUser>().eq(AdminUser::getUsername, username));
    }

    /**
     * 管理员-账号
     * @param adminIds
     * @return
     */
    public Map<Integer, String> adminNameMap(final List<Integer> adminIds){
        if(CollUtil.isEmpty(adminIds)){
            return Collections.emptyMap();
        }
        return lambdaQuery().in(AdminUser::getId, adminIds).list().stream().collect(Collectors.toMap(AdminUser::getId, AdminUser::getUsername));
    }

    /**
     * 角色下管理员
     * @return
     */
    public List<String> roleUser(Integer roleId){
        if(roleId < 1){
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        List<Integer> adminIds = adminRefUserRoleService.lambdaQuery().eq(AdminRefUserRole::getRoleId, roleId).list().stream().map(AdminRefUserRole::getAdminId).collect(Collectors.toList());
        if(CollUtil.isEmpty(adminIds)){
            return CollUtil.newArrayList();
        }
        return lambdaQuery().in(AdminUser::getId, adminIds).eq(AdminUser::getStatus, true).list().stream().map(AdminUser::getNick).collect(Collectors.toList());
    }

}
