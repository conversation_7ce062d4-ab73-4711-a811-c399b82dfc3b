package com.simi.admin.service.recharge;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.req.recharge.CreateOrUpdatePackageReq;
import com.simi.admin.req.recharge.QueryPackageReq;
import com.simi.admin.vo.recharge.RechargePackageAdminVO;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.revenue.RechargePackageDTO;
import com.simi.common.exception.AdminException;
import com.simi.entity.recharge.RechargePackage;
import com.simi.service.purse.RechargePackageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
@RequiredArgsConstructor
public class RechargePackageAdminService {

    private final RechargePackageService rechargePackageService;

    private final static BigDecimal RATE = new BigDecimal(100);

    /**
     * 查询
     *
     * @param param
     * @return
     */
    public ListWithTotal<RechargePackageAdminVO> search(QueryPackageReq param) {
        PageHelper.startPage(param.getPageNum(), param.getPageSize());
        List<RechargePackage> list = rechargePackageService.lambdaQuery()
                .eq(Objects.nonNull(param.getMerchant()) && StrUtil.isNotBlank(param.getMerchant()),
                        RechargePackage::getMerchant, param.getMerchant())
                .eq(Objects.nonNull(param.getChannel()) && StrUtil.isNotBlank(param.getChannel()),
                        RechargePackage::getChannel, param.getChannel())
                .eq(Objects.nonNull(param.getPlatformCurrencyType()) && !Objects.equals(param.getPlatformCurrencyType(), 0),
                        RechargePackage::getPlatformCurrencyType, param.getPlatformCurrencyType())
                .list();
        if (CollUtil.isEmpty(list)) {
            return ListWithTotal.empty();
        }
        PageInfo<RechargePackage> pageInfo = new PageInfo<>(list);
        List<RechargePackageAdminVO> result = BeanUtil.copyToList(pageInfo.getList(), RechargePackageAdminVO.class);
        result.forEach(e -> {
            if (Objects.nonNull(e.getDollarAmount())) {
                e.setDollarAmount(e.getDollarAmount().divide(RATE, 2, RoundingMode.DOWN));
            }
            if (Objects.nonNull(e.getCurrencyAmount())) {
                e.setCurrencyAmount(e.getCurrencyAmount().divide(RATE, 2, RoundingMode.DOWN));
            }
        });
        return ListWithTotal.<RechargePackageAdminVO>builder().total(pageInfo.getTotal()).list(result).build();
    }


    /**
     * 新建充值套餐
     *
     * @param param
     */
    public void create(CreateOrUpdatePackageReq param) {
        String packageId =
                StrUtil.isBlank(param.getId()) ? UUID.fastUUID().toString(true) : param.getId();
        if (StrUtil.isBlank(param.getChannel())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        if (Objects.isNull(param.getCurrencyType())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        RechargePackage exPackage = rechargePackageService.getById(packageId);
        if (Objects.nonNull(exPackage)) {
            throw new AdminException(CodeEnum.RECHARGE_PACKAGE_ID_EXIST);
        }
        RechargePackage entity = req2Entity(packageId, param);
        rechargePackageService.save(entity);
        // 写入redis
        if (entity.getStatus()) {
            RechargePackageDTO dto = RechargePackageDTO.builder()
                    .id(entity.getId())
                    .channel(entity.getChannel())
                    .currency(entity.getCurrency())
                    .currencyAmount(param.getCurrencyAmount())
                    .platformCurrencyType(param.getPlatformCurrencyType())
                    .coinAmount(entity.getCoinAmount())
                    .sortNo(entity.getSortNo())
                    .merchant(entity.getMerchant())
                    .dollarAmount(param.getDollarAmount())
                    .build();
            rechargePackageService.updateRedis(dto);
        }
    }

    /**
     * 更新充值套餐
     *
     * @param param
     */
    public void modify(CreateOrUpdatePackageReq param) {
        if (StrUtil.isBlank(param.getChannel())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        if (Objects.isNull(param.getCurrencyType())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        RechargePackage entity = req2Entity(param.getId(), param);
        rechargePackageService.updateById(entity);
        // 写入redis
        if (entity.getStatus()) {
            RechargePackageDTO dto = RechargePackageDTO.builder()
                    .id(entity.getId())
                    .channel(entity.getChannel())
                    .currency(entity.getCurrency())
                    .currencyAmount(param.getCurrencyAmount())
                    .platformCurrencyType(param.getPlatformCurrencyType())
                    .coinAmount(entity.getCoinAmount())
                    .sortNo(entity.getSortNo())
                    .merchant(entity.getMerchant())
                    .dollarAmount(param.getDollarAmount())
                    .build();
            rechargePackageService.updateRedis(dto);
        } else {
            rechargePackageService.removeFromRedis(entity.getId());
        }
    }

    /**
     * pb转实体
     *
     * @param id
     * @param param
     * @return
     */
    private RechargePackage req2Entity(String id, CreateOrUpdatePackageReq param) {
        return RechargePackage.builder()
                .id(id)
                .name(param.getPackageName())
                .status(param.getStatus())
                .channel(param.getChannel())
                .currency(param.getCurrencyType())
                .platformCurrencyType(param.getPlatformCurrencyType())
                .currencyAmount(param.getCurrencyAmount().multiply(RATE).longValue())
                .coinAmount(param.getCoinAmount())
                .sortNo(param.getSortNo())
                .remark(param.getRemark())
                .merchant(param.getMerchant())
                .dollarAmount(param.getDollarAmount().multiply(RATE).longValue())
                .useType(param.getUseType())
                .build();
    }

    public void remove(String id) {
        if (StrUtil.isBlank(id)) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        rechargePackageService.removeById(id);
        // 更新redis
        rechargePackageService.removeFromRedis(id);
    }

    public List<String> channelList(String channelName) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("apple", JSONUtil.toJsonStr(CollUtil.newArrayList("apple")));
        jsonObject.set("google", JSONUtil.toJsonStr(CollUtil.newArrayList("google")));
        jsonObject.set("payermax", JSONUtil.toJsonStr(CollUtil.newArrayList("payermax")));
        jsonObject.set("Pandapay", JSONUtil.toJsonStr(CollUtil.newArrayList("Pandapay")));
        jsonObject.set("huawei", JSONUtil.toJsonStr(CollUtil.newArrayList("huawei")));

        if (StrUtil.isBlank(channelName)) {
            return jsonObject.keySet().stream().toList();
        } else {
            Object object = jsonObject.get(channelName);
            return JSONUtil.toList(object.toString(), String.class);
        }
    }

}
