package com.simi.admin.service.download;

import cn.hutool.core.bean.BeanUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.constant.DownloadTypeEnum;
import com.simi.admin.dto.download.DownloadTaskDTO;
import com.simi.admin.entity.UserDownloadTaskRecord;
import com.simi.admin.req.revenue.PayReq;
import com.simi.admin.service.AdminThreadPoolService;
import com.simi.admin.service.OSSFileService;
import com.simi.admin.service.revenue.BillAdminService;
import com.simi.common.DownloadTaskReq;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ThreadPoolTaskExecutorMdcUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class AdminDownloadService {
    private final UserDownloadTaskRecordService userDownloadTaskRecordService;
    private final OSSFileService ossFileService;
    private final BillAdminService billAdminService;
    private final AdminThreadPoolService adminThreadPoolService;

    public ListWithTotal<DownloadTaskDTO> getTaskRecords(DownloadTaskReq req, int uid) {
        PageHelper.startPage(req.getPageNum(),req.getPageSize());
        List<UserDownloadTaskRecord> records = userDownloadTaskRecordService.selectByUid(uid,req.getType());
        PageInfo<UserDownloadTaskRecord> pageInfo = new PageInfo<>(records);
        List<DownloadTaskDTO> list = records.stream().map(e -> {
            DownloadTaskDTO downloadTaskDTO = BeanUtil.copyProperties(e, DownloadTaskDTO.class);
            return downloadTaskDTO;
        }).collect(Collectors.toList());
        return ListWithTotal.<DownloadTaskDTO>builder().total(pageInfo.getTotal()).list(list).build();
    }

    public void downloadPayList(PayReq payReq, int adminId) {
        String fileName = createFileName();
        payReq.setPageNum(1);
        payReq.setPageSize(50);
        //创建导出记录
        long recordId = userDownloadTaskRecordService.createRecord(adminId, fileName, DownloadTypeEnum.PAY_USER.getType());
        ThreadPoolTaskExecutor downloadThreadPoolTaskExecutor = adminThreadPoolService.getDownloadThreadPoolTaskExecutor();
        ThreadPoolTaskExecutorMdcUtils.submitCallableTask(downloadThreadPoolTaskExecutor,()->{
            //return doDownloadPayList(payReq,recordId,fileName);
            return null;
        });
    }

    private String createFileName() {
        String downloadTime = DateTimeUtil.formatTimeMillis(System.currentTimeMillis(), DateTimeUtil.DATE_TIME_PATTERN, DateTimeUtil.GMT_3);
        String fileName = "付费用户-" + downloadTime + ".xlsx";
        return fileName;
    }

//    private Boolean doDownloadPayList(PayReq payReq, long recordId, String fileName) {
//        List<PayItem> payList = new ArrayList<>();
//        List<PayUserModel> totalResult = new ArrayList<>();
//        Long uidGt = null;
//        try {
//            do {
//                userDownloadTaskRecordService.updateStatusById(recordId, DownloadStatusEnum.RUNNING.getType());
//
//                ListWithTotal<PayItem> payTotal = billAdminService.getPayList(payReq, false, uidGt,"uid ASC");
//                payList = payTotal.getList();
//                if (CollectionUtils.isEmpty(payList)) {
//                    break;
//                }
//                List<PayUserModel> payUserModels = convertPayUserModel(payList);
//                totalResult.addAll(payUserModels);
//
//                uidGt = payList.get(payList.size() - 1).getUid();
//            }while (CollectionUtils.isNotEmpty(payList));
//
//            ByteArrayOutputStream out = new ByteArrayOutputStream();
//            EasyExcel.write(out, PayUserModel.class).sheet("付费用户").doWrite(totalResult);
//
//            String fileUrl = ossFileService.uploadFile(new ByteArrayInputStream(out.toByteArray()), fileName);
//            userDownloadTaskRecordService.updateStatusById(recordId, DownloadStatusEnum.SUCCESSFUL.getType(),fileUrl);
//            return true;
//        }catch (Exception e){
//            log.error("doDownloadPayList error:{}", ExceptionUtil.formatEx(e));
//            userDownloadTaskRecordService.updateStatusById(recordId, DownloadStatusEnum.ERROR.getType(),"");
//            return false;
//        }
//    }
//
//    private List<PayUserModel> convertPayUserModel(List<PayItem> payList) {
//        List<PayUserModel> payUserModels = new ArrayList<>();
//        payList.forEach(payItem -> {
//            PayUserModel payUserModel = BeanUtil.copyProperties(payItem, PayUserModel.class);
//            String lastPayTime = DateTimeUtil.formatTimeMillis(payItem.getLastPayDate().getTime(),
//                    DateTimeUtil.DATE_TIME_PATTERN, DateTimeUtil.GMT_3);
//            payUserModel.setLastPayDateStr(lastPayTime);
//            payUserModels.add(payUserModel);
//        });
//        return payUserModels;
//    }
}
