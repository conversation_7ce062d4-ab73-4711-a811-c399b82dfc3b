package com.simi.admin.service.report;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.req.report.ModifyReportAdminReq;
import com.simi.admin.req.report.ReportAdminReq;
import com.simi.admin.vo.report.ReportRecordVO;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.AdminException;
import com.simi.entity.ReportRecord;
import com.simi.service.infrastructure.ReportRecordService;
import com.simi.service.user.UserServerService;
import com.simi.common.util.OssUrlUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 举报
 *
 * <AUTHOR>
 * @date 2024/2/1 14:08
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ReportAdminService {

    private final ReportRecordService reportRecordService;
    private final UserServerService userServerService;

    /**
     * 检索举报列表
     *
     * @return
     */
    public ListWithTotal<ReportRecordVO> search(ReportAdminReq req) {
        if (Objects.isNull(req.getPageNum()) || Objects.isNull(req.getPageSize())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        Long uid = null;
        if (Objects.nonNull(req.getUserNo()) && !Objects.equals(req.getUserNo(), 0)) {
            uid = userServerService.getUidByUserNo(req.getUserNo());
            if (Objects.isNull(uid)) {
                return ListWithTotal.empty();
            }
        }
        PageHelper.startPage(req.getPageNum(), req.getPageSize());
        List<ReportRecord> list = reportRecordService.lambdaQuery()
                .eq(Objects.nonNull(uid), ReportRecord::getUid, uid)
                .eq(Objects.nonNull(req.getReportType()), ReportRecord::getReportType, req.getReportType())
                .eq(Objects.nonNull(req.getStatusValue()), ReportRecord::getStatus, req.getStatusValue())
                .orderByDesc(ReportRecord::getId)
                .list();
        if (CollectionUtils.isEmpty(list)) {
            return ListWithTotal.empty();
        }
        List<Long> uids = list.stream().map(ReportRecord::getUid).collect(Collectors.toList());
        Map<Long, UserBaseInfoDTO> usersMap = userServerService.batchUserSummary(uids);
        PageInfo<ReportRecord> pageInfo = new PageInfo<>(list);

        List<ReportRecordVO> reportRecordVOS = BeanUtil.copyToList(list, ReportRecordVO.class);

        reportRecordVOS.forEach(e -> {
            UserBaseInfoDTO user = usersMap.get(e.getUid());
            if (Objects.nonNull(user)) {
                e.setUserNo(user.getUserNo());
            }
            if (CollUtil.isNotEmpty(e.getPhotos())) {
                List<String> ossPhotos = e.getPhotos().stream().map(OssUrlUtil::jointUrl).toList();
                e.setPhotos(ossPhotos);
            }
        });
        return ListWithTotal.<ReportRecordVO>builder().list(reportRecordVOS).total(pageInfo.getTotal()).build();
    }


    public void modify(ModifyReportAdminReq req) {
        reportRecordService.lambdaUpdate()
                .set(ReportRecord::getStatus, req.getStatusValue())
                .set(ReportRecord::getRemark, req.getRemark())
                .eq(ReportRecord::getId, req.getId())
                .update();
    }
}
