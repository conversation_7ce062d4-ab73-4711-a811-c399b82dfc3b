package com.simi.admin.service.resource;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.admin.req.resource.AttestationTagSaveOrUpdateReq;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.TagTypeEnum;
import com.simi.common.constant.resource.ResourceRedisKey;
import com.simi.common.constant.user.TagStatusEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.tag.AttestationTagInfoDto;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.AdminException;
import com.simi.common.util.RedissonManager;
import com.simi.entity.tag.AttestationTagInfo;
import com.simi.service.tag.AttestationTagInfoService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 认证标签管理service
 *
 * <AUTHOR>
 * @date 2024/05/27 17:21
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class AttestationTagInfoAdminService {


    private final AttestationTagInfoService attestationTagInfoService;
    private final RedissonManager redissonManager;
    private final UserServerService userServerService;

    public void saveOrUpdate(AttestationTagSaveOrUpdateReq req, int adminId) {
        if (Objects.isNull(req.getUserNo())) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        Long uid = userServerService.getUidByUserNo(req.getUserNo());
        if (Objects.isNull(uid)) {
            throw new AdminException(CodeEnum.PARAM_ILLEGAL);
        }
        AttestationTagInfo tagInfo = BeanUtil.toBean(req, AttestationTagInfo.class);
        tagInfo.setAdminId(adminId);
        tagInfo.setUpdateTime(new Date());
        tagInfo.setUid(uid);

        if (Objects.isNull(req.getId())) {
            tagInfo.setCreateTime(new Date());
        }
        if (Objects.isNull(req.getType())) {
            tagInfo.setType(TagTypeEnum.ORDINARY.getType());
        }
        if (Objects.isNull(tagInfo.getStartTime())){
            tagInfo.setStartTime(0L);
        }
        if (Objects.isNull(tagInfo.getEndTime())){
            tagInfo.setEndTime(0L);
        }

        boolean saveOrUpdate = attestationTagInfoService.saveOrUpdate(tagInfo);
        if (saveOrUpdate) {
            redissonManager.hSet(ResourceRedisKey.user_attestation_tag.getKey(String.valueOf(uid)), String.valueOf(tagInfo.getId()),
                    JSONUtil.toJsonStr(tagInfo));
        }
    }

    public void removeById(Long id) {
        AttestationTagInfo tagInfo = attestationTagInfoService.getById(id);
        if (Objects.isNull(tagInfo)) {
            return;
        }
        boolean removeById = attestationTagInfoService.removeById(id);
        if (removeById) {
            redissonManager.hDel(ResourceRedisKey.user_attestation_tag.getKey(String.valueOf(tagInfo.getUid())), String.valueOf(tagInfo.getId()));
        }
    }

    public ListWithTotal<AttestationTagInfoDto> queryList(Long uid, Long userNo, Integer pageNum, Integer pageSize, Integer tagStatus) {

        if (Objects.nonNull(userNo)) {
            Long uidByUserNo = userServerService.getUidByUserNo(userNo);
            if (Objects.isNull(uidByUserNo)) {
                return ListWithTotal.empty();
            }
            if (Objects.nonNull(uid) && !Objects.equals(uid, uidByUserNo)) {
                return ListWithTotal.empty();
            }
            uid = uidByUserNo;
        }
        long currentTimeMillis = System.currentTimeMillis();
        PageHelper.startPage(pageNum, pageSize);
        List<AttestationTagInfo> list = attestationTagInfoService.getEffectiveTag(uid, tagStatus,currentTimeMillis );

        if (CollUtil.isEmpty(list)) {
            ListWithTotal.empty();
        }
        PageInfo<AttestationTagInfo> pageInfo = new PageInfo<>(list);
        List<Long> uids = list.stream().map(AttestationTagInfo::getUid).toList();
        Map<Long, UserBaseInfoDTO> userMap = userServerService.batchUserSummary(uids);


        List<AttestationTagInfoDto> result = pageInfo.getList().stream().map(e -> {
            AttestationTagInfoDto bean = BeanUtil.toBean(e, AttestationTagInfoDto.class);
            UserBaseInfoDTO userBaseInfoDTO = userMap.get(bean.getUid());
            if (Objects.nonNull(userBaseInfoDTO)) {
                bean.setUserNo(userBaseInfoDTO.getUserNo());
            }
            bean.setStartTime(correctTime(e.getStartTime()));
            bean.setEndTime(correctTime(e.getEndTime()));
            Integer status = convertStatus(e.getStartTime(), e.getEndTime(), currentTimeMillis);
            bean.setTagStatus(status);
            return bean;
        }).toList();


        return ListWithTotal.<AttestationTagInfoDto>builder()
                .total(pageInfo.getTotal()).list(result).build();
    }

    private Integer convertStatus(Long startTime, Long endTime, long currentTimeMillis) {
        if (Objects.isNull(startTime) || Objects.isNull(endTime)){
            return TagStatusEnum.EFFECTIVE.getType();
        }

        if (startTime.equals(0L) || endTime.equals(0L)){
            return TagStatusEnum.EFFECTIVE.getType();
        }

        if (startTime < currentTimeMillis && currentTimeMillis < endTime){
            return TagStatusEnum.EFFECTIVE.getType();
        }

        if (startTime > currentTimeMillis){
            return TagStatusEnum.NOT_EFFECTIVE.getType();
        }

        if (endTime < currentTimeMillis){
            return TagStatusEnum.INVALID.getType();
        }
        return TagStatusEnum.EFFECTIVE.getType();
    }


    private Long correctTime(Long time) {
       if (Objects.isNull(time) || time <= 0){
           return null;
       }
       return time;
    }
}
