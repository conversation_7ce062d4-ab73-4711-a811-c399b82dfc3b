package com.simi.admin.req.user;

import com.simi.common.vo.req.privatePhote.PrivatePhotoItem;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

@Data
@Schema(description = "更新用户req")
public class UpdateUserReq {
    @Schema(description = "更新用户req")
    private Long uid;
    @Schema(description = "头像")
    private String avatar;
    @Schema(description = "用户前面")
    private String userDesc;
    @Schema(description = "备注")
    private String remark;
    @Schema(description = "国家代码")
    private String countryCode;
    @Schema(description = "相册")
    private List<PrivatePhotoItem> privatePhotoList;
}
