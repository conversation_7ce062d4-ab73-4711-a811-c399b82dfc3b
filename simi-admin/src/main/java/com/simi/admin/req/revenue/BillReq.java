package com.simi.admin.req.revenue;

import com.simi.common.vo.req.BasePageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/04/17 20:51
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "货币流水查询")
public class BillReq extends BasePageReq {

    @Schema(description = "用户id")
    private Long userNo;
    @Schema(description = "用户id")
    private Date beginTime;
    @Schema(description = "用户id")
    private Date endTime;
    @Schema(description = "货币类型: all-传空; 1-金币; 2-钻石")
    private Integer digitalCurrency;
    @Schema(description = "一级操作类型")
    private Integer billItem;
    @Schema(description = "二级操作类型")
    private Integer billDetail;
    @Schema(description = "对象用户id")
    private Long targetUserNo;
}
