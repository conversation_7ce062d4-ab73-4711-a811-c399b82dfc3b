package com.simi.admin.req.medal;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/19 11:24
 **/
@Data
@Schema(description = "勋章上下架")
public class MedalEnableReq {

    @Schema(description = "勋章id")
    private Integer id;

    /**
     * 勋章类型
     */
    @Min(0)
    @Max(1)
    @Schema(description = "状态: 1-上架; 2-下架")
    private Integer enableStatus;

}
