package com.simi.admin.req.medal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/08/19 11:24
 **/
@Data
@Schema(description = "勋章保存修改")
public class MedalSaveOrUpdateReq {

    @Schema(description = "id: 仅修改时候入参")
    private Integer id;

    /**
     * 勋章类型
     */
    @Schema(description = "勋章类型: 1-Achievement; 2-Gift; 3-Special")
    private Integer medalType;

    /**
     * 勋章名称（英）
     */
    @Schema(description = "勋章名称（英）")
    private String medalNameEn;

    /**
     * 勋章名称（阿）
     */
    @Schema(description = "勋章名称（阿）")
    private String medalNameAr;

    /**
     * 描述（英）
     */
    @Schema(description = "描述（英）")
    private String descEn;

    /**
     * 描述（阿）
     */
    @Schema(description = "描述（阿）")
    private String descAr;

    /**
     * 条件字段
     */
    @Schema(description = "条件字段")
    private String conditionField;

    /**
     * 初级图标
     */
    @Schema(description = "初级图标")
    private String beginnerIcon;

    /**
     * 初级动画
     */
    @Schema(description = "初级动画")
    private String beginnerAnimation;

    /**
     * 中级图标
     */
    @Schema(description = "中级图标")
    private String intermediateIcon;

    /**
     * 中级动画
     */
    @Schema(description = "中级动画")
    private String intermediateAnimation;

    /**
     * 高级图标
     */
    @Schema(description = "高级图标")
    private String advancedIcon;

    /**
     * 高级动画
     */
    @Schema(description = "高级动画")
    private String advancedAnimation;

    /**
     * 排序
     */
    @Schema(description = "排序")
    private Integer sortingOrder;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String notes;
}
