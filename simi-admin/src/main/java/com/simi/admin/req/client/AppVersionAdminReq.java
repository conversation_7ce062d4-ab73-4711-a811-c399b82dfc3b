package com.simi.admin.req.client;

import com.simi.common.vo.req.BasePageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 版本控制查询req
 *
 * <AUTHOR>
 * @date 2024/04/03 14:26
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "版本控制req")
public class AppVersionAdminReq extends BasePageReq {
    @Schema(description = "是否有效; 1-是; 0-否")
    private Integer status;
}
