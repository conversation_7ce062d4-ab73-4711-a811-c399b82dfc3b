package com.simi.admin.req.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/05/27 17:15
 **/
@Data
@Schema(description = "认证标签保存修改req")
public class AttestationTagSaveOrUpdateReq {

    @Schema(description = "id")
    private Long id;
    @Schema(description = "userNo")
    private Long userNo;
    //标签（英语）
    @Schema(description = "标签图片（英语）")
    private String tagEn;
    //标签（阿语）
    @Schema(description = "标签图片（阿语）")
    private String tagAr;
    @Schema(description = "类型")
    private Integer type;

    /**
     * 英文描述
     */
    @Schema(description = "英文描述")
    private String descEn;
    /**
     * 阿语描述
     */
    @Schema(description = "阿语描述")
    private String descAr;
    /**
     * 开始时间戳
     */
    @Schema(description = "开始时间戳,毫秒")
    private Long startTime;
    /**
     * 结束时间戳
     */
    @Schema(description = "结束时间戳，毫秒")
    private Long endTime;

    @Schema(description = "国家组id")
    private String groupIds;
}
