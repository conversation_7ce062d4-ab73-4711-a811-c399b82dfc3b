package com.simi.admin.req.system;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
@Schema(description = "保存 or 修改菜单")
public class SaveOrUpdateMenuReq {
    @Schema(description = "菜单 id")
    private Integer id;
    @Schema(description = "父菜单id, 0则为父菜单")
    private Integer parentId;
    @Schema(description = "名称")
    private String name;
    @Schema(description = "路径")
    private String path;
    @Schema(description = "iocn")
    private String icon;
    @Schema(description = "状态")
    private Boolean status;
    @Schema(description = "排序")
    private Integer order;
    @Schema(description = "描述")
    private String description;
}
