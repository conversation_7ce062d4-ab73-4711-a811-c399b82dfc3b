package com.simi.admin.req.dataReport;

import com.simi.common.vo.req.BasePageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PartyReportReq extends BasePageReq {
    @Schema(description = "日期，yyyyMMdd")
    private String reportDate;

    @Schema(description = "party id")
    private Long partyId;

    @Schema(description = "房间Id")
    private String roomNo;

    @Schema(description = "房主id")
    private Long uid;

    @Schema(description = "最长开播uid")
    private Long longestBroadcastUid;

}
