package com.simi.admin.req.resource;

import com.simi.common.vo.req.BaseCountryGroupSearchReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * banner列表req
 *
 * <AUTHOR>
 * @date 2024/04/02 10:46
 **/
@Data
@Schema(description = "banner列表req")
public class BannerAdminListReq extends BaseCountryGroupSearchReq {

    @Schema(description = "位置【1：首页; 2-房间; 3-启动闪屏; 4-钱包】")
    private Integer position;

    @Schema(description = "状态: 0-无效; 1-有效")
    private Integer status;
}
