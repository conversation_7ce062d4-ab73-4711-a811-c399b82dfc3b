package com.simi.admin.req.resource;

import com.simi.common.vo.req.BaseCommonCountryGroupBusinessReq;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/04/13 15:08
 **/
@Data
@Schema(description = "礼物新增")
public class GiftSaveReq {

    @Schema(description = "id")
    private Integer id;

    @Schema(description = "特效类型: 1-SVGA; 2-PAG; 3-VAP")
    private Integer animationType;

    @Schema(description = "特效地址")
    private String animationUrl;

    @Schema(description = "礼物名称")
    private String name;

    @Schema(description = "礼物图标")
    private String icon;

    @Schema(description = "礼物等级：1、普通；2、中级；3、高级；4、超级")
    private Integer levelType;

    @Schema(description = "价格")
    private Integer price;

    /**
     * 权重 越大越排前面
     */
    @Schema(description = "权重 越大越排前面")
    private Integer weight;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;

    /**
     * 角标
     */
    @Schema(description = "角标")
    private String cornerMark;

    /**
     * 是否预加载特效：1、否;2、是
     */
    @Schema(description = "是否预加载特效：1、否;2、是")
    private Integer preLoad;

    /**
     * 跳转链接
     */
    @Schema(description = "跳转链接")
    private String jumpLink;

    /**
     * 是英语banner图片
     */
    @Schema(description = "是英语banner图片")
    private String enBanner;

    /**
     * 阿拉伯语banner
     */
    @Schema(description = "阿拉伯语banner")
    private String arBanner;

    @Schema(description = "结束时间")
    private Long startTimeMillis;

    @Schema(description = "礼物新增")
    private Long endTimeMillis;

    @Schema(description = "是否连击 1、否;2、是")
    private Integer isCombo;

    @Schema(description = "方向 1、上 2、下 3、左 4、右")
    private Integer direction;

    @Schema(description = "礼物类型：1、普通礼物 2、魔法礼物")
    private Integer giftType;

    @Schema(description = "tanId")
    private Long tabId;

    @Schema(description = "贵族等级")
    private Integer aristocracyLevel;

    @Schema(description = "VIP 等级")
    private Integer vipLevel;

    @Schema(description = "投放目标")
    private BaseCommonCountryGroupBusinessReq countryGroupBusinessReq;

    @Schema(description = "新加国家组id (参数) id逗号隔开 比如101,102")
    private String groupIds;
}
