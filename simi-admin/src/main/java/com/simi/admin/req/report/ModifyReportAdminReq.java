package com.simi.admin.req.report;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 编辑举报req
 *
 * <AUTHOR>
 * @date 2024/04/01 14:19
 **/
@Data
@Schema(description = "编辑举报req")
public class ModifyReportAdminReq {
    @Schema(description = "状态")
    private Integer statusValue;
    @Schema(description = "举报id")
    private Long id;
    @Schema(description = "备注")
    private String remark;

}
