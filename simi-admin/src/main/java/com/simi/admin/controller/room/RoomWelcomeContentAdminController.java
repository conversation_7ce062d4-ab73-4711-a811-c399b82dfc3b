package com.simi.admin.controller.room;

import com.simi.admin.req.room.DeleteRoomWelcomeContentReq;
import com.simi.admin.req.room.RoomWelcomeContentListReq;
import com.simi.admin.req.room.UpdateRoomWelcomeContentReq;
import com.simi.admin.service.room.RoomWelcomeContentAdminService;
import com.simi.admin.vo.room.RoomWelcomeContentVO;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.dto.ListWithTotal;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Slf4j
@RestController
@RequestMapping("api/admin/roomWelcomeContent")
@RequiredArgsConstructor
@Tag(name = "进房欢迎公告", description = "进房欢迎公告")
public class RoomWelcomeContentAdminController {
    private final RoomWelcomeContentAdminService roomWelcomeContentAdminService;

    @PostMapping(value = "/getRoomWelcomeContentList")
    @Operation(summary = "查询房间欢迎公告")
    public BusiResult<ListWithTotal<RoomWelcomeContentVO>> getRoomWelcomeContentList(@Valid @RequestBody RoomWelcomeContentListReq req) {
        ListWithTotal<RoomWelcomeContentVO> result = roomWelcomeContentAdminService.getRoomWelcomeContentList(req);
        return BusiResultUtil.success(result);
    }

    @PostMapping(value = "/saveOrUpdateRoomWelcomeContent")
    @Operation(summary = "保存或编辑房间欢迎公告")
    public BusiResult<String> saveOrUpdateRoomWelcomeContent(@Valid @RequestBody UpdateRoomWelcomeContentReq req, HttpServletRequest request) {
        int adminId = new SessionUtil(request).getAdminId();
        roomWelcomeContentAdminService.saveOrUpdateRoomWelcomeContent(req,adminId);
        return BusiResultUtil.success("操作成功");
    }

    @PostMapping(value = "/deleteRoomWelcomeContent")
    @Operation(summary = "删除欢迎公告")
    public BusiResult<String> deleteRoomWelcomeContent(@Valid @RequestBody DeleteRoomWelcomeContentReq req, HttpServletRequest request) {
        int adminId = new SessionUtil(request).getAdminId();
        roomWelcomeContentAdminService.deleteRoomWelcomeContent(req,adminId);
        return BusiResultUtil.success("操作成功");
    }
}
