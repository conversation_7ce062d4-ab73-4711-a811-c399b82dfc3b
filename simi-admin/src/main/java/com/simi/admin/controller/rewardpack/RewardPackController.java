package com.simi.admin.controller.rewardpack;

import cn.hutool.json.JSONUtil;
import com.simi.admin.req.rewardpack.DeleteRewardPackReq;
import com.simi.admin.req.rewardpack.ListRewardPackReq;
import com.simi.admin.req.rewardpack.SaveRewardPackReq;
import com.simi.admin.service.rewardpack.RewardPackAdminService;
import com.simi.common.dto.RewardPackVO;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.exception.AdminException;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 *
 */
@Slf4j
@RestController
@RequestMapping("/api/admin/rewardPack")
@RequiredArgsConstructor
@Tag(name = "奖励包", description = "奖励包")
public class RewardPackController {

    private final RewardPackAdminService rewardPackAdminService;

    @Operation(summary = "奖励包列表查询")
    @PostMapping(value = "/list")
    public BusiResult<ListWithTotal<RewardPackVO>> list(@RequestBody ListRewardPackReq req) {
        ListWithTotal<RewardPackVO> result = rewardPackAdminService.listRewardPack(req);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "奖励包列表查询指定列表")
    @PostMapping(value = "/appointList")
    public BusiResult<ListWithTotal<RewardPackVO>> appointList() {
        ListWithTotal<RewardPackVO> result = rewardPackAdminService.appointList();
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "获取奖励包")
    @Parameters({
            @Parameter(name = "packId", description = "奖励包id", in = ParameterIn.QUERY)
    })
    @GetMapping(value = "/get")
    public BusiResult<RewardPackVO> get(@RequestParam("packId") Integer packId) {
        RewardPackVO rewardPack = rewardPackAdminService.getRewardPack(packId);
        return BusiResultUtil.success(rewardPack);

    }

    @Operation(summary = "保存修改奖励包")
    @PostMapping(value = "/save")
    public BusiResult<RewardPackVO> save(@RequestBody SaveRewardPackReq req, HttpServletRequest request) {
        String jsonStr = JSONUtil.toJsonStr(req);
        try {
            var sessionUtil = new SessionUtil(request);
            RewardPackVO result = rewardPackAdminService.save(req, sessionUtil.getAdminId(), jsonStr);
            return BusiResultUtil.success(result);
        } catch (Exception e) {
            log.error("saveRewardPack error jsonStr:{}", jsonStr, e);
            throw new AdminException(CodeEnum.DATA_TYPE_DUPLICATE);
        }
    }

    @Operation(summary = "删除奖励包")
    @PostMapping(value = "/delete")
    public BusiResult<?> delete(@RequestBody DeleteRewardPackReq req, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        boolean ret = rewardPackAdminService.delete(req, sessionUtil.getAdminId());
        if (!ret) {
            throw new AdminException(CodeEnum.SERVER_ERROR);
        }
        return BusiResultUtil.success();
    }

}
