package com.simi.admin.controller.system;

import com.simi.common.dto.ListWithTotal;
import com.simi.admin.entity.AdminRole;
import com.simi.admin.req.system.*;
import com.simi.admin.service.system.AdminRoleService;
import com.simi.admin.service.system.AdminUserService;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("api/admin/role")
@RequiredArgsConstructor
@Tag(name = "管理员角色", description = "管理员角色相关")
public class AdminRoleController {


    private final AdminRoleService adminRoleService;
    private final AdminUserService adminUserService;


    /**
     * 查询角色列表
     */
    @Operation(summary = "查询角色列表")
    @PostMapping(value = "")
    @Parameters({
            @Parameter(name = "token",description = "头部token", in = ParameterIn.HEADER)
    })
    public BusiResult<ListWithTotal<AdminRole>> search(@RequestBody QueryUserReq req) {
        ListWithTotal<AdminRole> result = adminRoleService.searchRole(req.getPageNum(), req.getPageSize(), req.getName());
        return BusiResultUtil.success(result);
    }

    /**
     * 保存角色
     * @return
     */
    @Operation(summary = "保存或修改角色")
    @PostMapping(value = "saveOrUpdate")
    @Parameters({
            @Parameter(name = "token",description = "头部token", in = ParameterIn.HEADER)
    })
    public BusiResult<?> addOrUpdate(@RequestBody AdminSaveRoleReq req) {
        adminRoleService.addOrUpdate(req.getId(), req.getName());
        return BusiResultUtil.success();
    }

    /**
     * 删除角色
     * @return
     */
    @Operation(summary = "删除角色")
    @PostMapping(value = "remove")
    @Parameters({
            @Parameter(name = "token",description = "头部token", in = ParameterIn.HEADER)
    })
    public BusiResult<?> remove(@RequestBody AdminIdReq req) {
        adminRoleService.remove(req.getId());
        return BusiResultUtil.success();
    }

    /**
     * 授权页面
     * @param param
     * @return
     */
    @Operation(summary = "授权角色菜单权限")
    @PostMapping(value = "authorize")
    @Parameters({
            @Parameter(name = "token",description = "头部token", in = ParameterIn.HEADER)
    })
    public BusiResult<?> authorize(@RequestBody AuthorizeRoleReq param){
        adminRoleService.authorize(param.getRoleId(), param.getMenuIds());
        return BusiResultUtil.success();
    }

    /**
     * 所有角色
     * @return
     */
    @Operation(summary = "所有角色")
    @PostMapping(value = "all")
    @Parameters({
            @Parameter(name = "token",description = "头部token", in = ParameterIn.HEADER)
    })
    public BusiResult<List<AdminRole>> all() {
        List<AdminRole> result = adminRoleService.all();
        return BusiResultUtil.success(result);
    }

    /**
     * 查询角色下用户
     */
    @Operation(summary = "查询角色下用户", description = "返回角色昵称列表")
    @PostMapping(value = "user")
    @Parameters({
            @Parameter(name = "token",description = "头部token", in = ParameterIn.HEADER)
    })
    public BusiResult<List<String>> user(@RequestBody AdminRoleReq req) {
        List<String> result = adminUserService.roleUser(req.getRoleId());
        return BusiResultUtil.success(result);
    }

}
