package com.simi.admin.controller.dataReport;

import com.simi.admin.req.dataReport.RoomReportReq;
import com.simi.admin.service.dataReport.RoomReportAdminService;
import com.simi.admin.vo.dataReport.RoomReportVO;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.dto.ListWithTotal;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 房间报表
 *
 */
@Slf4j
@RestController
@RequestMapping("api/admin/roomReport")
@RequiredArgsConstructor
@Tag(name = "房间报表", description = "房间报表")
public class RoomReportController {

    private final RoomReportAdminService roomReportAdminService;

    @Operation(summary = "获取房间报表")
    @PostMapping(value = "/getRoomReport")
    public BusiResult<ListWithTotal<RoomReportVO>> getRoomReport(@RequestBody RoomReportReq req) {
        ListWithTotal<RoomReportVO> result = roomReportAdminService.getRoomReport(req);
        return BusiResultUtil.success(result);
    }


}
