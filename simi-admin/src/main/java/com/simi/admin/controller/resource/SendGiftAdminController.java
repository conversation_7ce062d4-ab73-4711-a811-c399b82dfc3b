package com.simi.admin.controller.resource;

import cn.hutool.json.JSONUtil;
import com.simi.admin.req.resource.QuerySendGiftLogReq;
import com.simi.admin.service.resource.SendGiftAdminService;
import com.simi.admin.vo.resource.QuerySendGiftLogVO;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.dto.ListWithTotal;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/06/02 20:57
 **/
@Slf4j
@RestController
@RequestMapping("/api/admin/revenue/gift")
@RequiredArgsConstructor
@Tag(name = "礼物管理", description = "送礼流水")
public class SendGiftAdminController {

    private final SendGiftAdminService sendGiftAdminService;

    @PostMapping(value = "/giftLog/list")
    @Operation(summary = "送礼物流水")
    public BusiResult<ListWithTotal<QuerySendGiftLogVO>> list(@RequestBody QuerySendGiftLogReq req) {
        ListWithTotal<QuerySendGiftLogVO> result = sendGiftAdminService.querySendGiftLog(req, JSONUtil.toJsonStr(req));
        return BusiResultUtil.success(result);
    }

}
