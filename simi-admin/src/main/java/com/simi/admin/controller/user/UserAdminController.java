package com.simi.admin.controller.user;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.simi.admin.constant.AdminConstant;
import com.simi.admin.dto.user.AccountUserInfoCompleteDTO;
import com.simi.admin.req.user.*;
import com.simi.admin.service.user.UserAdminService;
import com.simi.admin.vo.user.AccountUserInfoVO;
import com.simi.admin.vo.user.CheckUserVO;
import com.simi.admin.vo.user.UserVO;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.user.FilterUserDTO;
//import com.simi.mapper.search.MeiliSearchAdminUserInfoMapper;
import com.simi.service.job.SyncUserInfoService;
import com.simi.service.oauth2.AccountService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户管理
 *
 * @Author: Andy
 * @Date: 2023/11/30
 */
@Slf4j
@RestController
@RequestMapping("api/admin/user")
@RequiredArgsConstructor
@Tag(name = "用户相关", description = "app用户相关操作")
public class UserAdminController {

    private final UserAdminService userAdminService;
    private final SyncUserInfoService syncUserInfoService;
    //private final MeiliSearchAdminUserInfoMapper meiliSearchAdminUserInfoMapper;
    private final AccountService accountService;


    @Operation(summary = "统计用户")
    @PostMapping(value = "/st")
    public BusiResult<Long> stUser() {
        return BusiResultUtil.success(userAdminService.stUser());
    }

    @Operation(summary = "用户信息列表")
    @PostMapping(value = "/list")
    public BusiResult<ListWithTotal<AccountUserInfoVO>> listUserInfo(@NotNull @RequestBody QueryUsersReq req) {
        ListWithTotal<AccountUserInfoVO> result = userAdminService.listUserInfo(req);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "用户信息列表V2")
    @PostMapping(value = "/listV2")
    public BusiResult<ListWithTotal<AccountUserInfoCompleteDTO>> queryUserInfoList(@RequestBody QueryUserAdminReq req) {
        ListWithTotal<AccountUserInfoCompleteDTO> result = userAdminService.queryUserInfoList(req);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "来源列表")
    @GetMapping(value = "/sourceList")
    public BusiResult<List<String>> getSourceList(){
        List<String> sourceList = accountService.getSourceList();
        return BusiResultUtil.success(sourceList);
    }

    @Operation(summary = "根据uid查询用户")
    @Parameters({
            @Parameter(name = "uid", description = "uid")
    })
    @GetMapping(value = "/get")
    public BusiResult<AccountUserInfoVO> getUserInfo(@RequestParam("uid") Long uid) {
        AccountUserInfoVO result = userAdminService.getUserInfo(uid);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "更新用户")
    @PostMapping(value = "/update")
    public BusiResult<?> updateUserInfo(@RequestBody UpdateUserReq req) {
        userAdminService.updateUserInfo(req);
        return BusiResultUtil.success();
    }

    @Operation(summary = "根据uid批量查询用户")
    @PostMapping(value = "/batchGetByUid")
    public BusiResult<List<UserVO>> batchGetUserByUid(@RequestBody @NotNull ParamUidsReq req) {
        List<UserVO> result = userAdminService.batchGetUserByUid(req.getUids());
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "根据userNo批量查询用户")
    @PostMapping(value = "/batchGetByUserNo")
    public BusiResult<List<UserVO>> batchGetUserByUserNo(@RequestBody @NotNull ParamUserNosReq req) {
        List<UserVO> result = userAdminService.batchGetUserByUserNo(req.getUserNos());
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "根据userNo批量检查用户")
    @PostMapping(value = "/checkByUserNo")
    public BusiResult<CheckUserVO> checkUserByUserNo(@RequestBody @NotNull ParamUserNosReq req) {
        CheckUserVO result = userAdminService.checkUserByUserNo(req.getUserNos());
        if (CollUtil.isNotEmpty(result.getNotExistsUserNoList())) {
            return BusiResultUtil.buildWithData(CodeEnum.PARAM_ILLEGAL, result);
        } else {
            return BusiResultUtil.success(result);
        }
    }

    @Operation(summary = "创建用户")
    @GetMapping(value = "system")
    public BusiResult<?> createSystem() {
        userAdminService.createSystemUser();
        return BusiResultUtil.success();
    }

    @Operation(summary = "重新加载用户信息")
    @GetMapping(value = "/user_no/reload")
    @Parameters({
            @Parameter(name = "adminPW", description = "系统密钥")
    })
    public BusiResult<?> reloadUserNo(@RequestParam("adminPW") String adminPW) {
        if (!StrUtil.equalsIgnoreCase(AdminConstant.ADMIN_PW, adminPW)) {
            return BusiResultUtil.success();
        }
        userAdminService.reloadUserNo();
        return BusiResultUtil.success();
    }

    @Operation(summary = "用户过滤")
    @PostMapping(value = "/filterUser")
    public BusiResult<ListWithTotal<FilterUserDTO>> filterUser(@RequestBody FilterUserAdminReq req) {
        ListWithTotal<FilterUserDTO> result = userAdminService.filterUser(req);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "用户过滤列表下载")
    @PostMapping(value = "/downloadFilterUser")
    public BusiResult<String> downloadFilterUser(@RequestBody FilterUserAdminReq req, HttpServletRequest request) {
        int adminId = new SessionUtil(request).getAdminId();
        userAdminService.downloadFile(req,adminId);
        return BusiResultUtil.success("下载任务已提交");
    }

    @Operation(summary = "同步用户数据")
    @PostMapping(value = "/asyncUserInfo")
    public BusiResult<String> asyncUserInfo() {
        //meiliSearchAdminUserInfoMapper.deleteAll();
        syncUserInfoService.executeAsyncUserInfo(null,20,true);
        return BusiResultUtil.success("同步用户数据成功");
    }

    @Operation(summary = "编辑付费用户备注")
    @PostMapping(value = "/editPayUserInfo")
    public BusiResult<String> editPayUserInfoRemark(@RequestBody EditPayUserRemarkReq editPayUserRemarkReq) {
        userAdminService.editPayUserInfoRemark(editPayUserRemarkReq);
        return BusiResultUtil.success("同步用户数据成功");
    }

}
