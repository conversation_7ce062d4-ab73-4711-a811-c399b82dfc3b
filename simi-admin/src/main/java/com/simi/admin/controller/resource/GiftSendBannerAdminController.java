package com.simi.admin.controller.resource;

import com.simi.admin.req.resource.GiftSendBannerDelReq;
import com.simi.admin.req.resource.GiftSendBannerSaveOrUpdateReq;
import com.simi.admin.service.resource.GiftSendBannerAdminService;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.dto.ListWithTotal;
import com.simi.entity.resource.GiftSendBanner;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 房间送礼横幅管理控制器
 *
 * <AUTHOR>
 * @date 2024/04/24 15:36
 **/
@Slf4j
@RestController
@RequestMapping("api/admin/giftSendBanner")
@RequiredArgsConstructor
@Tag(name = "房间送礼横幅", description = "房间送礼横幅")
public class GiftSendBannerAdminController {

    private final GiftSendBannerAdminService giftSendBannerAdminService;

    @GetMapping(value = "list")
    @Operation(summary = "列表")
    public BusiResult<ListWithTotal<GiftSendBanner>> list(@RequestParam(value = "id", required = false) Integer id) {
        ListWithTotal<GiftSendBanner> result = giftSendBannerAdminService.list(id);
        return BusiResultUtil.success(result);
    }

    @PostMapping(value = "saveOrUpdate")
    @Operation(summary = "保存或编辑")
    public BusiResult<?> save(@RequestBody GiftSendBannerSaveOrUpdateReq req) {
        giftSendBannerAdminService.saveOrUpdate(req);
        return BusiResultUtil.success();
    }

    @PostMapping(value = "del")
    @Operation(summary = "删除")
    public BusiResult<?> del(@RequestBody GiftSendBannerDelReq req) {
        giftSendBannerAdminService.del(req.getId());
        return BusiResultUtil.success();
    }


}
