package com.simi.admin.controller.user;

import com.simi.admin.service.user.BlockAdminService;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.constant.AccountConstant;
import com.simi.common.vo.req.BlockReq;
import com.simi.common.vo.req.UnblockRecordReq;
import com.simi.common.vo.req.UnblockReq;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;



/**
 * <AUTHOR>
 * @date 2024/2/2 11:14
 */
@Slf4j
@RestController
@RequestMapping("api/admin/user")
@RequiredArgsConstructor
@Tag(name = "封禁", description = "用户封禁相关")
public class UserBlockAdminController {

    private final BlockAdminService blockAdminService;


    @Operation(summary = "封禁用户")
    @PostMapping(value = "block")
    public BusiResult<?> block(@RequestBody BlockReq req, HttpServletRequest request){
        var sessionUtil = new SessionUtil(request);
        blockAdminService.block(req, 0, sessionUtil.getAdminId());
        return BusiResultUtil.success();
    }


    @Operation(summary = "解封用户")
    @PostMapping(value = "unblock")
    public BusiResult<?> unblock(@RequestBody UnblockRecordReq req, HttpServletRequest request){
        var sessionUtil = new SessionUtil(request);
        for (Integer type : req.getTypes()) {
            UnblockReq unblock = new UnblockReq();
            if (AccountConstant.BlockType.account_block == type) {
                unblock.setUid(Long.parseLong(req.getContentUid()));
                unblock.setContent(req.getContentUid());
            }
            if (AccountConstant.BlockType.device_block == type) {
                unblock.setContent(req.getContentDid());
            }
            if (AccountConstant.BlockType.ip_block == type) {
                unblock.setContent(req.getContentIp());
            }
            unblock.setType(type);
            blockAdminService.unblock(unblock, sessionUtil.getAdminId());
        }
        return BusiResultUtil.success();
    }

}
