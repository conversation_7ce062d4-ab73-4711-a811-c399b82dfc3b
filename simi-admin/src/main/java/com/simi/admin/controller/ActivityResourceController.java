package com.simi.admin.controller;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.exception.AdminException;
import com.simi.common.vo.req.ActivityResourcesReq;
import com.simi.common.vo.resp.ActivityResourcesResp;
import com.simi.entity.ActivityResources;
import com.simi.service.ActivityResourcesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-07 16:25
 **/
@Slf4j
@RestController
@RequestMapping("api/admin/activity/resource")
@RequiredArgsConstructor
@Tag(name = "活动资源", description = "活动资源")
public class ActivityResourceController {

    private final ActivityResourcesService activityResourcesService;

    /**
     * 新增活动资源
     */
    @Operation(summary = "新增")
    @PostMapping(value = "/save")
    public BusiResult<Integer> save(@RequestBody ActivityResourcesReq req){
        if (req.getId() == null) {
            // 新增
            ActivityResources activityResources = BeanUtil.copyProperties(req, ActivityResources.class);
            activityResourcesService.save(activityResources);
        } else {
            // 更新
            ActivityResources activityResources = activityResourcesService.getById(req.getId());
            if (Objects.isNull(activityResources)) {
                throw new AdminException(CodeEnum.PARAM_ILLEGAL);
            }
            ActivityResources update = BeanUtil.copyProperties(req, ActivityResources.class);
            activityResourcesService.updateById(update);
        }
        return BusiResultUtil.success();
    }


    /**
     * 分页获取
     */
    @Operation(summary = "分页获取")
    @GetMapping(value = "/getList")
    public BusiResult<ListWithTotal<ActivityResourcesResp>> getList(@RequestParam(value = "pageNum", required = false, defaultValue = "1") Integer pageNum,
                                                                    @RequestParam(value = "pageSize", required = false, defaultValue = "40") Integer pageSize,
                                                                    @RequestParam(value = "id", required = false) Integer id) {
        LambdaQueryWrapper<ActivityResources> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Objects.nonNull(id), ActivityResources::getId, id);
        wrapper.orderByDesc(ActivityResources::getCreateTime);

        Page<ActivityResources> page = new Page<>();
        page.setCurrent(pageNum);
        page.setSize(pageSize);
        Page<ActivityResources> curPage = activityResourcesService.page(page, wrapper);
        List<ActivityResources> records = curPage.getRecords();
        List<ActivityResourcesResp> activityResources = BeanUtil.copyToList(records, ActivityResourcesResp.class);

        return BusiResultUtil.success(ListWithTotal.<ActivityResourcesResp>builder().total(curPage.getTotal()).list(activityResources).build());
    }
}
