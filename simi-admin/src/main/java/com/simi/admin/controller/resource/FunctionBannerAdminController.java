package com.simi.admin.controller.resource;

import com.simi.admin.req.resource.*;
import com.simi.admin.service.resource.FunctionBannerAdminService;
import com.simi.admin.vo.resource.FunctionBannerVO;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.dto.ListWithTotal;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("api/admin/resource/function/banner")
@RequiredArgsConstructor
@Tag(name = "功能生效配置管理", description = "功能生效配置管理")
public class FunctionBannerAdminController {

    private final FunctionBannerAdminService functionBannerAdminService;

    @PostMapping()
    @Operation(summary = "banner列表查询")
    public BusiResult<ListWithTotal<FunctionBannerVO>> search(@RequestBody FunctionBannerAdminListReq param) {
        ListWithTotal<FunctionBannerVO> search = null;
        try {
            search = functionBannerAdminService.search(param);
        } catch (Exception e) {
            log.error("异常 ", e);
        }
        return BusiResultUtil.success(search);
    }

    @PostMapping(value = "save")
    @Operation(summary = "保存编辑banner")
    public BusiResult<?> save(@RequestBody SaveFunctionBannerReq param, HttpServletRequest request) {
        try {
            functionBannerAdminService.save(param);
        } catch (Exception e) {
            log.error("异常 ", e);
        }
        return BusiResultUtil.success();
    }

    @PostMapping(value = "remove")
    @Operation(summary = "删除banner")
    public BusiResult<?> remove(@RequestBody RemoveBannerReq param) {
        try {
            functionBannerAdminService.remove(param);
        } catch (Exception e) {
            log.error("异常 ", e);
        }

        return BusiResultUtil.success();
    }
}
