package com.simi.admin.controller.agent;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.vo.agent.AgentAdminResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@Tag(name = "代理相关接口", description = "代理相关接口API")
@RequestMapping("/api/admin/agent2")
public class AgentAdminController {

    @Operation(summary = "获取代理数据")
    @GetMapping("")
    public BusiResult<List<AgentAdminResp>> queryList(HttpServletRequest request) {
        return BusiResultUtil.success();
    }
}
