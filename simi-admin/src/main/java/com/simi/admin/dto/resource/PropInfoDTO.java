package com.simi.admin.dto.resource;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "道具列表")
public class PropInfoDTO {


    private Long id;

    @Schema(description = "道具类型")
    private Integer type;

    @Schema(description = "名称(英语)")
    private String nameEn;

    @Schema(description = "名称(阿语)")
    private String nameAr;

    @Schema(description = "图标")
    private String icon;

    @Schema(description = "特效地址")
    private String animationUrl;

    @Schema(description = "特效类型")
    private Integer animationType;

    @Schema(description = "权重")
    private Integer weight;

    @Schema(description = "删除标识：1、未删除;2、已删除")
    private Integer deleteFlag;

    @Schema(description = "记录创建时间")
    private Date createTime;

    @Schema(description = "记录修改时间")
    private Date modifyTime;

    @Schema(description = "记录创建用户id")
    private Integer createUid;

    @Schema(description = "记录创建用户名")
    private String createBy;

    @Schema(description = "记录修改用户id")
    private Integer modifyUid;

    @Schema(description = "记录修改用户名")
    private String modifyBy;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "价格")
    private String prices;



}
