package com.simi.admin.support;


import com.simi.common.support.BodyRequestWrapper;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;

@WebFilter(urlPatterns = {"/api/admin/*"})
public class RequestBodyFilter implements Filter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        //对请求进行拦截
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        filterChain.doFilter(new BodyRequestWrapper(request), servletResponse);
    }
}
