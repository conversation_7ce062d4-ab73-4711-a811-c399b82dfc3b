package com.simi.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.cloud.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.GamePhase;
import com.simi.common.constant.GamePushType;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.SystemConfigDTO;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.exception.ApiException;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.TimeUtils;
import com.simi.common.vo.req.game.RegularGameBillReq;
import com.simi.constant.BillEnum;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.dto.game.*;
import com.simi.entity.GameRoomViewRecord;
import com.simi.entity.game.GameFruitMachineBetRecord;
import com.simi.entity.game.GameFruitMachineConfig;
import com.simi.entity.game.GameFruitMachineMergedRecord;
import com.simi.entity.game.GameFruitMachineRoundRecord;
import com.simi.lobby.GameAllLobbyManage;
import com.simi.mapper.GameRoomViewRecordMapper;
import com.simi.service.cache.FruitMachineCache;
import com.simi.service.game.*;
import com.simi.service.purse.PurseManageService;
import com.simi.service.user.UserServerService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class FruitMachineService {

    private static final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    private final PurseManageService purseManageService;

    private final FruitMachineCache fruitMachineCache;

    private final RegularGamesService regularGamesService;

    private final FruitMachineConfigService fruitMachineConfigService;

    private final BetRecordService betRecordService;

    private final RoundRecordService roundRecordService;

    private final LongLinkService longLinkService;

    private final UserServerService userServerService;

    private final MergedRecordService mergedRecordService;

    private final GameRoomViewRecordMapper gameRoomViewRecordMapper;

    private final SystemConfigService systemConfigService;

    AtomicReference<GamePhase> currentPhase = new AtomicReference<>(GamePhase.BETTING);

    private static Long ROUND = 1L;

    private static String ROUND_ID = "";

    private static Long RESULT_ID = 0L;

    private static String RESULT = "";

    private static String DAY = "";

    // 是否自动开始下一回合
    private static boolean autoNextRound = true;

    // 当前回合的下注统计任务
    private static ScheduledFuture<?> betUpdateTask = null;

    private final GameAllLobbyManage gameAllLobbyManage;

    public ListWithTotal<MergedRecordDTO> mergedRecord(Long uid, Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<MergedRecordDTO> mergedRecordDTOS = new ArrayList<>();
        List<GameFruitMachineMergedRecord> mergedRecords = mergedRecordService.lambdaQuery()
                .eq(GameFruitMachineMergedRecord::getUid, uid)
                .orderByDesc(GameFruitMachineMergedRecord::getCreateTime)
                .list();
        PageInfo<GameFruitMachineMergedRecord> pageInfo = new PageInfo<>(mergedRecords);
        for (GameFruitMachineMergedRecord gameFruitMachineMergedRecord : pageInfo.getList()) {
            UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(uid);
            MergedRecordDTO dto = BeanUtil.copyProperties(gameFruitMachineMergedRecord, MergedRecordDTO.class);
            dto.setUserBaseInfo(userBaseInfo);
            mergedRecordDTOS.add(dto);
        }
        return ListWithTotal.<MergedRecordDTO>builder().list(mergedRecordDTOS).total(pageInfo.getTotal()).build();
    }


    public ListWithTotal<RoundRecordDTO> roundRecord(Integer pageNum, Integer pageSize) {
        PageHelper.startPage(pageNum, pageSize);
        List<RoundRecordDTO> roundRecordDTOS = new ArrayList<>();
        List<GameFruitMachineRoundRecord> gameFruitMachineRoundRecords = roundRecordService.lambdaQuery()
                .orderByDesc(GameFruitMachineRoundRecord::getCreateTime)
                .list();
        PageInfo<GameFruitMachineRoundRecord> pageInfo = new PageInfo<>(gameFruitMachineRoundRecords);
        for (GameFruitMachineRoundRecord gameFruitMachineRoundRecord : pageInfo.getList()) {
            RoundRecordDTO dto = BeanUtil.copyProperties(gameFruitMachineRoundRecord, RoundRecordDTO.class);
            roundRecordDTOS.add(dto);
        }
        return ListWithTotal.<RoundRecordDTO>builder().list(roundRecordDTOS).total(pageInfo.getTotal()).build();
    }


    // 使用UserWinRandDTO替代WinnerInfo内部类
    @PostConstruct
    public void init() {
        startRound(); // 启动就开始
    }

    @PreDestroy
    public void destroy() {
        // 关闭线程池，防止资源泄漏
        if (!scheduler.isShutdown()) {
            try {
                scheduler.shutdown();
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }

        // 取消下注统计任务
        if (betUpdateTask != null && !betUpdateTask.isDone()) {
            betUpdateTask.cancel(false);
        }
    }

    private void startRound() {
        try {
            log.info("===== 开始新回合 =====");
            // 立即执行第一阶段：回合开始
            executeBettingPhase();
        } catch (Exception e) {
            log.error("启动回合失败", e);
            // 出错后5秒重试
            scheduler.schedule(this::startRound, 5, TimeUnit.SECONDS);
        }
    }

    /**
     * 第1阶段：押注阶段（0秒开始，持续20秒）
     */
    private void executeBettingPhase() {
        try {
            log.info("===== 第1阶段：押注阶段开始 =====");

            //入口开关
            //下单长连接
            GamePayloadDTO dto = new GamePayloadDTO();
            dto.setType(GamePushType.phase.getType());
            GamePhase currentPhase = RoundState.getCurrentPhase();
            GamePhaseDTO gamePhaseDTO = new GamePhaseDTO();
            gamePhaseDTO.setGamePhase(currentPhase.getType());
            //离阶段结束还差几秒
            int countdown = RoundState.getCurrentPhaseRemainingSeconds();
            gamePhaseDTO.setRemainingSeconds(countdown);
            dto.setData(gamePhaseDTO);
            longLinkService.pushGameMsg(null, dto, PushEvent.fruit_machine_event, PushToType.MESSAGE_TO_ALL);
            DAY = TimeUtils.toStr(new Date(), TimeUtils.PATTERN_FORMAT_DATE);
            Long fruitMachineRound = fruitMachineCache.getFruitMachineRound(DAY);

            if (fruitMachineRound <= 0) {
                ROUND = fruitMachineCache.fruitMachineRound(DAY);
                ROUND_ID = DAY + "_" + ROUND;
            } else {
                ROUND = fruitMachineRound;
                ROUND_ID = DAY + "_" + ROUND;
            }
            RoundState.startNewRound();
            // 抽取开奖结果
            GameFruitMachineConfig draw = draw();
            //结果
            RESULT_ID = draw.getId();
            RESULT = draw.getName();
            // 初始化本轮下注统计
            fruitMachineCache.setFruitMachineBet(DAY, "total_bet_amount", 0L);

            // 保存回合记录
            saveRoundRecord();

            // 定期广播下注情况（每2秒一次）
            if (betUpdateTask != null && !betUpdateTask.isDone()) {
                betUpdateTask.cancel(false);
            }

            fruitMachineCache.fruitMachineRound(DAY);

            // 20秒后执行第2阶段
            scheduler.schedule(this::executeGrayPhase, 20, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("押注阶段处理失败", e);
        }
    }

    /**
     * 第2阶段：置灰阶段（20秒开始，持续5秒）
     */
    private void executeGrayPhase() {
        try {
            log.info("===== 第2阶段：置灰阶段开始 =====");

            // 这里可以添加置灰阶段的逻辑
            // 例如：禁止下注，发送置灰通知等

            // 5秒后执行第3阶段
            scheduler.schedule(this::executeDrawPhase, 5, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("置灰阶段处理失败", e);
        }
    }

    /**
     * 第3阶段：开奖阶段（25秒开始，持续5秒）
     */
    private void executeDrawPhase() {
        try {
            log.info("===== 第3阶段：开奖阶段开始 =====");

            GamePayloadDTO resultDto = new GamePayloadDTO();
            resultDto.setType(GamePushType.phase.getType());
            // 发送开奖阶段通知
            GamePhase currentPhase = RoundState.getCurrentPhase();
            GamePhaseDTO gamePhaseDTO = new GamePhaseDTO();
            gamePhaseDTO.setGamePhase(currentPhase.getType());
            gamePhaseDTO.setRemainingSeconds(RoundState.getCurrentPhaseRemainingSeconds());
            resultDto.setData(gamePhaseDTO);
            longLinkService.pushGameMsg(null, resultDto, PushEvent.fruit_machine_event, PushToType.MESSAGE_TO_ALL);
            // 处理开奖和发放奖金
            processDrawnAndRewards();
            //修改开奖记录
            roundRecordService.lambdaUpdate()
                    .eq(GameFruitMachineRoundRecord::getRoundId, ROUND_ID)
                    .set(GameFruitMachineRoundRecord::getIsDrawn, 2)
                    .update();
            // 获取中奖倍率
            Map<String, String> fruitMachineNameMultiple = fruitMachineCache.getFruitMachineNameMultiple();
            // 获取获奖信息
            String winnersJson = fruitMachineCache.getResult(ROUND_ID);
            GameFruitMachineConfig config = JSONUtil.toBean(fruitMachineNameMultiple.get(RESULT), GameFruitMachineConfig.class);
            int multiple = config.getMultiple();
            ResultDTO dto = new ResultDTO();
            // 发送榜单展示通知
            GamePayloadDTO rankDto = new GamePayloadDTO();
            dto.setResult(RESULT);
            dto.setMultiple(multiple);
            if (StrUtil.isNotBlank(winnersJson)) {
                List<UserWinRandDTO> winners = JSONUtil.toList(winnersJson, UserWinRandDTO.class);
                // 获取前三名
                List<UserWinRandDTO> top3 = winners.stream()
                        .limit(3)
                        .collect(Collectors.toList());

                dto.setUserWinRandTop3(top3);
                // 对于每个用户，发送包含他们自己获奖信息的消息
                for (UserWinRandDTO winner : winners) {
                    // 发送给特定用户
                    GamePayloadDTO userRankDto = new GamePayloadDTO();
                    userRankDto.setType(GamePushType.meWin.getType());
                    userRankDto.setData(winner);
                    longLinkService.pushGameMsg("" + winner.getUid(),userRankDto, PushEvent.fruit_machine_event, PushToType.EXCLUDE_OTHER);
                    SystemConfigDTO sysConfById = systemConfigService.getSysConfById(SystemConfigConstant.ROOM_GAME_AWARD_CONFIG);
                    if (sysConfById != null && StringUtils.isNotBlank(sysConfById.getValue())) {
                        GameAwardConfigDTO gameAwardConfigDTO = JSON.parseObject(sysConfById.getValue(), GameAwardConfigDTO.class);
                        GameFruitMachineConfig gameFruitMachineConfig = JSONUtil.toBean(fruitMachineNameMultiple.get(RESULT), GameFruitMachineConfig.class);
                        if (gameAwardConfigDTO != null) {
                            if (gameAwardConfigDTO.getCount() <= winner.getWinAmount() && gameAwardConfigDTO.getMultiple() <= gameFruitMachineConfig.getMultiple()) {
                                UserWinnerDTO userWinnerDTO = new UserWinnerDTO();
                                userWinnerDTO.setWinAmount(winner.getWinAmount());
                                userWinnerDTO.setMultiple(gameFruitMachineConfig.getMultiple());
                                userWinnerDTO.setUserBaseInfo(userServerService.getUserBaseInfo(winner.getUid()));
                                longLinkService.pushCustomerRoomBannerList(winner.getRoomId(),userWinnerDTO, PushEvent.fruit_machine_event, PushToType.EXCLUDE_OTHER);
                            }
                        }
                    }
                }
            }
            rankDto.setType(GamePushType.result.getType()); // 榜单展示类型
            rankDto.setData(dto);
            longLinkService.pushGameMsg(null,rankDto, PushEvent.fruit_machine_event, PushToType.MESSAGE_TO_ALL);

            // 5秒后执行第4阶段
            scheduler.schedule(this::executeDisplayPhase, 5, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("开奖阶段处理失败", e);
        }
    }

    /**
     * 第4阶段：展示阶段（30秒开始，持续4秒）
     */
    private void executeDisplayPhase() {
        try {
            log.info("===== 第4阶段：展示阶段开始 =====");

            // 发送展示阶段通知
            GamePayloadDTO resultDto = new GamePayloadDTO();
            resultDto.setType(GamePushType.phase.getType());
            GamePhase currentPhase = RoundState.getCurrentPhase();
            GamePhaseDTO gamePhaseDTO = new GamePhaseDTO();
            gamePhaseDTO.setGamePhase(currentPhase.getType());
            gamePhaseDTO.setRemainingSeconds(RoundState.getCurrentPhaseRemainingSeconds());
            resultDto.setData(gamePhaseDTO);
            longLinkService.pushGameMsg(null, resultDto, PushEvent.fruit_machine_event, PushToType.MESSAGE_TO_ALL);

            // 4秒后检查是否开始下一回合
            scheduler.schedule(() -> {
                if (autoNextRound) {
                    log.info("===== 自动开始下一回合 =====");
                    startRound();
                } else {
                    log.info("===== 回合结束，等待手动开始下一回合 =====");
                }
            }, 4, TimeUnit.SECONDS);

        } catch (Exception e) {
            log.error("展示阶段处理失败", e);
            // 即使出错，也要确保游戏继续
            if (autoNextRound) {
                scheduler.schedule(this::startRound, 4, TimeUnit.SECONDS);
            }
        }
    }

    /**
     * 设置是否自动开始下一回合
     *
     * @param auto true表示自动开始下一回合，false表示不自动开始
     */
    public void setAutoNextRound(boolean auto) {
        autoNextRound = auto;
    }

    /**
     * 获取当前自动下一回合的设置状态
     *
     * @return 当前是否自动开始下一回合
     */
    public boolean isAutoNextRound() {
        return autoNextRound;
    }

    /**
     * 手动开始下一回合
     */
    public void manualStartNextRound() {
        // 只有在不是自动模式时才允许手动开始
        if (!autoNextRound) {
            startRound();
            log.info("===== 手动开始下一回合 =====");
        } else {
            log.info("===== 当前为自动模式，无需手动开始下一回合 =====");
        }
    }

    /**
     * 结束当前回合，并停止下一回合
     * 这个方法会立即结束当前回合，并将自动下一回合设置为关闭状态
     */
    public void endCurrentRound() {
        try {
            // 取消所有定时任务
            if (betUpdateTask != null && !betUpdateTask.isDone()) {
                betUpdateTask.cancel(false);
                log.info("===== 取消下注统计任务 =====");
            }

            // 强制处理开奖和发放奖金
            processDrawnAndRewards();
            log.info("===== 强制处理开奖和发放奖金 =====");

            // 关闭自动下一回合
            setAutoNextRound(false);
            log.info("===== 结束当前回合并停止下一回合 =====");
        } catch (Exception e) {
            log.error("结束当前回合失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 保存回合记录到数据库
     */
    private void saveRoundRecord() {
        try {
            // 检查是否已存在该回合记录
            GameFruitMachineRoundRecord existingRecord = roundRecordService.lambdaQuery()
                    .eq(GameFruitMachineRoundRecord::getRoundId, ROUND_ID)
                    .one();

            if (existingRecord != null) {
                log.info("回合记录已存在，跳过保存: {}", ROUND_ID);
                return;
            }

            // 创建新的回合记录
            GameFruitMachineRoundRecord gameFruitMachineRoundRecord = GameFruitMachineRoundRecord.builder()
                    .roundId(ROUND_ID)
                    .round(ROUND)
                    .resultId(RESULT_ID)
                    .result(RESULT)
                    .isDrawn(1) // 1表示未开奖，2表示已开奖
                    .createTime(new Date())
                    .build();

            // 保存到数据库
            roundRecordService.save(gameFruitMachineRoundRecord);
            log.info("成功保存回合记录: {}", ROUND_ID);
        } catch (Exception e) {
            log.error("保存回合记录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理开奖和发放奖金
     */
    private void processDrawnAndRewards() {
        try {
            // 获取本轮所有下注记录
            List<GameFruitMachineBetRecord> gameFruitMachineBetRecords = betRecordService.lambdaQuery()
                    .eq(GameFruitMachineBetRecord::getRoundId, ROUND_ID)
                    .eq(GameFruitMachineBetRecord::getIsDrawn, 1) // 只处理未开奖的记录
                    .list();

            if (gameFruitMachineBetRecords.isEmpty()) {
                log.info("本轮没有下注记录: {}", ROUND_ID);
                return;
            }

            log.info("处理开奖和奖金发放，回合: {}，下注记录数: {}", ROUND_ID, gameFruitMachineBetRecords.size());

            // 获取中奖倍率
            Map<String, String> fruitMachineNameMultiple = fruitMachineCache.getFruitMachineNameMultiple();
            String multipleStr = fruitMachineNameMultiple.get(RESULT);
            GameFruitMachineConfig config = JSONUtil.toBean(multipleStr, GameFruitMachineConfig.class);
            long multiple = config.getMultiple();

            // 计算并发放奖金
            List<UserWinRandDTO> winners = new ArrayList<>();
            Map<Long, Long> userWinAmounts = new HashMap<>();

            // 用于汇总用户下注金额的Map
            Map<Long, Long> userBetAmounts = new HashMap<>();

            Map<Long, BetChannelDTO> betChannelMap = new HashMap<>();

            for (GameFruitMachineBetRecord record : gameFruitMachineBetRecords) {
                // 更新记录为已开奖
                record.setIsDrawn(2);
                record.setResult(RESULT);
                record.setResultId(RESULT_ID);
                BetChannelDTO betChannelDTO = new BetChannelDTO();
                betChannelDTO.setGameType(record.getGameType());
                betChannelDTO.setRoomId(record.getRoomId());
                betChannelMap.put(record.getUid(), betChannelDTO);

                // 累计用户下注金额
                userBetAmounts.merge(record.getUid(), record.getAmount(), Long::sum);

                // 如果下注的水果与结果相同，则中奖
                if (RESULT.equals(record.getBet())) {
                    Long winAmount = record.getAmount() * multiple;
                    record.setWin(winAmount);

                    // 累计用户赢取金额
                    userWinAmounts.merge(record.getUid(), winAmount, Long::sum);

                    // 发放奖金
                    String commonId = CommonUtil.genId();
                    try {
                        purseManageService.addCoin(
                                record.getUid(),
                                winAmount,
                                BillEnum.FRUIT_PARTY_RECEIVE_AWARD,
                                commonId,
                                "Fruit machine award",
                                Collections.emptyMap(),
                                0L
                        );
                        log.info("发放奖金成功: 用户={}, 金额={}, 回合={}", record.getUid(), winAmount, ROUND_ID);
                    } catch (Exception e) {
                        log.error("发放奖金失败: 用户={}, 金额={}, 错误={}", record.getUid(), winAmount, e.getMessage(), e);
                    }
                } else {
                    record.setWin(0L);
                }
            }

            // 批量更新下注记录
            betRecordService.updateBatchById(gameFruitMachineBetRecords);

            // 更新回合记录为已开奖
            roundRecordService.lambdaUpdate()
                    .eq(GameFruitMachineRoundRecord::getRoundId, ROUND_ID)
                    .set(GameFruitMachineRoundRecord::getIsDrawn, 2)
                    .update();

            // 汇总用户下注和奖金到merged_record表
            List<GameFruitMachineMergedRecord> gameFruitMachineMergedRecords = new ArrayList<>();
            Date now = new Date();

            // 处理所有参与下注的用户
            Set<Long> allUserIds = new HashSet<>();
            allUserIds.addAll(userBetAmounts.keySet());

            for (Long uid : allUserIds) {
                // 获取用户下注金额和获奖金额
                Long betAmount = userBetAmounts.getOrDefault(uid, 0L);
                Long winAmount = userWinAmounts.getOrDefault(uid, 0L);

                // 创建汇总记录
                GameFruitMachineMergedRecord gameFruitMachineMergedRecord = GameFruitMachineMergedRecord.builder()
                        .uid(uid)
                        .amount(betAmount)
                        .win(winAmount)
                        .resultId(RESULT_ID)
                        .result(RESULT)
                        .roundId(ROUND_ID)
                        .round(ROUND)
                        .createTime(now)
                        .build();
                BetChannelDTO betChannelDTO = betChannelMap.get(uid);
                if (betChannelDTO != null) {
                    gameFruitMachineMergedRecord.setGameType(betChannelDTO.getGameType());
                    gameFruitMachineMergedRecord.setRoomId(betChannelDTO.getRoomId());
                }
                gameFruitMachineMergedRecords.add(gameFruitMachineMergedRecord);

                // 如果用户有获奖，添加到获奖者列表
                if (winAmount > 0) {
                    try {
                        BetChannelDTO dto = betChannelMap.get(uid);
                        UserBaseInfoDTO user = userServerService.getUserBaseInfo(uid);
                        UserWinRandDTO winnerDto = new UserWinRandDTO(uid, user.getNick(), user.getAvatar(), betAmount, winAmount,dto.getRoomId());
                        winners.add(winnerDto);
                        //数据写入缓存
                        gameAllLobbyManage.addLobby(user, (int) multiple, winAmount);

                    } catch (Exception e) {
                        log.error("获取用户信息失败: uid={}, 错误={}", uid, e.getMessage());
                    }
                }
            }

            // 批量保存汇总记录
            if (!gameFruitMachineMergedRecords.isEmpty()) {
                mergedRecordService.saveBatch(gameFruitMachineMergedRecords);
                log.info("成功保存用户下注和奖金汇总记录: 回合={}, 数量={}", ROUND_ID, gameFruitMachineMergedRecords.size());
            }

            // 按获奖金额排序
            winners.sort(Comparator.comparing(UserWinRandDTO::getWinAmount).reversed());

            // 保存获奖信息到Redis，用于展示阶段
            fruitMachineCache.setResult(ROUND_ID, JSONUtil.toJsonStr(winners));

            log.info("开奖处理完成，回合: {}，中奖人数: {}", ROUND_ID, winners.size());
        } catch (Exception e) {
            log.error("处理开奖和奖金发放失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 获取当前回合的下注金额统计
     */
    private List<BetAmountDTO> getBetAmounts() {
        try {
            // 查询所有水果配置
            List<GameFruitMachineConfig> configs = fruitMachineConfigService.list();
            List<BetAmountDTO> result = new ArrayList<>();

            // 获取每种水果的下注金额
            for (GameFruitMachineConfig config : configs) {
                String betKey = config.getName();
                Long amount = 0L;

                // 从Redis获取该水果的下注金额
                try {
                    String amountStr = fruitMachineCache.getTotalBetAmount(ROUND_ID, betKey);
                    if (StrUtil.isNotBlank(amountStr)) {
                        amount = Long.parseLong(amountStr);
                    }
                } catch (Exception e) {
                    log.error("获取下注金额失败: {}, {}", betKey, e.getMessage());
                }

                BetAmountDTO dto = new BetAmountDTO();
                dto.setBet(betKey);
                dto.setAmount(amount);
                result.add(dto);
            }

            // 获取总下注金额
            String totalAmountStr = fruitMachineCache.getTotalBetAmount(ROUND_ID, "total_bet_amount");
            Long totalAmount = 0L;
            if (StrUtil.isNotBlank(totalAmountStr)) {
                totalAmount = Long.parseLong(totalAmountStr);
            }

            // 添加总下注金额
            BetAmountDTO totalDto = new BetAmountDTO();
            totalDto.setBet("total");
            totalDto.setAmount(totalAmount);
            result.add(totalDto);

            return result;
        } catch (Exception e) {
            log.error("获取下注金额统计失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }


    public GameHomeDTO gameHome(Long uid, Long roomId) {

        GameHomeDTO dto = new GameHomeDTO();

        PurseDTO purse = purseManageService.getPurse(uid);
        dto.setPurse(purse);
        //当前阶段
        GamePhase currentPhase = RoundState.getCurrentPhase();
        dto.setCurrentPhase(currentPhase.getType());
        //离阶段结束还差几秒
        int countdown = RoundState.getCurrentPhaseRemainingSeconds();
        dto.setCountdown(countdown);
        //是否自动下一回合
        dto.setAutoNextRound(autoNextRound);
        //当前回合数
        dto.setCurrentRound(ROUND);

        //存储玩家进来
        GameRoomViewRecord roomViewRecord = new GameRoomViewRecord();
        roomViewRecord.setUid(uid);
        roomViewRecord.setGameId(1);
        roomViewRecord.setRoomId(roomId);
        roomViewRecord.setViewTime(new Date());
        gameRoomViewRecordMapper.insert(roomViewRecord);
        return dto;
    }

    private Boolean isBet() {
        //当前阶段
        GamePhase currentPhase = RoundState.getCurrentPhase();
        if (currentPhase == null || currentPhase.getType() != GamePhase.BETTING.getType()) {
            throw new ApiException(CodeEnum.NOT_AT_BETTING_TIME);
        }
        return true;
    }

    public void bet(Long uid, List<BetAmountReq> reqs, String roomId, Integer gameType) {
        try {
            //判断下注阶段
            isBet();

            // 获取用户钱包信息，检查是否有足够金币
            PurseDTO purse = purseManageService.getPurse(uid);
            long totalBetAmount = 0;
            for (BetAmountReq req : reqs) {
                totalBetAmount += req.getAmount();
            }

            if (purse.getCoin() < totalBetAmount) {
                throw new ApiException(CodeEnum.COIN_BALANCE_NOT_ENOUGH);
            }

            for (BetAmountReq req : reqs) {
                // 创建下注记录
                GameFruitMachineBetRecord record = new GameFruitMachineBetRecord();
                record.setBet(req.getBet());
                record.setUid(uid);
                record.setRound(ROUND);
                record.setCreateTime(new Date());
                record.setIsDrawn(1); // 1表示未开奖
                record.setResult(RESULT);
                record.setRoundId(ROUND_ID);
                record.setResultId(RESULT_ID);
                record.setAmount(req.getAmount());
                record.setWin(0L); // 初始化为0，开奖后更新
                record.setGameType(gameType);
                record.setRoomId(roomId);
                // 保存下注记录
                betRecordService.save(record);

                // 更新Redis中的下注统计
                fruitMachineCache.setFruitMachineBet(DAY, req.getBet(), req.getAmount());

                // 更新总下注金额
                String totalAmountStr = fruitMachineCache.getTotalBetAmount(ROUND_ID, "total_bet_amount");
                int totalAmount = 0;
                if (StrUtil.isNotBlank(totalAmountStr)) {
                    totalAmount = Integer.parseInt(totalAmountStr);
                }
                totalAmount += req.getAmount();
                fruitMachineCache.setTotalBetAmount(ROUND_ID, "total_bet_amount", String.valueOf(totalAmount));

                // 创建扣除金币记录
                RegularGameBillReq regularGameBillReq = new RegularGameBillReq();
                regularGameBillReq.setJoyType(1);
                regularGameBillReq.setGold(req.getAmount());
                regularGamesService.createBill(uid, regularGameBillReq);
            }
            Map<String, String> fruitMachineNameMultiple = fruitMachineCache.getFruitMachineNameMultiple();
            List<String> bets = reqs.stream().map(BetAmountReq::getBet).toList();
            fruitMachineNameMultiple.forEach((k,v) -> {
                if (!bets.contains(k)) {
                    BetAmountReq req = new BetAmountReq();
                    req.setBet(k);
                    req.setAmount(0L);
                    reqs.add(req);
                }
            });
            // 发送下注更新通知
            GamePayloadDTO betUpdateDto = new GamePayloadDTO();
            BetDTO betDTO = new BetDTO();
            betDTO.setUid(uid);
            betDTO.setReqs(reqs);
            betUpdateDto.setType(GamePushType.bet.getType());
            betUpdateDto.setData(betDTO);
            longLinkService.pushGameMsg(null, betUpdateDto, PushEvent.fruit_machine_event, PushToType.MESSAGE_TO_ALL);

            log.info("用户下注成功: uid={}, 金额={}, 回合={}", uid, totalBetAmount, ROUND_ID);
        } catch (ApiException e) {
            log.warn("用户下注失败: uid={}, 错误={}", uid, e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("用户下注失败: uid={}, 错误={}", uid, e.getMessage(), e);
            throw new ApiException(CodeEnum.SERVER_BUSY);
        }
    }

    //开奖奖品
    public GameFruitMachineConfig draw() {
        //重置奖池
        List<GameFruitMachineConfig> gameFruitMachineConfigs = fruitMachineConfigService.list();
        if (fruitMachineCache.getFruitMachinePoolSize() <= 0) {
            if (CollectionUtils.isNotEmpty(gameFruitMachineConfigs)) {
                List<String> pool = new ArrayList<>();
                Map<String, String> maps = new HashMap<>();
                for (GameFruitMachineConfig gameFruitMachineConfig : gameFruitMachineConfigs) {
                    for (int i = 0; i < gameFruitMachineConfig.getProb(); i++) {
                        pool.add(gameFruitMachineConfig.getName());
                    }
                    maps.put(gameFruitMachineConfig.getName(), JSONUtil.toJsonStr(gameFruitMachineConfig));
                }
                fruitMachineCache.setFruitMachineNameMultiple(maps);
                Collections.shuffle(pool);
                fruitMachineCache.setFruitMachinePool(pool);
            }
        }
        Map<String, String> fruitMachineNameMultiple = fruitMachineCache.getFruitMachineNameMultiple();
        String fruitMachine = fruitMachineCache.getFruitMachine();

        return JSONUtil.toBean(fruitMachineNameMultiple.get(fruitMachine), GameFruitMachineConfig.class);
    }
}
