package com.simi.lobby;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.dto.GameAllWinListDTO;
import com.simi.common.dto.banner.BannerResp;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.LobbyStatisticsResp;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.RoomRedisKey;
import com.simi.entity.Banner;
import com.simi.entity.agent.AgentAffiliation;
import com.simi.mapper.GameFruitMachineBetRecordMapper;
import com.simi.mapper.GameRoomViewRecordMapper;
import com.simi.service.BannerServerService;
import com.simi.service.BannerService;
import com.simi.service.LongLinkService;
import com.simi.service.user.UserServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RequiredArgsConstructor
public class GameAllLobbyManage {
    private final RedissonManager redissonManager;
    private final UserServerService userServerService;
    private final LongLinkService longLinkService;
    private final GameFruitMachineBetRecordMapper gameFruitMachineBetRecordMapper;
    private final GameRoomViewRecordMapper gameRoomViewRecordMapper;
    private final BannerService bannerService;

    public void updateState(Long uid, int state) {
        //关闭
        if (state == 0) {
            redissonManager.del(RoomRedisKey.rome_owner_maintenance_lobby.getKey(), uid.toString());
        }
        //打开
        if (state == 1) {
            redissonManager.hSet(RoomRedisKey.rome_owner_maintenance_lobby.getKey(), uid.toString(), "open");
        }
        //通知房间的用户
        UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(uid);
        longLinkService.pushLobbyStatueMsg(userBaseInfoDTO.getRoomId(), state, PushEvent.RoomLobbyStatusEvent, PushToType.MESSAGE_TO_ALL);
    }

    public List<GameAllWinListDTO> getLobbyWinList(Long uid, int start, int end) {
        List<String> jsonList = redissonManager.lRange(RoomRedisKey.game_all_win_list.getKey(), start, end);
        if (jsonList == null || jsonList.isEmpty()) {
            return Collections.emptyList();
        }
        List<GameAllWinListDTO> list = jsonList.stream()
                .map(json -> {
                    try {
                        return JSON.parseObject(json, GameAllWinListDTO.class);
                    } catch (Exception e) {
                        log.error("解析Lobby中奖记录失败: {}", json, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());

        LanguageEnum languageEnum = userServerService.userAppLanguage(uid);

        List<Banner> banners = bannerService.list(Wrappers.<Banner>lambdaQuery()
                .eq(Banner::getPosition, 5));
        String smallIcon = null;
        if (!banners.isEmpty()) {
            Banner banner = banners.get(0);
            smallIcon = banner.getCover() == null ? StrUtil.EMPTY : banner.getCover().getOrDefault(languageEnum.name(), StrUtil.EMPTY);
        }
        for (GameAllWinListDTO gameAllWinListDTO : list) {
            gameAllWinListDTO.setIcon(smallIcon);
        }
        return list;
    }

    /**
     * 添加大厅全服中奖记录，只保留最新100条
     */
    public void addLobby(UserBaseInfoDTO userBaseInfo, int multiple, long winAmount) {
        try {
            GameAllWinListDTO gameAllWinListDTO = new GameAllWinListDTO();
            gameAllWinListDTO.setAvatar(userBaseInfo.getAvatar());
            gameAllWinListDTO.setUid(userBaseInfo.getUid());
            gameAllWinListDTO.setNick(userBaseInfo.getNick());
            gameAllWinListDTO.setGameId(1);
            gameAllWinListDTO.setWin(winAmount);
            gameAllWinListDTO.setMultiple(multiple);
            // 添加到列表头部
            redissonManager.leftPush(RoomRedisKey.game_all_win_list.getKey(), JSON.toJSONString(gameAllWinListDTO));
            // 只保留最新100条
            redissonManager.trimList(RoomRedisKey.game_all_win_list.getKey(), 0, 99);
        } catch (Exception e) {
            log.error("异常", e);
        }
    }

    public LobbyStatisticsResp getLobbyStatisticsResp(Long uid, Date startTime, Date endTime, Integer type) {
        LobbyStatisticsResp resp = new LobbyStatisticsResp();
        try {
            UserBaseInfoDTO userBaseInfoDTO = userServerService.getUserBaseInfo(uid);
            Long roomId = Long.valueOf(userBaseInfoDTO.getRoomId());

            //--------------------1：统计总的日期----------------------------------
            // 计算上一周期
            long diffDays = ChronoUnit.DAYS.between(startTime.toInstant(), endTime.toInstant());
            Date lastStart = Date.from(startTime.toInstant().minus(diffDays, ChronoUnit.DAYS));
            Date lastEnd = Date.from(endTime.toInstant().minus(diffDays, ChronoUnit.DAYS));

            // 当前周期数据
            Long curPlayerCount = gameFruitMachineBetRecordMapper.countDistinctPlayer(startTime, endTime, roomId);
            Long curTotalBet = gameFruitMachineBetRecordMapper.sumTotalBet(startTime, endTime, roomId);
            Long curViewerCount = gameRoomViewRecordMapper.countDistinctViewer(startTime, endTime, roomId);

            // 上一周期数据
            Long lastPlayerCount = gameFruitMachineBetRecordMapper.countDistinctPlayer(lastStart, lastEnd, roomId);
            Long lastTotalBet = gameFruitMachineBetRecordMapper.sumTotalBet(lastStart, lastEnd, roomId);
            Long lastViewerCount = gameRoomViewRecordMapper.countDistinctViewer(lastStart, lastEnd, roomId);

            // 计算环比
            resp.setPlayer(buildTrendMetric(curPlayerCount, lastPlayerCount));
            resp.setBet(buildTrendMetric(curTotalBet, lastTotalBet));
            resp.setViewer(buildTrendMetric(curViewerCount, lastViewerCount));

            //--------------------2：走势图----------------------------------
            resp.setTrends(buildTrends(startTime, endTime, roomId));

            //--------------------3：游戏统计----------------------------------
            resp.getGameStats().add(buildGameTrend(startTime, endTime, roomId));

            //--------------------4：排行榜----------------------------------
            resp.setRankList(gameFruitMachineBetRecordMapper.getGameTrends(startTime, endTime, roomId));

        } catch (Exception e) {
            log.error("Exception ", e);
        }
        return resp;
    }

    private LobbyStatisticsResp.TrendMetric buildTrendMetric(Long current, Long previous) {
        LobbyStatisticsResp.TrendMetric metric = new LobbyStatisticsResp.TrendMetric();
        metric.setValue(current == null ? 0L : current);

        if (previous == null || previous == 0) {
            // 无对比数据
            metric.setRatio(BigDecimal.ZERO);
            metric.setTrendType(2);
        } else {
            BigDecimal cur = BigDecimal.valueOf(current == null ? 0 : current);
            BigDecimal pre = BigDecimal.valueOf(previous);
            BigDecimal diff = cur.subtract(pre);
            BigDecimal ratio = diff.multiply(BigDecimal.valueOf(100)).divide(pre, 2, RoundingMode.HALF_UP);
            metric.setRatio(ratio);

            int cmp = diff.compareTo(BigDecimal.ZERO);
            if (cmp > 0) {
                metric.setTrendType(1); // 上升
            } else if (cmp < 0) {
                metric.setTrendType(-1); // 下降
            } else {
                metric.setTrendType(0); // 无变化
            }
        }

        return metric;
    }


    public List<LobbyStatisticsResp.Trend> buildTrends(Date startTime, Date endDate, Long roomId) {
        List<LobbyStatisticsResp.Trend> trends = new ArrayList<>();

        // 转换为 LocalDate 处理
        LocalDate start = toLocalDate(startTime);
        LocalDate end = toLocalDate(endDate);

        for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
            // 每天开始和结束时间
            Date beginTime = toDate(date.atStartOfDay());
            Date finishTime = toDate(date.atTime(LocalTime.MAX));

            // 查询当日下注总额
            Long sum = gameFruitMachineBetRecordMapper.sumTotalBet(beginTime, finishTime, roomId);

            // 构建趋势数据
            LobbyStatisticsResp.Trend trend = new LobbyStatisticsResp.Trend();
            trend.setLabel(date.toString()); // yyyy-MM-dd
            trend.setValue(sum);
            trends.add(trend);
        }

        return trends;
    }

    private LocalDate toLocalDate(Date date) {
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    private Date toDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    private LobbyStatisticsResp.GameTrend buildGameTrend(Date start, Date end, Long roomId) {
        Long current = gameFruitMachineBetRecordMapper.sumTotalBet(start, end, roomId);

        long duration = end.getTime() - start.getTime();
        Date prevStart = new Date(start.getTime() - duration);
        Date prevEnd = new Date(end.getTime() - duration);
        Long previous = gameFruitMachineBetRecordMapper.sumTotalBet(prevStart, prevEnd, roomId);

        LobbyStatisticsResp.TrendMetric metric = new LobbyStatisticsResp.TrendMetric();
        metric.setValue(current != null ? current : 0L);

        if (previous != null && previous > 0) {
            BigDecimal ratio = BigDecimal.valueOf(current - previous)
                    .multiply(BigDecimal.valueOf(100))
                    .divide(BigDecimal.valueOf(previous), 2, RoundingMode.HALF_UP);
            metric.setRatio(ratio);

            int cmp = current.compareTo(previous);
            if (cmp > 0) {
                metric.setTrendType(1); // 上升
            } else if (cmp < 0) {
                metric.setTrendType(-1); // 下降
            } else {
                metric.setTrendType(0); // 无变化
            }
        } else {
            // 无法对比的情况
            metric.setRatio(BigDecimal.ZERO);
            metric.setTrendType(2); // 无对比数据
        }

        LobbyStatisticsResp.GameTrend trend = new LobbyStatisticsResp.GameTrend();
        trend.setGameId(1);
        trend.setMetric(metric);

        return trend;
    }

}
