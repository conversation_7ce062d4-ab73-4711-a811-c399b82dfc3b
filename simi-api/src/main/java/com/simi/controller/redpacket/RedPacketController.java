package com.simi.controller.redpacket;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.dto.redpacket.RedPacketConfigDTO;
import com.simi.common.vo.redpacket.RedPacketGrabVO;
import com.simi.common.vo.redpacket.RedPacketHaveBoxVO;
import com.simi.common.vo.redpacket.RedPacketVO;
import com.simi.common.vo.req.redpacket.GrabRedPacketReq;
import com.simi.common.vo.req.redpacket.SendRedPacketReq;
import com.simi.service.redpacket.RedPacketServerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/10 14:09
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/redPacket")
@Tag(name = "红包", description = "红包")
public class RedPacketController {

    private final RedPacketServerService redPacketServerService;

    @Operation(summary = "发送")
    @PostMapping(value = "/push")
    public BusiResult<RedPacketVO> push(HttpServletRequest request, @RequestBody @Valid SendRedPacketReq req){
        var sessionUtil = new SessionUtil(request);
        RedPacketVO result = redPacketServerService.push(req, sessionUtil.currentUid());
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "获取配置")
    @GetMapping(value = "/config")
    public BusiResult<?> configVO(@RequestParam(required = false) String roomId, HttpServletRequest request){
        SessionUtil sessionUtil = new SessionUtil(request);
        RedPacketConfigDTO result = redPacketServerService.configVO(sessionUtil.currentUid(), sessionUtil.getXAuthToken(), roomId);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "列表")
    @GetMapping(value = "/list")
    public BusiResult<?> redPacketVOS(@RequestParam String roomId){
        List<RedPacketVO> result = redPacketServerService.redPacketVOS(roomId);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "抢")
    @PostMapping(value = "/grab")
    public BusiResult<RedPacketGrabVO> grab(HttpServletRequest request, @RequestBody @Valid GrabRedPacketReq req){
        var sessionUtil = new SessionUtil(request);
        RedPacketGrabVO result = redPacketServerService.grab(sessionUtil.currentUid(), req.getLuckyBagId());
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "通过roomId判断是否有宝箱")
    @PostMapping(value = "/haveBox")
    public BusiResult<List<RedPacketHaveBoxVO>> haveBox(@RequestBody List<String> roomIds){
        List<RedPacketHaveBoxVO> result = redPacketServerService.haveBox(roomIds);
        return BusiResultUtil.success(result);
    }


}
