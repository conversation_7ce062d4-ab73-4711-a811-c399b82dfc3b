package com.simi.controller.room;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.req.RoomPushMsgReq;
import com.simi.common.vo.req.RoomWelcomeContentPushReq;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.dto.game.UserWinnerDTO;
import com.simi.service.room.RoomMsgService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.security.NoSuchAlgorithmException;

@RestController
@Tag(name = "房间公屏消息", description = "房间公屏消息")
@RequestMapping("/api/room/msg")
public class RoomMsgController {

    @Autowired
    private RoomMsgService roomMsgService;

    @PostMapping(value = "/push")
    @Operation(summary = "发送房间公屏消息")
    public BusiResult<?> pushMsg(HttpServletRequest request, @RequestBody RoomPushMsgReq req) throws NoSuchAlgorithmException {
        var sessionUtil = new SessionUtil(request);
        roomMsgService.pushMsg(sessionUtil.currentUid(),req.getEvent(),req.getRoomId(),req.getData());
        return BusiResultUtil.success();
    }

    @PostMapping(value = "/pushWelcomeContent")
    @Operation(summary = "发送欢迎消息")
    public BusiResult<?> pushWelcomeContent(HttpServletRequest request, @RequestBody RoomWelcomeContentPushReq req) throws NoSuchAlgorithmException {
        SessionUtil sessionUtil = new SessionUtil(request);
        Long uid = sessionUtil.currentUid();
        roomMsgService.pushWelcomeContent(req,uid);
        return BusiResultUtil.success();
    }

    @GetMapping(value = "/game/winner")
    @Operation(summary = "发送欢迎消息")
    public BusiResult<?> gameWinner(HttpServletRequest request,
                                    @RequestParam(required = false) String roomId) {
        SessionUtil sessionUtil = new SessionUtil(request);
        Long uid = sessionUtil.currentUid();
        roomMsgService.gameWinner(uid,roomId);
        return BusiResultUtil.success();
    }


}
