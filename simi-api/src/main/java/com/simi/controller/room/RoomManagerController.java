package com.simi.controller.room;

import com.google.common.collect.Lists;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.room.RoomIdentity;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.room.RoomUserBaseDTO;
import com.simi.common.entity.user.NextEventData;
import com.simi.common.vo.RoomManagerVo;
import com.simi.common.vo.RoomUserBaseVo;
import com.simi.common.vo.req.RoomManagerCancelReq;
import com.simi.common.vo.req.RoomRemoveBlacklistReq;
import com.simi.common.vo.req.RoomSilenceReq;
import com.simi.common.vo.room.RoomBalckListVO;
import com.simi.common.vo.room.RoomSilenceOptionVO;
import com.simi.common.vo.room.RoomSilenceVO;
import com.simi.service.room.RoomManagerHighService;
import com.simi.service.user.UserServerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 房间管理员
 * <AUTHOR>
 * @Date: 2023/11/8
 */
@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("api/room/manager")
@Tag(name = "房间管理相关", description = "房间管理员相关")
public class RoomManagerController {

    private final RoomManagerHighService roomManagerHighService;
    private final UserServerService userServerService;

    @GetMapping(value = "/get/info")
    @Operation(summary = "管理员列表")
    public BusiResult<List<RoomUserBaseDTO>> listRoomManager(HttpServletRequest request, @RequestParam("roomId") String roomId){
        var sessionUtil = new SessionUtil(request);
        RoomManagerVo vo = roomManagerHighService.listRoomManager(sessionUtil.currentUid(), roomId);
        List<RoomUserBaseVo> userList = vo.getUserList();
        List<RoomUserBaseDTO> dtoList = Lists.newArrayListWithCapacity(userList.size());
        if (CollectionUtils.isNotEmpty(userList)){
            List<Long> uids = userList.stream().map(RoomUserBaseVo::getUid).collect(Collectors.toList());
            List<UserBaseInfoDTO> userBaseInfoDTOS = userServerService.listUserDTO(uids);
            Map<Long, UserBaseInfoDTO> dtoMap = new HashMap<>();
            if (userBaseInfoDTOS != null) {
                dtoMap = userBaseInfoDTOS.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
            }
            for (RoomUserBaseVo userBaseVo : userList) {
                RoomUserBaseDTO dto = new RoomUserBaseDTO();
                UserBaseInfoDTO userBaseInfoDTO = dtoMap.get(userBaseVo.getUid());
                if (userBaseInfoDTO != null) {
                    dto.setUserBase(userBaseInfoDTO);
                }
                dto.setRoomIdentity(RoomIdentity.getByType(userBaseVo.getIdentity()));
                dtoList.add(dto);
            }
        }
        return BusiResultUtil.success(dtoList);
    }

    @PostMapping(value = "/add")
    @Operation(summary = "添加管理员")
    public BusiResult<NextEventData> addRoomManager(HttpServletRequest request, @RequestBody RoomManagerCancelReq req){
        var sessionUtil = new SessionUtil(request);
        NextEventData eventData = roomManagerHighService.addRoomManager(sessionUtil.currentUid(), req.getRoomId(), req.getTargetUid());
        return BusiResultUtil.success(eventData);
    }

    @PostMapping(value = "/cancel")
    @Operation(summary = "移除管理员")
    public BusiResult<NextEventData> cancelRoomManager(HttpServletRequest request, @RequestBody RoomManagerCancelReq req){
        var sessionUtil = new SessionUtil(request);
        NextEventData eventData = roomManagerHighService.cancelRoomManager(sessionUtil.currentUid(), req.getRoomId(), req.getTargetUid());
        return BusiResultUtil.success(eventData);
    }


    @GetMapping(value = "/blacklist")
    @Operation(summary = "黑名单列表")
    public BusiResult<List<RoomBalckListVO>> blackList(@RequestParam("roomId") String roomId){

        List<RoomBalckListVO> result = roomManagerHighService.blacklist(roomId);

        return BusiResultUtil.success(result);
    }


    @GetMapping(value = "/silenceList")
    @Operation(summary = "禁言列表")
    public BusiResult<List<RoomSilenceVO>> silenceList(@RequestParam("roomId") String roomId){

        List<RoomSilenceVO> result = roomManagerHighService.silenceList(roomId);

        return BusiResultUtil.success(result);
    }


    @PostMapping(value = "/silence")
    @Operation(summary = "禁言/取消")
    public BusiResult<?> silence(HttpServletRequest request, @RequestBody RoomSilenceReq req){
        SessionUtil sessionUtil = new SessionUtil(request);
        roomManagerHighService.silence(sessionUtil.currentUid(), req);

        return BusiResultUtil.success();
    }

    @GetMapping(value = "/silenceOption")
    @Operation(summary = "禁言选项列表")
    public BusiResult<List<RoomSilenceOptionVO>> silenceOption(){
        List<RoomSilenceOptionVO> result = roomManagerHighService.silenceOption();
        return BusiResultUtil.success(result);
    }


    @PostMapping(value = "/removeBlacklist")
    @Operation(summary = "移出房间黑名单")
    public BusiResult<?> removeBlacklist(HttpServletRequest request, @RequestBody @Validated  RoomRemoveBlacklistReq req){
        SessionUtil sessionUtil = new SessionUtil(request);
        roomManagerHighService.removeBlacklist(sessionUtil.currentUid(), req.getRoomId(), req.getTargetUid());
        return BusiResultUtil.success();
    }
}
