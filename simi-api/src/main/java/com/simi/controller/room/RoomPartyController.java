package com.simi.controller.room;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.google.gson.Gson;
import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.audit.AuditManage;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.RewardPackCopywritingEnum;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.activity.prize.PrizeDTO;
import com.simi.common.dto.room.RoomPartyTagDTO;
import com.simi.common.dto.room.RoomPartyUserDataDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.vo.UserSimpleVO;
import com.simi.common.vo.req.AttestationTagSaveOrUpdateReq;
import com.simi.common.vo.req.roomParty.*;
import com.simi.common.vo.room.RoomPartyDetailVO;
import com.simi.common.vo.room.RoomPartyVO;
import com.simi.common.vo.room.SingleRoomPartyVO;
import com.simi.entity.room.Room;
import com.simi.service.rewardpack.RewardPackServerService;
import com.simi.service.room.RoomService;
import com.simi.service.room.party.RoomPartyServerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/30 11:53
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/room/party")
@Tag(name = "房间party", description = "房间party")
public class RoomPartyController {

    private final RoomPartyServerService roomPartyServerService;
    private final RewardPackServerService rewardPackServerService;
    private final RoomService roomService;
    private final AuditManage auditManage;

    @Operation(summary = "party标签列表")
    @GetMapping(value = "/tagList")
    public BusiResult<List<RoomPartyTagDTO>> tagList() {
        List<RoomPartyTagDTO> result = roomPartyServerService.tagList();
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "创建party")
    @PostMapping(value = "/create")
    public BusiResult<Long> createParty(HttpServletRequest request, @RequestBody @Valid RoomPartyCreateReq req) {
        var sessionUtil = new SessionUtil(request);
        Long partyId = roomPartyServerService.createParty(req, sessionUtil.currentUid());
        return BusiResultUtil.success(partyId);
    }

    @Operation(summary = "修改party")
    @PostMapping(value = "/update")
    public BusiResult<?> beginPk(HttpServletRequest request, @RequestBody @Valid RoomPartyUpdateReq req) {
        var sessionUtil = new SessionUtil(request);
        roomPartyServerService.updateParty(req, sessionUtil.currentUid());
        return BusiResultUtil.success();
    }

    @Operation(summary = "订阅或取消")
    @PostMapping(value = "/subscribe")
    public BusiResult<RoomPartyVO> subscribe(HttpServletRequest request, @RequestBody @Valid RoomPartySubscribeReq req) {
        var sessionUtil = new SessionUtil(request);
        RoomPartyVO result = roomPartyServerService.subscribe(req, sessionUtil.currentUid());
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "取消活动")
    @PostMapping(value = "/cancle")
    public BusiResult<?> cancle(HttpServletRequest request, @RequestBody @Valid RoomPartyCancleReq req) {
        var sessionUtil = new SessionUtil(request);
        roomPartyServerService.cancle(req, sessionUtil.currentUid());
        return BusiResultUtil.success();
    }

    @Operation(summary = "前置检查")
    @PostMapping(value = "/preCheck")
    public BusiResult<?> preCheck(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        roomPartyServerService.preCheck(sessionUtil.currentUid());
        return BusiResultUtil.success();
    }

    @Operation(summary = "party列表")
    @Parameters({
            @Parameter(name = "type", description = "状态 1-进行中; 2-未开始; 3-已订阅; 4-我的创建;", in = ParameterIn.QUERY),
            @Parameter(name = "pageNum", description = "分页页数", in = ParameterIn.QUERY),
            @Parameter(name = "pageSize", description = "分页大小", in = ParameterIn.QUERY),
    })
    @GetMapping(value = "/list")
    public BusiResult<List<RoomPartyVO>> partyList(HttpServletRequest request,
                                                   @RequestParam(value = "type") Integer type,
                                                   @RequestParam(defaultValue = "1") Integer pageNum,
                                                   @RequestParam(defaultValue = "10") Integer pageSize) {
        List<RoomPartyVO> result;
        var sessionUtil = new SessionUtil(request);
        XAuthToken xAuthToken = sessionUtil.getXAuthToken();
        boolean interfere = auditManage.isAudit(sessionUtil.currentUid(), xAuthToken);
        //判断是否进入审核数据
        if (interfere) {
            result = auditManage.auditParty(xAuthToken, type, sessionUtil.currentUid(), pageNum, pageSize);
        }
        //正常返回数据
        else {
            result = roomPartyServerService.partyList(type, sessionUtil.currentUid(), pageNum, pageSize);
        }
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "party详情")
    @Parameters({
            @Parameter(name = "partyId", description = "party对应id", in = ParameterIn.QUERY),
    })
    @GetMapping(value = "/detail")
    public BusiResult<RoomPartyDetailVO> partyDetail(HttpServletRequest request,
                                                     @RequestParam(value = "partyId") Long partyId) {
        var sessionUtil = new SessionUtil(request);
        RoomPartyDetailVO result = roomPartyServerService.partyDetail(partyId, sessionUtil.currentUid());
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "订阅用户列表")
    @Parameters({
            @Parameter(name = "partyId", description = "party对应id", in = ParameterIn.QUERY),
            @Parameter(name = "pageNum", description = "页码", in = ParameterIn.QUERY),
            @Parameter(name = "pageSize", description = "页长", in = ParameterIn.QUERY),
    })
    @GetMapping(value = "/followerList")
    public BusiResult<ListWithTotal<UserSimpleVO>> followerList(HttpServletRequest request,
                                                                @RequestParam(value = "partyId") Long partyId,
                                                                @RequestParam(defaultValue = "1") Integer pageNum,
                                                                @RequestParam(defaultValue = "10") Integer pageSize) {
        var sessionUtil = new SessionUtil(request);
        ListWithTotal<UserSimpleVO> result = roomPartyServerService.followerList(partyId, pageNum, pageSize);
        return BusiResultUtil.success(result);
    }

    @Operation(summary = "流水数据榜单")
    @Parameters({
            @Parameter(name = "partyId", description = "party对应id", in = ParameterIn.QUERY),
            @Parameter(name = "type", description = "1-送礼; 2-收礼", in = ParameterIn.QUERY),
    })
    @GetMapping(value = "/rank")
    public BusiResult<ListWithTotal<RoomPartyUserDataDTO>> dataRank(HttpServletRequest request,
                                                                    @RequestParam(value = "partyId") Long partyId,
                                                                    @RequestParam(value = "type") Integer type,
                                                                    @RequestParam(defaultValue = "1") Integer pageNum,
                                                                    @RequestParam(defaultValue = "10") Integer pageSize) {
        var sessionUtil = new SessionUtil(request);
        ListWithTotal<RoomPartyUserDataDTO> result = roomPartyServerService.dataRank(partyId, type, pageNum, pageSize, false);
        return BusiResultUtil.success(result);
    }

    /**
     * 通过房间筛选party
     *
     * @return
     */
    @PostMapping(value = "/getPartyListByRoomId")
    @Operation(summary = "通过房间筛选party")
    public BusiResult<List<SingleRoomPartyVO>> getPartyListByRoomId(HttpServletRequest request, @RequestBody @Validated RoomPartyFilterReq roomPartyFilterReq) {
        var sessionUtil = new SessionUtil(request);
        List<SingleRoomPartyVO> result = roomPartyServerService.getPartyListByRoomId(sessionUtil.currentUid(), roomPartyFilterReq, true);
        return BusiResultUtil.success(result);
    }

    @GetMapping(value = "/go/task/list")
    @Operation(summary = "获取任务列表")
    public Map goTaskList(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/task/list"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("party goTaskList:[{}]", response.body());
            Gson gson = new Gson();
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goTaskList error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/task/reward/claim/{taskType}")
    @Operation(summary = "获取任务列表")
    public Map taskRewardClaim(HttpServletRequest req, @PathVariable String taskType) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/task/reward/claim/" + taskType))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party taskRewardClaim:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party taskRewardClaim error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/rank/best-party")
    @Operation(summary = "获取任务列表")
    public Map rankBestParty(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/rank/best-party"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party rankBestParty:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party rankBestParty error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/rank/top-supporter")
    @Operation(summary = "获取任务列表")
    public Map goRankTopSupporter(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/rank/top-supporter"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("party goRankTopSupporter:[{}]", response.body());
            Gson gson = new Gson();
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goRankTopSupporter error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/rank/me")
    @Operation(summary = "获取任务列表")
    public Map goRankMe(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/rank/me"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party goRankMe:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goRankMe error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/rank/top-star")
    @Operation(summary = "获取任务列表")
    public Map goRankTopStar(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/rank/top-star"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party goRankTopStar:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goRankTopStar error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/reward/best-party")
    @Operation(summary = "获取任务列表")
    public Map goRewardBestParty(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/reward/best-party"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party goRewardBestParty:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goRewardBestParty error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/reward/top-supporter")
    @Operation(summary = "获取任务列表")
    public Map goRewardTopSupporter(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/reward/top-supporter"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party goRewardTopSupporter:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goRewardTopSupporter error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/reward/top-star")
    @Operation(summary = "获取任务列表")
    public Map goRewardTopStar(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-party-carnival:3000/party-carnival/api/reward/top-star"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("party goRewardTopStar:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("party goRewardTopStar error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }

    @GetMapping(value = "/go/sendReward")
    @DoNotCheckSimiMPAuthStatus
    public BusiResult<?> sendReward(@RequestParam("prizeId") Integer prizeId, @RequestParam("uid") Long uid) {
        try {
            String bizId = StrUtil.format("{}{}",
                    DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN),
                    Math.abs(RandomUtil.randomInt(999999999)));
            PrizeDTO prizeDTO = PrizeDTO.builder()
                    .uid(uid)
                    .packId(prizeId)
                    .bizOrderId(Long.parseLong(bizId))
                    .sourceTypeEnum(RewardPackCopywritingEnum.PARTY_ACTIVITY_REWARD).build();
            log.info("room party go sendReward:[{}]", JSONUtil.toJsonStr(prizeDTO));
            rewardPackServerService.sendRewardPack(prizeDTO);
        } catch (NumberFormatException e) {
            log.info("room party go sendReward failed, prizeId:[{}]  uid:[{}] err:[{}]", prizeId, uid, ExceptionUtil.formatEx(e));
        }
        return BusiResultUtil.success();
    }

    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/sendAttestationTag")
    public BusiResult<Long> sendAttestationTag(@RequestBody AttestationTagSaveOrUpdateReq req, HttpServletRequest request) {
        Long result = roomPartyServerService.sendAttestationTag(req);
        return BusiResultUtil.success(result);
    }

    @DoNotCheckSimiMPAuthStatus
    @GetMapping(value = "/delAttestationTag")
    public BusiResult<?> del(@RequestParam(value = "id") Integer id) {
        roomPartyServerService.delAttestationTag(id);
        return BusiResultUtil.success();
    }


    @DoNotCheckSimiMPAuthStatus
    @GetMapping(value = "/room/me")
    public BusiResult<String> roomMe(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        Room room = roomService.getRoomByUid(sessionUtil.currentUid());
        if (Objects.nonNull(room)) {
            return BusiResultUtil.success(room.getId());
        }
        return BusiResultUtil.failed();
    }
}
