package com.simi.controller;

import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.PositionEnum;
import com.simi.common.dto.banner.BannerResp;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.vo.req.BannerListReq;
import com.simi.common.vo.resp.BannerFunctionResp;
import com.simi.service.BannerServerService;
import com.simi.service.FunctionBannerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * banner控制器
 *
 * <AUTHOR>
 * @date 2023/11/11 10:50
 */
@Slf4j
@RestController
@RequestMapping("api/resource")
@RequiredArgsConstructor
@Tag(name = "banner", description = "banner")
public class BannerController {

    private final BannerServerService bannerServerService;
    private final FunctionBannerService functionBannerService;

    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/banner")
    @Operation(summary = "banner列表", description = "不同类型不同入参")
    public BusiResult<BannerResp> list(@RequestBody BannerListReq req, HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        XAuthToken xAuthToken = sessionUtil.getXAuthToken();
        PositionEnum positionEnum = PositionEnum.forNumber(req.getPosition());
        BannerResp bannerResp = bannerServerService.normalBanners(positionEnum, sessionUtil.currentUid(), xAuthToken);
        return BusiResultUtil.success(bannerResp);
    }

    @DoNotCheckSimiMPAuthStatus
    @GetMapping(value = "/function")
    @Operation(summary = "功能生效功能列表")
    public BusiResult<List<BannerFunctionResp>> functionList(HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        List<BannerFunctionResp> bannerResp = functionBannerService.normalBanners(sessionUtil.currentUid());
        return BusiResultUtil.success(bannerResp);
    }
}
