package com.simi.controller.third;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.dto.TokenDTO;
import com.simi.service.LongLinkService;
import com.simi.service.tencent.TencentIMComponent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/token")
@RequiredArgsConstructor
@Tag(name = "token", description = "token")
public class TokenController {
    private final TencentIMComponent tencentIMComponent;
    private final LongLinkService longLinkService;

    @Operation(summary = "获取Tim 签名")
    @GetMapping(value = "/tim")
    public BusiResult<TokenDTO> getTimSign(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        return BusiResultUtil.success(tencentIMComponent.genUserSig(sessionUtil.currentUid().toString()));
    }
    
    @Operation(summary = "获取Long Link Token")
    @GetMapping(value = "/long-link")
    public BusiResult<String> getLongLinkToken(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        return BusiResultUtil.success(longLinkService.genAuthToken(sessionUtil.currentUid()));
    }
}
