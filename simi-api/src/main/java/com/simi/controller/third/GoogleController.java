package com.simi.controller.third;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.vo.login.LoginReq;
import com.simi.service.oauth2.login.GoogleApiComponent;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("api/google")
public class GoogleController {

    @Autowired
    private GoogleApiComponent googleApiComponent;

    /**
     * 谷歌三方接口
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "官网登录")
    @PostMapping(value = "/login")
    public BusiResult googleOauth2TokenInfo(@RequestBody LoginReq loginReq) {
        //LoginResp tokenDTO = googleApiComponent.googleOauth2TokenInfo(loginReq);
        return BusiResultUtil.success();
    }
}
