package com.simi.controller.wheel;

import com.alibaba.fastjson2.JSON;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.req.wheel.CreateLuckyWheelReq;
import com.simi.common.vo.req.wheel.LuckyWheelDetailReq;
import com.simi.common.vo.req.wheel.RoomLuckyWheelReq;
import com.simi.common.vo.wheel.CreateLuckyWheelVO;
import com.simi.common.vo.wheel.LuckyWheelDetailVO;
import com.simi.service.wheel.LuckyWheelApiService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 幸运转盘
 */
@Slf4j
@RestController
@RequestMapping("api/wheel")
@RequiredArgsConstructor
@Tag(name = "幸运转盘", description = "幸运转盘接口")
public class LuckyWheelController {
    private final LuckyWheelApiService luckyWheelApiService;

    @Operation(summary = "创建幸运装盘")
    @PostMapping(value = "/createLuckyWheel")
    public BusiResult<CreateLuckyWheelVO> createLuckyWheel(HttpServletRequest request, @Validated @RequestBody CreateLuckyWheelReq createLuckyWheelReq){
        Long uid = new SessionUtil(request).currentUid();
        CreateLuckyWheelVO luckyWheel = luckyWheelApiService.createLuckyWheel(uid, createLuckyWheelReq);
        log.info("createLuckyWheel success,uid:{},params:{},result:{}", uid, JSON.toJSONString(createLuckyWheelReq));
        return BusiResultUtil.success(luckyWheel);
    }


    @Operation(summary = "获取幸运装盘详情")
    @PostMapping(value = "/getLuckyWheelDetail")
    public BusiResult<LuckyWheelDetailVO> getLuckyWheelDetail(@RequestBody RoomLuckyWheelReq roomLuckyWheelReq){
        LuckyWheelDetailVO luckyWheelDetail = luckyWheelApiService.getLuckyWheelDetail(roomLuckyWheelReq);
        log.info("getLuckyWheelDetail success,params:{},result:{}", JSON.toJSONString(roomLuckyWheelReq),JSON.toJSONString(luckyWheelDetail));
        return BusiResultUtil.success(luckyWheelDetail);
    }

    @Operation(summary = "开始转盘")
    @PostMapping(value = "/startLuckyWheel")
    public BusiResult<String> startLuckyWheel(HttpServletRequest request, @RequestBody LuckyWheelDetailReq luckyWheelDetailReq){
        Long uid = new SessionUtil(request).currentUid();
        luckyWheelApiService.startLuckyWheel(uid,luckyWheelDetailReq);
        return BusiResultUtil.success("操作成功");
    }

    @Operation(summary = "取消转盘")
    @PostMapping(value = "/cancelLuckyWheel")
    public BusiResult<String> cancelLuckyWheel(HttpServletRequest request, @RequestBody LuckyWheelDetailReq luckyWheelDetailReq){
        Long uid = new SessionUtil(request).currentUid();
        luckyWheelApiService.cancelLuckyWheel(uid,luckyWheelDetailReq);
        return BusiResultUtil.success("操作成功");
    }

    @Operation(summary = "加入转盘")
    @PostMapping(value = "/joinLuckyWheel")
    public BusiResult<String> joinLuckyWheel(HttpServletRequest request, @RequestBody LuckyWheelDetailReq luckyWheelDetailReq){
        Long uid = new SessionUtil(request).currentUid();
        luckyWheelApiService.joinLuckyWheel(uid,luckyWheelDetailReq);
        return BusiResultUtil.success("操作成功");
    }
}