package com.simi.controller.infrastructure;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.req.ReportReq;
import com.simi.service.infrastructure.ReportService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("api/report")
@RequiredArgsConstructor
@Tag(name = "举报", description = "举报")
public class ReportController {

    private final ReportService reportService;

    @PostMapping()
    @Operation(summary = "举报用户/房间")
    public BusiResult<?> report(@RequestBody ReportReq req, HttpServletRequest request){
        var sessionUtil = new SessionUtil(request);
        reportService.report(req, sessionUtil.currentUid());
        return BusiResultUtil.success();
    }
}
