package com.simi.controller.activity;

import cn.hutool.core.map.MapUtil;
import com.google.gson.Gson;
import com.simi.common.base.SessionUtil;
import com.simi.common.util.ExceptionUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("api/activity")
@RequiredArgsConstructor
@Tag(name = "辅助go国庆活动", description = "辅助go国庆活动")
public class ActivityAuxiliaryGoController {

    @GetMapping(value = "/go/task/list")
    @Operation(summary = "获取任务列表")
    public Map goTaskList(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-saudi-national-day:3000/saudi-national-day/api/task/list"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("saudi-national-day goTaskList:[{}]", response.body());
            Gson gson = new Gson();
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("saudi-national-day goTaskList error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "/go/task/reward/claim/{taskType}")
    @Operation(summary = "获取任务列表")
    public Map taskRewardClaim(HttpServletRequest req, @PathVariable String taskType) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-saudi-national-day:3000/saudi-national-day/api/task/reward/claim/" + taskType))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("saudi-national-day taskRewardClaim:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("saudi-national-day taskRewardClaim error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "/go/rank/top-supporter")
    @Operation(summary = "获取任务列表")
    public Map goRankTopSupporter(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-saudi-national-day:3000/saudi-national-day/api/rank/top-supporter"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("saudi-national-day goRankTopSupporter:[{}]", response.body());
            Gson gson = new Gson();
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("saudi-national-day goRankTopSupporter error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "/go/rank/top-star")
    @Operation(summary = "获取任务列表")
    public Map goRankTopStar(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-saudi-national-day:3000/saudi-national-day/api/rank/top-star"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("saudi-national-day goRankTopStar:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("saudi-national-day goRankTopStar error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "/go/reward/top-supporter")
    @Operation(summary = "获取任务列表")
    public Map goRewardTopSupporter(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-saudi-national-day:3000/saudi-national-day/api/reward/top-supporter"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("saudi-national-day goRewardTopSupporter:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("saudi-national-day goRewardTopSupporter error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "/go/reward/top-star")
    @Operation(summary = "获取任务列表")
    public Map goRewardTopStar(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-saudi-national-day:3000/saudi-national-day/api/reward/top-star"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("saudi-national-day goRewardTopStar:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("saudi-national-day goRewardTopStar error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }



    @GetMapping(value = "v2/go/task/list")
    @Operation(summary = "获取任务列表")
    public Map goTaskListV2(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-iraq-national-day:3000/iraq-national-day/api/task/list"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("goTaskListV2 goTaskList:[{}]", response.body());
            Gson gson = new Gson();
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("iraq-national-day goTaskList error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "v2/go/task/reward/claim/{taskType}")
    @Operation(summary = "获取任务列表")
    public Map taskRewardClaimv2(HttpServletRequest req, @PathVariable String taskType) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-iraq-national-day:3000/iraq-national-day/api/task/reward/claim/" + taskType))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("iraq-national-day taskRewardClaim:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("iraq-national-day taskRewardClaimv2 error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "v2/go/rank/top-supporter")
    @Operation(summary = "获取任务列表")
    public Map goRankTopSupporterv2(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-iraq-national-day:3000/iraq-national-day/api/rank/top-supporter"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            log.info("iraq-national-day goRankTopSupporterv2:[{}]", response.body());
            Gson gson = new Gson();
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("iraq-national-day goRankTopSupporter error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "v2/go/rank/top-star")
    @Operation(summary = "获取任务列表")
    public Map goRankTopStarv2(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-iraq-national-day:3000/iraq-national-day/api/rank/top-star"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("iraq-national-day goRankTopStarv2:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("iraq-national-day goRankTopStarv2 error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "v2/go/reward/top-supporter")
    @Operation(summary = "获取任务列表")
    public Map goRewardTopSupporterv2(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-iraq-national-day:3000/iraq-national-day/api/reward/top-supporter"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("iraq-national-day goRewardTopSupporterv2:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("iraq-national-day goRewardTopSupporterv2 error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }


    @GetMapping(value = "v2/go/reward/top-star")
    @Operation(summary = "获取任务列表")
    public Map goRewardTopStarv2(HttpServletRequest req) {
        SessionUtil sessionUtil = new SessionUtil(req);
        HttpClient client = HttpClient.newHttpClient();

        HttpRequest request = HttpRequest.newBuilder()
                .uri(URI.create("http://simi-game-iraq-national-day:3000/iraq-national-day/api/reward/top-star"))
                .header("Content-Type", "application/json")
                .header("pub-uid", sessionUtil.getCustomHead("pub-uid"))
                .header("oauth-token", sessionUtil.getOauthToken())
                .header("x-auth-token", sessionUtil.getCustomHead("x-auth-token"))
                .GET()
                .build();
        try {
            HttpResponse<String> response = client.send(request, HttpResponse.BodyHandlers.ofString());
            Gson gson = new Gson();
            log.info("iraq-national-day goRewardTopStarv2:[{}]", response.body());
            return gson.fromJson(response.body(), Map.class);
        } catch (Exception e) {
            log.error("iraq-national-day goRewardTopStarv2 error:[{}]", ExceptionUtil.formatEx(e));
            return MapUtil.empty();
        }
    }
}
