package com.simi.controller.activity;

import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.activity.corban.CorbanActInfoVO;
import com.simi.common.vo.activity.corban.CorbanRechargeInfoVO;
import com.simi.service.activity.ActivityCorbanService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * 2024宰牲节活动
 *
 * <AUTHOR>
 * @date 2024/05/17 10:18
 **/
@Slf4j
@RestController
@RequestMapping("api/activity/corban")
@RequiredArgsConstructor
@Tag(name = "2024宰牲节活动", description = "2024宰牲节活动")
public class ActivityCorbanController {

    private final ActivityCorbanService activityCorbanService;

    @Operation(summary = "主题活动页信息")
    @GetMapping(value = "/info")
    public BusiResult<CorbanActInfoVO> info(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        CorbanActInfoVO info = activityCorbanService.info(sessionUtil.currentUid());
        return BusiResultUtil.success(info);
    }

    @Operation(summary = "领取奖励")
    @PostMapping(value = "/claim")
    public BusiResult<?> claim(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        activityCorbanService.claim(sessionUtil.currentUid());
        return BusiResultUtil.success();
    }


    @Operation(summary = "充值礼包信息")
    @GetMapping(value = "/rechargeTask")
    public BusiResult<CorbanRechargeInfoVO> rechargeTask() {
        CorbanRechargeInfoVO result = activityCorbanService.rechargePack();
        return BusiResultUtil.success(result);
    }

    /*@Operation(summary = "礼包购买排行榜")
    @Parameters({
            @Parameter(name = "packFlag", description = "礼包标志(充值礼包信息接口返回该字段)", in = ParameterIn.QUERY)
    })
    @GetMapping(value = "/buyPackRank")
    public BusiResult<GenericRankVO> buyPackRank(HttpServletRequest request, @RequestParam("packFlag") String packFlag) {
        var sessionUtil = new SessionUtil(request);
        GenericRankVO genericRankVO = activityCorbanService.buyPackRank(sessionUtil.currentUid(), packFlag);
        return BusiResultUtil.success(genericRankVO);
    }*/

    @PostMapping(value = "/test")
    @DoNotCheckSimiMPAuthStatus
    public BusiResult<?> test(@RequestBody() Map<Object, Object> object) {
        activityCorbanService.test(object);
        return BusiResultUtil.success();
    }
}
