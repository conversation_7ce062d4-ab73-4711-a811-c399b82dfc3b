package com.simi.controller.game3rd;

import cn.hutool.json.JSONUtil;
import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.constant.GameType;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.util.JsonUtils;
import com.simi.common.util.RedissonManager;
import com.simi.constant.BillEnum;
import com.simi.service.cache.TokenCache;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.user.UserServerService;
import com.simi.common.vo.req.game3rd.*;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.simi.common.constant.CodeEnum.SUCCESS_ZERO;

@Slf4j
@RestController
@RequestMapping("api/game3rd")
@RequiredArgsConstructor
@Tag(name = "第三方游戏", description = "第三方游戏")
public class Game3rdController {

    private final TokenCache tokenCache;
    private final UserServerService userServerService;
    private final PurseManageService purseManageService;
    private final RedissonManager redissonManager;
    private final MedalTaskService medalTaskService;


    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/get_channel_token")
    public BusiResult<GetChannelTokenResp> getChannelToken(@Valid @RequestBody GetChannelTokenReq req) {
        log.info("getChannelToken req:{}", JsonUtils.toJson(req));
        String token = tokenCache.getUidToken(Long.parseLong(req.getCUid()));
        log.info("getChannelToken token:{}", token);
        return BusiResultUtil.success(SUCCESS_ZERO, GetChannelTokenResp.builder()
                .token(token)
                .leftTime((long) (30 * 24 * 3600))
                .build());
    }

    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/refresh_channel_token")
    public BusiResult<RefreshChannelTokenResp> refreshChannelToken(@Valid @RequestBody RefreshChannelTokenReq req) {
        return BusiResultUtil.success(SUCCESS_ZERO, RefreshChannelTokenResp.builder()
                .token(tokenCache.getUidToken(Long.parseLong(req.getCUid())))
                .leftTime((long) (30 * 24 * 3600))
                .build());
    }


    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/get_channel_user_info")
    public BusiResult<GetChannelUserInfoResp> refreshChannelToken(@Valid @RequestBody GetChannelUserInfoReq req) {
        val userInfo = userServerService.getUserBaseInfo(Long.parseLong(req.getCUid()));
        return BusiResultUtil.success(SUCCESS_ZERO, GetChannelUserInfoResp.builder()
                .avatar(userInfo.getAvatar())
                .cUid(req.getCUid())
                .name(userInfo.getNick())
                .coins(purseManageService.getPurse(Long.parseLong(req.getCUid())).getCoin())
                .build());
    }

    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/create_channel_order")
    public BusiResult<List<CreateChannelOrderRespElement>> refreshChannelToken(@Valid @RequestBody CreateChannelOrderReq req) {
        var ret = new ArrayList<CreateChannelOrderRespElement>();
        for (var orderData : req.getData()) {
            StringBuilder builder = new StringBuilder();
            builder.append(orderData.getGameOrderId()).append(":").append(orderData.getGId());
            PurseDTO dto = new PurseDTO();
            if (GameType.LUDO.getNumber().equals(orderData.getGId())) {
                dto = purseManageService.deductCoin(Long.parseLong(orderData.getCUid()), orderData.getCoinsCost(), BillEnum.LUDO_GAME_DEDUCT_COIN
                        , builder.toString(), "", Collections.emptyMap(), 0L, PurseRoleTypeEnum.USER.getType());
            } else if (GameType.BA_LOOT.getNumber().equals(orderData.getGId())) {
                dto = purseManageService.deductCoin(Long.parseLong(orderData.getCUid()), orderData.getCoinsCost(), BillEnum.BA_LOOT_GAME_DEDUCT_COIN
                        , builder.toString(), "", Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
            } else if (GameType.UMO.getNumber().equals(orderData.getGId())) {
                dto = purseManageService.deductCoin(Long.parseLong(orderData.getCUid()), orderData.getCoinsCost(), BillEnum.UMO_GAME_DEDUCT_COIN
                        , builder.toString(), "", Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
            }
            if (dto != null) {
                CreateChannelOrderRespElement element = new CreateChannelOrderRespElement();
                element.setCoins(dto.getCoin());
                element.setStatus(1);
                element.setCUid(dto.getUid().toString());
                element.setOrderId(orderData.getGameOrderId());
                ret.add(element);
            }
        }

        return BusiResultUtil.success(SUCCESS_ZERO, ret);
    }

    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/notify_channel_order")
    public BusiResult<List<NotifyChannelOrderRespElement>> refreshChannelToken(@Valid @RequestBody NotifyChannelOrderReq req) {
        List<NotifyChannelOrderRespElement> elements = new ArrayList<>();
        for (var orderData : req.getData()) {
            StringBuilder builder = new StringBuilder();
            builder.append(orderData.getGameOrderId()).append(":").append(orderData.getGId());
            PurseDTO dto = new PurseDTO();
            if (GameType.LUDO.getNumber().equals(orderData.getGId()) && orderData.getCoinsAward() > 0) {
                dto = purseManageService.addCoin(Long.parseLong(orderData.getCUid()), orderData.getCoinsAward(), BillEnum.LUDO_GAME_INCREASE_COIN
                        , builder.toString(), "", Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
                // 勋章任务
                log.info("Execute ludo game medal task, req:[{}]", JSONUtil.toJsonStr(req));
                medalTaskService.executeMedalTask(MedalTaskEnum.WIN_TIMES_IN_LUDO, Long.parseLong(orderData.getCUid()), 1);
            } else if (GameType.BA_LOOT.getNumber().equals(orderData.getGId()) && orderData.getCoinsAward() > 0) {
                dto = purseManageService.addCoin(Long.parseLong(orderData.getCUid()), orderData.getCoinsAward(), BillEnum.BA_LOOT_GAME_INCREASE_COIN
                        , builder.toString(), "", Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
            } else if (GameType.UMO.getNumber().equals(orderData.getGId()) && orderData.getCoinsAward() > 0) {
                dto = purseManageService.addCoin(Long.parseLong(orderData.getCUid()), orderData.getCoinsAward(), BillEnum.UMO_GAME_INCREASE_COIN
                        , builder.toString(), "", Collections.emptyMap(), 0L,PurseRoleTypeEnum.USER.getType());
            }
            log.info("refreshChannelToken dto:{}",JSONUtil.toJsonStr(dto));
            if (dto != null && orderData.getCoinsAward() > 0) {
                NotifyChannelOrderRespElement element = new NotifyChannelOrderRespElement();
                element.setCoins(dto.getCoin());
                element.setScore(0L);
                element.setCUid(dto.getUid().toString());
                element.setOrderId(orderData.getGameOrderId());
                elements.add(element);
            }
            if (orderData.getCoinsAward() == 0) {
                NotifyChannelOrderRespElement element = new NotifyChannelOrderRespElement();
                PurseDTO purseDTO = purseManageService.reloadPurse(Long.valueOf(orderData.getCUid()));
                if (purseDTO != null) {
                    element.setCoins(purseDTO.getCoin());
                } else {
                    element.setCoins(0L);
                }
                element.setScore(0L);
                element.setCUid(orderData.getCUid());
                element.setOrderId(orderData.getGameOrderId());
                elements.add(element);
            }
            log.info("refreshChannelToken elements:{}",JSONUtil.toJsonStr(elements));
        }


        return BusiResultUtil.success(SUCCESS_ZERO, elements);
    }


    @DoNotCheckSimiMPAuthStatus
    @PostMapping(value = "/notify_game")
    public BusiResult<String> notifyGame(@Valid @RequestBody NotifyGameReq req) {

        if (req.getNotifyType() == 2L) {
            var state = JSONUtil.toBean(req.getData(), NotifyGameReq.InGameState.class);
            redissonManager.set("simi:room:gaming:" + state.getRoomId(), "1");
        } else if (req.getNotifyType() == 3L) {
            var state = JSONUtil.toBean(req.getData(), NotifyGameReq.FinishGameState.class);
            redissonManager.del("simi:room:gaming:" + state.getRoomId());

        }

        return BusiResultUtil.success(SUCCESS_ZERO, "");
    }
}
