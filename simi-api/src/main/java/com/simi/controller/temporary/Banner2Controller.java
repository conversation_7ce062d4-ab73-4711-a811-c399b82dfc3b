package com.simi.controller.temporary;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.service.temporary.Banner2DTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/api/banner")
@Tag(name = "banner", description = "banner")
@RequiredArgsConstructor
public class Banner2Controller {

    @GetMapping(value = "/list")
    @Operation(summary = "banner列表")
    public BusiResult<List<Banner2DTO>> getUserRank(HttpServletRequest request){
        List<Banner2DTO> dtos = new ArrayList<>();
        Banner2DTO dto = new Banner2DTO();
        dto.setPicUrl("https://simi-cos.oss-eu-central-1.aliyuncs.com/dev/1280X1280.JPEG");
        dto.setJumpLink("https://docs.google.com/forms/d/e/1FAIpQLSdJjoxjm3fYro6puW5N983Lz3hJ8MkurqLMn8ZN79P6xofhHA/viewform?usp=dialog");
        dtos.add(dto);
        Banner2DTO dto1 = new Banner2DTO();
        dto1.setPicUrl("https://simi-cos.oss-eu-central-1.aliyuncs.com/dev/1280.PNG");
        dto1.setJumpLink("https://www.simisoul.com/h5-statics/promotionRewards.html");
        dtos.add(dto1);
        return BusiResultUtil.success(dtos);
    }

}
