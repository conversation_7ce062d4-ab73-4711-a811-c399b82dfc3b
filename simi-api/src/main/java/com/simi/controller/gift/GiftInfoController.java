package com.simi.controller.gift;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.gift.GiftSourceEnum;
import com.simi.common.dto.gift.*;
import com.simi.common.dto.revenue.PurseDTO;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.common.service.LuckyGiftService;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.DateTimeUtil;
import com.simi.dto.weeklyStar.WeekStarConfigDTO;
import com.simi.service.GiftInfoService;
import com.simi.service.WeeklyStarConfigService;
import com.simi.service.gift.GiftTabService;
import com.simi.service.gift.UserGiftBackpackService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.user.UserServerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/api/gift/info")
@RequiredArgsConstructor
@Tag(name = "礼物列表", description = "礼物列表")
public class GiftInfoController {

    @Autowired
    private GiftInfoService giftInfoService;
    @Autowired
    private UserGiftBackpackService userGiftBackpackService;
    @Autowired
    private PurseManageService purseManageService;
    @Autowired
    private WeeklyStarConfigService weeklyStarConfigService;
    @Autowired
    private SystemConfigService systemConfigService;

    private final GiftTabService giftTabService;
    private final LuckyGiftService luckyGiftService;
    private final UserServerService userServerService;

    @Operation(summary = "礼物/背包礼物列表 type：1、礼物 2、背包")
    @GetMapping(value = "/list")
    public BusiResult<GiftListDTO> giftList(HttpServletRequest request, @RequestParam(required = false) Integer type) {
        var sessionUtil = new SessionUtil(request);
        GiftListDTO dto = new GiftListDTO();
        List<GiftInfoDTO> giftInfoDTOS = new ArrayList<>();
        if (Objects.equals(type, GiftSourceEnum.BACKPACK.getType())) {
            giftInfoDTOS = userGiftBackpackService.getBackpack(sessionUtil.currentUid(), MessageSourceUtil.getLang().name());
        } else if (Objects.equals(type, GiftSourceEnum.PURCHASE.getType())) {
            giftInfoDTOS = giftInfoService.getGiftInfo(userServerService, sessionUtil.currentUid(), MessageSourceUtil.getLang().name());

            // 周星榜礼物添加角标
            // 如果角标已存在，则不替换
            try {
                Date date = new Date();
                int weekIndex = DateTimeUtil.getWeekIndex(date.getTime());
                WeekStarConfigDTO weeklyStarConfig = weeklyStarConfigService.getWeeklyStarConfig(weekIndex);
                String giftIcon = systemConfigService.getSysConfValueById(SystemConfigConstant.WEEK_STAR_GIFT_ICON);
                if (null != weeklyStarConfig && giftIcon != null) {
                    List<Integer> giftList = weeklyStarConfig.getGiftList();
                    giftInfoDTOS.forEach(item -> {
                        Integer id = item.getId();
                        if (StringUtils.isBlank(item.getCornerMark()) &&
                                giftList.contains(id)) {
                            item.setCornerMark(giftIcon);
                        }
                    });
                }
            } catch (Exception e) {
                log.error("weeklyStar gift icon error", e);
            }
        }
        List<GiftInfoDTO> infoDTOS = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(giftInfoDTOS)) {
            infoDTOS = giftInfoDTOS.stream().filter(g -> g.getGiftType() != 2).collect(Collectors.toList());
        }
        PurseDTO purse = purseManageService.getPurse(sessionUtil.currentUid());
        dto.setGiftInfoDTOS(infoDTOS);
        dto.setGold(purse.getCoin());
        return BusiResultUtil.success(dto);
    }

    @Operation(summary = "模拟抽奖")
    @GetMapping(value = "/getPrize")
    public BusiResult<Integer> getPrize(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        int a0 = 0;
        int a2 = 0;
        int a7 = 0;
        int a10 = 0;
        int a100 = 0;
        int a77 = 0;
        int a777 = 0;
        int amount = 0;
        for (int i = 0; i < 1000000; i++) {
            Integer prize = luckyGiftService.getPrize(sessionUtil.currentUid());
            if (prize == 2) {
                a2 += 1;
            }
            if (prize == 0) {
                a0 += 1;
            }
            if (prize == 7) {
                a7 += 1;
            }
            if (prize == 10) {
                a10 += 1;
            }
            if (prize == 77) {
                a77 += 1;
            }
            if (prize == 100) {
                a100 += 1;
            }
            if (prize == 777) {
                a777 += 1;
            }
            amount += prize;
        }
        log.info("a0:{} a2:{} a7:{} a10:{} a77:{} a100:{} a777:{},amount:{}", a0, a2, a7, a10, a77, a100, a777, amount);
        return BusiResultUtil.success();
    }


    @Operation(summary = "背包礼物列表")
    @GetMapping(value = "/getBackpack")
    public BusiResult<GiftListDTO> getBackpack(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        GiftListDTO dto = new GiftListDTO();
        List<GiftInfoDTO> giftInfoDTOS = userGiftBackpackService.getBackpack(sessionUtil.currentUid(), MessageSourceUtil.getLang().name());
        PurseDTO purse = purseManageService.getPurse(sessionUtil.currentUid());
        dto.setGiftInfoDTOS(giftInfoDTOS);
        dto.setGold(purse.getCoin());
        return BusiResultUtil.success(dto);
    }


    @Operation(summary = "tab下礼物列表")
    @GetMapping(value = "/tabGiftList")
    public BusiResult<TabGiftListResp> tabGiftList(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        TabGiftListResp resp = new TabGiftListResp();
        List<GiftTabDTO> tabs = giftTabService.getList();
        if (CollectionUtils.isEmpty(tabs)) {
            return BusiResultUtil.success(resp);
        }
        List<GiftInfoDTO> giftInfoDTOS = new ArrayList<>();
        giftInfoDTOS = giftInfoService.getGiftInfo(userServerService, sessionUtil.currentUid(), MessageSourceUtil.getLang().name());
        // 周星榜礼物添加角标
        // 如果角标已存在，则不替换
//        try {
//            Date date = new Date();
//            int weekIndex = DateTimeUtil.getWeekIndex(date.getTime());
//            WeekStarConfigDTO weeklyStarConfig = weeklyStarConfigService.getWeeklyStarConfig(weekIndex);
//            String giftIcon = systemConfigService.getSysConfValueById(SystemConfigConstant.WEEK_STAR_GIFT_ICON);
//            if (null != weeklyStarConfig && giftIcon != null) {
//                List<Integer> giftList = weeklyStarConfig.getGiftList();
//                giftInfoDTOS.forEach(item->{
//                    Integer id = item.getId();
//                    if (StringUtils.isBlank(item.getCornerMark()) &&
//                            giftList.contains(id)) {
//                        item.setCornerMark(giftIcon);
//                    }
//                });
//            }
//        } catch (Exception e) {
//            log.error("weeklyStar gift icon error", e);
//        }
        Map<Long, List<GiftInfoDTO>> tabGiftMap = giftInfoDTOS.stream().collect(Collectors.groupingBy(GiftInfoDTO::getTabId));
        List<GiftTabResp> giftTabResps = new ArrayList<>();
        for (GiftTabDTO tab : tabs) {
            GiftTabResp giftTabResp = new GiftTabResp();
            List<GiftInfoDTO> giftInfos = tabGiftMap.get(tab.getId());
            if (MessageSourceUtil.getLang().name().equals(LanguageEnum.ar.name())) {
                giftTabResp.setTab(tab.getTabAr());
            } else {
                giftTabResp.setTab(tab.getTabEn());
            }
            if (CollectionUtils.isNotEmpty(giftInfos)) {
                for (GiftInfoDTO giftInfo : giftInfos) {
                    if (MessageSourceUtil.getLang().name().equals(LanguageEnum.ar.name())) {
                        giftInfo.setTab(tab.getTabAr());
                    } else {
                        giftInfo.setTab(tab.getTabEn());
                    }
                }
            }
            giftTabResp.setTabId(tab.getId());
            giftTabResp.setGiftInfoDTOS(giftInfos);
            giftTabResps.add(giftTabResp);
        }

        PurseDTO purse = purseManageService.getPurse(sessionUtil.currentUid());
        resp.setTabGiftInfos(giftTabResps);
        resp.setGold(purse.getCoin());
        return BusiResultUtil.success(resp);
    }

}
