package com.simi.controller.gift;

import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.annotation.MissionComplete;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.MissionEnum;
import com.simi.common.dto.DateWithMessageDTO;
import com.simi.common.dto.gift.SendGiftReq;
import com.simi.service.gift.GiftSendService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 送礼
 */
@RestController
@Tag(name = "送礼", description = "送礼")
@RequiredArgsConstructor
@RequestMapping("/api/gift")
public class SendGiftController {

    private final GiftSendService giftSendService;

    /**
     * 送礼
     *
     * @return
     */
    @Parameter()
    @DoNotCheckSimiMPAuthStatus
    @Operation(summary = "送礼接口")
    @PostMapping(value = "/send")
    @MissionComplete({MissionEnum.SEND_GIFTS_IN_THE_ROOM})
    public BusiResult<String> login(@RequestBody SendGiftReq param, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        DateWithMessageDTO<String> dto = giftSendService.sendGift(sessionUtil.currentUid(), param);
        return BusiResultUtil.success(dto.getData());
    }



}
