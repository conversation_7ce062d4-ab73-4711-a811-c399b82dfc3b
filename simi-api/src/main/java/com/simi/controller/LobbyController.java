package com.simi.controller;

import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.dto.GameAllWinListDTO;
import com.simi.common.vo.*;
import com.simi.lobby.GameAllLobbyManage;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Date;
import java.util.List;

@Slf4j
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("api/lobby")
@Tag(name = "大厅功能", description = "大厅功能操作")
public class LobbyController {

    private final GameAllLobbyManage gameAllLobbyManage;

    @DoNotCheckSimiMPAuthStatus
    @Operation(summary = "大厅相关数据")
    @GetMapping("/lists")
    public BusiResult<List<GameAllWinListDTO>> getLobby(HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        Long uid = sessionUtil.currentUid();
        return BusiResultUtil.success(gameAllLobbyManage.getLobbyWinList(uid,0, 100));
    }

    @DoNotCheckSimiMPAuthStatus
    @Operation(summary = "房主操作大厅模式")
    @PostMapping("/open")
    public BusiResult open(HttpServletRequest request, @RequestBody LobbyStateOpenReq lobbyStateOpenReq) {
        var sessionUtil = new SessionUtil(request);
        Long uid = sessionUtil.currentUid();
        gameAllLobbyManage.updateState(uid, lobbyStateOpenReq.getStatus());
        return BusiResultUtil.success();
    }

    @Operation(summary = "游戏数据统计")
    @GetMapping("/counts")
    @DoNotCheckSimiMPAuthStatus
    public BusiResult<LobbyStatisticsResp> counts(
            HttpServletRequest request,

            @Parameter(description = "开始时间，格式为 yyyy-MM-dd")
            @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            Date startTime,

            @Parameter(description = "结束时间，格式为 yyyy-MM-dd")
            @RequestParam(required = false)
            @DateTimeFormat(pattern = "yyyy-MM-dd")
            Date endTime,

            @Parameter(description = "统计类型：1=按日，2=按周")
            @RequestParam(required = false)
            Integer type
    ) {
        var sessionUtil = new SessionUtil(request);
        Long uid = sessionUtil.currentUid();
        return BusiResultUtil.success(gameAllLobbyManage.getLobbyStatisticsResp(uid, startTime, endTime, type));
    }

}
