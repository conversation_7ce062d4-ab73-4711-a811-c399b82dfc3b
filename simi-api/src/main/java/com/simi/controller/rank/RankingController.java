package com.simi.controller.rank;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.rank.GenericRankVO;
import com.simi.common.vo.rank.GenericRoomRankVO;
import com.simi.service.activity.rank.CommonlyRankService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.enums.ParameterIn;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/rank")
@Tag(name = "榜单", description = "榜单相关")
public class RankingController {


    private final CommonlyRankService commonlyRankService;


    @Operation(summary = "常用榜单([房间用户/平台用户的贡献榜/魅力榜])")
    @Parameters({
            @Parameter(name = "roomId", description = "房间id", in = ParameterIn.QUERY),
            @Parameter(name = "rankType", description = "榜单类型: [1-财富; 2-魅力]", in = ParameterIn.QUERY),
            @Parameter(name = "frequencyType", description = "榜单频率类型:[1-总榜; 2-年榜单; 3-月榜单; 4-周榜单; 5-日榜单; 6-小时榜单]", in = ParameterIn.QUERY),
            @Parameter(name = "isInRoom", description = "是否在房间内的榜单", in = ParameterIn.QUERY),
            @Parameter(name = "size", description = "榜单大小", in = ParameterIn.QUERY),
    })
    @GetMapping(value = "/commonly")
    public BusiResult<GenericRankVO> commonly(@RequestParam(value = "roomId", required = false) String roomId,
                                              @RequestParam(value = "rankType") Integer rankType,
                                              @RequestParam(value = "frequencyType") Integer frequencyType,
                                              @RequestParam(value = "isInRoom", defaultValue = "false") Boolean isInRoom,
                                              @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                              HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        GenericRankVO result = commonlyRankService.commonlyRankInfo(isInRoom, frequencyType, rankType, roomId, sessionUtil.currentUid(), size);
        return BusiResultUtil.success(result);
    }


    @Operation(summary = "房间榜单")
    @Parameters({
            @Parameter(name = "frequencyType", description = "榜单频率类型:[1-总榜; 2-年榜单; 3-月榜单; 4-周榜单; 5-日榜单; 6-小时榜单]", in = ParameterIn.QUERY),
            @Parameter(name = "size", description = "榜单大小", in = ParameterIn.QUERY),
    })
    @GetMapping(value = "/room/commonly")
    public BusiResult<GenericRoomRankVO> roomRankInfo(@RequestParam(value = "frequencyType") Integer frequencyType,
                                              @RequestParam(value = "size", required = false, defaultValue = "20") Integer size,
                                              HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        GenericRoomRankVO result = commonlyRankService.roomRankInfo(frequencyType, sessionUtil.currentUid(), size);
        return BusiResultUtil.success(result);
    }

}
