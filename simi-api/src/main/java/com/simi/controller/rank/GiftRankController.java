package com.simi.controller.rank;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.dto.rank.GiftReceiveRankResp;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 *
 */
@Slf4j
@RestController
@RequiredArgsConstructor
@RequestMapping("/api/rank/gift")
@Tag(name = "个人收礼排行榜", description = "个人收礼排行榜")
public class GiftRankController {

    //private final GiftRankService giftRankService;

    @Operation(summary = "榜单列表 rankFrequencyType: 榜单频率类型 1、总榜 2、年榜单 3、月榜单 4、周榜单 5、日榜单 6、小时榜单")
    @GetMapping(value = "/receivePersonalRank")
    public BusiResult<GiftReceiveRankResp> receivePersonalRank(@RequestParam(required = false) String roomId,
                                                               @RequestParam(required = false) Integer rankFrequencyType, HttpServletRequest request) {
        //List<GiftReceiveRankPB> resultList = giftRankService.receivePersonalRank(req);
        GiftReceiveRankResp resp = new GiftReceiveRankResp();
        return BusiResultUtil.success(resp);
    }
}
