package com.simi.controller;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.config.LongLinkConfig;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/config")
@RequiredArgsConstructor
@Tag(name = "config", description = "config")
public class ConfigController {
    private final LongLinkConfig longLinkConfig;

    @Operation(summary = "获取token")
    @GetMapping(value = "/long-link-url")
    public BusiResult<String> getLongLinkUrl(HttpServletRequest request) {
        return BusiResultUtil.success(longLinkConfig.getUrl());
    }

}
