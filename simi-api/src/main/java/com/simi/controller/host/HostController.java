package com.simi.controller.host;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.host.ContributionListResp;
import com.simi.service.gift.GiftSendRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("api/host")
@RequiredArgsConstructor
@Tag(name = "主播相关接口", description = "主播相关接口")
public class HostController {

    private final GiftSendRecordService giftSendRecordService;

    @Operation(summary = "送礼贡献列表")
    @GetMapping(value = "/gift/contributionList/")
    public BusiResult<List<ContributionListResp>> contributionList(@Parameter(description = "开始时间，格式为 yyyy-MM-dd")
                                                                   @RequestParam()
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   Date startTime,

                                                                   @Parameter(description = "结束时间，格式为 yyyy-MM-dd")
                                                                   @RequestParam()
                                                                   @DateTimeFormat(pattern = "yyyy-MM-dd")
                                                                   Date endTime,

                                                                   @Parameter(description = "页码】")
                                                                   @RequestParam()
                                                                   Integer page,

                                                                   HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        return BusiResultUtil.success(giftSendRecordService.contributionList(sessionUtil.currentUid(), startTime, endTime, page));
    }
}
