package com.simi.controller.agent;

import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.vo.agent.*;
import com.simi.service.agent.AgentInviteLogService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.Date;

@Slf4j
@RestController
@RequestMapping("api/agent")
@RequiredArgsConstructor
@Tag(name = "代理相关接口", description = "邀请相关API")
public class AgentInviteController {

    private final AgentInviteLogService agentInviteLogService;

    @Operation(summary = "代理数据")
    @GetMapping(value = "/data")
    public BusiResult<AgentDataResp> data(@Parameter(description = "开始时间，格式为 yyyy-MM-dd")
                                          @RequestParam()
                                          @DateTimeFormat(pattern = "yyyy-MM-dd")
                                          Date startTime,

                                          @Parameter(description = "结束时间，格式为 yyyy-MM-dd")
                                          @RequestParam()
                                          @DateTimeFormat(pattern = "yyyy-MM-dd")
                                          Date endTime,

                                          @Parameter(description = "页码】")
                                          @RequestParam()
                                          Integer page,

                                          HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        return BusiResultUtil.success(agentInviteLogService.data(sessionUtil.currentUid(), startTime, endTime, page));
    }

    @Operation(summary = "发送邀请")
    @PostMapping(value = "/send/invite")
    public BusiResult<?> sendInvite(@RequestBody SendInviteReq req, HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        agentInviteLogService.sendInvite(sessionUtil.currentUid(), req);
        return BusiResultUtil.success();
    }

    @Operation(summary = "发送申请")
    @PostMapping(value = "/send/apply")
    public BusiResult<?> sendApply(@RequestBody SendApplyReq req, HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        agentInviteLogService.sendApply(sessionUtil.currentUid(), req);
        return BusiResultUtil.success();
    }

    @Operation(summary = "发送验证码")
    @GetMapping(value = "/send/code")
    public BusiResult<?> sendCode(@Parameter(description = "被邀请人的用户ID")
                                  @RequestParam(required = false) Long uid,
                                  HttpServletRequest request) {

        SessionUtil sessionUtil = new SessionUtil(request);
        agentInviteLogService.sendCode(sessionUtil.currentUid(), uid);
        return BusiResultUtil.success();
    }

    @Operation(summary = "端内-确认加入或拒绝")
    @PostMapping(value = "/operation")
    public BusiResult<?> operation(@RequestBody InviteOperationReq req, HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        agentInviteLogService.operation(sessionUtil.currentUid(), req);
        return BusiResultUtil.success();
    }

    @Operation(summary = "端外-确认加入")
    @PostMapping(value = "/operation2")
    public BusiResult<?> operation(@RequestBody InviteOperationReq2 req, HttpServletRequest request) {
        SessionUtil sessionUtil = new SessionUtil(request);
        agentInviteLogService.operation2(sessionUtil.currentUid(), req);
        return BusiResultUtil.success();
    }
}
