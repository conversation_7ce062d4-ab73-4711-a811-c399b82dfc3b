package com.simi.controller.agent;

import cn.hutool.core.bean.BeanUtil;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.dto.agent.AgentCommissionConfigDTO;
import com.simi.dto.agent.AgentCommissionConfigReq;
import com.simi.entity.agent.AgentCommissionConfig;
import com.simi.service.agent.AgentCommissionConfigService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 代理分佣配置控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/agent/commission/config")
@RequiredArgsConstructor
@Tag(name = "代理分佣配置", description = "代理分佣配置相关接口")
@Validated
public class AgentCommissionConfigController {

    private final AgentCommissionConfigService agentCommissionConfigService;

    @Operation(summary = "获取代理分佣配置列表")
    @GetMapping("/list")
    public BusiResult<List<AgentCommissionConfigDTO>> getConfigList(
            @Parameter(description = "代理UID") @RequestParam Long agentUid) {
        
        List<AgentCommissionConfig> configs = agentCommissionConfigService.getByAgentUid(agentUid);
        List<AgentCommissionConfigDTO> dtos = configs.stream()
                .map(config -> BeanUtil.copyProperties(config, AgentCommissionConfigDTO.class))
                .collect(Collectors.toList());
        
        return BusiResultUtil.success(dtos);
    }

    @Operation(summary = "添加分佣配置")
    @PostMapping("/add")
    public BusiResult<Boolean> addConfig(@Valid @RequestBody AgentCommissionConfigReq req) {
        AgentCommissionConfig config = BeanUtil.copyProperties(req, AgentCommissionConfig.class);
        boolean success = agentCommissionConfigService.addConfig(config);
        
        if (success) {
            return BusiResultUtil.success(true);
        } else {
            return BusiResultUtil.fail("添加分佣配置失败，请检查金额区间是否重叠");
        }
    }

    @Operation(summary = "更新分佣配置")
    @PostMapping("/update")
    public BusiResult<Boolean> updateConfig(@Valid @RequestBody AgentCommissionConfigReq req) {
        if (req.getId() == null) {
            return BusiResultUtil.fail("配置ID不能为空");
        }
        
        AgentCommissionConfig config = BeanUtil.copyProperties(req, AgentCommissionConfig.class);
        boolean success = agentCommissionConfigService.updateConfig(config);
        
        if (success) {
            return BusiResultUtil.success(true);
        } else {
            return BusiResultUtil.fail("更新分佣配置失败，请检查金额区间是否重叠");
        }
    }

    @Operation(summary = "删除分佣配置")
    @PostMapping("/delete/{id}")
    public BusiResult<Boolean> deleteConfig(
            @Parameter(description = "配置ID") @PathVariable Long id) {
        
        boolean success = agentCommissionConfigService.removeById(id);
        return BusiResultUtil.success(success);
    }

    @Operation(summary = "获取分佣比例")
    @GetMapping("/rate")
    public BusiResult<BigDecimal> getCommissionRate(
            @Parameter(description = "代理UID") @RequestParam Long agentUid,
            @Parameter(description = "收益金额") @RequestParam BigDecimal amount) {
        
        BigDecimal rate = agentCommissionConfigService.getCommissionRate(agentUid, amount);
        return BusiResultUtil.success(rate);
    }

    @Operation(summary = "计算分佣金额")
    @GetMapping("/calculate")
    public BusiResult<BigDecimal> calculateCommission(
            @Parameter(description = "代理UID") @RequestParam Long agentUid,
            @Parameter(description = "收益金额") @RequestParam BigDecimal amount) {
        
        BigDecimal commission = agentCommissionConfigService.calculateCommission(agentUid, amount);
        return BusiResultUtil.success(commission);
    }

    @Operation(summary = "删除代理的所有分佣配置")
    @PostMapping("/deleteAll/{agentUid}")
    public BusiResult<Boolean> deleteAllByAgentUid(
            @Parameter(description = "代理UID") @PathVariable Long agentUid) {
        
        boolean success = agentCommissionConfigService.deleteByAgentUid(agentUid);
        return BusiResultUtil.success(success);
    }
}
