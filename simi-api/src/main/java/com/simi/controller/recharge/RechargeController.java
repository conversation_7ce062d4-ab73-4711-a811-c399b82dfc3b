package com.simi.controller.recharge;

import cn.hutool.json.JSONUtil;
import com.simi.alipaymp.DoNotCheckSimiMPAuthStatus;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.dto.recharge.RechargeOrderResp;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.vo.req.recharge.PandaPayRefundReq;
import com.simi.common.vo.req.recharge.RechargeCenterReq;
import com.simi.service.recharge.RechargeCenterService;
import com.simi.service.recharge.RechargeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/09/03 20:36
 **/
@Slf4j
@RestController
@RequestMapping("api/revenue/recharge")
@RequiredArgsConstructor
@Tag(name = "充值", description = "充值")
public class RechargeController {

    private final RechargeService rechargeService;
    private final RechargeCenterService rechargeCenterService;

    @Operation(summary = "pandaPay订单")
    @PostMapping()
    @DoNotCheckSimiMPAuthStatus
    public BusiResult<RechargeOrderResp> order(HttpServletRequest request, @Valid @RequestBody RechargeCenterReq req) throws Exception {
        var sessionUtil = new SessionUtil(request);
        Long paymentUid = null;
        try {
            paymentUid = sessionUtil.currentUid();
        } catch (Exception e) {
            log.info("RechargeController create order from H5, req:[{}]", JSONUtil.toJsonStr(req));
        }
        XAuthToken xAuthToken = sessionUtil.getXAuthToken();
        String clientIp = sessionUtil.getClientIp();
        RechargeOrderResp result = rechargeService.recharge(paymentUid, req, clientIp, xAuthToken);
        return BusiResultUtil.success(result);
    }

    @PostMapping(value = "pandaPayRefund")
    public BusiResult<?> pandaPayRefund(@Valid @RequestBody PandaPayRefundReq req) throws Exception {
        rechargeCenterService.pandaPayRefund(req);
        return BusiResultUtil.success();
    }

    @PostMapping(value = "pandaPayAuth")
    public BusiResult<?> pandaPayAuth(@RequestBody Object req) {
        String payAuth = rechargeCenterService.pandaPayAuth(req);
        return BusiResultUtil.success(payAuth);
    }



}
