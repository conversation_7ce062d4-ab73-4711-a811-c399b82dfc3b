package com.simi.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.simi.audit.AuditManage;
import com.simi.common.BusiResult;
import com.simi.common.BusiResultUtil;
import com.simi.common.base.SessionUtil;
import com.simi.common.constant.CodeEnum;
import com.simi.common.constant.FollowRelation;
import com.simi.common.constant.YesOrNoEnums;
import com.simi.common.dto.ListWithTotal;
import com.simi.common.dto.ReasonReq;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.user.XAuthToken;
import com.simi.common.exception.ApiException;
import com.simi.common.util.RedissonManager;
import com.simi.common.vo.login.LoginResp;
import com.simi.common.vo.req.IdReq;
import com.simi.common.vo.req.InvitationLikeReq;
import com.simi.constant.CommentType;
import com.simi.constant.InvitationNoticeEnum;
import com.simi.dto.*;
import com.simi.entity.MapInvitation;
import com.simi.service.MapInvitationService;
import com.simi.service.cache.CollectCache;
import com.simi.service.following.FollowingLowService;
import com.simi.service.user.UserServerService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

@RestController
@Slf4j
@Tag(name = "地图帖子", description = "地图帖子")
@RequiredArgsConstructor
@RequestMapping("/invitation")
public class MapInvitationController {

    private final MapInvitationService mapInvitationService;

    private final CollectCache collectCache;

    private final UserServerService userServerService;

    private final FollowingLowService followingLowService;

    private final RedissonManager redissonManager;

    private final AuditManage auditManage;

    /**
     * 发帖子
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "发帖子")
    @PostMapping(value = "/send")
    public BusiResult<LoginResp> login(@RequestBody MapInvitationReq req, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        XAuthToken xAuthToken = sessionUtil.getXAuthToken();
        mapInvitationService.addInvitation(sessionUtil.currentUid(), req, xAuthToken.getSimCountryCode(), InvitationNoticeEnum.WHAT_IS_UP.getDrawType());
        return BusiResultUtil.success();
    }

    @Operation(summary = "帖子详情")
    @GetMapping(value = "/getInvitation")
    public BusiResult<MapInvitationDTO> getInvitation(@RequestParam(required = false) Long id,
                                                      HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        MapInvitation one = mapInvitationService.getInvitationById(id, sessionUtil.currentUid());
        MapInvitationDTO mapInvitationDTO = new MapInvitationDTO();
        if (one == null) {
            throw new ApiException(CodeEnum.DATA_DOES_NOT_EXIST);
        }
        mapInvitationDTO = BeanUtil.copyProperties(one, MapInvitationDTO.class);
        List<ReplyDTO> replys = collectCache.getReply(id);
        if (CollectionUtils.isNotEmpty(replys) && sessionUtil.currentUid().equals(mapInvitationDTO.getUid())) {
            List<Long> uids = replys.stream().map(ReplyDTO::getUid).toList();
            Map<Long, UserBaseInfoDTO> userMap = userServerService.batchUserSummary(uids);

            for (ReplyDTO replyDTO : replys) {
                UserBaseInfoDTO userBaseInfoDTO = userMap.get(replyDTO.getUid());
                if (userBaseInfoDTO != null) {
                    replyDTO.setNick(userBaseInfoDTO.getNick());
                    replyDTO.setAvatar(userBaseInfoDTO.getAvatar());
                }
            }
            mapInvitationDTO.setReply(replys);
        }
        UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(mapInvitationDTO.getUid());
        if (userBaseInfo != null) {
            FollowRelation userRelation = followingLowService.getUserRelation(sessionUtil.currentUid(), userBaseInfo.getUid());
            userBaseInfo.setFollowRelation(userRelation);
            mapInvitationDTO.setSendUser(userBaseInfo);
        }
        List<CommentCacheDTO> commentCacheDTOS = collectCache.getComment(mapInvitationDTO.getId());
        if (CollectionUtils.isNotEmpty(commentCacheDTOS)) {
            List<Long> uids = commentCacheDTOS.stream().map(CommentCacheDTO::getUid).collect(Collectors.toList());
            Map<Long, UserBaseInfoDTO> collect = userServerService.batchUserSummary(uids);
            List<CommentResp> commentResps = new ArrayList<>();
            for (CommentCacheDTO commentCacheDTO : commentCacheDTOS) {
                if (commentCacheDTO.getCommentType().equals(CommentType.reply.getType())) {
                    if (!commentCacheDTO.getUid().equals(sessionUtil.currentUid()) && !sessionUtil.currentUid().equals(mapInvitationDTO.getUid())) {
                        continue;
                    }
                    mapInvitationDTO.setIsReply(true);
                    if (mapInvitationDTO.getTargets().contains(sessionUtil.currentUid().toString())) {
                        if (!commentCacheDTO.getUid().equals(sessionUtil.currentUid())) {
                            mapInvitationDTO.setIsReply(false);
                        }
                    }
                }
                UserBaseInfoDTO userBaseInfoDTO = collect.get(commentCacheDTO.getUid());

                CommentResp resp = new CommentResp();
                if (userBaseInfoDTO != null) {
                    resp.setUserBaseInfoDTO(userBaseInfoDTO);
                }
                resp.setComment(commentCacheDTO.getContent());
                resp.setTime(new Date(commentCacheDTO.getScore()));
                resp.setCommentType(commentCacheDTO.getCommentType());
                commentResps.add(resp);
            }

            mapInvitationDTO.setCommentResps(commentResps);
            mapInvitationDTO.setIsCollect(collectCache.hasCollect(sessionUtil.currentUid(), mapInvitationDTO.getId()));
        }
        mapInvitationDTO.setLikeNum(collectCache.getUserLikeLen(id));
        mapInvitationDTO.setIsLike(collectCache.isLike(id, sessionUtil.currentUid()));
        mapInvitationDTO.setBrowseNum(collectCache.getUserBrowse(id));
        return BusiResultUtil.success(mapInvitationDTO);
    }

    @Operation(summary = "帖子点赞/取消点赞")
    @PostMapping(value = "/user/browse")
    public BusiResult<?> browse(@RequestBody IdReq idReq){
        collectCache.setUserBrowse(idReq.getId());
        return BusiResultUtil.success();
    }

    /**
     * 帖子列表
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "地图帖子")
    @GetMapping(value = "/getMapList")
    public BusiResult<ListWithTotal<MapInvitationDTO>> getList(@RequestParam(required = false) Double longitude,
                                                               @RequestParam(required = false) Double latitude,
                                                               @RequestParam(required = false) Integer pageSize,
                                                               @RequestParam(required = false) Integer pageNum,
                                                               HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        XAuthToken xAuthToken = sessionUtil.getXAuthToken();
        List<Long> ids = new ArrayList<>();
        List<Long> blockUid = userServerService.getBlock(sessionUtil.currentUid());
        Map<String, String> idMap = redissonManager.hGetAll("simi:map_invitation_uninterested:" + sessionUtil.currentUid());
        if (idMap.isEmpty()) {
            List<String> idStr = new ArrayList<>(idMap.keySet());
            ids = idStr.stream().map(Long::parseLong).toList();
        }
        boolean interfere = auditManage.isAudit(sessionUtil.currentUid(), xAuthToken);
        ListWithTotal<MapInvitationDTO> mapList;
        //判断是否进入审核数据
        if (interfere) {
            mapList = auditManage.auditMapInvitation(xAuthToken, longitude, latitude, pageSize, pageNum);
        }
        //正常返回数据
        else {
            mapList = mapInvitationService.getMapList(longitude, latitude, pageSize, pageNum, blockUid, ids);
        }
        if (CollectionUtils.isNotEmpty(mapList.getList())) {
            List<Long> sendUids = mapList.getList().stream().map(MapInvitationDTO::getUid).toList();
            List<UserBaseInfoDTO> sendUser = userServerService.listUserDTO(sendUids);
            Map<Long, UserBaseInfoDTO> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sendUser)) {
                userMap = sendUser.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
            }
            for (MapInvitationDTO mapInvitationDTO : mapList.getList()) {
                UserBaseInfoDTO infoDTO = userMap.get(mapInvitationDTO.getUid());
                if (infoDTO != null) {
                    mapInvitationDTO.setSendUser(infoDTO);
                }
                mapInvitationDTO.setIsCollect(collectCache.hasCollect(sessionUtil.currentUid(), mapInvitationDTO.getId()));
            }
        }
        return BusiResultUtil.success(mapList);
    }


    /**
     * 帖子列表
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "地图帖子")
    @GetMapping(value = "/getMeOfFriend")
    public BusiResult<ListWithTotal<MapInvitationDTO>> getMeOfFriend(@RequestParam(required = false) Double longitude,
                                                                     @RequestParam(required = false) Double latitude,
                                                                     @RequestParam(required = false) Integer range,
                                                                     @RequestParam(required = false) Integer mapType,
                                                                     HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        ListWithTotal<MapInvitationDTO> mapList = mapInvitationService.getMeOfFriend(longitude, latitude, range, mapType, sessionUtil.currentUid());
        if (CollectionUtils.isNotEmpty(mapList.getList())) {
            List<Long> sendUids = mapList.getList().stream().map(MapInvitationDTO::getUid).toList();
            List<UserBaseInfoDTO> sendUser = userServerService.listUserDTO(sendUids);
            Map<Long, UserBaseInfoDTO> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sendUser)) {
                userMap = sendUser.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
            }
            for (MapInvitationDTO mapInvitationDTO : mapList.getList()) {
                UserBaseInfoDTO infoDTO = userMap.get(mapInvitationDTO.getUid());
                if (infoDTO != null) {
                    mapInvitationDTO.setSendUser(infoDTO);
                }
                mapInvitationDTO.setIsCollect(collectCache.hasCollect(sessionUtil.currentUid(), mapInvitationDTO.getId()));
            }
        }
        return BusiResultUtil.success(mapList);
    }


    /**
     * 帖子列表
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "帖子列表")
    @GetMapping(value = "/getList")
    public BusiResult<ListWithTotal<MapInvitationDTO>> getList(@RequestParam(required = false) Integer pageSize,
                                                               @RequestParam(required = false) Long targetUid,
                                                               @RequestParam(required = false) Integer pageNum,
                                                               @RequestParam(required = false) Integer status,
                                                               @RequestParam(value = "startTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date startTime,
                                                               @RequestParam(value = "endTime", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") Date endTime,
                                                               HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        List<Long> ids = new ArrayList<>();
        List<Long> blockUid = userServerService.getBlock(sessionUtil.currentUid());
        Map<String, String> idMap = redissonManager.hGetAll("simi:map_invitation_uninterested:" + sessionUtil.currentUid());
        if (idMap.isEmpty()) {
            List<String> idStr = new ArrayList<>(idMap.keySet());
            ids = idStr.stream().map(Long::parseLong).toList();
        }
        ListWithTotal<MapInvitationDTO> list = mapInvitationService.getList(sessionUtil.currentUid(), targetUid, pageSize, pageNum, startTime, endTime, status,blockUid, ids);
        if (CollectionUtils.isNotEmpty(list.getList())) {
            List<Long> sendUids = list.getList().stream().map(MapInvitationDTO::getUid).toList();
            List<UserBaseInfoDTO> sendUser = userServerService.listUserDTO(sendUids);
            Map<Long, UserBaseInfoDTO> userMap = new HashMap<>();
            if (CollectionUtils.isNotEmpty(sendUser)) {
                userMap = sendUser.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
            }
            for (MapInvitationDTO mapInvitationDTO : list.getList()) {
                UserBaseInfoDTO infoDTO = userMap.get(mapInvitationDTO.getUid());
                if (infoDTO != null) {
                    mapInvitationDTO.setSendUser(infoDTO);
                }
                List<CommentCacheDTO> commentCacheDTOS = collectCache.getComment(mapInvitationDTO.getId());
                if (CollectionUtils.isNotEmpty(commentCacheDTOS)) {
                    List<Long> uids = commentCacheDTOS.stream().map(CommentCacheDTO::getUid).collect(Collectors.toList());
                    List<UserBaseInfoDTO> userBaseInfoDTOS = userServerService.listUserDTO(uids);
                    Map<Long, UserBaseInfoDTO> collect = userBaseInfoDTOS.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
                    List<CommentResp> commentResps = new ArrayList<>();
                    for (CommentCacheDTO commentCacheDTO : commentCacheDTOS) {
                        CommentResp resp = new CommentResp();
                        if (commentCacheDTO.getCommentType().equals(CommentType.reply.getType())) {
                            if (!commentCacheDTO.getUid().equals(sessionUtil.currentUid()) && !sessionUtil.currentUid().equals(mapInvitationDTO.getUid())) {
                                continue;
                            }
                        }
                        UserBaseInfoDTO userBaseInfoDTO = collect.get(commentCacheDTO.getUid());

                        if (userBaseInfoDTO != null) {
                            resp.setUserBaseInfoDTO(userBaseInfoDTO);
                        }
                        resp.setComment(commentCacheDTO.getContent());
                        resp.setTime(new Date(commentCacheDTO.getScore()));
                        resp.setCommentType(commentCacheDTO.getCommentType());
                        commentResps.add(resp);
                    }
                    mapInvitationDTO.setCommentResps(commentResps);
                }
                mapInvitationDTO.setBrowseNum(collectCache.getUserBrowse(mapInvitationDTO.getId()));
                mapInvitationDTO.setLikeNum(collectCache.getUserLikeLen(mapInvitationDTO.getId()));
                mapInvitationDTO.setIsLike(collectCache.isLike(mapInvitationDTO.getId(), sessionUtil.currentUid()));
                mapInvitationDTO.setIsCollect(collectCache.hasCollect(sessionUtil.currentUid(), mapInvitationDTO.getId()));
            }
        }
        return BusiResultUtil.success(list);
    }

    /**
     * 不感兴趣
     *
     * @return
     */
    @Operation(summary = "不感兴趣")
    @PostMapping(value = "/uninterested")
    public BusiResult uninterested(@RequestBody IdReq idReq, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        redissonManager.hSet("simi:map_invitation_uninterested:" + sessionUtil.currentUid(), idReq.getId().toString(), "1");
        return BusiResultUtil.success();
    }

    /**
     * 不感兴趣
     *
     * @return
     */
    @Operation(summary = "举报")
    @PostMapping(value = "/report")
    public BusiResult report(@RequestBody ReasonReq reasonReq, HttpServletRequest request) {
        return BusiResultUtil.success();
    }

    /**
     * 删除帖子
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "删除帖子")
    @PostMapping(value = "/delete")
    public BusiResult delete(@RequestBody IdReq idReq) {
        mapInvitationService.delete(idReq.getId());
        return BusiResultUtil.success();
    }


    /**
     * 收藏帖子
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "收藏帖子")
    @PostMapping(value = "/collect")
    public BusiResult collect(@RequestBody CollectDTO collectDTO, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        collectCache.setCollect(sessionUtil.currentUid(), collectDTO.getId(), collectDTO.getIsCollect());
        return BusiResultUtil.success();
    }

    /**
     * 评论帖子
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "评论帖子")
    @PostMapping(value = "/comment")
    public BusiResult setComment(@RequestBody CommentDTO commentDTO, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        XAuthToken xAuthToken = sessionUtil.getXAuthToken();
        commentDTO.setUid(sessionUtil.currentUid());
        CommentDetailsDTO dto = new CommentDetailsDTO();
        dto.setInvitationId(commentDTO.getInvitationId());
        dto.setContent(commentDTO.getContent());
        if (commentDTO.getCommentType().equals(CommentType.reply.getType())) {
            MapInvitationReq mapInvitationReq = BeanUtil.copyProperties(commentDTO, MapInvitationReq.class);
            mapInvitationReq.setTargets(commentDTO.getUid().toString());
            mapInvitationReq.setStatus(2);
            Long id = mapInvitationService.addInvitation(sessionUtil.currentUid(), mapInvitationReq, xAuthToken.getSimCountryCode(), InvitationNoticeEnum.INVITATION_REPLY.getDrawType());
            dto.setReplyId(id);
            ReplyIdAndUidDTO idAndUidDTO = new ReplyIdAndUidDTO();
            idAndUidDTO.setInvitationId(id);
            idAndUidDTO.setUid(sessionUtil.currentUid());
            idAndUidDTO.setMaterial(commentDTO.getMaterial());
            collectCache.setReply(commentDTO.getInvitationId(), JSON.toJSONString(idAndUidDTO));
        } else {
            MapInvitation invitationById = mapInvitationService.getInvitationById(commentDTO.getInvitationId(), sessionUtil.currentUid());
            if (!sessionUtil.currentUid().equals(invitationById.getUid())) {
                mapInvitationService.sendInvitation(invitationById, sessionUtil.currentUid(), invitationById.getUid(), InvitationNoticeEnum.COMMENT.getDrawType());
            }
        }

        dto.setUid(commentDTO.getUid());
        dto.setCommentType(commentDTO.getCommentType());
        collectCache.setComment(commentDTO.getInvitationId(), JSON.toJSONString(dto));
        return BusiResultUtil.success();
    }

    /**
     * 收藏帖子
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "收藏帖子列表")
    @GetMapping(value = "/collect/list")
    public BusiResult<ListWithTotal<MapInvitationDTO>> collectList(HttpServletRequest request,
                                                                   @RequestParam(required = false) Integer pageSize,
                                                                   @RequestParam(required = false) Integer pageNum) {
        var sessionUtil = new SessionUtil(request);
        ListWithTotal<MapInvitationDTO> list = new ListWithTotal<>();
        List<String> collectList = collectCache.getCollectList(sessionUtil.currentUid());

        if (CollectionUtils.isNotEmpty(collectList)) {
            List<Long> ids = collectList.stream().map(Long::parseLong).toList();
            list = mapInvitationService.getInvitationList(ids, pageSize, pageNum);
            if (CollectionUtils.isNotEmpty(list.getList())) {
                List<Long> sendUids = list.getList().stream().map(MapInvitationDTO::getUid).toList();
                List<UserBaseInfoDTO> sendUser = userServerService.listUserDTO(sendUids);
                Map<Long, UserBaseInfoDTO> userMap = new HashMap<>();
                if (CollectionUtils.isNotEmpty(sendUser)) {
                    userMap = sendUser.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
                }
                for (MapInvitationDTO mapInvitationDTO : list.getList()) {
                    UserBaseInfoDTO infoDTO = userMap.get(mapInvitationDTO.getUid());
                    if (infoDTO != null) {
                        mapInvitationDTO.setSendUser(infoDTO);
                    }
                    List<CommentCacheDTO> commentCacheDTOS = collectCache.getComment(mapInvitationDTO.getId());
                    if (CollectionUtils.isNotEmpty(commentCacheDTOS)) {
                        List<Long> uids = commentCacheDTOS.stream().map(CommentCacheDTO::getUid).collect(Collectors.toList());
                        List<UserBaseInfoDTO> userBaseInfoDTOS = userServerService.listUserDTO(uids);
                        Map<Long, UserBaseInfoDTO> collect = userBaseInfoDTOS.stream().collect(Collectors.toMap(UserBaseInfoDTO::getUid, u -> u));
                        List<CommentResp> commentResps = new ArrayList<>();
                        for (CommentCacheDTO commentCacheDTO : commentCacheDTOS) {
                            if (commentCacheDTO.getCommentType().equals(CommentType.reply.getType())) {
                                if (!commentCacheDTO.getUid().equals(sessionUtil.currentUid()) && !sessionUtil.currentUid().equals(mapInvitationDTO.getUid())) {
                                    continue;
                                }
                            }
                            UserBaseInfoDTO userBaseInfoDTO = collect.get(commentCacheDTO.getUid());
                            CommentResp resp = new CommentResp();
                            if (userBaseInfoDTO != null) {
                                resp.setUserBaseInfoDTO(userBaseInfoDTO);
                            }
                            resp.setComment(commentCacheDTO.getContent());
                            resp.setTime(new Date(commentCacheDTO.getScore()));
                            resp.setCommentType(commentCacheDTO.getCommentType());
                            commentResps.add(resp);
                        }
                        mapInvitationDTO.setCommentResps(commentResps);
                    }
                    mapInvitationDTO.setBrowseNum(collectCache.getUserBrowse(mapInvitationDTO.getId()));
                    mapInvitationDTO.setLikeNum(collectCache.getUserLikeLen(mapInvitationDTO.getId()));
                    mapInvitationDTO.setIsLike(collectCache.isLike(mapInvitationDTO.getId(), sessionUtil.currentUid()));
                    mapInvitationDTO.setIsCollect(collectCache.hasCollect(sessionUtil.currentUid(), mapInvitationDTO.getId()));
                }
            }
        }
        return BusiResultUtil.success(list);
    }

    /**
     * 帖子点赞/取消点赞
     *
     * @return
     */
    @Parameter()
    @Operation(summary = "帖子点赞/取消点赞")
    @PostMapping(value = "/isLike")
    public BusiResult isLike(@RequestBody InvitationLikeReq req, HttpServletRequest request) {
        var sessionUtil = new SessionUtil(request);
        if (req == null || req.getIsLike() == null) {
            throw new ApiException(CodeEnum.PARAM_ILLEGAL);
        }
        if (req.getIsLike().equals(YesOrNoEnums.NO.getType())) {
            collectCache.delUserLike(req.getInvitationId(), sessionUtil.currentUid());
        } else {
            collectCache.setUserLike(req.getInvitationId(), sessionUtil.currentUid());
            MapInvitation invitationById = mapInvitationService.getInvitationById(req.getInvitationId(), sessionUtil.currentUid());
            if (!sessionUtil.currentUid().equals(invitationById.getUid())) {
                mapInvitationService.sendInvitation(invitationById, sessionUtil.currentUid(),invitationById.getUid(), InvitationNoticeEnum.INVITATION_LIKE.getDrawType());
            }
        }
        return BusiResultUtil.success();
    }


}
