package com.simi.interceptor;

import com.simi.common.base.SessionUtil;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import java.util.UUID;

@Component
public class TraceInterceptor implements HandlerInterceptor {
    private final String TRACE_ID = "traceId";
    private final String USER_ID = "userId";
    private final String TRACE_PREFIX = "traceId=>";
    private final String USER_ID_PREFIX = "userId=>";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        Long uid = new SessionUtil(request).currentUid(false);
        MDC.put(TRACE_ID, TRACE_PREFIX + UUID.randomUUID());
        MDC.put(USER_ID, USER_ID_PREFIX + uid);
        return true;
    }
 
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        MDC.remove(TRACE_ID);
        MDC.remove(USER_ID);
    }
}