package com.simi.interceptor;

import com.simi.common.BusiResult;
import com.simi.common.util.AESUtils;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

import java.util.Objects;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-30 22:12
 **/
@RestControllerAdvice
@Slf4j
public class SimiGameRespBody implements ResponseBodyAdvice {

    @Override
    public boolean supports(MethodParameter returnType, Class converterType) {
        try {
            EncodeBody annotation = returnType.getMethod().getAnnotation(EncodeBody.class);
            return null != annotation;
        } catch (Exception e) {
            log.error("simiGameRespBody error{}", ExceptionUtil.formatEx(e), e);
        }
        return false;
    }

    @Override
    public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType, Class selectedConverterType, ServerHttpRequest request, ServerHttpResponse response) {
        if (body == null) {
            return body;
        }

        if (body instanceof BusiResult) {
            Object data = ((BusiResult<?>) body).getData();
            if (Objects.nonNull(data)) {
                String bodyStr = JsonUtils.toJson(body);
                log.info("simiGameRespBody 原始Payload {}", bodyStr);
                // 解码为原始数据
                String encrypt = AESUtils.encrypt(bodyStr);
                log.info("simiGameRespBody 加密后Payload {}", encrypt);
                ((BusiResult) body).setData(encrypt);
            }
            return body;
        }

        return body;
    }
}
