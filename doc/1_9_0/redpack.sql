CREATE TABLE `red_packet` (
                              `id` varchar(255) COLLATE utf8mb4_unicode_ci NOT NULL,
                              `uid` bigint DEFAULT NULL COMMENT '发送人',
                              `amount` bigint DEFAULT NULL COMMENT '红包金额',
                              `count` int DEFAULT NULL COMMENT '红包个数',
                              `countdown` int DEFAULT NULL COMMENT '红包倒计时',
                              `type` int DEFAULT NULL COMMENT '类型: 1-房间; 2-全服',
                              `room_id` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '房间id',
                              `create_time` datetime DEFAULT NULL COMMENT '创建时间',
                              `begin_time` datetime DEFAULT NULL COMMENT '开始时间',
                              `expire_time` datetime DEFAULT NULL COMMENT '过期时间',
                              PRIMARY KEY (`id`),
                              KEY `idx_begin_time` (`begin_time`),
                              KEY `idx_room_id` (`room_id`),
                              KEY `idx_room_begin_time` (`room_id`,`begin_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



CREATE TABLE `red_packet_record` (
                                     `id` bigint NOT NULL AUTO_INCREMENT,
                                     `red_packet_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                     `uid` bigint DEFAULT NULL,
                                     `sender` bigint DEFAULT NULL,
                                     `receive_amount` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                                     `create_time` datetime DEFAULT NULL,
                                     PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;


