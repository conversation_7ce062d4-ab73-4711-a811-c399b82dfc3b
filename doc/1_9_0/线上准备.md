
## 数据库
``` sql
alter table gift_info
    add aristocracy_level int default 0 not null;
    
    
    
create table aristocracy_plaque_retrieval
(
    id                int auto_increment comment '自增ID'
        primary key,
    english_name      varchar(50)                        not null comment '英文名称',
    arabic_name       varchar(50)                        not null comment '阿拉伯语名称',
    english_image_url varchar(255)                       null comment '英文图片URL',
    arabic_image_url  varchar(255)                       null comment '阿拉伯语图片URL',
    bound_user_id     bigint                             null comment '绑定用户ID',
    sort_order        int      default 0                 null comment '排序',
    create_time       datetime default CURRENT_TIMESTAMP not null comment '创建时间',
    modify_time       datetime default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '修改时间'
)
    comment '贵族铭牌领取表' charset = utf8mb4;

    
```

``` 短信增加字段: 用途类型
ALTER TABLE `simi`.`sms_send_record` 
ADD COLUMN `type` int NULL COMMENT '类型: 1-注册; 2-忘记密码; 3-账号绑定; 4-手机换绑' AFTER `create_time`;
```



## 待完善的东西
1. mp4动图
2. 打折数据
3. 4、5、6级签到金币奖励包
4. 勋章贵族配置
5. 4、5、6贵族进房公告
6. 4、5、6 签到奖励包
8. 勋章佩戴数量


## 待补充的资源
1. 4、5、6 的动态图（购买后出现的动态图）
2. 资料卡
3. 进房公告背景图
4. 房间边框
5. 贵族装扮
6. 4、5、6 权益预览图
