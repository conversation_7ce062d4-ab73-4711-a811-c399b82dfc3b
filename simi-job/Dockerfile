# 基于 java 镜像构建
FROM amazoncorretto:17-alpine


RUN mkdir -p /data/app
RUN mkdir -p /data/applogs

WORKDIR /data/app

COPY simi-job-0.0.1-SNAPSHOT.jar .
COPY startup.sh .

EXPOSE 8093

ARG ENVIRONMENT

ENV ENVIRONMENT ${ENVIRONMENT}

# 运行项目
CMD ["sh", "startup.sh", "simi-job-0.0.1-SNAPSHOT.jar"]
#CMD ["java", "-Xmx2g","-XX:+UseG1GC", "-XX:+UseStringDeduplication","-Duser.timezone=Asia/Shanghai", "-Dfile.encoding=utf-8", "-Dspring.profiles.active=dev","-jar", "ksing-admin-1.0-SNAPSHOT-exec.jar"]
