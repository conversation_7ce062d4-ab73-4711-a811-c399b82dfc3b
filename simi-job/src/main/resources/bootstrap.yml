# Common configuration for all profiles
spring:
  application:
    name: simi-job
  profiles:
    active: @spring.profile@  # 由 Maven 动态注入
  banner:
    location: /banner.txt

  decorator:
    datasource:
      enabled: true # 是否启用
      p6spy:
        log-format: yyyy-MM-dd HH:mm:ss.SSS
        log-file: p6spy.log # 日志文件路径
        logging: slf4j

  cloud:
    compatibility-verifier:
      enabled: false
    nacos:
      discovery:
        username: @nacos.username@
        password: @nacos.password@
        server-addr: @nacos.server.addr@
        namespace: @nacos.namespace@
      config:
        username: @nacos.username@
        password: @nacos.password@
        server-addr: @nacos.server.addr@
        namespace: @nacos.namespace@
        refresh-enabled: true
        file-extension: yml
        import-check:
          enabled: false
        shared-configs:
          - data-id: simi-api.yml
            refresh: true
          - data-id: ossResource.yml
            refresh: true
          - data-id: level.yml
            refresh: true
          - data-id: medal.yml
            refresh: true
          - data-id: shumei.yml
            refresh: true
          - data-id: system-config.yml
            refresh: true
          - data-id: RocketMQ.yml
            refresh: true
          - data-id: tencentIm.yml
            refresh: true
          - data-id: platform.yml
            refresh: true
          - data-id: longlink.yml
            refresh: true
          - data-id: expression.yml
            refresh: true
          - data-id: google.yml
            refresh: true
          - data-id: meilisearch.yml
            refresh: true
          - data-id: country_settings.yml
            refresh: true
          - data-id: restricted_range.yml
            refresh: true
          - data-id: white_list.yml
            refresh: true
          - data-id: internal-services-config.yml
            refresh: true
          - data-id: giftSend.yml
            refresh: true
          - data-id: payermax.yml
            refresh: true
          - data-id: crazy-triple.yml
            refresh: true
          - data-id: ws.yml
            refresh: true
          - data-id: aristocracy.yml
            refresh: true
          - data-id: appVersion.yml
            refresh: true
          - data-id: pk-week-star-rank.yml
            refresh: true
          - data-id: vip.yml
            refresh: true
          - data-id: private_photo.yml
            refresh: true
          - data-id: multi_datasource.yml
            refresh: true
          - data-id: achievement_medal.yml
            refresh: true
          - data-id: recharge.yml
            refresh: true
          - data-id: facebook.yml
            refresh: true
          - data-id: huawei.yml
            refresh: true
          - data-id: application.yml
            refresh: true
          - data-id: sensitive.yml
            refresh: true
