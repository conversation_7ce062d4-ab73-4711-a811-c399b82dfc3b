package com.simi;

import com.alibaba.nacos.spring.context.annotation.config.EnableNacosConfig;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;


@EnableScheduling
@SpringBootApplication(scanBasePackages = {"com.simi.common", "com.simi"})
@MapperScan("com.simi.*.mapper")
@EnableNacosConfig
public class SimiJobApplication {

    public static void main(String[] args) {
        SpringApplication.run(SimiJobApplication.class, args);
        System.out.println("启动成功。。。。。。。。。。。。");
    }
}
