package com.simi.service.nlp;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import edu.stanford.nlp.ling.CoreAnnotations;
import edu.stanford.nlp.ling.CoreLabel;
import edu.stanford.nlp.pipeline.Annotation;
import edu.stanford.nlp.pipeline.StanfordCoreNLP;
import edu.stanford.nlp.util.CoreMap;

import java.util.List;
import java.util.Properties;

/**
 * <AUTHOR>
 * @date 2024/05/07 11:32
 **/
public class CoreNlpService {

    private static volatile CoreNlpService instance;

    private final StanfordCoreNLP pipeline;

    private CoreNlpService() {
        Properties props = new Properties();
        props.setProperty("annotators", "tokenize,ssplit");
        this.pipeline = new StanfordCoreNLP(props);
    }

    public static CoreNlpService getInstance() {
        if (instance == null) {
            synchronized (CoreNlpService.class) {
                if (instance == null) {
                    instance = new CoreNlpService();
                }
            }
        }
        return instance;
    }

    // 分词方法
    public List<String> tokenize(String text) {
        List<String> tokens = CollUtil.newArrayList();

        if (StrUtil.isBlank(text)) {
            return tokens;
        }
        Annotation document = new Annotation(text);
        pipeline.annotate(document);

        // 检查注解对象是否包含句子
        if (!document.containsKey(CoreAnnotations.SentencesAnnotation.class)) {
            return tokens;
        }
        // 获取句子列表
        List<CoreMap> sentences = document.get(CoreAnnotations.SentencesAnnotation.class);
        if (CollUtil.isEmpty(sentences)) {
            return tokens; // 或者抛出异常
        }
        // 获取分词结果
        for (CoreMap sentence : sentences) {
            for (CoreLabel token : sentence.get(CoreAnnotations.TokensAnnotation.class)) {
                tokens.add(token.get(CoreAnnotations.TextAnnotation.class));
            }
        }
        return tokens;
    }
}
