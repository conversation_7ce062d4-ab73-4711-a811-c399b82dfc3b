package com.simi.xxl;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.aristocracy.AristocracyConfigDTO;
import com.simi.common.dto.aristocracy.AristocracyRemainDayConfigDTO;
import com.simi.common.dto.aristocracy.UserCurAristocracyInfo;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.util.ClientRouteUtil;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.constant.UserRedisKey;
import com.simi.dto.push.AristocracyMsgDTO;
import com.simi.entity.aristocracy.UserAristocracyRecordsDO;
import com.simi.service.BannerServerService;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.aristocracy.AristocracyPlaqueRetrievalService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.medal.MedalServerService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.user.UserServerService;
import com.simi.util.PushMsgUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-05 10:45
 **/
@Slf4j
@Service
@AllArgsConstructor
public class AristocracyTask {
    private final UserAristocracyRecordsService userAristocracyRecordsService;
    private final RedissonManager redissonManager;
    private final NotifyMessageComponent notifyMessageComponent;
    private final UserServerService userServerService;
    private final AristocracyConfigService aristocracyConfigService;
    private final BannerServerService bannerServerService;
    private final MedalServerService medalServerService;
    private final AristocracyPlaqueRetrievalService aristocracyPlaqueRetrievalService;

    /**
     * 过期提醒
     */
    @XxlJob("AristocracyExpireRemind")
    public void expireRemind() {
        /*
         *  获取 剩余 X 天过期的用户id
         */
        // 剩余7天过期的用户，过期时间+7天少于当前时间
        long expireTime = System.currentTimeMillis() + 7 * 24 * 60 * 60 * 1000;

        LambdaQueryWrapper<UserAristocracyRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(UserAristocracyRecordsDO::getEndTime, expireTime);
        wrapper.eq(UserAristocracyRecordsDO::getRecordStatus, AristocracyConstant.Status.EFFECTIVE.getStatus());
        List<UserAristocracyRecordsDO> list = userAristocracyRecordsService.list(wrapper);
        // 根据用户分组
        Map<Long, List<UserAristocracyRecordsDO>> userIdMap
                = list.stream().collect(Collectors.groupingBy(UserAristocracyRecordsDO::getUserId));
        Set<Long> userIds = userIdMap.keySet();
        // 每个用户的贵族信息
        Map<Long, UserCurAristocracyInfo> userAristocracyRecordsBatch
                = userAristocracyRecordsService.getUserAristocracyRecordsBatch(Lists.newArrayList(userIds));
        userAristocracyRecordsBatch.forEach((k,v)->{
            List<UserAristocracyRecordsDO> userAristocracyRecordsDOS = userIdMap.get(k);
            // 最后一条的记录的id
            Integer recordId = userAristocracyRecordsDOS.get(userAristocracyRecordsDOS.size() - 1).getId();
            // 统计用户当前有效的贵族有多少天
            int days = userAristocracyRecordsDOS.stream().mapToInt(UserAristocracyRecordsDO::getDurationDays).sum();

            // 判断用户剩余多少天
            Long endTime = v.getEndTime();
            long remainDay = ((endTime - System.currentTimeMillis()) / 1000)/ DateTimeUtil.SECONDS_PER_DAY;
            log.info("aristocracy send renew message.uid:{},remainDay:{}", k, remainDay);
            if (remainDay < 1) {
                String value = redissonManager.get(UserRedisKey.user_aristocracy_expire_push_record.getKey(k, 1, recordId));
                if (days > 1 && StringUtils.isBlank(value)) {
                    // 推送
                    send(k, "1", v.getCurAristocracy(),0);
                    redissonManager.setnx(UserRedisKey.user_aristocracy_expire_push_record.getKey(k, 1, recordId), "1", 1, TimeUnit.DAYS);
                }
                return;
            }
            if (remainDay < 3) {
                String value = redissonManager.get(UserRedisKey.user_aristocracy_expire_push_record.getKey(k, 3, recordId));
                if (days > 3 && StringUtils.isBlank(value)) {
                    send(k, "3", v.getCurAristocracy(),0);
                    redissonManager.setnx(UserRedisKey.user_aristocracy_expire_push_record.getKey(k, 3, recordId), "1", 3, TimeUnit.DAYS);
                }
                return;
            }
            if (remainDay < 7) {
                String value = redissonManager.get(UserRedisKey.user_aristocracy_expire_push_record.getKey(k, 7, recordId));
                if (days > 7 && StringUtils.isBlank(value)) {
                    send(k, "7", v.getCurAristocracy(), 0);
                    redissonManager.setnx(UserRedisKey.user_aristocracy_expire_push_record.getKey(k, 7, recordId), "1", 7, TimeUnit.DAYS);
                }
                return;
            }
        });

    }

    /**
     * 处理贵族过期
     */
    @XxlJob("AristocracyHandleAristocracyExpire")
    public void handleAristocracyExpire() {
       // 获取状态生效，但已过期的数据
        long expireTime = System.currentTimeMillis();
        LambdaQueryWrapper<UserAristocracyRecordsDO> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(UserAristocracyRecordsDO::getEndTime, expireTime);
        wrapper.eq(UserAristocracyRecordsDO::getRecordStatus, AristocracyConstant.Status.EFFECTIVE.getStatus());
        List<UserAristocracyRecordsDO> list = userAristocracyRecordsService.list(wrapper);
        // 拿到过期的用户id
        Set<Long> userIds = list.stream().map(UserAristocracyRecordsDO::getUserId).collect(Collectors.toSet());

        // 更新数据
        list.forEach(k->{
            Long userId = k.getUserId();
            k.setRecordStatus(AristocracyConstant.Status.INVALID.getStatus());
            // 去除贵族铭牌
            aristocracyPlaqueRetrievalService.clear(userId);

            // 更新勋章佩戴数量
            medalServerService.updateUserMedalNum(userId, 3);

            // 发送过期的消息
            Optional<UserCurAristocracyInfo> optional
                    = userAristocracyRecordsService.getUserAristocracyRecordsCache(userId);
            // 已过期
            // 如果对象为空，说明用户已经过期
            if (optional.isEmpty() || expireTime > optional.get().getEndTime()) {
                // 清缓存
                userAristocracyRecordsService.cleanCache(userId);
                // 发送过期的消息
                send(k.getUserId(), "", k.getAristocracyLevel(), 1);
                log.info("aristocracy send expire message.uid:{}", k.getUserId());
            };
        });
        userAristocracyRecordsService.updateBatchById(list);
        log.info("aristocracy expire list:{}", list);
    }


    /**
     *
     * @param targetUid
     * @param replaceText
     * @param aristocracyId
     * @param type 0 续费提醒 1 过期
     */
    private void send(long targetUid, String replaceText, int aristocracyId, int type) {

        try {
            LanguageEnum languageEnum = userServerService.userAppLanguage(targetUid);
            String routeUrl = ClientRouteUtil.toBackpack();
            // 获取贵族信息
            Optional<AristocracyConfigDTO> aristocracyOptional = aristocracyConfigService.getAristocracyConfigById(aristocracyId);
            if (aristocracyOptional.isEmpty()) {
                log.error("aristocracy is null.aristocracyId:{}", aristocracyId);
            }
            AristocracyConfigDTO aristocracyConfigDTO = aristocracyOptional.get();
            Map<String, String> titleMap = new HashMap<>();
            Map<String, String> textMap = new HashMap<>();
            Map<String, String> nameMap = new HashMap<>();

            String offlineTitle = "";
            String offlineText = "";
            //发送消息
            if (type == 0) {
                AristocracyRemainDayConfigDTO aristocracyRemainDayConfig = aristocracyConfigService.getAristocracyRemainDayConfig();
                titleMap.put("ar", aristocracyRemainDayConfig.getArTitle());
                titleMap.put("en", aristocracyRemainDayConfig.getEnTitle());
                textMap.put("ar", String.format(aristocracyRemainDayConfig.getArText(), replaceText));
                textMap.put("en", String.format(aristocracyRemainDayConfig.getEnText(), replaceText));
                if (languageEnum.equals(LanguageEnum.ar)) {
                    offlineTitle = aristocracyRemainDayConfig.getArTitle();
                    offlineText = String.format(aristocracyRemainDayConfig.getOfflineArText(), replaceText);
                } else {
                    offlineTitle = aristocracyRemainDayConfig.getEnTitle();
                    offlineText = String.format(aristocracyRemainDayConfig.getOfflineEnText(), replaceText);
                }

            }else if (type==1) {
                AristocracyRemainDayConfigDTO expireConfig = aristocracyConfigService.getAristocracyExpireConfig();
                titleMap.put("ar", expireConfig.getArTitle());
                titleMap.put("en", expireConfig.getEnTitle());
                textMap.put("ar", expireConfig.getArText());
                textMap.put("en", expireConfig.getEnText());
                if (languageEnum.equals(LanguageEnum.ar)) {
                    offlineTitle = expireConfig.getArTitle();
                    offlineText = String.format(expireConfig.getOfflineArText(), replaceText);
                } else {
                    offlineTitle = expireConfig.getEnTitle();
                    offlineText = String.format(expireConfig.getOfflineEnText(), replaceText);
                }
            }
            AristocracyMsgDTO.AristocracyInfo aristocracyInfo = new AristocracyMsgDTO.AristocracyInfo();
            AristocracyMsgDTO textMsgModel = new AristocracyMsgDTO();

            textMsgModel.setTitleMap(titleMap);
            textMsgModel.setTextMap(textMap);
            textMsgModel.setType(AristocracyConstant.MsgType.RENEW.getType());
            textMsgModel.setLinkUrl("nady:///main/me-aristocracy");
            nameMap.put("ar", aristocracyConfigDTO.getArName());
            nameMap.put("en", aristocracyConfigDTO.getEnName());

            aristocracyInfo.setAristocracyId(aristocracyConfigDTO.getId());
            aristocracyInfo.setIconUrl(aristocracyConfigDTO.getIconUrl());
            aristocracyInfo.setNameMap(nameMap);

            textMsgModel.setAristocracyInfo(aristocracyInfo);

            String textMsgModelStr = JSONUtil.toJsonStr(textMsgModel);
            CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                    IMMsgType.Aristocracy,
                    System.currentTimeMillis(), textMsgModelStr);
            OfflinePushInfo offlinePushInfo = null;
            try {
                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                        offlineTitle,
                        offlineText,
                        aristocracyInfo.getIconUrl(),
                        textMsgModel.getLinkUrl());
            } catch (Exception e) {
                // ignore
                log.info("sendAristocracyInfo build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
            }

            notifyMessageComponent.publishSystemDefineMessage(imMessage, targetUid + "",
                    offlinePushInfo);
            log.info("aristocracy send message: [{}]  targetUid: [{}]", textMsgModelStr, targetUid);
        } catch (Exception e) {
            log.error("aristocracy send message error.targetUid:{},{}", targetUid, ExceptionUtil.formatEx(e));
        }
    }


    @XxlJob("testAristocracyPush")
    public void TestPush(){
        long userId = 35181460;
        Optional<AristocracyConfigDTO> aristocracyConfigById = aristocracyConfigService.getAristocracyConfigById(5);
        bannerServerService.pushAristocracyRoomNotice(userId, aristocracyConfigById.get());

        // 上线
        bannerServerService.pushAristocracyOnline(userId, aristocracyConfigById.get());
    }
}
