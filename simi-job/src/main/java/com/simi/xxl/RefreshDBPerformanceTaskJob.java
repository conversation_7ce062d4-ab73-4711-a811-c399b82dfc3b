package com.simi.xxl;

import com.alibaba.fastjson.JSON;
import com.simi.common.dto.xxl.RefreshDBPerformanceParamDTO;
import com.simi.service.job.BDPerformanceService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Objects;


@Slf4j
@Service
@AllArgsConstructor
public class RefreshDBPerformanceTaskJob {

    private final BDPerformanceService bdPerformanceService;

    /**
     *
     * @throws Exception
     */
    @XxlJob("refreshDBPerformanceTaskJob")
    public void refreshDBPerformanceTaskJob() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("RefreshDBPerformanceTaskJob start,jobParam:{}",jobParam);
        doRefreshDBPerformanceTaskJob(jobParam);
    }

    public void doRefreshDBPerformanceTaskJob(String jobParam){
        jobParam = StringUtils.isBlank(jobParam) ? "" : jobParam;
        RefreshDBPerformanceParamDTO refreshDBPerformanceParamDTO = JSON.parseObject(jobParam, RefreshDBPerformanceParamDTO.class);
        if (Objects.isNull(refreshDBPerformanceParamDTO)){
            refreshDBPerformanceParamDTO = new RefreshDBPerformanceParamDTO();
        }
        bdPerformanceService.refreshBDPerformance(refreshDBPerformanceParamDTO.getPerformanceDate(), refreshDBPerformanceParamDTO.getBdId());
    }

    public void doFreshIncrement(Long uid,String tableName){
        bdPerformanceService.incrementRefresh(uid,tableName);
    }
}
