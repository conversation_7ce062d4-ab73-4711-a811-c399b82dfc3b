package com.simi.xxl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.dto.room.RoomAudienceDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.room.MicStateInfoDTO;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.RoomRedisKey;
import com.simi.service.LongLinkService;
import com.simi.service.cache.HomeRoomCache;
import com.simi.service.internal.InternalService;
import com.simi.service.room.RoomHighService;
import com.simi.service.room.roommic.RoomMicHighService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.redisson.client.protocol.ScoredEntry;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * @description:
 * @author: lrq
 * @create: 2024-09-09 16:09
 **/
@Slf4j
@Service
@AllArgsConstructor
public class RoomTask {
    private final HomeRoomCache homeRoomCache;
    private final RedissonClient redissonClient;
    private final RoomMicHighService roomMicHighService;
    private final LongLinkService longLinkService;
    private final RoomHighService roomHighService;
    private final SystemConfigService systemConfigService;
    private final InternalService internalService;

    @PostConstruct
    public void init() {

    }

    /**
     * 校对房间内数据
     */
//    @XxlJob("checkRoomData")
//    public void checkRoomData() throws InterruptedException {
//        try {
//            String fruitRankDate = systemConfigService.getSysConfValueById(SystemConfigConstant.ROOM_DATA_CORRECTION_SWITCH);
//            log.info("校对房间内数据 start.switch:{}", fruitRankDate);
//            boolean dataSwitch = false;
//            if (StringUtils.isNotBlank(fruitRankDate) && "true".equals(fruitRankDate)) {
//                dataSwitch = true;
//            }
//            // 获取所有在线房间
//            List<String> homeRooms = homeRoomCache.getOverall(0, 100);
//            List<String> roomIds = new ArrayList<>();
//            // 房间的热度
//            HashMap<String, Long> map = new HashMap<>();
//            if (CollectionUtils.isNotEmpty(homeRooms)) {
//                for (String homeRoom : homeRooms) {
//                    String[] split = homeRoom.split(":");
//                    roomIds.add(split[0]);
//                    map.put(split[0],Long.valueOf(split[1]));
//                }
//            }
//            log.info("校对房间内数据 处理的房间:{}", roomIds);
//            for (int i = 0; i < roomIds.size(); i++) {
//                try {
//                    String roomId = roomIds.get(i);
//                    // 获取房间的在线列表
//                    RScoredSortedSet<String> audienceRScore = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)));
//                    Collection<ScoredEntry<String>> audienceEntries = audienceRScore.entryRange(0, -1);
//                    List<Long> uids = audienceEntries.stream().map(e -> Long.parseLong(e.getValue())).toList();
//                    log.info("校对房间内数据 roomId:{} 用户:[{}]", roomId, uids);
//
//                    // 获取即构的在线列表
//                    try {
//                        List<Long> roomOnlineList = internalService.getRoomOnlineList(Long.parseLong(roomId));
//                        log.info("校对房间内数据 即构的在线列表 roomId:{} 用户:[{}]", roomId, roomOnlineList);
//                        List<Long> noExistUid = new ArrayList<>();
//                        // 比较，机构在线房间不在线
//                        for (Long l : roomOnlineList) {
//                            if (!uids.contains(l)) {
//                                noExistUid.add(l);
//                            }
//                        }
//                        log.info("校对房间内数据 数据不一致 机构在房，服务端不在 roomId:{} noExistUid:[{}]", roomId, noExistUid);
//                        noExistUid.clear();
//                        for (Long uid : uids) {
//                            if (!roomOnlineList.contains(uid)) {
//                                noExistUid.add(uid);
//                                if (dataSwitch) {
//                                    try {
//                                        roomHighService.outRoom(uid, roomId, new Date());
//                                    } catch (Exception e) {
//                                        log.error("校对房间内数据 提出用户离房失败 error {}", ExceptionUtil.formatEx(e), e);
//                                    }
//                                }
//                            }
//                        }
//                        log.info("校对房间内数据 数据不一致 服务端在房，机构不在 roomId:{} noExistUid:[{}]", roomId, noExistUid);
//                    } catch (Exception e) {
//                        log.error("校对房间内数据 获取即构的在线列表 error {}", ExceptionUtil.formatEx(e), e);
//                    }
//
//
//                    // 获取房间的在线列表
//                    audienceRScore = redissonClient.getScoredSortedSet(RoomRedisKey.room_audience.getKey(StrUtil.format("{{}}", roomId)));
//                    audienceEntries = audienceRScore.entryRange(0, -1);
//                    uids = audienceEntries.stream().map(e -> Long.parseLong(e.getValue())).toList();
//                    log.info("校对房间内数据 再次获取在线列表数据 roomId:{} 用户:[{}]", roomId, uids);
//
//                    // 处理麦位列表
//                    List<MicStateInfoDTO> micStateInfoDTOS = roomMicHighService.listMicInfo(roomId);
//                    log.info("校对房间内数据 roomId:{} 麦位:[{}]", roomId, micStateInfoDTOS);
//
//                    boolean hadSend = false;
//                    // 获取在麦的用户
//                    for (MicStateInfoDTO k : micStateInfoDTOS) {
//                        if (Objects.nonNull(k.getRoomUserBaseDto()) && Objects.nonNull(k.getRoomUserBaseDto().getUserBase())) {
//                            UserBaseInfoDTO userBase = k.getRoomUserBaseDto().getUserBase();
//                            Long uid = userBase.getUid();
//                            log.info("校对房间内数据 roomId:{} 用户:{} 麦位:{}", roomId, uid, k.getPosition());
//                            // 如果用户不在在线列表
//                            if (Objects.nonNull(uid) && !uids.contains(uid)) {
//                                log.info("校对房间内数据 不在线却在麦 roomId:{} 用户:{} ", roomId, uid);
//                                if (dataSwitch) {
//                                    roomMicHighService.downMic(uid, roomId, k.getPosition());
//                                }
//                                hadSend = true;
//                            }
//                        }
//                    }
//
//                    // 发送麦位更新事件
//                    if (!hadSend && dataSwitch) {
//                        longLinkService
//                                .pushMicChangedNotice(roomId, roomMicHighService.listMicInfo(roomId), PushEvent.room_mic_update_event, PushToType.MESSAGE_TO_ALL);
//                    }
//
//                    // 发送在线列表更新事件
//                    RoomAudienceDTO roomAudienceDTO = roomHighService.audienceTop(roomId, 3);
//                    if (roomAudienceDTO != null) {
//                        longLinkService.pushCustomerRoomMsg(roomId, roomAudienceDTO, PushEvent.room_audience_update_event, PushToType.MESSAGE_TO_ALL);
//                    }
//
//                    // 校对房间的热度
//                    Integer hotScore = homeRoomCache.getHotScore(roomId);
//                    Integer roomHotScore = Objects.isNull(map.get(roomId)) ? 0 :  map.get(roomId).intValue();
//                    log.info("校对房间内数据 roomId:{} 热度:{} 房间热度:{}", roomId, hotScore, roomHotScore);
//                    if (!Objects.equals(hotScore, roomHotScore)) {
//                        // 再次校验
//                        HashMap<String, Long> homeRoomMap = getHomeRoom();
//                        Integer lastRoomHotScore = Objects.isNull(homeRoomMap.get(roomId)) ? 0 :  homeRoomMap.get(roomId).intValue();
//                        if (!Objects.equals(hotScore, lastRoomHotScore)) {
//                            log.error("校对房间内数据 热度不一致 roomId:{}  hotScore:{} roomHotScore:{},lastRoomHotScore:{}",
//                                    roomId, hotScore, roomHotScore, lastRoomHotScore);
//                        }
//                    }
//
//                    // 避免热度房间更新更频繁
//                    if (i < 10) {
//                        Thread.sleep(2000);
//                    }else {
//                        Thread.sleep(1000);
//                    }
//                } catch (InterruptedException e) {
//                    log.error("校对房间内数据 error {}", ExceptionUtil.formatEx(e), e);
//                }
//            }
//        } catch (Exception e) {
//            log.error("校对房间内数据 error {}", ExceptionUtil.formatEx(e), e);
//            throw  e;
//        }
//    }

    private HashMap<String, Long> getHomeRoom(){
        // 获取所有在线房间
        List<String> homeRooms = homeRoomCache.getOverall(0, 100);
        List<String> roomIds = new ArrayList<>();
        // 房间的热度
        HashMap<String, Long> map = new HashMap<>();
        if (CollectionUtils.isNotEmpty(homeRooms)) {
            for (String homeRoom : homeRooms) {
                String[] split = homeRoom.split(":");
                roomIds.add(split[0]);
                map.put(split[0],Long.valueOf(split[1]));
            }
        }
        return map;
    }


}
