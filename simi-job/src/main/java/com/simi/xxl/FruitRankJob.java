package com.simi.xxl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.simi.common.config.PkRankConfig;
import com.simi.common.constant.RewardPackCopywritingEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.TimeZoneEnum;
import com.simi.common.constant.rank.RankRedisKey;
import com.simi.common.dto.activity.prize.PrizeDTO;
import com.simi.common.dto.rank.RankScoreWeeklyStarRank;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.common.vo.rank.GenericRankVO;
import com.simi.constant.UserRedisKey;
import com.simi.entity.rank.RankScoreHistory;
import com.simi.service.EventTrackingService;
import com.simi.service.activity.rank.FruitRankService;
import com.simi.service.rank.RankScoreHistoryService;
import com.simi.service.rewardpack.RewardPackServerService;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: lrq
 * @create: 2024-09-02 14:38
 **/
@Slf4j
@Service
@AllArgsConstructor
public class FruitRankJob {

    private final static List<String> fruits = Lists.newArrayList("cherry", "grape",
            "watermelon", "apple", "lemon", "orange", "mango", "strawberry");

    private final SystemConfigService systemConfigService;
    private final FruitRankService fruitRankHandler;
    private final RedissonManager redissonManager;
    private final PkRankConfig pkRankConfig;
    private final RewardPackServerService rewardPackServerService;
    private final RankScoreHistoryService rankScoreHistoryService;
    private final EventTrackingService eventTrackingService;

    /*@PostConstruct
    public void init() {
        fruitRankJob();
    }*/


    /**
     * 赢家总榜
     */
    @XxlJob("fruitWinnerJob")
    public void fruitRankJob() {
        log.info("赢家总榜");
        // 判断活动是否开启
        String fruitRankDate = systemConfigService.getSysConfValueById(SystemConfigConstant.FRUIT_RANK_START);
        boolean fruitTest = pkRankConfig.isFruitTest();
        // 转成日期
        if (StringUtils.isBlank(fruitRankDate)) {
            log.info("fruitRankJob fruitRankDate is null");
            return;
        }
        // yyyy-MM-dd hh:mm:ss 转成 Date 这个是0时区的时间
        Date date = DateUtil.parse(fruitRankDate);
        // -3个小时 转成 UTC+3 时区的时间
        Date recordTime = TimeZoneUtils.getTime(date, TimeZoneEnum.GMT_WEST3).getRecordTime();
        // 获取日期
        String dayFormat = DateUtil.format(DateUtil.beginOfDay(date), DateTimeUtil.DAY_PATTERN);
        long startTime = recordTime.getTime();

        log.info("赢家总榜结算 开始日期 dayFormat {} {}", dayFormat, startTime);
        // 判断是否在8天内
        // 8天时间
        long endTime = startTime + 8 * 24 * 60 * 60 * 1000;
        // 当前时间
        long nowTime = System.currentTimeMillis();
        if (nowTime < startTime) {
            log.info("赢家总榜结算 活动未开启 ");
            return;
        }
        // 判断今天是否第9天
        // 计算目前是活动开始的第 X 天，注意从0开始
        int dayIndex = (int) ((nowTime - startTime) / (1000 * DateTimeUtil.SECONDS_PER_DAY));
        log.info("赢家总榜结算 dayIndex:{}", dayIndex);
        // 第9天，结算总榜
        if (dayIndex == 8 || fruitTest) {
            GenericRankVO genericRankVO = fruitRankHandler.commonlyRankInfo(null, 1, 2, dayFormat, 0);
            List<GenericRankVO.Rank> rankVOs = genericRankVO.getRankVOs();
            if (CollectionUtil.isNotEmpty(rankVOs)) {
                GenericRankVO.Rank rank = genericRankVO.getRankVOs().get(0);
                redissonManager.set(UserRedisKey.fruit_winner.getKey(), rank.getUid() + "", 30, TimeUnit.DAYS);
                Integer fruitTotalRankId = pkRankConfig.getFruitTotalRankId();
                log.info("赢家总榜结算 uid:{},fruitTotalRankId:{}", rank.getUid(), fruitTotalRankId);
                // 发送奖励
                PrizeDTO prizeDTO = PrizeDTO.builder()
                        .uid(rank.getUid())
                        .packId(fruitTotalRankId)
                        .sourceTypeEnum(RewardPackCopywritingEnum.FRUIT_LOTTERY)
                        .bizOrderId(System.currentTimeMillis())
                        .copywritingPlaceholder("").build();
                rewardPackServerService.sendRewardPack(prizeDTO);
                log.info("赢家总榜结算 发送奖励成功.uid:{},rewardId:{}", rank.getUid(), fruitTotalRankId);

                List<RankScoreWeeklyStarRank> starRanks = new ArrayList<>();
                rankVOs.forEach(k -> {
                    RankScoreWeeklyStarRank rankInfo = new RankScoreWeeklyStarRank();
                    rankInfo.setUserId(k.getUid());
                    rankInfo.setCount(k.getRankVal().intValue());
                    starRanks.add(rankInfo);
                });

                String key = RankRedisKey.fruit_rank_win_count.getKey(dayFormat);
                RankScoreHistory rankScoreHistory = RankScoreHistory.builder()
                        .startTime(new Date(startTime))
                        .endTime(new Date(endTime))
                        .rankKey(key)
                        .rankInfo(JSONUtil.toJsonStr(starRanks))
                        .createTime(new Date())
                        .modifyTime(new Date())
                        .build();
                rankScoreHistoryService.save(rankScoreHistory);
            }


            genericRankVO = fruitRankHandler.commonlyRankInfo(null, 1, 3, dayFormat, 0);
            rankVOs = genericRankVO.getRankVOs();
            if (CollectionUtil.isNotEmpty(rankVOs)) {
                GenericRankVO.Rank rank = genericRankVO.getRankVOs().get(0);
                Integer fruitPlaysRankId = pkRankConfig.getFruitPlaysRankId();
                log.info("局数总榜结算 uid:{},fruitTotalRankId:{}", rank.getUid(), fruitPlaysRankId);
                // 发送奖励
                PrizeDTO prizeDTO = PrizeDTO.builder()
                        .uid(rank.getUid())
                        .packId(fruitPlaysRankId)
                        .sourceTypeEnum(RewardPackCopywritingEnum.FRUIT_LOTTERY_TOTAL)
                        .bizOrderId(System.currentTimeMillis())
                        .copywritingPlaceholder("").build();
                rewardPackServerService.sendRewardPack(prizeDTO);
                log.info("局数总榜结算 发送奖励成功.uid:{},rewardId:{}", rank.getUid(), fruitPlaysRankId);


                List<RankScoreWeeklyStarRank> starRanks = new ArrayList<>();
                rankVOs.forEach(k -> {
                    RankScoreWeeklyStarRank rankInfo = new RankScoreWeeklyStarRank();
                    rankInfo.setUserId(k.getUid());
                    rankInfo.setCount(k.getRankVal().intValue());
                    starRanks.add(rankInfo);
                });

                String key = RankRedisKey.fruit_rank_bets_count.getKey(dayFormat);
                RankScoreHistory rankScoreHistory = RankScoreHistory.builder()
                        .startTime(new Date(startTime))
                        .endTime(new Date(endTime))
                        .rankKey(key)
                        .rankInfo(JSONUtil.toJsonStr(starRanks))
                        .createTime(new Date())
                        .modifyTime(new Date())
                        .build();
                rankScoreHistoryService.save(rankScoreHistory);
            }


            // fruit 总参与人数
            String totalKey = RankRedisKey.fruit_rank_total_participant_count.getKey(dayFormat);
            int totalSize = redissonManager.sMembers(totalKey).size();

            log.info("水果机 活动结算统计数据 总参与人数:{}", totalSize);
            Map<String, Integer> value = new HashMap<>();
            value.put("total_number_of_participants_in_the_event", totalSize);
            eventTrackingService.eventTrackingForFruitMachinePerDay(value);

        }

    }
    @XxlJob("fruitPerData")
    public void pushPerDayData(){
        log.info("水果机 每天统计数据");
        // 判断活动是否开启
        String fruitRankDate = systemConfigService.getSysConfValueById(SystemConfigConstant.FRUIT_RANK_START);
        // 转成日期
        if (StringUtils.isBlank(fruitRankDate)) {
            log.info("水果机 每天统计数据 fruitRankDate is null");
            return;
        }
        // yyyy-MM-dd hh:mm:ss 转成 Date 这个是0时区的时间
        Date date = DateUtil.parse(fruitRankDate);
        // -3个小时 转成 UTC+3 时区的时间
        Date recordTime = TimeZoneUtils.getTime(date, TimeZoneEnum.GMT_WEST3).getRecordTime();
        // 获取日期
        String dayFormat = DateUtil.format(DateUtil.beginOfDay(date), DateTimeUtil.DAY_PATTERN);
        long startTime = recordTime.getTime();

        log.info("水果机 每天统计数据 开始日期 dayFormat {} {}", dayFormat, startTime);
        // 判断是否在8天内
        // 8天时间
        long endTime = startTime + 8 * 24 * 60 * 60 * 1000;
        // 当前时间
        long nowTime = System.currentTimeMillis();
        if (nowTime < startTime) {
            log.info("水果机 每天统计数据 活动未开启 ");
            return;
        }
        if (nowTime > endTime) {
            log.info("水果机 每天统计数据 活动已经结束 ");
            return;
        }
        // 计算目前是活动开始的第 X 天 注意：从0天开始
        int dayIndex = (int) ((nowTime - startTime) / (1000 * DateTimeUtil.SECONDS_PER_DAY));
        // 计算上一天
        dayIndex--;

        // fruit 参与人数
        String countKey = RankRedisKey.fruit_rank_participant_count.getKey(dayFormat, dayIndex);
        int size = redissonManager.sMembers(countKey).size();

        // fruit 总参与人数
        String totalKey = RankRedisKey.fruit_rank_total_participant_count.getKey(dayFormat);
        int totalSize = redissonManager.sMembers(totalKey).size();

        // fruit 下注金额
        String betKey = RankRedisKey.fruit_rank_bets_amount.getKey(dayFormat, dayIndex);
        String betsStr = redissonManager.get(betKey);
        int bets = StringUtils.isBlank(betsStr) ? 0 : Integer.parseInt(betsStr);

        // 中奖金额
        String amountKey = RankRedisKey.fruit_rank_total_amount.getKey(dayFormat, dayIndex);
        String winTotalStr = redissonManager.get(amountKey);
        int winTotal = StringUtils.isBlank(winTotalStr) ? 0 : Integer.parseInt(winTotalStr);

        // 平台获利
        int platformProfit = bets - winTotal;

        log.info("水果机 每天统计数据 参与人数:{},总参与人数:{},下注金额:{},中奖金额:{},platformProfit:{}",
                size, totalSize, bets, winTotal, platformProfit);
        Map<String, Integer> value = new HashMap<>();
        value.put("number_of_participants_per_day", size);
        value.put("daily_event_betting_amount", bets);
        value.put("winning_amount", winTotal);
        value.put("platform_profit_amount", platformProfit);
        eventTrackingService.eventTrackingForFruitMachinePerDay(value);
    }

    @XxlJob("fruitPerData_test")
    public void pushPerDayDataTest(){
        // fruit 参与人数
        String countKey = RankRedisKey.fruit_rank_participant_count.getKey("Test");
        int size = redissonManager.sMembers(countKey).size();

        // fruit 总参与人数
        String totalKey = RankRedisKey.fruit_rank_total_participant_count.getKey("Test");
        int totalSize = redissonManager.sMembers(totalKey).size();

        // fruit 下注金额
        String betKey = RankRedisKey.fruit_rank_bets_amount.getKey("Test");
        String betsStr = redissonManager.get(betKey);
        int bets = StringUtils.isBlank(betsStr) ? 0 : Integer.parseInt(betsStr);

        // 中奖金额
        String amountKey = RankRedisKey.fruit_rank_total_amount.getKey("Test");
        String winTotalStr = redissonManager.get(amountKey);
        int winTotal = StringUtils.isBlank(winTotalStr) ? 0 : Integer.parseInt(winTotalStr);

        // 平台获利
        int platformProfit = bets - winTotal;

        log.info("水果机 每天统计数据Test 参与人数:{},总参与人数:{},下注金额:{},中奖金额:{},platformProfit:{}",
                size, totalSize, bets, winTotal, platformProfit);
        Map<String, Integer> value = new HashMap<>();
        value.put("number_of_participants_per_day", 10);
        value.put("daily_event_betting_amount", 10);
        value.put("winning_amount", 10);
        value.put("platform_profit_amount", 10);
        eventTrackingService.eventTrackingForFruitMachinePerDay(value);
    }


}
