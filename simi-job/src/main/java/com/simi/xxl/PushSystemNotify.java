package com.simi.xxl;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.fixed.FixPushNotifyDTO;
import com.simi.common.dto.xxl.PushActSystemNotifyDTO;
import com.simi.common.handler.MessageSourceUtil;
import com.simi.service.FixedService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/07/24 17:26
 **/
@Slf4j
@Service
public class PushSystemNotify {

    @Autowired
    private FixedService fixedService;

    @XxlJob("testPushActSystemNotify")
    public void testPushActSystemNotify() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle testPushActSystemNotify jobParam:[{}]", jobParam);
        FixPushNotifyDTO map = JSONUtil.toBean(jobParam, FixPushNotifyDTO.class);
        List<Long> uids = map.getUids();
        uids.forEach(uid -> fixedService.pushSystemNotify(uid, map.getTitleMap(), map.getTextMap(),
                map.getLinkUrl(), map.getPicUrl(), map.getRoomId(), true));
    }

    @XxlJob("pushActToActiveUserSystemNotifyOne")
    public void pushActToActiveUserSystemNotifyOne() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle pushActToActiveUserSystemNotify jobParam:[{}]", jobParam);
        PushActSystemNotifyDTO notifyDTO = JSONUtil.toBean(jobParam, PushActSystemNotifyDTO.class);

        String arStrTitle = MessageSourceUtil.i18nByCode(notifyDTO.getTitleMapKey(), LanguageEnum.ar);
        TranslationCopyDTO titleMap = TranslationCopyDTO.builder().ar(arStrTitle).en(arStrTitle).build();

        String arStrText = MessageSourceUtil.i18nByCode(notifyDTO.getTextMapKey(), LanguageEnum.ar);
        TranslationCopyDTO textMap = TranslationCopyDTO.builder().ar(arStrText).en(arStrText).build();

        fixedService.pushRecentlySystemNotify(titleMap, textMap, notifyDTO.getLinkUrl(),
                notifyDTO.getPicUrl(), notifyDTO.getRoomId(), notifyDTO.getCheckOnline(), notifyDTO.getOffsetDay(), notifyDTO.getUids());
    }

    @XxlJob("pushActToActiveUserSystemNotifyTwo")
    public void pushActToActiveUserSystemNotifyTwo() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle pushActToActiveUserSystemNotify jobParam:[{}]", jobParam);
        PushActSystemNotifyDTO notifyDTO = JSONUtil.toBean(jobParam, PushActSystemNotifyDTO.class);

        String arStrTitle = MessageSourceUtil.i18nByCode(notifyDTO.getTitleMapKey(), LanguageEnum.ar);
        TranslationCopyDTO titleMap = TranslationCopyDTO.builder().ar(arStrTitle).en(arStrTitle).build();

        String arStrText = MessageSourceUtil.i18nByCode(notifyDTO.getTextMapKey(), LanguageEnum.ar);
        TranslationCopyDTO textMap = TranslationCopyDTO.builder().ar(arStrText).en(arStrText).build();

        fixedService.pushRecentlySystemNotify(titleMap, textMap, notifyDTO.getLinkUrl(),
                notifyDTO.getPicUrl(), notifyDTO.getRoomId(), notifyDTO.getCheckOnline(), notifyDTO.getOffsetDay(), notifyDTO.getUids());
    }

    @XxlJob("pushActToActiveUserSystemNotifyThree")
    public void pushActToActiveUserSystemNotifyThree() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle pushActToActiveUserSystemNotify jobParam:[{}]", jobParam);
        PushActSystemNotifyDTO notifyDTO = JSONUtil.toBean(jobParam, PushActSystemNotifyDTO.class);

        String arStrTitle = MessageSourceUtil.i18nByCode(notifyDTO.getTitleMapKey(), LanguageEnum.ar);
        TranslationCopyDTO titleMap = TranslationCopyDTO.builder().ar(arStrTitle).en(arStrTitle).build();

        String arStrText = MessageSourceUtil.i18nByCode(notifyDTO.getTextMapKey(), LanguageEnum.ar);
        TranslationCopyDTO textMap = TranslationCopyDTO.builder().ar(arStrText).en(arStrText).build();

        fixedService.pushRecentlySystemNotify(titleMap, textMap, notifyDTO.getLinkUrl(),
                notifyDTO.getPicUrl(), notifyDTO.getRoomId(), notifyDTO.getCheckOnline(), notifyDTO.getOffsetDay(), notifyDTO.getUids());
    }

    @XxlJob("pushActToActiveUserSystemNotifyFour")
    public void pushActToActiveUserSystemNotifyFour() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle pushActToActiveUserSystemNotify jobParam:[{}]", jobParam);
        PushActSystemNotifyDTO notifyDTO = JSONUtil.toBean(jobParam, PushActSystemNotifyDTO.class);

        String arStrTitle = MessageSourceUtil.i18nByCode(notifyDTO.getTitleMapKey(), LanguageEnum.ar);
        TranslationCopyDTO titleMap = TranslationCopyDTO.builder().ar(arStrTitle).en(arStrTitle).build();

        String arStrText = MessageSourceUtil.i18nByCode(notifyDTO.getTextMapKey(), LanguageEnum.ar);
        TranslationCopyDTO textMap = TranslationCopyDTO.builder().ar(arStrText).en(arStrText).build();

        fixedService.pushRecentlySystemNotify(titleMap, textMap, notifyDTO.getLinkUrl(),
                notifyDTO.getPicUrl(), notifyDTO.getRoomId(), notifyDTO.getCheckOnline(), notifyDTO.getOffsetDay(), notifyDTO.getUids());
    }

    @XxlJob("pushActToActiveUserSystemNotifyFive")
    public void pushActToActiveUserSystemNotifyFive() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle pushActToActiveUserSystemNotify jobParam:[{}]", jobParam);
        PushActSystemNotifyDTO notifyDTO = JSONUtil.toBean(jobParam, PushActSystemNotifyDTO.class);

        String arStrTitle = MessageSourceUtil.i18nByCode(notifyDTO.getTitleMapKey(), LanguageEnum.ar);
        String enStrTitle = MessageSourceUtil.i18nByCode(notifyDTO.getTitleMapKey(), LanguageEnum.en);
        TranslationCopyDTO titleMap = TranslationCopyDTO.builder().ar(arStrTitle).en(enStrTitle).build();

        String arStrText = MessageSourceUtil.i18nByCode(notifyDTO.getTextMapKey(), LanguageEnum.ar);
        String enStrText = MessageSourceUtil.i18nByCode(notifyDTO.getTextMapKey(), LanguageEnum.en);
        TranslationCopyDTO textMap = TranslationCopyDTO.builder().ar(arStrText).en(enStrText).build();

        fixedService.pushRecentlySystemNotify(titleMap, textMap, notifyDTO.getLinkUrl(),
                notifyDTO.getPicUrl(), notifyDTO.getRoomId(), notifyDTO.getCheckOnline(), notifyDTO.getOffsetDay(), notifyDTO.getUids());
    }
}
