package com.simi.xxl;


import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.simi.common.dto.xxl.ReportDTO;
import com.simi.service.job.RefreshPartyReportService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;


@Slf4j
@Service
@AllArgsConstructor
public class RefreshPartyReportTaskJob {

    private final RefreshPartyReportService refreshPartyReportService;


    /**
     *
     * @throws Exception
     */
    @XxlJob("handleRefreshPartyReportTaskJob")
    public void handleRefreshPartyReportTaskJob() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("handleRefreshPartyReportTaskJob start,jobParam:{}",jobParam);
        String date = "";
        if (StringUtils.isNotBlank(jobParam)){
            ReportDTO reportDTO = JSON.parseObject(jobParam, ReportDTO.class);
            date = reportDTO.getDate();
        }
        refreshPartyReportService.doRefresh(date);
    }

}
