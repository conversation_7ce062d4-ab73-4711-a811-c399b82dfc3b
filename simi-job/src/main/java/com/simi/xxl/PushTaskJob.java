package com.simi.xxl;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.pushMsg.AutoPushMsgTypeEnum;
import com.simi.common.dto.TranslationCopyDTO;
import com.simi.common.dto.xxl.TestPushDTO;
import com.simi.common.dto.xxl.TestBatchTimMsgDTO;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.push.CmsPushManager;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/08/13 17:49
 **/
@Slf4j
@Service
@AllArgsConstructor
public class PushTaskJob {

    private final NotifyMessageComponent notifyMessageComponent;
    private final CmsPushManager cmsPushManager;

    @XxlJob("testBatchTimMsg")
    public void testBatchTimMsg() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle testBatchTimMsg jobParam:[{}]", jobParam);

        TestBatchTimMsgDTO notifyDTO = JSONUtil.toBean(jobParam, TestBatchTimMsgDTO.class);

        notifyMessageComponent.batchPushSystemComplexIMMsg(notifyDTO.getUids(), notifyDTO.getLinkUrl(),
                notifyDTO.getTitleTranslation(),
                notifyDTO.getTextTranslation(),
                notifyDTO.getTitleTranslation().getEn(),
                notifyDTO.getTextTranslation().getEn(),
                notifyDTO.getImageTranslation().getEn());
    }

    @XxlJob("testAllStaffPushService")
    public void testAllStaffPushService() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle testAllStaffPushService jobParam:[{}]", jobParam);

        TestPushDTO notifyDTO = JSONUtil.toBean(jobParam, TestPushDTO.class);
        TranslationCopyDTO title = TranslationCopyDTO.builder().ar(notifyDTO.getTitle()).en(notifyDTO.getTitle()).build();
        TranslationCopyDTO text = TranslationCopyDTO.builder().ar(notifyDTO.getText()).en(notifyDTO.getText()).build();
        notifyMessageComponent.allStaffPushService(notifyDTO.getLinkUrl(), title, text);
    }


    @XxlJob("testSingleShotPushService")
    public void testSingleShotPushService() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle testSingleShotPushService jobParam:[{}]", jobParam);

        TestPushDTO notifyDTO = JSONUtil.toBean(jobParam, TestPushDTO.class);
        TranslationCopyDTO title = TranslationCopyDTO.builder().ar(notifyDTO.getTitle()).en(notifyDTO.getTitle()).build();
        TranslationCopyDTO text = TranslationCopyDTO.builder().ar(notifyDTO.getText()).en(notifyDTO.getText()).build();
        notifyMessageComponent.singleShotPushService(null, notifyDTO.getUids(), notifyDTO.getTitle(),
                notifyDTO.getText(), notifyDTO.getLinkUrl(), notifyDTO.getImage());
    }

    @XxlJob("pushCmsPushMsgHandMovement")
    public void pushCmsPushMsgHandMovement() throws Exception {
        log.info("xxl-Job handle pushCmsPushMsgHandMovement");
        cmsPushManager.pushCmsPushMsgHandMovement();
    }

    @XxlJob("autoPushMsg")
    public void autoPushMsg() throws Exception {
        String jobParam = XxlJobHelper.getJobParam();
        log.info("xxl-Job handle autoPushMsg param:[{}]", jobParam);
        AutoPushMsgTypeEnum typeEnum = AutoPushMsgTypeEnum.getByType(Integer.parseInt(jobParam));
        if (Objects.isNull(typeEnum)) {
            log.info("xxl-Job handle autoPushMsg param error:[{}]", jobParam);
            return;
        }
        cmsPushManager.autoPushMsg(typeEnum);
    }


}
