package com.simi.xxl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.TimeZoneEnum;
import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.constant.vip.VIPConstant;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.dto.vip.config.VipConfigDTO;
import com.simi.common.dto.vip.config.VipProConfigDTO;
import com.simi.common.dto.vip.config.VipRemainDayConfigDTO;
import com.simi.common.entity.CommonIMMessage;
import com.simi.common.util.DateTimeUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.util.RedissonManager;
import com.simi.common.util.TimeZoneUtils;
import com.simi.constant.UserRedisKey;
import com.simi.dto.push.VipMsgDTO;
import com.simi.entity.vip.UserVipRecord;
import com.simi.entity.vip.UserVipStatus;
import com.simi.manager.VipManager;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.rewardpack.RewardPackServerService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import com.simi.service.vip.UserVipStatusService;
import com.simi.service.vip.VipConfigService;
import com.simi.service.vip.VipRecordService;
import com.simi.util.PushMsgUtil;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: lrq
 * @create: 2024-08-16 17:33
 **/
@Slf4j
@Service
@AllArgsConstructor
public class VIPTask {

    private final RedissonManager redissonManager;
    private final VipConfigService vipConfigService;
    private final UserVipStatusService userVipStatusService;
    private final VipRecordService vipRecordService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final UserVipService userVipService;
    private final VipManager vipManager;
    private final RewardPackServerService rewardPackServerService;
    private final UserServerService userServerService;

  /*  @PostConstruct
    public void init()  {

        handleExpire();

    }*/


    /**
     * 重新刷新用户VIP信息
     */
    @XxlJob("refreshVipInfoJob")
    public void refreshVipInfoJob() {
        log.info("refreshVipInfoJob, star");
        // 批量查询用户vip 信息
        LambdaQueryWrapper<UserVipStatus> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserVipStatus::getVipStatus, VIPConstant.Status.EFFECTIVE.getStatus());
        wrapper.orderBy(true, true, UserVipStatus::getId);
        List<UserVipStatus> vipStatuses = userVipStatusService.list(wrapper);
        vipStatuses.forEach(userVipStatus -> {
            // 刷新缓存
            try {
                userVipService.flushCache(userVipStatus.getUserId());
                log.info("refreshVipInfoJob, end,uid:{}", userVipStatus.getUserId());
            } catch (Exception e) {
                log.error("refreshVipInfoJob error, userId:{}", userVipStatus.getUserId(), e);
            }
        });
    }

    /**
     * 每月扣减经验
     */
    @XxlJob("vipDeductionExperienceJob")
    public void execute() {
        log.info("vipDeductionExperienceJob, star");

        // 获取上月升级的用户
        Date date = new Date();
        DateTime dateTime = DateUtil.offsetDay(date, -1);
        TimeZoneDateDTO timeZoneDateDTO = TimeZoneUtils.getTime(dateTime, TimeZoneEnum.GMT3);
        String monthKey = DateUtil.format(DateUtil.beginOfMonth(timeZoneDateDTO.getRecordTime()), DateTimeUtil.CACHE_MONTH_PATTERN);
        String key = UserRedisKey.user_vip_upgrade.getKey(monthKey);
        Map<String, String> userIdMaps = redissonManager.hGetAll(key);
        Set<Long> userIds = userIdMaps.keySet().stream().map(Long::parseLong).collect(Collectors.toSet());
        log.info("vip vipDeductionExperienceJob 本月升级的用户:{}", userIds);

        // 获取大于等于2级的用户
        Map<Integer, VipProConfigDTO> vipProConfigDTOMap = vipConfigService.getVipProConfigDTOMap();
        VipProConfigDTO vipProConfigDTO = vipProConfigDTOMap.get(2);
        int experience = vipProConfigDTO.getExperience();
        // 批量查询用户vip 信息
        LambdaQueryWrapper<UserVipStatus> wrapper = new LambdaQueryWrapper<>();
        wrapper.ge(UserVipStatus::getVipExperience, experience);
        wrapper.orderBy(true, true, UserVipStatus::getId);
        List<UserVipStatus> records = userVipStatusService.list(wrapper);
        log.info("vip vipDeductionExperienceJob 处理数据数量。size:{}", records.size());
        deductionExperience(records, userIds);
    }

    /**
     *
     * @param records
     * @param userIds 上月升级的用户
     */
    private void deductionExperience(List<UserVipStatus> records, Set<Long> userIds) {
        Date recordTime = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3).getRecordTime();

        // 扣减经验
        // 如果用户上个月升级了，则不扣减
        records.stream().filter(k -> !userIds.contains(k.getUserId())).forEach(k -> {
            Long userId = k.getUserId();
            // 扣减
            Optional<UserCurVipInfo> optional = userVipStatusService.deductionExperienceByLevel(userId);
            log.info("vip vipDeductionExperienceJob 扣减经验，userId:{}", userId);
            // 判断用户是否降回1级
            if (optional.isPresent()) {
                UserCurVipInfo userCurVipInfo = optional.get();
                Integer vipLevel = userCurVipInfo.getVipLevel();
                if (vipLevel == 1) {
                    log.info("vip vipDeductionExperienceJob 用户降回1级，userId:{}", userId);
                    return;
                }
                // 如果高于1级，则给用户增加一个月的时间
                // 月末时间
                Date monthEnd = DateTimeUtil.getMonthEnd(recordTime);
                log.info("vip 高于一级的用户，给用户增加一个月时间：uid:{},月末时间:{}", userId, monthEnd);
                long duration = monthEnd.getTime() - System.currentTimeMillis();
                int monthIndex = DateTimeUtil.getMonthIndex(recordTime);
                String userVipFreezeTimeKey = UserRedisKey.user_vip_freeze_time.getKey(userId, monthIndex);
                if (redissonManager.setnx(userVipFreezeTimeKey, monthIndex + "", 10, TimeUnit.DAYS)) {
                    vipRecordService.giveVip(userId, duration, VIPConstant.Source.VIP2_FROZEN, null);
                    log.info("vip 用户增加冻结时间 ，userId:{},duration:{}，monthEnd：{}", userId, duration, monthEnd.getTime());
                    return;
                } else {
                    log.info("vip 用户增加冻结时间失败，本月已增加冻结时间，userId：{}", userId);
                }
            }
        });


        // 升级的用户增加一个月时间
        records.stream().filter(k -> userIds.contains(k.getUserId())).forEach(k -> {
            Long userId = k.getUserId();
            // 如果高于1级，则给用户增加一个月的时间
            // 月末时间
            Date monthEnd = DateTimeUtil.getMonthEnd(recordTime);
            log.info("vip 升级的用户增加一个月时间 uid:{},月末时间:{}", userId, monthEnd);
            long duration = monthEnd.getTime() - System.currentTimeMillis();
            int monthIndex = DateTimeUtil.getMonthIndex(recordTime);
            String userVipFreezeTimeKey = UserRedisKey.user_vip_freeze_time.getKey(userId, monthIndex);
            if (redissonManager.setnx(userVipFreezeTimeKey, monthIndex + "", 10, TimeUnit.DAYS)) {
                vipRecordService.giveVip(userId, duration, VIPConstant.Source.VIP2_FROZEN, null);
                log.info("vip 升级的用户增加冻结时间 userId:{},duration:{}，monthEnd：{}", userId, duration, monthEnd.getTime());
                return;
            } else {
                log.info("vip 升级的用户用户增加冻结时间失败，本月已增加冻结时间，userId：{}", userId);
            }
        });
    }

    /**
     * 续费提醒
     */
    @XxlJob("VipRenewRemind")
    public void expireRemind() {
        /*
         *  获取 剩余 X 天过期的用户id
         */
        // 剩余15天过期的用户，过期时间+15天少于当前时间
        long expireTime = System.currentTimeMillis() + 15 * 24 * 60 * 60 * 1000;


        // 获取小于2级的用户
        Map<Integer, VipProConfigDTO> vipProConfigDTOMap = vipConfigService.getVipProConfigDTOMap();
        VipProConfigDTO vipProConfigDTO = vipProConfigDTOMap.get(2);
        int experience = vipProConfigDTO.getExperience();
        // 批量查询用户vip 信息
        LambdaQueryWrapper<UserVipStatus> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(UserVipStatus::getVipExperience, experience);
        wrapper.lt(UserVipStatus::getEndTime, expireTime);
        wrapper.eq(UserVipStatus::getVipStatus, VIPConstant.Status.EFFECTIVE.getStatus());
        wrapper.orderBy(true, true, UserVipStatus::getId);

        List<UserVipStatus> vipStatuses = userVipStatusService.list(wrapper);

        vipStatuses.forEach(k->{
            // 判断用户剩余多少天
            Long userId = k.getUserId();
            Long endTime = k.getEndTime();
            long remainDay = ((endTime - System.currentTimeMillis()) / 1000)/ DateTimeUtil.SECONDS_PER_DAY;

            // 最后一条的记录的id
            LambdaQueryWrapper<UserVipRecord> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserVipRecord::getUserId,userId);
            queryWrapper.eq(UserVipRecord::getRecordStatus, VIPConstant.Status.EFFECTIVE.getStatus());
            // 排除冻结的
            queryWrapper.ne(UserVipRecord::getGetSource, VIPConstant.Source.VIP2_FROZEN.getSource());
            queryWrapper.orderByDesc(UserVipRecord::getCreateTime);
            List<UserVipRecord> records = vipRecordService.list(queryWrapper);
            UserVipRecord userVipRecord = null;
            if (CollectionUtil.isNotEmpty(records)) {
                userVipRecord = records.get(0);
            }else {
                log.error("vip 查询不到用户获取VIP的有效记录。userId:{}", userId);
                return;
            }
            long recordId = userVipRecord.getId();
            log.info("vip send renew message.uid:{},remainDay:{},recordId:{}", k, remainDay, recordId);
            if (remainDay < 1) {
                String value = redissonManager.get(UserRedisKey.user_vip_expire_push_record.getKey(k, 1, recordId));
                if (StringUtils.isBlank(value)) {
                    // 推送
                    send(userId, "1", 0);
                    redissonManager.setnx(UserRedisKey.user_vip_expire_push_record.getKey(k, 1, recordId), "1", 365, TimeUnit.DAYS);
                }
                return;
            }
            if (remainDay < 3) {
                String value = redissonManager.get(UserRedisKey.user_vip_expire_push_record.getKey(k, 3, recordId));
                if (StringUtils.isBlank(value)) {
                    send(userId, "3", 0);
                    redissonManager.setnx(UserRedisKey.user_vip_expire_push_record.getKey(k, 3, recordId), "1", 365, TimeUnit.DAYS);
                }
                return;
            }
            if (remainDay < 7) {
                String value = redissonManager.get(UserRedisKey.user_vip_expire_push_record.getKey(k, 7, recordId));
                if (StringUtils.isBlank(value)) {
                    send(userId, "7", 0);
                    redissonManager.setnx(UserRedisKey.user_vip_expire_push_record.getKey(k, 7, recordId), "1", 365, TimeUnit.DAYS);
                }
                return;
            }
            if (remainDay < 15) {
                String value = redissonManager.get(UserRedisKey.user_vip_expire_push_record.getKey(k, 15, recordId));
                if (StringUtils.isBlank(value)) {
                    send(userId, "15", 0);
                    redissonManager.setnx(UserRedisKey.user_vip_expire_push_record.getKey(k, 15, recordId), "1", 365, TimeUnit.DAYS);
                }
                return;
            }
        });

    }

    /**
     * 处理Vip过期
     */
    @XxlJob("VipExpire")
    public void handleExpire() {
        // 获取状态生效，但已过期的数据
        long expireTime = System.currentTimeMillis();
        // 获取小于2级的用户
        Map<Integer, VipProConfigDTO> vipProConfigDTOMap = vipConfigService.getVipProConfigDTOMap();
        VipProConfigDTO vipProConfigDTO = vipProConfigDTOMap.get(2);
        int experience = vipProConfigDTO.getExperience();
        // 批量查询用户vip 信息
        LambdaQueryWrapper<UserVipStatus> wrapper = new LambdaQueryWrapper<>();
        wrapper.lt(UserVipStatus::getVipExperience, experience);
        wrapper.lt(UserVipStatus::getEndTime, expireTime);
        wrapper.eq(UserVipStatus::getVipStatus, VIPConstant.Status.EFFECTIVE.getStatus());
        wrapper.orderBy(true, true, UserVipStatus::getId);
        List<UserVipStatus> vipStatuses = userVipStatusService.list(wrapper);

        // 拿到过期的用户id
        Set<Long> userIds = vipStatuses.stream().map(UserVipStatus::getUserId).collect(Collectors.toSet());

        // 更新数据
        vipStatuses.forEach(k->{
            Long userId = k.getUserId();
            k.setVipStatus(AristocracyConstant.Status.INVALID.getStatus());
            userVipStatusService.updateById(k);
            // 刷新缓存
            userVipService.flushCache(userId);
            // 发送过期的消息
            send(k.getUserId(), "", 1);
            log.info("vip send expire message.uid:{}", k.getUserId());
        });
    }


    /**
     * @param targetUid
     * @param replaceText
     * @param type          0 续费提醒 1 过期
     */
    private void send(long targetUid, String replaceText, int type) {

        try {
            LanguageEnum languageEnum = userServerService.userAppLanguage(targetUid);
            Map<String, String> titleMap = new HashMap<>();
            Map<String, String> textMap = new HashMap<>();
            Map<String, String> nameMap = new HashMap<>();
            Map<Integer, VipConfigDTO> vipConfigDTOS = vipConfigService.getVipConfigDTOS();
            VipConfigDTO vipConfigDTO = vipConfigDTOS.get(1);
            String offlineTitle = "";
            String offlineText = "";
            //发送消息
            if (type == 0) {
                VipRemainDayConfigDTO vipRemainDayConfigDTO = vipConfigService.getVipRemainDayConfigDTO();
                titleMap.put("ar", vipRemainDayConfigDTO.getArTitle());
                titleMap.put("en", vipRemainDayConfigDTO.getEnTitle());
                textMap.put("ar", String.format(vipRemainDayConfigDTO.getArText(), replaceText));
                textMap.put("en", String.format(vipRemainDayConfigDTO.getEnText(), replaceText));
                if (languageEnum.equals(LanguageEnum.ar)) {
                    offlineTitle = vipRemainDayConfigDTO.getArTitle();
                    offlineText = String.format(vipRemainDayConfigDTO.getOfflineArText(), replaceText);
                } else {
                    offlineTitle = vipRemainDayConfigDTO.getEnTitle();
                    offlineText = String.format(vipRemainDayConfigDTO.getOfflineEnText(), replaceText);
                }
            } else if (type == 1) {
                VipRemainDayConfigDTO expireConfig = vipConfigService.getVipExpireConfigDTO();
                titleMap.put("ar", expireConfig.getArTitle());
                titleMap.put("en", expireConfig.getEnTitle());
                textMap.put("ar", expireConfig.getArText());
                textMap.put("en", expireConfig.getEnText());
                if (languageEnum.equals(LanguageEnum.ar)) {
                    offlineTitle = expireConfig.getArTitle();
                    offlineText = String.format(expireConfig.getOfflineArText(), replaceText);
                } else {
                    offlineTitle = expireConfig.getEnTitle();
                    offlineText = String.format(expireConfig.getOfflineEnText(), replaceText);
                }
            }
            VipMsgDTO.VipInfo vipInfo = new VipMsgDTO.VipInfo();
            VipMsgDTO textMsgModel = new VipMsgDTO();

            textMsgModel.setTitleMap(titleMap);
            textMsgModel.setTextMap(textMap);
            textMsgModel.setType(VIPConstant.MsgType.RENEW.getType());
            textMsgModel.setLinkUrl("nady:///main/me-vip");
            nameMap.put("ar", vipConfigDTO.getArName());
            nameMap.put("en", vipConfigDTO.getEnName());

            vipInfo.setVipLevel(1);
            vipInfo.setIconUrl(vipConfigDTO.getIconUrl());
            vipInfo.setNameMap(nameMap);

            textMsgModel.setVipInfo(vipInfo);

            String textMsgModelStr = JSONUtil.toJsonStr(textMsgModel);
            CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                    IMMsgType.Vip,
                    System.currentTimeMillis(), textMsgModelStr);

            OfflinePushInfo offlinePushInfo = null;
            try {
                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                        offlineTitle,
                        offlineText,
                        vipInfo.getIconUrl(),
                        textMsgModel.getLinkUrl());
            } catch (Exception e) {
                // ignore
                log.info("sendvipInfo build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
            }

            notifyMessageComponent.publishSystemDefineMessage(imMessage, targetUid + "",
                    offlinePushInfo);
            log.info("vip 发送临期/过期消息: [{}]  targetUid: [{}]", textMsgModelStr, targetUid);
        } catch (Exception e) {
            log.error("vip send message error.targetUid:{},{}", targetUid, ExceptionUtil.formatEx(e));
        }
    }

    /**
     * 增加经验
     */
   // @XxlJob("addExperience")
    public void addExperience(long userId, int experience) {
        userVipService.addExperience(userId, experience, VIPConstant.ExperienceSource.REWARD);
    }

}
