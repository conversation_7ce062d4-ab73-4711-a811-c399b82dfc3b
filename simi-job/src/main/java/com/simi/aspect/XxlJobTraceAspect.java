package com.simi.aspect;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Aspect
@Component
public class XxlJobTraceAspect {
    private final String TRACE_ID = "traceId";
    private final String TRACE_PREFIX = "traceId=>";

    /**
     * 注入traceId
     * @param joinPoint
     */
    @Before("@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public void beforeMethod(JoinPoint joinPoint) {
        // 注入traceId
        MDC.put(TRACE_ID, TRACE_PREFIX + UUID.randomUUID());
    }

    /**
     * 清除MDC
     * @param joinPoint
     */
    @After("@annotation(com.xxl.job.core.handler.annotation.XxlJob)")
    public void afterMethod(JoinP<PERSON> joinPoint) {
        MDC.clear();
    }
}
