package com.simi.task;

import com.simi.service.user.BlockServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;


@Slf4j
@Component
@RequiredArgsConstructor
public class BlockStaleDatedTask {


    private final BlockServerService blockServerService;


    //过期
    @Scheduled(cron = "0 0/1 * * * ? ")
    public void blockStaleDated(){
        log.info("EventTrackingTask reportedRoomPeakValue start ...");
        blockServerService.blockStaleDated();
        log.info("EventTrackingTask reportedRoomPeakValue end ...");
    }
}
