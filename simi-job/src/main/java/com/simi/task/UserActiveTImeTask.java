package com.simi.task;

import com.simi.service.UserActiveTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/05/09 11:18
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class UserActiveTImeTask {

    private final UserActiveTaskService userActiveTaskService;

    /**
     * 扫描已经失活用户,解绑, 推消息
     */
    //@Scheduled(fixedRate = 2000)
    @Scheduled(cron = "5 1 21 * * ?")
    public void unBoundActiveUser(){
        log.info("time to updunBound active user status.");
        long start = System.currentTimeMillis();
        userActiveTaskService.unBoundActiveUser();
        long end = System.currentTimeMillis();
        log.info("Finish to updunBound active user status. cost:[{}]ms", end - start);
    }

    /**
     * 扫描不活跃用户推消息给邀请者
     */
    @Scheduled(cron = "0 1 21 * * ?")
    public void expiringSoonActiveUser(){
        log.info("time to expiring soon active user status.");
        long start = System.currentTimeMillis();
        userActiveTaskService.expiringSoonActiveUser();
        long end = System.currentTimeMillis();
        log.info("Finish to expiring soon active user status. cost:[{}]ms", end - start);
    }


}
