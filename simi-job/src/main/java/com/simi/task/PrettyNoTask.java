package com.simi.task;

import com.simi.service.pretty.PrettyNoService;
import com.simi.service.RewardPackTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class PrettyNoTask {

    private final PrettyNoService prettyNoService;

    /**
     * 奖励包定时发放
     */
    @Scheduled(fixedRate = 2 * 60 * 1000)
    public void usePrettyNoTask(){
        log.info("time to grant usePrettyNo.");
        prettyNoService.usePrettyNo();
        log.info("Finish to grant usePrettyNo.");
    }

}
