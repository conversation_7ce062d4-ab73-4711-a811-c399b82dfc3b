package com.simi.listener.tim;

import com.simi.common.constant.rocketmq.RocketMQGroup;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.service.user.UserOnlineService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMQTopic.TIM_LOGIN_CALLBACK_TOPIC, consumerGroup = RocketMQGroup.TIM_LOGIN_CALLBACK, consumeMode = ConsumeMode.CONCURRENTLY)
public class TimLoginCallBackListener implements RocketMQListener<MessageExt> {

    private final UserOnlineService userOnlineService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("tim login callback topic message listener msg[{}] msgId[{}]", new String(message.getBody()), message.getMsgId());
        String uidStr = new String(message.getBody());
        try {
            userOnlineService.setOnline(Long.parseLong(uidStr), Boolean.TRUE);
        } catch (Exception e) {
            log.error("tim login callback topic fail uid:[{}] e:[{}]", uidStr, e.getMessage());
        }
    }
}
