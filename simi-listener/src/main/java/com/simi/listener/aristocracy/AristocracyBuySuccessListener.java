package com.simi.listener.aristocracy;

import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.google.common.collect.Sets;
import com.simi.common.config.PlatformConfig;
import com.simi.common.constant.LanguageEnum;
import com.simi.common.constant.RewardPackCopywritingEnum;
import com.simi.common.constant.TimeZoneEnum;
import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.constant.rocketmq.RocketMQGroup;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.constant.tim.IMMsgType;
import com.simi.common.dto.TimeZoneDateDTO;
import com.simi.common.dto.UserPropInfoDTO;
import com.simi.common.dto.activity.prize.PrizeDTO;
import com.simi.common.dto.aristocracy.*;
import com.simi.common.dto.tencent.OfflinePushInfo;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.entity.CommonIMMessage;
import com.simi.constant.PushEvent;
import com.simi.constant.PushToType;
import com.simi.constant.RoomRedisKey;
import com.simi.constant.UserRedisKey;
import com.simi.dto.push.AristocracyMsgDTO;
import com.simi.entity.rewardpack.RewardPackResource;
import com.simi.entity.room.Room;
import com.simi.manager.BackpackManager;
import com.simi.manager.RewardPackManager;
import com.simi.service.BannerServerService;
import com.simi.service.LongLinkService;
import com.simi.service.PropServerService;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.message.NotifyMessageComponent;
import com.simi.service.mission.MissionService;
import com.simi.service.rewardpack.RewardPackServerService;
import com.simi.service.room.RoomService;
import com.simi.service.room.roommic.RoomMicMiddleService;
import com.simi.service.user.UserServerService;
import com.simi.util.PushMsgUtil;
import com.simi.common.util.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-30 15:26
 **/
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMQTopic.ARISTOCRACY_BUY_SUCCESS, consumerGroup = RocketMQGroup.ARISTOCRACY_CONSUMER, consumeMode = ConsumeMode.CONCURRENTLY)
public class AristocracyBuySuccessListener implements RocketMQListener<MessageExt> {

    private final AristocracyConfigService aristocracyConfig;
    private final RewardPackServerService rewardPackServerService;
    private final RewardPackManager rewardPackManager;
    private final PropServerService propServerService;
    private final BackpackManager backpackManager;
    private final UserServerService userServerService;
    private final MissionService missionService;
    private final UserAristocracyRecordsService userAristocracyRecordsService;
    private final NotifyMessageComponent notifyMessageComponent;
    private final BannerServerService bannerServerService;
    private final RedissonManager redissonManager;
    private final LongLinkService longLinkService;
    private final RoomMicMiddleService roomMicMiddleService;

    /**
     * 处理的资源类型
     * 头像框、坐骑、气泡、光圈、进房特效
     */
    private final static Set<Integer> handleResourceType = Sets.newHashSet(3, 8, 2, 5, 4);

    @Override
    public void onMessage(MessageExt message) {
        String s = new String(message.getBody());
        log.info("aristocracy buy success listener msg:[{}] msgId:[{}]", s, message.getMsgId());
        BuySuccessMsg buySuccessMsg = JSONUtil.toBean(s, BuySuccessMsg.class);
        if (Objects.isNull(buySuccessMsg)) {
            log.error("aristocracy buy message is null .s:{}", s);
            return;
        }

        /**
         *  1.发送奖励包
         *  2.给用户穿戴奖励
         */
        int aristocracyId = buySuccessMsg.getAristocracyId();
        Optional<RewardIdConfigDTO> rewardIdConfigDTOOptional = aristocracyConfig.getRewardIdConfigDTO(aristocracyId);
        Optional<AristocracyConfigDTO> aristocracyConfigOpt = aristocracyConfig.aristocracyConfigById(aristocracyId);
        if (rewardIdConfigDTOOptional.isEmpty() || aristocracyConfigOpt.isEmpty()) {
            log.error("aristocracy config is not exist. id:{},msg:{},", aristocracyId, buySuccessMsg);
            return;
        }
        AristocracyConfigDTO aristocracyConfig = aristocracyConfigOpt.get();
        int grade = buySuccessMsg.getGrade();
        long userId = buySuccessMsg.getUserId();
        int recordId = buySuccessMsg.getRecordId();
        int source = buySuccessMsg.getSource();
        RewardIdConfigDTO rewardIdConfigDTO = rewardIdConfigDTOOptional.get();
        // 获取档位对应的奖励Id
        List<RewardIdConfigDTO.GradeVO> grades = rewardIdConfigDTO.getGrades();
        Optional<RewardIdConfigDTO.GradeVO> first = grades.stream().filter(k -> k.getId() == grade).findFirst();
        if (first.isEmpty()) {
            log.error("aristocracy config grade is not exist. id:{},msg:{},rewardIdConfigDTO:{}",
                    aristocracyId, buySuccessMsg, rewardIdConfigDTO);
            return;
        }
        RewardIdConfigDTO.GradeVO gradeVO = first.get();
        // 发送奖励包
        int packId = gradeVO.getPackageId();
        sendPackAndUse(userId, packId, recordId,aristocracyConfig);
        sendSignReward(userId, source, buySuccessMsg.getBuyType());
        // 如果是赠送的，那么发送消息
        if (AristocracyConstant.Source.GIFT.getSource() == source
                || AristocracyConstant.Source.GIFT_6.getSource() == source) {
            Long giveUserId = buySuccessMsg.getGiveUserId();
            sendMessage(userId, giveUserId, aristocracyId, AristocracyConstant.MsgType.GIFT.getType(), grade);
        }

        // 判断用户是否升级
        if ((AristocracyConstant.BuyType.UPGRADE == buySuccessMsg.getBuyType()
                || AristocracyConstant.BuyType.ADD == buySuccessMsg.getBuyType())
                && aristocracyConfig.getId() > 3
                && (AristocracyConstant.Source.GIFT.getSource() == source || AristocracyConstant.Source.BUY.getSource() == source)) {
            bannerServerService.pushAristocracyRoomNotice(userId, aristocracyConfig);
        }
    }

    private void sendMessage(long targetUid, Long sendUserId, int aristocracyId, int type, int grade) {
        try {
            TimeZoneEnum userTimeZone = userServerService.userTimeZone(targetUid);
            TimeZoneDateDTO zoneDateDTO = TimeZoneUtils.getTime(new Date(), userTimeZone);
            String routeUrl = ClientRouteUtil.toBackpack();
            // 获取贵族信息
            Optional<AristocracyConfigDTO> aristocracyOptional = aristocracyConfig.getAristocracyConfigById(aristocracyId);
            if (aristocracyOptional.isEmpty()) {
                log.error("aristocracy is null.aristocracyId:{}", aristocracyId);
                return;
            }
            AristocracyConfigDTO aristocracyConfigDTO = aristocracyOptional.get();
            Optional<AristocracyConfigDTO.GradeDTO> gradeDTOOptional = aristocracyConfigDTO.getGrades().stream().filter(k -> k.getId() == grade).findAny();
            if (gradeDTOOptional.isEmpty()) {
                log.error("aristocracy grade is null.aristocracyId:{} grade:{}", aristocracyId, grade);
                return;
            }
            Map<String, String> titleMap = new HashMap<>();
            Map<String, String> nameMap = new HashMap<>();
            //发送消息
            AristocracyRemainDayConfigDTO sendConfig = aristocracyConfig.getAristocracySendConfig();
            titleMap.put("ar", sendConfig.getArTitle());
            titleMap.put("en", sendConfig.getEnTitle());
            AristocracyMsgDTO.AristocracyInfo aristocracyInfo = new AristocracyMsgDTO.AristocracyInfo();
            AristocracyMsgDTO textMsgModel = new AristocracyMsgDTO();

            textMsgModel.setTitleMap(titleMap);
            textMsgModel.setType(type);
            textMsgModel.setLinkUrl("nady:///main/me-aristocracy");
            nameMap.put("ar", aristocracyConfigDTO.getArName());
            nameMap.put("en", aristocracyConfigDTO.getEnName());

            aristocracyInfo.setAristocracyId(aristocracyConfigDTO.getId());
            aristocracyInfo.setIconUrl(aristocracyConfigDTO.getIconUrl());
            aristocracyInfo.setDay(gradeDTOOptional.get().getDay());
            aristocracyInfo.setNameMap(nameMap);
            textMsgModel.setAristocracyInfo(aristocracyInfo);

            if (sendUserId != null) {
                UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(sendUserId);
                Long userNo = userBaseInfo.getUserNo();
                textMsgModel.setSendUserNo(userNo);
            }

            String textMsgModelStr = JSONUtil.toJsonStr(textMsgModel);
            CommonIMMessage imMessage = notifyMessageComponent.buildCommonIMMessage(
                    IMMsgType.Aristocracy,
                    new Date().getTime(), textMsgModelStr);

            OfflinePushInfo offlinePushInfo = null;
            try {
                LanguageEnum languageEnum = userServerService.userAppLanguage(targetUid);
                String offlineTitle = textMsgModel.getTitleMap().get(languageEnum.name());
                offlinePushInfo = PushMsgUtil.buildImOfflinePushInfo(String.valueOf(PlatformConfig.systemUid),
                        offlineTitle,
                        offlineTitle,
                        aristocracyInfo.getIconUrl(),
                        textMsgModel.getLinkUrl());
            } catch (Exception e) {
                // ignore
                log.info("sendAristocracyInfo build tim message offlinePushInfo error:[{}]", ExceptionUtil.formatEx(e));
            }

            notifyMessageComponent.publishSystemDefineMessage(imMessage, targetUid + "",
                    offlinePushInfo);
            log.info("aristocracy send message: [{}]  targetUid: [{}]", textMsgModelStr, targetUid);
        } catch (Exception e) {
            log.error("aristocracy send message error.targetUid:{},{}", targetUid, ExceptionUtil.formatEx(e));
        }
    }

    /**
     * 补发当日签到内容
     */
    private void sendSignReward(long userId, int source, AristocracyConstant.BuyType buyType) {
        try {
            // 如果购买行为是 新增，则判断是否需要补发签到奖励
            if (AristocracyConstant.Source.BUY.getSource() != source
                    && AristocracyConstant.Source.GIFT.getSource() != source) {
                return;
            }
            // 判断用户是否今天补发过
            Date recordTime = TimeZoneUtils.getTime(new Date(), TimeZoneEnum.GMT3).getRecordTime();
            String dayFormat = DateUtil.format(DateUtil.beginOfDay(recordTime), DateTimeUtil.DAY_PATTERN);
            String value = redissonManager.get(UserRedisKey.user_aristocracy_check_in.getKey(userId, dayFormat));

            // 判断用户是否已签到和新购买
            if (AristocracyConstant.BuyType.ADD == buyType && missionService.todaySigned(userId)) {
                log.info("aristocracy send sign reward. userId:{}", userId);
                // 贵族奖励
                Optional<UserCurAristocracyInfo> optional = userAristocracyRecordsService.getUserAristocracyRecordsCache(userId);
                optional.ifPresent(item -> {
                    Integer signPackId = item.getAristocracyPropInfo().getSignPackId();
                    if (Objects.nonNull(signPackId)) {
                        PrizeDTO signPackDTO = PrizeDTO.builder()
                                .uid(userId)
                                .packId(signPackId)
                                .bizOrderId(System.currentTimeMillis())
                                .sourceTypeEnum(RewardPackCopywritingEnum.ARISTOCRACY_CHECK_IN).build();
                        rewardPackServerService.sendRewardPack(signPackDTO);
                        redissonManager.set(UserRedisKey.user_aristocracy_check_in.getKey(userId, dayFormat), "1", 1, TimeUnit.DAYS);
                    }
                });
            }
        } catch (Exception e) {
            log.error("aristocracy sendSignReward error.userId:{},", userId);
        }
    }

    /**
     * 发送奖励包
     * @param userId
     * @param packId
     * @param recordId
     * @param aristocracyConfig
     */
    private void sendPackAndUse(long userId, int packId, int recordId, AristocracyConfigDTO aristocracyConfig) {
        log.info("aristocracy send reward. userId:{},rewardId:{}.recordId:{}", userId, packId, recordId);
        LanguageEnum languageEnum = userServerService.userAppLanguage(userId);
        // 发送奖励
        PrizeDTO prizeDTO = PrizeDTO.builder()
                .uid(userId)
                .packId(packId)
                .sourceTypeEnum(RewardPackCopywritingEnum.ARISTOCRACY)
                .bizOrderId(System.currentTimeMillis())
                .copywritingPlaceholder(languageEnum == LanguageEnum.ar
                        ? aristocracyConfig.getArName() : aristocracyConfig.getEnName())
                .build();
        rewardPackServerService.sendRewardPack(prizeDTO);
        log.info("aristocracy send reward success. userId:{},rewardId:{}.recordId:{}", userId, packId, recordId);
        // 给用户穿上装扮
        List<RewardPackResource> resourceList = rewardPackManager.validRewardResourceListByPackId(packId);
        Map<Integer, UserPropInfoDTO> integerUserPropInfoDTOMap
                = Optional.ofNullable(propServerService.batchUserUsingProp(Lists.newArrayList(userId)).get(userId))
                .orElseGet(HashMap::new);

        resourceList.forEach(item -> {
            Integer prizeType = item.getPrizeType();
            Integer prizeId = item.getPrizeId();
            if (handleResourceType.contains(prizeType)) {
                try {
                    log.info("aristocracy handle resource. userId:{},recordId:{},propId:{},type:{},item:{}",
                            userId, recordId, prizeId, prizeType, item);
                    // 判断用户是否有穿
                    UserPropInfoDTO userPropInfoDTO = integerUserPropInfoDTOMap.get(prizeType);
                    if (Objects.isNull(userPropInfoDTO)) {
                        // 装备
                        backpackManager.useGoods(userId, prizeId, prizeType);
                        try {
                            String roomId = redissonManager.hGet(RoomRedisKey.room_user_in.getKey(), userId + "");
                            if (StringUtils.isNotBlank(roomId)) {
                                longLinkService.pushMicChangedNotice(roomId,roomMicMiddleService.listMicInfo(roomId,false), PushEvent.room_mic_update_event, PushToType.MESSAGE_TO_ALL);
                            }

                        } catch (Exception e) {
                            log.info("gift message listener room mic update:[{}] e:[{}]  ",  e.getMessage());
                        }
                    }
                } catch (Exception e) {
                    log.error("aristocracy handle resource error. userId:{},recordId:{},item:{}", userId, recordId, item, e);
                }
            }
        });
    }
}
