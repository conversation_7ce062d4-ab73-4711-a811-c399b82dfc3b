package com.simi.listener.aristocracy;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.aristocracy.AristocracyConstant;
import com.simi.common.constant.rocketmq.RocketMQGroup;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.dto.aristocracy.UnSubscribeConfigDTO;
import com.simi.common.dto.aristocracy.UnsubscribeMsg;
import com.simi.common.util.CommonUtil;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.BillEnum;
import com.simi.entity.aristocracy.UserAristocracyRecordsDO;
import com.simi.service.aristocracy.AristocracyConfigService;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.purse.PurseManageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.assertj.core.util.Lists;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @description:
 * @author: lrq
 * @create: 2024-07-30 16:00
 **/
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMQTopic.ARISTOCRACY_UNSUBSCRIBE, consumerGroup = RocketMQGroup.ARISTOCRACY_UNSUBSCRIBE, consumeMode = ConsumeMode.CONCURRENTLY)
public class AristocracyUnsubscribeListener implements RocketMQListener<MessageExt> {

    private final UserAristocracyRecordsService userAristocracyRecordsService;
    private final PurseManageService purseManageService;
    private final AristocracyConfigService aristocracyConfigService;

    @Override
    public void onMessage(MessageExt message) {
        String s = new String(message.getBody());
        log.info("aristocracy unsubscribe listener msg:[{}] msgId:[{}]", s, message.getMsgId());
        UnsubscribeMsg bean = JSONUtil.toBean(s, UnsubscribeMsg.class);
        if (Objects.isNull(bean)) {
            log.error("aristocracy unsubscribe message is null .s:{}", s);
            return;
        }
        // 批量获取记录
        List<Integer> recordIds = bean.getRecordIds();
        List<UserAristocracyRecordsDO> recordsByIds = userAristocracyRecordsService.getRecordsByIds(recordIds);
        // 回退金币
        recordsByIds.forEach(this::returnCoin);
    }

    /**
     * 回退金币逻辑
     */
    public void returnCoin(UserAristocracyRecordsDO item) {
        try {
            // 判断来源，只处理购买或赠送
            Integer getSource = item.getGetSource();
            Integer aristocracyLevel = item.getAristocracyLevel();
            if (item.getRecordStatus() != AristocracyConstant.Status.INVALID.getStatus()) {
                log.error("aristocracy unsubscribe record status error. item:{},{}", item, userAristocracyRecordsService.getRecordsByIds(Lists.newArrayList(item.getId())));
            }
            if (AristocracyConstant.Source.BUY.getSource() != getSource
                    && AristocracyConstant.Source.GIFT.getSource() != getSource) {
                return;
            }
            Optional<UnSubscribeConfigDTO> unSubscribeConfigDTOOptional = aristocracyConfigService.getUnSubscribeConfig(aristocracyLevel);
            if (unSubscribeConfigDTOOptional.isEmpty()) {
                log.error("aristocracy unsubscribe config not found. userId:[{}] recordId:[{}],aristocracyId:{}",
                        item.getUserId(), item.getId(), aristocracyLevel);
                return;
            }
            log.info("aristocracy unsubscribe return gold coins userId:[{}] recordId:[{}]",
                    item.getUserId(), item.getId());
            UnSubscribeConfigDTO unSubscribeConfigDTO = unSubscribeConfigDTOOptional.get();
            int price = unSubscribeConfigDTO.getPrice();
            Long startTime = item.getStartTime();
            Long endTime = item.getEndTime();
            // 计算剩余天数 向上取整
            long now = System.currentTimeMillis();
            long endTimeMillis = endTime;
            // 该贵族是否已经生效 判断开始时间是否大于当前时间
            long days = 0;
            if (startTime <= now) {
                // 已生效
                // 计算两个时间戳之间的时间差
                Duration duration = Duration.ofMillis(endTimeMillis - now);
                days = duration.toDays();
                // 获取总天数并向上取整，这里无论怎么算都是加1天
                days++;
            } else {
                // 未生效，直接取订单的天数
                days = item.getDurationDays();
            }
            if (days == 0) {
                log.info("aristocracy unsubscribe return gold coins day is zero .userId:[{}] recordId:[{}] days:[{}]",
                        item.getUserId(), item.getId(), days);
                return;
            }
            long amount = price * days;
            log.info("aristocracy unsubscribe return gold coins userId:[{}] recordId:[{}] days:[{}],amount:{},unSubscribeConfig:{}",
                    item.getUserId(), item.getId(), days, amount, unSubscribeConfigDTO);
            // 回退金币
            purseManageService.addCoin(item.getUserId(), amount, BillEnum.ARISTOCRACY_REWARD_GOLDEN_TICKET,
                    CommonUtil.genId(), "", Collections.emptyMap(), 0l, PurseRoleTypeEnum.USER.getType());
            // 更新记录
            item.setRecordStatus(AristocracyConstant.Status.REVERTED.getStatus());
            userAristocracyRecordsService.updateById(item);
            log.info("aristocracy unsubscribe return gold coins success userId:[{}] recordId:[{}] amount:{}",
                    item.getUserId(), item.getId(), amount);
        } catch (Exception e) {
            log.error("aristocracy unsubscribe error.item:{},{}", item, ExceptionUtil.formatEx(e), e);
        }
    }
}
