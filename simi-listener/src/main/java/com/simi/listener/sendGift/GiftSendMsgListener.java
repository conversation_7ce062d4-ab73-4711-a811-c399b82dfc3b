package com.simi.listener.sendGift;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.SystemConfigConstant;
import com.simi.common.constant.UserLevelTypeEnum;
import com.simi.common.constant.gift.GiftTypeEnum;
import com.simi.common.constant.medal.MedalTaskEnum;
import com.simi.common.constant.rocketmq.RocketMQGroup;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.dto.LuckyGiftBigMultipleDTO;
import com.simi.common.dto.SendLuckyGiftBannerDTO;
import com.simi.common.dto.SmallMultipleDTO;
import com.simi.common.dto.aristocracy.UserCurAristocracyInfo;
import com.simi.common.dto.game.FruitPartyConfigDTO;
import com.simi.common.dto.gift.GiftInfoDTO;
import com.simi.common.dto.user.UserBaseInfoDTO;
import com.simi.common.dto.vip.UserCurVipInfo;
import com.simi.common.entity.room.RoomInfoDTO;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.service.LuckyGiftService;
import com.simi.common.service.SystemConfigService;
import com.simi.common.util.ExceptionUtil;
import com.simi.common.vo.resp.UserLevelBaseVO;
import com.simi.config.UserLevelConfig;
import com.simi.constant.*;
import com.simi.entity.GiftInfo;
import com.simi.entity.LevelInfo;
import com.simi.entity.gift.GiftSendRecord;
import com.simi.event.SendGiftEvent;
import com.simi.message.GiftSendMessage;
import com.simi.service.GiftInfoService;
import com.simi.service.LongLinkService;
import com.simi.service.agent.AgentAffiliationService;
import com.simi.service.agent.AgentCache;
import com.simi.service.aristocracy.UserAristocracyRecordsService;
import com.simi.service.cache.GiftWallCache;
import com.simi.service.cache.MicCharmCache;
import com.simi.service.gift.GiftSendRecordService;
import com.simi.service.gift.UserGiftService;
import com.simi.service.medal.MedalTaskService;
import com.simi.service.purse.PurseManageService;
import com.simi.service.room.RoomHighService;
import com.simi.service.room.roommic.RoomMicMiddleService;
import com.simi.service.user.UserLevelRecordsService;
import com.simi.service.user.UserServerService;
import com.simi.service.vip.UserVipService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.redisson.api.RScoredSortedSet;
import org.redisson.api.RedissonClient;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;

@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMQTopic.SEND_GIFT, consumerGroup = RocketMQGroup.GROUP_SEND_GIFT, consumeMode = ConsumeMode.CONCURRENTLY)
public class GiftSendMsgListener implements RocketMQListener<MessageExt> {

    private final GiftSendRecordService giftSendRecordService;
    private final RedissonClient redissonClient;
    private final RedissonDistributionLocker distributionLocker;
    private final PurseManageService purseManageService;
    private final ApplicationContext applicationContext;
    private final UserGiftService userGiftService;
    private final UserServerService userServerService;
    private final GiftWallCache giftWallCache;
    private final GiftInfoService  giftInfoService;
    private final SystemConfigService systemConfigService;
    private final UserAristocracyRecordsService userAristocracyRecordsService;
    private final UserLevelRecordsService userLevelRecordsService;
    private final UserVipService userVipService;
    private final LuckyGiftService luckyGiftService;
    private final LongLinkService longLinkService;
    private final MedalTaskService medalTaskService;
    private final RoomHighService roomHighService;
    private final MicCharmCache micCharmCache;
    private final RoomMicMiddleService roomMicMiddleService;
    private final AgentCache agentCache;
    private final AgentAffiliationService agentAffiliationService;

    private static final int  multiple_demarcation = 10;

    public void onMessage(MessageExt message) {
        String giftMessageStr = JSONUtil.toJsonStr(new String(message.getBody()));
        GiftSendMessage giftSendMsg = JSONUtil.toBean(new String(message.getBody()), GiftSendMessage.class);
        log.info("gift message listener msg[{}] cost:[{}] msgId:[{}]", giftMessageStr,
                System.currentTimeMillis() - giftSendMsg.getMessageTime(),
                message.getMsgId());

        try (Locker locker = distributionLocker.lock(giftLockKey(giftSendMsg.getMessageId()))) {
            // 防止消息被重复消费
            if (Objects.isNull(locker)) {
                log.warn("GiftSendListener message had handle, mess: " + message);
                return;
            }
            String messStatus = redissonClient.<String, String>getMap(mqStatusKey()).get(giftSendMsg.getMessageId());
            if (StringUtils.isBlank(messStatus)) {
                return;
            }
            handleGiftSendMessage(giftSendMsg);
        } catch (Exception e) {
            log.info("gift message listener error giftMessage:[{}] e:[{}]  ", giftMessageStr, e.getMessage());
        }
        try {
            longLinkService.pushMicChangedNotice(giftSendMsg.getRoomId(),roomMicMiddleService.listMicInfo(giftSendMsg.getRoomId(),false), PushEvent.room_mic_update_event, PushToType.MESSAGE_TO_ALL);
        } catch (Exception e) {
            log.info("gift message listener room mic update:[{}] e:[{}]  ", giftMessageStr, e.getMessage());
        }
    }

    private void sendLuckyGift(GiftSendRecord giftSendMsg,Integer giftPrice, Integer giftType) {
        log.info("lucky gift giftSendMsg:{} giftPrice:{} giftType:{}",JSONUtil.toJsonStr(giftSendMsg),giftPrice,giftType);
        String roomId = giftSendMsg.getRoomId();
        List<Integer> multiplys = new ArrayList<>();
        Long amount = 0L;
        if (GiftTypeEnum.GIFT_TYPE_LUCKY.getType().equals(giftType)) {
            String giftGoldScale = systemConfigService.getSysConfValueById(SystemConfigConstant.LUCK_GIFT_GOLD_EARNINGS_SCALE);
            UserBaseInfoDTO userBaseInfo = userServerService.getUserBaseInfo(giftSendMsg.getUid());
            GiftInfo giftInfoFromCache = giftInfoService.getGiftInfoFromCache(giftSendMsg.getGiftId());
            for (int i = 0; i < giftSendMsg.getGiftNum() * giftSendMsg.getComboTimes(); i++) {
                Integer prize = luckyGiftService.getPrize(giftSendMsg.getUid());
                if (prize > 0) {
                    multiplys.add(prize);
                    Long price = new BigDecimal(giftPrice).multiply(new BigDecimal(giftGoldScale)).longValue();
                    amount += new BigDecimal(price).multiply(new BigDecimal(prize)).longValue();
                }
            }
            log.info("lucky gift uid:{} amount:{}",giftSendMsg.getUid(),amount);
            GiftInfoDTO dto = new GiftInfoDTO();
            if (giftInfoFromCache != null) {
                dto = new GiftInfoDTO();
                BeanUtil.copyProperties(giftInfoFromCache,dto);
            }
            boolean hasBigMultiple = false;
            if (CollectionUtils.isNotEmpty(multiplys)) {
                for (Integer multiply : multiplys) {
                    Long price = new BigDecimal(giftPrice).multiply(new BigDecimal(giftGoldScale)).longValue();
                    if (multiply <= multiple_demarcation && price > 0) {
                        int awardAmount = new BigDecimal(price).multiply(new BigDecimal(multiply)).intValue();
                        SmallMultipleDTO multipleDTO = new SmallMultipleDTO();
                        multipleDTO.setAwardAmount(awardAmount);
                        multipleDTO.setUid(giftSendMsg.getUid());
                        if (awardAmount > 0) {
                            longLinkService.pushCustomerRoomMsg(roomId, multipleDTO, PushEvent.lucky_gift_small_multiple, PushToType.EXCLUDE_OTHER);
                        }
                    }
                    if (multiply > multiple_demarcation && price > 0) {
                        hasBigMultiple = true;
                        SendLuckyGiftBannerDTO bannerDTO = new SendLuckyGiftBannerDTO();
                        bannerDTO.setMultiply(multiply);
                        bannerDTO.setUserBaseInfo(userBaseInfo);
                        bannerDTO.setGiftInfo(dto);
                        String config = systemConfigService.getSysConfValueById(SystemConfigConstant.LUCKY_GIFT_BANNER_CONFIG);
                        if (StrUtil.isBlank(config)) {
                            log.warn("joy effect banner config empty");
                            return;
                        }
                        FruitPartyConfigDTO configDTO = JSONUtil.toBean(config, FruitPartyConfigDTO.class);
                        bannerDTO.setEffectUrl(configDTO.getEffectUrl());
                        bannerDTO.setUpperEffect(configDTO.getUpperEffect());
                        bannerDTO.setIcon(dto.getIcon());
                        bannerDTO.setRoomId(roomId);
                        String roomIdFlag = null;
                        RoomInfoDTO roomInfoDTO = roomHighService.getById(roomId);
                        if (Objects.nonNull(roomInfoDTO) && Objects.nonNull(roomInfoDTO.getRoomLock())
                                && roomInfoDTO.getRoomLock() && StrUtil.isNotBlank(roomInfoDTO.getRoomPasswd())) {
                            roomIdFlag = roomInfoDTO.getRoomId();
                        }
                        log.info("sendLuckyGift uid:{} roomInfoDTO:{} roomIdFlag:{}",giftSendMsg.getUid(),JSONUtil.toJsonStr(roomInfoDTO),roomIdFlag);
                        if (StringUtils.isEmpty(roomIdFlag)) {
                            longLinkService.pushCustomerRoomBannerList(roomIdFlag, bannerDTO,PushEvent.room_luck_gift_send_banner_event, PushToType.MESSAGE_TO_ALL);
                        }
                        longLinkService.pushCustomerRoomMsg(roomIdFlag ,bannerDTO, PushEvent.room_luck_gift_send_banner_event, PushToType.MESSAGE_TO_ALL);
                    }
                }
                if (hasBigMultiple && amount > 0) {
                    LuckyGiftBigMultipleDTO multipleDTO = new LuckyGiftBigMultipleDTO();
                    multipleDTO.setAmount(amount);
                    multipleDTO.setUserBaseInfo(userBaseInfo);
                    longLinkService.pushCustomerRoomMsg(roomId,multipleDTO, PushEvent.lucky_gift_big_multiple, PushToType.MESSAGE_TO_ALL);
                }
            }
        }
        log.info("lucky gift addCoin uid:{} amount:{}",giftSendMsg.getUid(),amount);
        if (amount > 0) {
            purseManageService.addCoin(giftSendMsg.getUid(), amount, BillEnum.GIFT_COMMISSION_GOLD, giftSendMsg.getId().toString() + "+1", "", Collections.emptyMap(),0L,PurseRoleTypeEnum.USER.getType());
        }

    }

    private void handleGiftSendMessage(GiftSendMessage giftSendMsg) {

        long begin = System.currentTimeMillis();
        Long toUid = giftSendMsg.getTargeUid();
        long sender = giftSendMsg.getUid();
        String roomId = giftSendMsg.getRoomId();
        long receiver = giftSendMsg.getTargeUid();
        Map<Long, UserBaseInfoDTO> userMap = userServerService.batchUserSummary(CollUtil.newArrayList(sender, receiver));
        Integer giftNum = giftSendMsg.getGiftNum();
        Integer giftPrice = giftSendMsg.getGiftPrice();
        Integer comboTimes = giftSendMsg.getComboTimes();
        Long exp = giftPrice * giftNum * comboTimes.longValue();

        redissonClient.<String, String>getMap(mqStatusKey()).remove(giftSendMsg.getMessageId());

        userGiftService.addReceiveGiftValue(toUid, giftNum, giftPrice, comboTimes);
        // 事件分发
        publishEvent(giftSendMsg);
        //加经验值
        try {
            sendLevel(userMap, receiver, exp, sender);
        } catch (Exception e) {
            log.error("sendLevel error:[{}].............", e.getMessage(), e);
        }
        //操作钱包
        try {
            handlePurse(giftSendMsg);
        } catch (Exception e) {
            log.error("handleGiftMessage error[{}].............", e.getMessage(), e);
        }
        try {
            micCharmCache.setMicCharm(roomId,toUid,exp);
            micCharmCache.setSendRoom(roomId);
            micCharmCache.setSendMicCharm(roomId,sender,toUid,exp);
        } catch (Exception e) {
            log.error("act giftWallCache error[{}].............", e.getMessage(), e);
        }
        try {
            giftWallCache.setGiftWallCache(toUid, giftSendMsg.getGiftId(), giftNum);
        } catch (Exception e) {
            log.error("act giftWallCache error[{}].............", e.getMessage(), e);
        }

        long end = System.currentTimeMillis();
        log.info("deal giftMessageListener done:[{}]ms", (end - begin));
    }

    private void sendLevel(Map<Long, UserBaseInfoDTO> userMap, long receiver, Long exp, long sender) {
        /**
         * 如果是贵族，魅力值经验加成
         */
        long charmExp = 0;
        try {
            charmExp = exp;
            Optional<UserCurAristocracyInfo> optional = userAristocracyRecordsService.getUserAristocracyRecordsCache(receiver);
            if (optional.isPresent()) {
                UserCurAristocracyInfo userCurAristocracyInfo = optional.get();
                Integer charmBonus = userCurAristocracyInfo.getAristocracyPropInfo().getCharmBonus();
                // 计算加成后的经验 向下取整
                charmExp = new BigDecimal(exp).multiply(new BigDecimal(charmBonus)).longValue() / 100;
                log.info("aristocracy add charm reward. userId:{},charmBonus:{},charmExp:{},exp:{}", receiver, charmBonus, charmExp, exp);
            }
        } catch (Exception e) {
            log.error("aristocracy add charm error. userId:{},charmExp:{},exp:{},e:{}", receiver, charmExp, exp, ExceptionUtil.formatEx(e), e);
        }

        if (charmExp > 0) {
            // 勋章任务
            medalTaskService.executeMedalTask(MedalTaskEnum.RECEIVE_GOLDS_GIFTS, receiver, (int) charmExp);
        }

        /**
         * 如果是VIP，财富值经验加成
         */
        long wealthExp = exp;
        try {
            Optional<UserCurVipInfo> userCurVipInfoOptional = userVipService.getUserVipCache(sender);
            if (userCurVipInfoOptional.isPresent()) {
                UserCurVipInfo userCurVipInfo = userCurVipInfoOptional.get();
                int wealthBonus = userCurVipInfo.getPropInfo().getWealthCoefficient();
                // 计算加成后的经验 向下取整
                wealthExp = new BigDecimal(wealthExp).multiply(new BigDecimal(wealthBonus)).longValue() / 100;
                log.info("vip add wealth reward. userId:{},wealthBonus:{},wealthExp:{},exp:{}", sender, wealthBonus, wealthExp, exp);
            }
        } catch (Exception e) {
            log.error("vip add wealth error. userId:{},wealthExp:{},exp:{},e:{}", sender, wealthExp, exp, ExceptionUtil.formatEx(e), e);
        }


        long userCharmAmount = redissonClient.<String, Long>getMap(UserRedisKey.user_charm_level.getKey()).addAndGet(String.valueOf(receiver), charmExp);
        userLevelRecordsService.saveOrUpdateUserLevelRecords(receiver,userCharmAmount,UserLevelTypeEnum.LEVEL_CHARM.getType());
        long userWealthAmount = redissonClient.<String, Long>getMap(UserRedisKey.user_wealth_level.getKey()).addAndGet(String.valueOf(sender), wealthExp);
        userLevelRecordsService.saveOrUpdateUserLevelRecords(receiver,userWealthAmount,UserLevelTypeEnum.LEVEL_WEALTH.getType());

        UserBaseInfoDTO recvUserBaseInfoDTO = userMap.get(receiver);
        if (Objects.nonNull(recvUserBaseInfoDTO)) {
            UserLevelBaseVO recvUserLevel = recvUserBaseInfoDTO.getUserLevel();
            if (Objects.isNull(recvUserLevel)) { // 补偿处理一下, 优化前缓存没有等级的信息
                userServerService.clearUserSummaryCache(receiver);
            } else {
                LevelInfo recvLevelInfo = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_CHARM, userCharmAmount);
                if (!Objects.equals(recvUserLevel.getCharmLevel(), recvLevelInfo.getLevel())) {
                    userServerService.clearUserSummaryCache(receiver);
                }

            }
        }

        UserBaseInfoDTO sendUserBaseInfoDTO = userMap.get(sender);
        if (Objects.nonNull(sendUserBaseInfoDTO)) {
            UserLevelBaseVO sendUserLevel = sendUserBaseInfoDTO.getUserLevel();
            if (Objects.isNull(sendUserLevel)) {
                userServerService.clearUserSummaryCache(sender);
            } else {
                LevelInfo sendLevelInfo = UserLevelConfig.currentLevel(UserLevelTypeEnum.LEVEL_WEALTH, userWealthAmount);
                if (!Objects.equals(sendUserLevel.getWealthLevel(), sendLevelInfo.getLevel())) {
                    userServerService.clearUserSummaryCache(sender);
                }
            }
        }
    }

    public void handlePurse(GiftSendMessage message) {
        Integer diamondPerGift = message.getDiamondPerGift();
        log.info("user[{}] can get {} diamonds per gift profit.", message.getTargeUid(), diamondPerGift);
        Long diamonds = message.getTotalDiamond().longValue();
        GiftSendRecord record = buildRecord(message, diamonds);
        giftSendRecordService.save(record);
        if (diamonds > 0) {
            purseManageService.getPurse(message.getTargeUid());
            //钻石收益比例
            String diamondScale = "0";
            if (GiftTypeEnum.GIFT_TYPE_LUCKY.getType().equals(message.getGiftType())) {
                diamondScale = systemConfigService.getSysConfValueById(SystemConfigConstant.LUCK_DIAMOND_EARNINGS_SCALE);
            } else {
                diamondScale = systemConfigService.getSysConfValueById(SystemConfigConstant.DIAMOND_EARNINGS_SCALE);
            }
            long diamondNum = new BigDecimal(diamondScale).multiply(new BigDecimal(diamonds)).longValue();
            purseManageService.addDiamond(message.getTargeUid(), diamondNum, BillEnum.GIFT_COMMISSION_DIAMOND, record.getId().toString(), "", Collections.emptyMap(),message.getUid(), PurseRoleTypeEnum.USER.getType());
            boolean host = agentAffiliationService.isHost(message.getTargeUid());
            if (host) {
                agentCache.agentIncome(message.getTargeUid(), (int)diamondNum);
            }
            //金币收益比例
            String goldScale = "0";
            if (GiftTypeEnum.GIFT_TYPE_LUCKY.getType().equals(message.getGiftType())) {
                goldScale = systemConfigService.getSysConfValueById(SystemConfigConstant.LUCK_GOLD_EARNINGS_SCALE);
            } else {
                goldScale = systemConfigService.getSysConfValueById(SystemConfigConstant.GOLD_EARNINGS_SCALE);
            }
            long goldNum = new BigDecimal(goldScale).multiply(new BigDecimal(diamonds)).longValue();
            if (goldNum != 0) {
                purseManageService.addCoin(message.getTargeUid(), goldNum, BillEnum.GIFT_COMMISSION_GOLD, record.getId().toString(), "", Collections.emptyMap(),message.getUid(),PurseRoleTypeEnum.USER.getType());
            }
        }
        if (StringUtils.isNotBlank(message.getRoomId())) {
            RScoredSortedSet<String> zSet = redissonClient.<String>getScoredSortedSet(
                    RevenueRedisKey.giftSendCensusKey(message.getRoomId()));
            String element = StrUtil.format("{}&{}", message.getMessageId(), message.getTotalCoin());
            zSet.addScore(element, message.getSendTime().getTime());
        }
        try {
            sendLuckyGift(record, message.getGiftPrice(), message.getGiftType());
        } catch (Exception e) {
            log.info("lucky gift message listener error giftMessage:[{}] e:[{}]  ", JSONUtil.toJsonStr(record), ExceptionUtil.formatEx(e));
        }
    }


    private GiftSendRecord buildRecord(GiftSendMessage message, Long diamonds) {
        return GiftSendRecord.builder()
                .comboId(message.getComboId())
                .uid(message.getUid())
                .targetUid(message.getTargeUid())
                .sendType(message.getSendType())
                .roomId(message.getRoomId())
                .giftId(message.getGiftId())
                .giftNum(message.getGiftNum())
                .comboTimes(message.getComboTimes())
                .totalCoin(message.getGiftPrice() * message.getGiftNum() * message.getComboTimes())
                .giftSource(message.getGiftSource())
                .diamondNum(diamonds)
                .actualCoinNum(message.getGiftPrice() * message.getGiftNum() * message.getComboTimes())
                .createTime(message.getSendTime())
                .build();
    }

    public static String mqStatusKey() {
        return RevenueRedisKey.mq_gift_status.getKey();
    }

    private static String giftLockKey(String messageId) {
        return RevenueRedisKey.gift_message_lock.getKey(messageId);
    }


    public void publishEvent(GiftSendMessage message) {
        SendGiftEvent event = new SendGiftEvent(message);
        applicationContext.publishEvent(event);
    }
}
