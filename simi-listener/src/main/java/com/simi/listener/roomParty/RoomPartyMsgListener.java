package com.simi.listener.roomParty;

import cn.hutool.json.JSONUtil;
import com.simi.common.constant.rocketmq.RocketMQGroup;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.exception.ApiException;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.common.util.ExceptionUtil;
import com.simi.constant.RoomPartyRedisKey;
import com.simi.message.RoomPartyMessage;
import com.simi.service.room.party.RoomPartyServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/07/15 21:35
 **/
@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMQTopic.ROOM_PARTY_DELAY_TOPIC, consumerGroup = RocketMQGroup.ROOM_PARTY_NOTIFY_CONSUMER, consumeMode = ConsumeMode.CONCURRENTLY)
public class RoomPartyMsgListener implements RocketMQListener<MessageExt> {

    private final RedissonDistributionLocker distributionLocker;
    private final RoomPartyServerService roomPartyServerService;


    @Override
    public void onMessage(MessageExt messageExt) {
        String messageStr = new String(messageExt.getBody());
        log.info("Room party mq msg listener message:[{}] msgId[{}]", messageStr, messageExt.getMsgId());
        RoomPartyMessage message = JSONUtil.toBean(messageStr, RoomPartyMessage.class);
        try (Locker locker = distributionLocker.lock(RoomPartyRedisKey.room_party_mq_msg_local.getKey(message.getMessageId()))) {
            if (Objects.isNull(locker)) {
                log.info("Unable to obtain lock for room party mq msg message:[{}] msgId[{}]", messageStr, messageExt.getMsgId());
                return;
            }
            roomPartyServerService.dealRoomPartyEvent(message);
        } catch (ApiException ae) {
            throw ae;
        } catch (Exception e) {
            log.error("handling mission message:[{}], error:{}", messageStr, ExceptionUtil.formatEx(e));
        }
    }

}
