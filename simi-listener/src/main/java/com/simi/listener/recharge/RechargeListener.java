package com.simi.listener.recharge;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.simi.common.constant.BlockReasonEnum;
import com.simi.common.constant.PurseRoleTypeEnum;
import com.simi.common.constant.RechargeRecordStatusEnum;
import com.simi.common.constant.rocketmq.RocketMQGroup;
import com.simi.common.constant.rocketmq.RocketMQTopic;
import com.simi.common.redis.Locker;
import com.simi.common.redis.RedissonDistributionLocker;
import com.simi.constant.BillEnum;
import com.simi.constant.RevenueRedisKey;
import com.simi.entity.purse.RechargeRecord;
import com.simi.message.recharge.OneTimeProductNotification;
import com.simi.message.recharge.RechargeMessage;
import com.simi.message.recharge.VoidedPurchaseNotification;
import com.simi.service.purse.PurseManageService;
import com.simi.service.purse.RechargeRecordService;
import com.simi.service.user.BlockServerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.stereotype.Component;

import java.util.*;

@Component
@Slf4j
@RequiredArgsConstructor
@RocketMQMessageListener(topic = RocketMQTopic.RECHARGE_CALL_BACK, consumerGroup = RocketMQGroup.GROUP_RECHARGE_CALL_BACK, consumeMode = ConsumeMode.CONCURRENTLY)
public class RechargeListener implements RocketMQListener<MessageExt> {

    private final RedissonDistributionLocker distributionLocker;
    private final RechargeRecordService rechargeRecordService;
    private final PurseManageService purseManageService;
    private final BlockServerService blockServerService;

    @Override
    public void onMessage(MessageExt message) {
        log.info("recharge message listener msg:[{}] msgId:[{}]", new String(message.getBody()), message.getMsgId());
        try {
            RechargeMessage rechargeMessage = JSONUtil.toBean(new String(message.getBody()), RechargeMessage.class);
            log.info("recharge message listener rechargeMessage[{}]", JSONUtil.toJsonStr(rechargeMessage));

            if (rechargeMessage != null) {
                if (rechargeMessage.getOneTimeProductNotification() != null) {
                    OneTimeProductNotification oneTimeProductNotification = rechargeMessage.getOneTimeProductNotification();
                    try (Locker locker = distributionLocker.lock(RevenueRedisKey.recharge_google_check_lock.getKey(StrUtil.format("{{}}", oneTimeProductNotification.getPurchaseToken())))) {
                        if (Objects.isNull(locker)) {
                            log.warn("recharge Listener message get locker fail, mess: " + message);
                            return;
                        }
                        rechargeRecordService.rechargeSuccess(oneTimeProductNotification);
                    } catch (Exception e) {
                        log.error("recharge message listener error:{} ", e.getMessage(), e);
                    }
                } else if (rechargeMessage.getVoidedPurchaseNotification() != null) {
                    //退款
                    VoidedPurchaseNotification voidedPurchaseNotification = rechargeMessage.getVoidedPurchaseNotification();

                    purchaseNotification(voidedPurchaseNotification, message.getMsgId());

                }
            }
        } catch (Exception e) {
            log.error("recharge message listener fail msg:[{}]  msgId:[{}] exceMsg:[{}]", new String(message.getBody()), message.getMsgId(), e.getMessage(), e);
        }
    }


    public void purchaseNotification(VoidedPurchaseNotification voidedPurchaseNotification, String msgId) {
        log.info("user google refund voidedPurchaseNotification:[{}] msgId:[{}]", JSONUtil.toJsonStr(voidedPurchaseNotification), msgId);
        //扣金币
        String orderId = voidedPurchaseNotification.getOrderId();
        LambdaQueryWrapper<RechargeRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(RechargeRecord::getChannelOrderId, orderId);
        wrapper.eq(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_SUCCESS.getType());
        RechargeRecord record = rechargeRecordService.getOne(wrapper);
        if (Objects.isNull(record)) {
            log.error("google refund callback service record is empty voidedPurchaseNotification:[{}] msgId:[{}]",
                    voidedPurchaseNotification, msgId);
            return;
        }
        LambdaUpdateWrapper<RechargeRecord> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.set(RechargeRecord::getStatus, RechargeRecordStatusEnum.RECHARGE_REFUND.getType());
        lambdaUpdateWrapper.set(RechargeRecord::getRefundTime, new Date());
        lambdaUpdateWrapper.eq(RechargeRecord::getId, record.getId());
        boolean update = rechargeRecordService.update(lambdaUpdateWrapper);
        if (!update) {
            log.error("google refund update record status fail record:[{}] msgId:[{}]",
                    record, msgId);
        }

        // 封号
        blockServerService.block(null,record.getUid(), BlockReasonEnum.GOOGLE_REFUND, null);

        purseManageService.deductSystemCoin(record.getUid(), record.getCoinAmount(), BillEnum.GOOGLE_PAY_REFUND, voidedPurchaseNotification.getOrderId(), "google recharge", Collections.emptyMap(),0L, true, PurseRoleTypeEnum.USER.getType());

    }

}
