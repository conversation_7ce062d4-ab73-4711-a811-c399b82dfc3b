package com.simi.listener.canal.handler;

import com.simi.common.dto.canal.CanalMsgItem;

import java.util.List;

/**
 * canal消息的处理器
 */
public interface CanalMsgHandler {
     /**
      * 执行时间
      * @param canalMsgItems
      */
     void handle(List<CanalMsgItem> canalMsgItems);

     /**
      * 是否执行
      * @param canalMsgItem
      * @return
      */
     boolean executeHandle(CanalMsgItem canalMsgItem);

     /**
      * 优先级，越大越快执行
      * @return
      */
     int priority();
}
